package com.turbospaces.rules.serialization;

import java.util.List;

public class RuleValidationException extends IllegalArgumentException {
    private final ValidationReport validationReport;
    private final List<ValidationError> errors;

    public RuleValidationException(ValidationReport validationReport) {
        super(buildExceptionMessage(validationReport));
        this.validationReport = validationReport;
        this.errors = validationReport.getErrors();
    }

    public ValidationReport getValidationReport() {
        return validationReport;
    }

    public List<ValidationError> getErrors() {
        return errors;
    }

    public List<ValidationError> getErrorsByType(ValidationError.ErrorType type) {
        return validationReport.getErrorsByType(type);
    }

    public List<String> getErrorFieldPaths() {
        return validationReport.getErrorFieldPaths();
    }


    private static String buildExceptionMessage(ValidationReport report) {
        StringBuilder message = new StringBuilder();

        message.append("Rule validation failed with ").append(report.getErrors().size()).append(" error(s)");
        message.append("\n");
        message.append("Validation Summary:\n");
        message.append("   • Errors: ").append(report.getErrors().size()).append("\n");
        if (!report.getErrors().isEmpty()) {
            message.append("Validation Errors:\n");
            for (int i = 0; i < report.getErrors().size(); i++) {
                ValidationError error = report.getErrors().get(i);
                message.append("   ").append(i + 1).append(". [").append(error.getPath()).append("] ")
                        .append(error.getType()).append(": ").append(error.getMessage());

                if (error.getFieldPath() != null) {
                    message.append("\n      Field: ").append(error.getFieldPath());
                }
                if (error.getOperator() != null) {
                    message.append("\n      Operator: ").append(error.getOperator());
                }
                if (error.getFieldType() != null) {
                    message.append("\n      Expected Type: ").append(error.getFieldType().getSimpleName());
                }
                if (error.getValue() != null) {
                    message.append("\n      Value: '").append(error.getValue()).append("'");
                }
                message.append("\n\n");
            }
        }

        List<String> errorFieldPaths = report.getErrorFieldPaths();
        if (!errorFieldPaths.isEmpty()) {
            message.append("Fields with Errors: ").append(String.join(", ", errorFieldPaths)).append("\n\n");
        }
        return message.toString();
    }
}
