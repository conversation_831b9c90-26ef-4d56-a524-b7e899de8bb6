package com.turbospaces.rules.serialization;

import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.rules.EntityMetadata;
import com.turbospaces.rules.FieldDescriptor;
import com.turbospaces.rules.FieldOperator;
import com.turbospaces.rules.OperatorInfo;
import com.turbospaces.rules.RuleCondition;
import com.turbospaces.rules.RuleGroup;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@UtilityClass
public class RuleValidationEngine {
    public static ValidationReport validateRule(RuleGroup ruleGroup, EntityMetadata<?> metadata, CommonObjectMapper mapper) {
        ValidationReport.ValidationReportBuilder builder = ValidationReport.builder();
        List<ValidationError> errors = new ArrayList<>();

        validateRuleGroupRecursive(ruleGroup, metadata, mapper, errors, "");
        return builder
                .valid(errors.isEmpty())
                .errors(errors)
                .build();
    }

    public static ValidationReport validateCondition(RuleCondition condition, EntityMetadata<?> metadata, CommonObjectMapper mapper) {
        ValidationReport.ValidationReportBuilder builder = ValidationReport.builder();
        List<ValidationError> errors = new ArrayList<>();

        validateSingleCondition(condition, metadata, mapper, errors, "condition");

        return builder
                .valid(errors.isEmpty())
                .errors(errors)
                .build();
    }

    private static void validateRuleGroupRecursive(RuleGroup group,
                                                   EntityMetadata<?> metadata,
                                                   CommonObjectMapper mapper,
                                                   List<ValidationError> errors,
                                                   String path) {

        String groupPath = StringUtils.isEmpty(path) ? "root" : path;

        if (group.getConditions() != null) {
            for (int i = 0; i < group.getConditions().size(); i++) {
                RuleCondition condition = group.getConditions().get(i);
                String conditionPath = groupPath + ".conditions[" + i + "]";
                validateSingleCondition(condition, metadata, mapper, errors, conditionPath);
            }
        }

        if (group.getNested() != null) {
            for (int i = 0; i < group.getNested().size(); i++) {
                RuleGroup nested = group.getNested().get(i);
                String nestedPath = groupPath + ".nested[" + i + "]";
                validateRuleGroupRecursive(nested, metadata, mapper, errors, nestedPath);
            }
        }
    }

    private static void validateSingleCondition(RuleCondition condition,
                                                EntityMetadata<?> metadata,
                                                CommonObjectMapper mapper,
                                                List<ValidationError> errors,
                                                String path) {


        String fieldPath = buildFieldPath(condition);
        FieldDescriptor fieldDescriptor = metadata.getFieldDescriptor(fieldPath);

        if (fieldDescriptor == null) {
            errors.add(ValidationError.builder()
                    .path(path + ".field")
                    .type(ValidationError.ErrorType.FIELD_NOT_FOUND)
                    .message("Field '" + fieldPath + "' not found in entity metadata")
                    .fieldPath(fieldPath)
                    .build());
            return;
        }


        if (condition.getOperator() != null) {
            validateOperatorCompatibility(condition, fieldDescriptor, errors, path);
        }


        if (condition.getValue() != null) {
            validateValueTypeCompatibility(condition, fieldDescriptor, mapper, errors, path);
        }


        if (condition.getSubPath() != null) {
            validateNestedPathStructure(condition, metadata, errors, path);
        }
    }

    private static void validateOperatorCompatibility(RuleCondition condition,
                                                      FieldDescriptor fieldDescriptor,
                                                      List<ValidationError> errors,
                                                      String path) {

        FieldOperator operator = condition.getOperator();
        Class<?> fieldType = fieldDescriptor.getType();


        if (operator == FieldOperator.IS_EMPTY || operator == FieldOperator.IS_NOT_EMPTY) {
            return;
        }


        List<OperatorInfo> validOperators = fieldDescriptor.get();
        boolean isOperatorValid = validOperators.stream()
                .anyMatch(op -> op.getOperator() == operator);

        if (!isOperatorValid) {
            errors.add(ValidationError.builder()
                    .path(path + ".operator")
                    .type(ValidationError.ErrorType.OPERATOR_INCOMPATIBLE)
                    .message("Operator '" + operator + "' is not compatible with field type '" + fieldType.getSimpleName() + "'")
                    .fieldPath(buildFieldPath(condition))
                    .operator(operator)
                    .fieldType(fieldType)
                    .build());
        }
    }

    private static void validateValueTypeCompatibility(RuleCondition condition,
                                                       FieldDescriptor fieldDescriptor,
                                                       CommonObjectMapper mapper,
                                                       List<ValidationError> errors, String path) {

        String value = condition.getValue();
        Class<?> fieldType = fieldDescriptor.getType();
        FieldOperator operator = condition.getOperator();


        if (operator == FieldOperator.IS_EMPTY || operator == FieldOperator.IS_NOT_EMPTY) {
            return;
        }

        try {
            validateValueForType(value, fieldType, operator, mapper);
        } catch (Exception e) {
            errors.add(ValidationError.builder()
                    .path(path + ".value")
                    .type(ValidationError.ErrorType.VALUE_TYPE_MISMATCH)
                    .message("Value '" + value + "' cannot be converted to type '" + fieldType.getSimpleName() + "': " + e.getMessage())
                    .fieldPath(buildFieldPath(condition))
                    .value(value)
                    .fieldType(fieldType)
                    .build());
        }
    }

    private static void validateValueForType(String value, Class<?> fieldType, FieldOperator operator, CommonObjectMapper mapper) throws Exception {
        if (StringUtils.isEmpty(value)) {
            return;
        }

        if (fieldType == Integer.class || fieldType == int.class) {
            Integer.parseInt(value);
        } else if (fieldType == Long.class || fieldType == long.class) {
            Long.parseLong(value);
        } else if (fieldType == Double.class || fieldType == double.class) {
            Double.parseDouble(value);
        } else if (fieldType == Float.class || fieldType == float.class) {
            Float.parseFloat(value);
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            if (!"true".equalsIgnoreCase(value) && !"false".equalsIgnoreCase(value)) {
                throw new IllegalArgumentException("Boolean value must be 'true' or 'false'");
            }
        } else if (fieldType == LocalDate.class) {
            try {
                LocalDate.parse(value, DateTimeFormatter.ISO_DATE);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("Date must be in ISO format (yyyy-MM-dd)");
            }
        } else if (fieldType == LocalDateTime.class) {
            try {
                LocalDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("DateTime must be in ISO format (yyyy-MM-ddTHH:mm:ss)");
            }
        } else if (fieldType == Date.class) {
            try {
                mapper.readValue("\"" + value + "\"", Date.class);
            } catch (Exception e) {
                throw new IllegalArgumentException("Date format is invalid");
            }
        } else if (fieldType.isEnum()) {
            try {
                @SuppressWarnings({"unchecked", "rawtypes"})
                Class<Enum> enumClass = (Class<Enum>) fieldType;
                var unused = Enum.valueOf(enumClass, value);
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("'" + value + "' is not a valid enum value for " + fieldType.getSimpleName());
            }
        } else if (operator == FieldOperator.SIZE_EQUALS || operator == FieldOperator.SIZE_GREATER_THAN || operator == FieldOperator.SIZE_LESS_THAN) {
            Integer.parseInt(value);
        }
    }

    private static void validateNestedPathStructure(RuleCondition condition, EntityMetadata<?> metadata,
                                                    List<ValidationError> errors, String path) {

        String subPath = condition.getSubPath();
        FieldDescriptor subPathDescriptor = metadata.getFieldDescriptor(subPath);

        if (subPathDescriptor == null) {
            errors.add(ValidationError.builder()
                    .path(path + ".subPath")
                    .type(ValidationError.ErrorType.NESTED_PATH_NOT_FOUND)
                    .message("Nested path '" + subPath + "' not found in entity metadata")
                    .fieldPath(subPath)
                    .build());
        }
    }

    private static String buildFieldPath(RuleCondition condition) {
        return condition.getSubPath() != null
                ? condition.getSubPath() + "." + condition.getField()
                : condition.getField();
    }
}
