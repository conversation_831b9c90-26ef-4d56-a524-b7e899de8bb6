package com.turbospaces.rules.serialization;

import com.turbospaces.rules.FieldOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ValidationError {
    private String path;
    private ErrorType type;
    private String message;
    private String fieldPath;
    private FieldOperator operator;
    private Class<?> fieldType;
    private String value;

    public enum ErrorType {
        FIELD_NOT_FOUND,
        NESTED_PATH_NOT_FOUND,
        OPERATOR_INCOMPATIBLE,
        VALUE_TYPE_MISMATCH;
    }
}
