package com.turbospaces.rules.serialization;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ValidationReport {
    private boolean valid;
    private List<ValidationError> errors;

    public String getSummary() {
        StringBuilder summary = new StringBuilder();

        summary.append("Validation Report:\n");
        summary.append("- Errors: ").append(errors.size()).append("\n");

        if (!errors.isEmpty()) {
            summary.append("\nErrors:\n");
            for (int i = 0; i < errors.size(); i++) {
                ValidationError error = errors.get(i);
                summary.append("  ").append(i + 1).append(". [").append(error.getPath()).append("] ")
                        .append(error.getType()).append(": ").append(error.getMessage()).append("\n");
            }
        }
        return summary.toString();
    }

    public List<ValidationError> getErrorsByType(ValidationError.ErrorType type) {
        return errors.stream()
                .filter(error -> error.getType() == type)
                .collect(Collectors.toList());
    }

    public boolean hasFieldErrors() {
        return errors.stream().anyMatch(error ->
                error.getType() == ValidationError.ErrorType.FIELD_NOT_FOUND ||
                        error.getType() == ValidationError.ErrorType.NESTED_PATH_NOT_FOUND);
    }

    public boolean hasOperatorErrors() {
        return errors.stream().anyMatch(error ->
                error.getType() == ValidationError.ErrorType.OPERATOR_INCOMPATIBLE);
    }

    public boolean hasValueErrors() {
        return errors.stream().anyMatch(error ->
                error.getType() == ValidationError.ErrorType.VALUE_TYPE_MISMATCH);
    }

    public List<String> getErrorFieldPaths() {
        return errors.stream()
                .map(ValidationError::getFieldPath)
                .filter(path -> path != null)
                .distinct()
                .collect(Collectors.toList());
    }

    public RuleValidationException toException() {
        if (this.valid || this.errors.isEmpty()) {
            throw new IllegalStateException("Cannot create exception from valid validation report. " +
                    "Check isValid() before calling toException().");
        }
        return new RuleValidationException(this);
    }
}
