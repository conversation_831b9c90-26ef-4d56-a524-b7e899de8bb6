package com.turbospaces.rules;

import java.time.Duration;
import java.util.Objects;

import com.turbospaces.common.EventTimeClock;

import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;
import io.micrometer.core.instrument.distribution.HistogramSnapshot;
import io.micrometer.core.instrument.step.StepDistributionSummary;

public class DistributionAccumulator implements DistributionSummary, Accumulator {
    private final StepDistributionSummary summary;
    private final EventTimeClock clock;

    public DistributionAccumulator(Duration interval, EventTimeClock clock) {
        this(interval, clock, 3);
    }
    public DistributionAccumulator(Duration interval, EventTimeClock clock, int buffer) {
        Id id = new Id(toString(), Tags.empty(), null, null, Type.DISTRIBUTION_SUMMARY);
        DistributionStatisticConfig cfg = DistributionStatisticConfig.builder().bufferLength(buffer).expiry(interval).build();
        this.clock = Objects.requireNonNull(clock);
        this.summary = new StepDistributionSummary(id, clock, cfg, 1, interval.toMillis(), false);
    }
    @Override
    public void accept(Number t) {
        record(t.doubleValue());
    }
    @Override
    public Id getId() {
        return summary.getId();
    }
    @Override
    public HistogramSnapshot takeSnapshot() {
        return summary.takeSnapshot();
    }
    @Override
    public void record(double amount) {
        summary.record(amount);
    }
    @Override
    public long count() {
        return summary.count();
    }
    @Override
    public double totalAmount() {
        return summary.totalAmount();
    }
    @Override
    public double max() {
        return summary.max();
    }
    @Override
    public Id id() {
        return summary.getId();
    }
    @Override
    public Clock clock() {
        return clock;
    }
    public static DistributionAccumulator infinite(EventTimeClock clock) {
        return new DistributionAccumulator(Duration.ofMinutes(Integer.MAX_VALUE), clock);
    }
    public static DistributionAccumulator create(Duration interval, EventTimeClock clock) {
        return new DistributionAccumulator(interval, clock);
    }
}
