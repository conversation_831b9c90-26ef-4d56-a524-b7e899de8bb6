package com.turbospaces.rules.mapper;

import com.turbospaces.rules.FieldOperator;
import com.turbospaces.rules.LogicalOperator;
import com.turbospaces.rules.RuleCondition;
import com.turbospaces.rules.RuleGroup;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class RuleGroupMapper {

    public static rule.api.v1.RuleGroup toProto(RuleGroup source) {
        if (source == null) {
            return null;
        }

        rule.api.v1.RuleGroup.Builder builder = rule.api.v1.RuleGroup.newBuilder();

        if (source.getOperator() != null) {
            builder.setOperator(source.getOperator().name().toLowerCase());
        }
        if (source.getName() != null) {
            builder.setName(source.getName());
        }

        if (source.getConditions() != null) {
            List<rule.api.v1.RuleCondition> protobufConditions = source.getConditions().stream()
                    .map(RuleGroupMapper::toProtobufCondition)
                    .collect(Collectors.toList());
            builder.addAllConditions(protobufConditions);
        }

        if (source.getNested() != null) {
            List<rule.api.v1.RuleGroup> protobufNested = source.getNested().stream()
                    .map(RuleGroupMapper::toProto)
                    .collect(Collectors.toList());
            builder.addAllNested(protobufNested);
        }

        return builder.build();
    }

    public static RuleGroup fromProto(rule.api.v1.RuleGroup source) {
        if (source == null) {
            return null;
        }

        RuleGroup.RuleGroupBuilder builder = RuleGroup.builder();

        if (!source.getOperator().isEmpty()) {
            try {
                LogicalOperator operator = LogicalOperator.valueOf(source.getOperator().toUpperCase());
                builder.operator(operator);
            } catch (IllegalArgumentException e) {
                builder.operator(LogicalOperator.AND);
            }
        }
        if (StringUtils.isNotEmpty(source.getName())) {
            builder.name(source.getName());
        }

        List<RuleCondition> internalConditions = source.getConditionsList().stream()
                .map(RuleGroupMapper::fromProto)
                .collect(Collectors.toList());
        builder.conditions(internalConditions);

        List<RuleGroup> internalNested = source.getNestedList().stream()
                .map(RuleGroupMapper::fromProto)
                .collect(Collectors.toList());
        builder.nested(internalNested);

        return builder.build();
    }

    private static rule.api.v1.RuleCondition toProtobufCondition(RuleCondition source) {
        if (source == null) {
            return null;
        }

        rule.api.v1.RuleCondition.Builder builder = rule.api.v1.RuleCondition.newBuilder();

        if (source.getField() != null) {
            builder.setField(source.getField());
        }

        if (source.getSubPath() != null) {
            builder.setSubpath(source.getSubPath());
        }

        if (source.getOperator() != null) {
            builder.setOperator(source.getOperator().name());
        }

        if (source.getValue() != null) {
            builder.setValue(source.getValue());
        }

        return builder.build();
    }

    private static RuleCondition fromProto(rule.api.v1.RuleCondition source) {
        if (source == null) {
            return null;
        }

        RuleCondition.RuleConditionBuilder builder = RuleCondition.builder();

        if (!source.getField().isEmpty()) {
            builder.field(source.getField());
        }

        if (!source.getSubpath().isEmpty()) {
            builder.subPath(source.getSubpath());
        }

        if (!source.getOperator().isEmpty()) {
            try {
                FieldOperator operator = FieldOperator.valueOf(source.getOperator());
                builder.operator(operator);
            } catch (IllegalArgumentException e) {
                builder.operator(FieldOperator.EQUALS);
            }
        }

        if (!source.getValue().isEmpty()) {
            builder.value(source.getValue());
        }

        return builder.build();
    }


}
