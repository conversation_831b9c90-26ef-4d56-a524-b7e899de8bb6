package com.turbospaces.rules.mapper;

import com.turbospaces.rules.FieldOperator;
import com.turbospaces.rules.FieldType;
import com.turbospaces.rules.admin.FieldDescriptor;

import java.util.ArrayList;

public class FieldDescriptorMapper {
    public static FieldDescriptor toAdmin(rule.api.v1.FieldDescriptor pbField) {
        FieldDescriptor adminField = new FieldDescriptor();

        adminField.setPath(pbField.getPath());
        adminField.setLabel(pbField.getLabel());
        adminField.setType(FieldType.valueOf(pbField.getType().toUpperCase()));
        adminField.setOperators(new ArrayList<>(pbField.getOperatorsList().stream().map(op -> FieldOperator.valueOf(op.toUpperCase())).toList()));
        if (pbField.hasTooltip()) {
            adminField.setTooltip(pbField.getTooltip());
        }

        return adminField;
    }
}
