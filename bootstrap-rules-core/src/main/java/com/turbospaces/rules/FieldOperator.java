package com.turbospaces.rules;

public enum FieldOperator {
    EQUALS("=="),
    NOT_EQUALS("!="),
    GREATER_THAN(">"),
    GREATER_THAN_OR_EQUAL(">="),
    LESS_THAN("<"),
    LESS_THAN_OR_EQUAL("<="),
    IN("in"),
    NOT_IN("not in"),
    BETWEEN("between"),
    CONTAINS("contains"),
    NOT_CONTAINS("not contains"),
    STARTS_WITH("startsWith"),
    ENDS_WITH("endsWith"),
    BEFORE("before"),
    AFTER("after"),
    CONTAINS_KEY("containsKey"),
    NOT_CONTAINS_KEY("notContainsKey"),
    CONTAINS_VALUE("containsValue"),
    NOT_CONTAINS_VALUE("notContainsValue"),
    IS_EMPTY("isEmpty"),
    IS_NOT_EMPTY("isNotEmpty"),
    SIZE_EQUALS("sizeEquals"),
    SIZE_GREATER_THAN("sizeGreaterThan"),
    SIZE_LESS_THAN("sizeLessThan");

    private final String code;

    FieldOperator(String code) {
        this.code = code;
    }
    public String getCode() {
        return code;
    }
}

