package com.turbospaces.rules.config;

import com.turbospaces.rules.EntityMetadata;
import com.turbospaces.rules.FieldType;
import com.turbospaces.rules.OperatorInfo;
import rule.api.v1.FieldDescriptor;

import java.util.List;
import java.util.stream.Collectors;

public class EntityMetadataToFieldDescriptorConverter {
    public static <T> List<FieldDescriptor> convertToFieldDescriptor(EntityMetadata<T> metadata) {
        return metadata.getFieldDescriptors().entrySet().stream()
                .map(entry -> convertFieldDescriptor(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    private static FieldDescriptor convertFieldDescriptor(String path, com.turbospaces.rules.FieldDescriptor descriptor) {
        FieldDescriptor.Builder builder = FieldDescriptor.newBuilder()
                .setPath(path)
                .setLabel(getFieldLabel(descriptor))
                .setType(getFieldType(descriptor))
                .addAllOperators(getOperatorNames(descriptor.get()));

        if (descriptor.getTooltip() != null) {
            builder.setTooltip(descriptor.getTooltip());
        }
        return builder.build();
    }

    private static String getFieldLabel(com.turbospaces.rules.FieldDescriptor descriptor) {
        if (descriptor.getLabel() != null) {
            return descriptor.getLabel();
        }
        return convertToTitleCase(descriptor.getPath());
    }

    private static String getFieldType(com.turbospaces.rules.FieldDescriptor descriptor) {
        if (descriptor.getFieldType() != null) {
            return descriptor.getFieldType().getCode();
        }
        FieldType fieldType = FieldType.fromClass(descriptor.getJavaType());
        return fieldType.getCode();
    }

    private static List<String> getOperatorNames(List<OperatorInfo> operators) {
        return operators.stream()
                .map(op -> op.getOperator().name().toLowerCase())
                .collect(Collectors.toList());
    }

    private static String convertToTitleCase(String input) {
        String result = input.replaceAll("([a-z])([A-Z])", "$1 $2")
                .replaceAll("_", " ")
                .toLowerCase();

        String[] words = result.split(" ");
        StringBuilder titleCase = new StringBuilder();
        for (String word : words) {
            if (!word.isEmpty()) {
                titleCase.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    titleCase.append(word.substring(1));
                }
                titleCase.append(" ");
            }
        }
        return titleCase.toString().trim();
    }


}
