package com.turbospaces.rules;

import com.google.common.collect.Maps;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@RequiredArgsConstructor
@Getter
public class EntityMetadata<T> {
    private final Class<T> entityClass;
    private final Map<String, FieldDescriptor> fieldDescriptors = Maps.newHashMap();

    public <V> EntityMetadata<T> field(String path, Class<V> type, Expression<V> expression) {
        String generatedLabel = generateLabelFromPath(path);
        fieldDescriptors.put(path, new FieldDescriptor(path, type, expression, generatedLabel));
        return this;
    }

    public <V> EntityMetadata<T> field(String path, Class<V> type, Expression<V> expression, String label) {
        fieldDescriptors.put(path, new FieldDescriptor(path, type, expression, label));
        return this;
    }

    public <V> EntityMetadata<T> field(String path, Class<V> type, Expression<V> expression, String label, String tooltip) {
        fieldDescriptors.put(path, new FieldDescriptor(path, type, expression, label, tooltip));
        return this;
    }

    public <V> EntityMetadata<T> field(String path, Class<V> type, FieldType fieldType, Expression<V> expression, String label, String tooltip) {
        fieldDescriptors.put(path, new FieldDescriptor(path, type, fieldType, expression, label, tooltip));
        return this;
    }

    public FieldDescriptor getFieldDescriptor(String path) {
        return fieldDescriptors.get(path);
    }

    public Predicate asPredicate(RuleGroup ruleGroup, CommonObjectMapper mapper) throws Exception {
        return QueryDslRuleAdapter.convertToQueryDSL(ruleGroup, this, mapper);
    }

    private String generateLabelFromPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return "Unknown Field";
        }

        String fieldName = path.contains(".") ? path.substring(path.lastIndexOf(".") + 1) : path;

        // Convert camelCase and snake_case to Title Case
        String result = fieldName.replaceAll("([a-z])([A-Z])", "$1 $2")
                .replaceAll("_", " ")
                .toLowerCase();

        String[] words = result.split(" ");
        StringBuilder titleCase = new StringBuilder();
        for (String word : words) {
            if (!word.isEmpty()) {
                titleCase.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    titleCase.append(word.substring(1));
                }
                titleCase.append(" ");
            }
        }
        return titleCase.toString().trim();
    }
}
