package com.turbospaces.rules.admin;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.turbospaces.rules.FieldOperator;
import com.turbospaces.rules.FieldType;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FieldDescriptor {
    /**
     * The field path (e.g., "bet", "win", "account.tags").
     */
    private String path;
    private String label;
    private FieldType type;
    private List<FieldOperator> operators;
    private String tooltip;
}
