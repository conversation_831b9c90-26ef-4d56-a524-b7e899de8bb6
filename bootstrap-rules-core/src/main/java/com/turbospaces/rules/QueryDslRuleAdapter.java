package com.turbospaces.rules;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.ListPath;
import com.querydsl.core.types.dsl.MapPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import lombok.experimental.UtilityClass;

@SuppressWarnings({ "unchecked", "rawtypes" })
@UtilityClass
public class QueryDslRuleAdapter {
    public static <T> Predicate convertToQueryDSL(RuleGroup ruleGroup, EntityMetadata<T> metadata, CommonObjectMapper mapper) throws Exception {
        return buildPredicate(ruleGroup, metadata, mapper);
    }
    private static <T> Predicate buildPredicate(RuleGroup group, EntityMetadata<T> metadata, CommonObjectMapper mapper) throws Exception {
        BooleanBuilder builder = new BooleanBuilder();

        for (RuleCondition condition : group.getConditions()) {
            Predicate conditionPredicate = createPredicateForCondition(condition, metadata, mapper);
            if (group.getOperator() == LogicalOperator.AND) {
                builder.and(conditionPredicate);
            } else {
                builder.or(conditionPredicate);
            }
        }

        for (RuleGroup nestedGroup : group.getNested()) {
            Predicate nestedPredicate = buildPredicate(nestedGroup, metadata, mapper);
            if (group.getOperator() == LogicalOperator.AND) {
                builder.and(nestedPredicate);
            } else {
                builder.or(nestedPredicate);
            }
        }

        return builder.getValue();
    }
    private static <T> Predicate createPredicateForCondition(RuleCondition condition, EntityMetadata<T> metadata, CommonObjectMapper mapper) throws Exception {
        String fieldPath = StringUtils.isNotEmpty(condition.getSubPath()) ? condition.getSubPath() + "." + condition.getField() : condition.getField();

        FieldDescriptor fieldDescriptor = metadata.getFieldDescriptor(fieldPath);
        Preconditions.checkArgument(fieldDescriptor != null, "No field descriptor found for path: %s", fieldPath);

        Expression<?> path = fieldDescriptor.getExpression();
        Preconditions.checkArgument(path != null, "No path expression found for: %s", fieldPath);

        return applyOperatorWithTypeSafety(path, condition.getOperator(), StringUtils.trim(condition.getValue()), fieldDescriptor.getType(), mapper);
    }
    private static Predicate applyOperatorWithTypeSafety(
            Expression<?> path,
            FieldOperator operator,
            String valueStr,
            Class<?> fieldType,
            CommonObjectMapper mapper) throws Exception {
        if (path instanceof StringPath stringPath) {
            return applyStringOperator(stringPath, operator, valueStr);
        } else if (path instanceof NumberPath numberPath) {
            return applyNumberOperator(numberPath, operator, valueStr, fieldType, mapper);
        } else if (path instanceof BooleanPath booleanPath) {
            return applyBooleanOperator(booleanPath, operator, valueStr);
        } else if (path instanceof DateTimePath datetimePath) {
            return applyDateTimeOperator(datetimePath, operator, valueStr, fieldType, mapper);
        } else if (path instanceof EnumPath enumPath) {
            return applyEnumOperator(enumPath, operator, valueStr);
        } else if (path instanceof ListPath listPath) {
            return applyListOperator(listPath, operator, valueStr, fieldType, mapper);
        } else if (path instanceof MapPath mapPath) {
            return applyMapOperator(mapPath, operator, valueStr, fieldType, mapper);
        }

        throw new IllegalArgumentException("Unsupported path type: " + path.getClass().getName());
    }
    private static Predicate applyStringOperator(StringPath path, FieldOperator operator, String value) {
        return switch (operator) {
            case EQUALS -> path.eq(value);
            case NOT_EQUALS -> path.ne(value);
            case CONTAINS -> path.contains(value);
            case NOT_CONTAINS -> path.contains(value).not();
            case STARTS_WITH -> path.startsWith(value);
            case ENDS_WITH -> path.endsWith(value);
            case IN -> path.in(Arrays.asList(value.split(",")));
            case NOT_IN -> path.in(Arrays.asList(value.split(","))).not();
            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for string fields");
        };
    }
    private static Predicate applyNumberOperator(
            NumberPath path,
            FieldOperator operator,
            String valueStr,
            Class<?> fieldType,
            CommonObjectMapper mapper) throws Exception {
        var converter = new Function<String, Number>() {
            @Override
            public Number apply(String input) {
                if (fieldType == Integer.class || fieldType == int.class) {
                    return Integer.parseInt(input);
                } else if (fieldType == Long.class || fieldType == long.class) {
                    return Long.parseLong(input);
                } else if (fieldType == Double.class || fieldType == double.class) {
                    return Double.parseDouble(input);
                } else if (fieldType == Float.class || fieldType == float.class) {
                    return Float.parseFloat(input);
                } else if (fieldType == BigInteger.class) {
                    return new BigInteger(input);
                } else if (fieldType == BigDecimal.class) {
                    return new BigDecimal(input);
                }

                throw new IllegalArgumentException("Unsupported numeric type: " + fieldType.getName());
            }
        };

        return switch (operator) {
            case EQUALS -> path.eq(converter.apply(valueStr));
            case NOT_EQUALS -> path.ne(converter.apply(valueStr));
            case GREATER_THAN -> path.gt(converter.apply(valueStr));
            case GREATER_THAN_OR_EQUAL -> path.goe(converter.apply(valueStr));
            case LESS_THAN -> path.lt(converter.apply(valueStr));
            case LESS_THAN_OR_EQUAL -> path.loe(converter.apply(valueStr));
            case BETWEEN -> {
                var range = mapper.readValue(valueStr, Range.class);
                var lower = (Number) PlatformUtil.lowerClosedEndpoint(range);
                var upper = (Number) PlatformUtil.upperClosedEndpoint(range);
                yield path.between(lower, upper);
            }
            case IN -> path.in(Splitter.on(',').trimResults().omitEmptyStrings().splitToStream(valueStr).map(converter::apply).toList());
            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for numeric fields");
        };
    }
    private static Predicate applyBooleanOperator(BooleanPath path, FieldOperator operator, String valueStr) {
        boolean value = Boolean.parseBoolean(valueStr);

        return switch (operator) {
            case EQUALS -> path.eq(value);
            case NOT_EQUALS -> path.ne(value);
            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for boolean fields");
        };
    }
    private static Predicate applyDateTimeOperator(
            DateTimePath path,
            FieldOperator operator,
            String valueStr,
            Class<?> fieldType,
            CommonObjectMapper mapper) throws Exception {
        var converter = new Function<String, Comparable>() {
            @Override
            public Comparable<?> apply(String input) {
                if (fieldType == LocalDate.class) {
                    return LocalDate.parse(input, DateTimeFormatter.ISO_DATE);
                } else if (fieldType == LocalDateTime.class) {
                    try {
                        return LocalDateTime.parse(input, DateTimeFormatter.ISO_DATE_TIME);
                    } catch (Exception e) {
                        return LocalDate.parse(input, DateTimeFormatter.ISO_DATE).atStartOfDay();
                    }
                } else {
                    return LocalDate.parse(input, DateTimeFormatter.ISO_DATE).atStartOfDay();
                }
            }
        };

        return switch (operator) {
            case EQUALS -> path.eq(converter.apply(valueStr));
            case NOT_EQUALS -> path.ne(converter.apply(valueStr));
            case GREATER_THAN -> path.gt(converter.apply(valueStr));
            case GREATER_THAN_OR_EQUAL -> path.goe(converter.apply(valueStr));
            case LESS_THAN -> path.lt(converter.apply(valueStr));
            case LESS_THAN_OR_EQUAL -> path.loe(converter.apply(valueStr));
            case BEFORE -> path.before(converter.apply(valueStr));
            case AFTER -> path.after(converter.apply(valueStr));
            case BETWEEN -> {
                var range = mapper.readValue(valueStr, Range.class);
                var lower = PlatformUtil.lowerClosedEndpoint(range);
                var upper = PlatformUtil.upperClosedEndpoint(range);
                yield path.between(lower, upper);
            }
            case IN -> path.in(Splitter.on(',').trimResults().omitEmptyStrings().splitToStream(valueStr).map(converter::apply).toList());
            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for date fields");
        };
    }
    private static Predicate applyEnumOperator(EnumPath path, FieldOperator operator, String valueStr) {
        Enum enumValue = Enum.valueOf((Class<Enum>) path.getType(), valueStr);

        return switch (operator) {
            case EQUALS -> path.eq(enumValue);
            case NOT_EQUALS -> path.ne(enumValue);
            case IN -> {
                List<Enum> values = Lists.newLinkedList();
                for (String part : Splitter.on(',').trimResults().omitEmptyStrings().split(valueStr)) {
                    values.add(Enum.valueOf((Class<Enum>) path.getType(), part.trim()));
                }
                yield path.in(values);
            }
            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for enum fields");
        };
    }
    private static Predicate applyListOperator(
            ListPath listPath,
            FieldOperator operator,
            String valueStr,
            Class<?> fieldType,
            CommonObjectMapper mapper) throws Exception {

        var converter = new Function<String, Object>() {
            @Override
            public Object apply(String input) {
                if (fieldType == Integer.class || fieldType == int.class) {
                    return Integer.parseInt(input);
                } else if (fieldType == Long.class || fieldType == long.class) {
                    return Long.parseLong(input);
                } else if (fieldType == Double.class || fieldType == double.class) {
                    return Double.parseDouble(input);
                } else if (fieldType == Float.class || fieldType == float.class) {
                    return Float.parseFloat(input);
                } else if (fieldType == BigInteger.class) {
                    return new BigInteger(input);
                } else if (fieldType == BigDecimal.class) {
                    return new BigDecimal(input);
                } else if (fieldType == String.class) {
                    return input;
                }

                throw new IllegalArgumentException("Unsupported list item type: " + fieldType.getName());
            }
        };

        return switch (operator) {
            case CONTAINS, IN -> listPath.contains(converter.apply(valueStr));
            case NOT_CONTAINS, NOT_IN -> listPath.contains(converter.apply(valueStr)).not();
            case IS_EMPTY -> listPath.isEmpty();
            case IS_NOT_EMPTY -> listPath.isNotEmpty();
            case SIZE_EQUALS -> listPath.size().eq(Integer.parseInt(valueStr));
            case SIZE_GREATER_THAN -> listPath.size().gt(Integer.parseInt(valueStr));
            case SIZE_LESS_THAN -> listPath.size().lt(Integer.parseInt(valueStr));

            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for list fields");
        };
    }
    private static Predicate applyMapOperator(
            MapPath mapPath,
            FieldOperator operator,
            String valueStr,
            Class<?> fieldType,
            CommonObjectMapper mapper) throws Exception {

        var converter = new Function<String, Object>() {
            @Override
            public Object apply(String input) {
                if (fieldType == Integer.class || fieldType == int.class) {
                    return Integer.parseInt(input);
                } else if (fieldType == Long.class || fieldType == long.class) {
                    return Long.parseLong(input);
                } else if (fieldType == Double.class || fieldType == double.class) {
                    return Double.parseDouble(input);
                } else if (fieldType == Float.class || fieldType == float.class) {
                    return Double.parseDouble(input);
                } else if (fieldType == Boolean.class || fieldType == boolean.class) {
                    return Boolean.parseBoolean(input);
                } else if (fieldType == BigInteger.class) {
                    return new BigInteger(input);
                } else if (fieldType == BigDecimal.class) {
                    return new BigDecimal(input);
                } else if (fieldType == String.class) {
                    return input;
                }

                throw new IllegalArgumentException("Unsupported map value type: " + fieldType.getName());
            }
        };

        return switch (operator) {
            case CONTAINS_KEY -> mapPath.containsKey(valueStr);
            case NOT_CONTAINS_KEY -> mapPath.containsKey(valueStr).not();
            case CONTAINS_VALUE -> mapPath.containsValue(converter.apply(valueStr));
            case NOT_CONTAINS_VALUE -> mapPath.containsValue(converter.apply(valueStr)).not();
            case IS_EMPTY -> mapPath.isEmpty();
            case IS_NOT_EMPTY -> mapPath.isNotEmpty();
            case SIZE_EQUALS -> mapPath.size().eq(Integer.parseInt(valueStr));
            case SIZE_GREATER_THAN -> mapPath.size().gt(Integer.parseInt(valueStr));
            case SIZE_LESS_THAN -> mapPath.size().lt(Integer.parseInt(valueStr));
            case EQUALS -> {
                if (valueStr.contains(":")) {
                    String[] parts = valueStr.split(":", 2);
                    String key = parts[0].trim();
                    String expectedValue = parts[1].trim();
                    yield mapPath.get(key).eq(converter.apply(expectedValue));
                }
                throw new IllegalArgumentException("EQUALS on map requires 'key:value' format, got: " + valueStr);
            }
            case NOT_EQUALS -> {
                if (valueStr.contains(":")) {
                    String[] parts = valueStr.split(":", 2);
                    String key = parts[0].trim();
                    String expectedValue = parts[1].trim();
                    yield mapPath.get(key).ne(converter.apply(expectedValue));
                }
                throw new IllegalArgumentException("NOT_EQUALS on map requires 'key:value' format, got: " + valueStr);
            }

            default -> throw new IllegalArgumentException("Operator " + operator + " not supported for map fields");
        };
    }
}
