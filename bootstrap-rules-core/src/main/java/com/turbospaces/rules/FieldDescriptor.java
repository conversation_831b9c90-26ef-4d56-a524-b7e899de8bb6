package com.turbospaces.rules;

import com.querydsl.core.types.Expression;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Data
public class FieldDescriptor implements Supplier<List<OperatorInfo>> {
    private final String path;
    private final Class<?> javaType;
    private final FieldType fieldType;
    private final Expression<?> expression;
    private final String label;
    private final String tooltip;

    public FieldDescriptor(String path, Class<?> javaType, FieldType fieldType, Expression<?> expression, String label, String tooltip) {
        this.path = Objects.requireNonNull(path, "Field path cannot be null");
        this.javaType = Objects.requireNonNull(javaType, "Java type cannot be null");
        this.fieldType = fieldType;
        this.expression = Objects.requireNonNull(expression, "Expression cannot be null");
        this.label = Objects.requireNonNull(label, "Field label cannot be null");
        this.tooltip = tooltip;
    }

    public FieldDescriptor(String path, Class<?> javaType, Expression<?> expression, String label, String tooltip) {
        this(path, javaType, FieldType.fromClass(javaType), expression, label, tooltip);
    }

    public FieldDescriptor(String path, Class<?> javaType, Expression<?> expression, String label) {
        this(path, javaType, expression, label, null);
    }

    public Class<?> getType() {
        return javaType;
    }

    @Override
    public List<OperatorInfo> get() {
        return fieldType.getSupportedOperators().stream()
                .map(OperatorInfo::new)
                .collect(Collectors.toList());
    }
}
