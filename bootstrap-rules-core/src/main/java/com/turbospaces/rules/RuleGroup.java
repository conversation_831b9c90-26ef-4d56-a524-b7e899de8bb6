package com.turbospaces.rules;

import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleGroup {
    private String name;

    @Builder.Default
    private final LogicalOperator operator = LogicalOperator.AND;

    @Builder.Default
    private final List<RuleCondition> conditions = new ArrayList<>();

    @Builder.Default
    private final List<RuleGroup> nested = new ArrayList<>();
}
