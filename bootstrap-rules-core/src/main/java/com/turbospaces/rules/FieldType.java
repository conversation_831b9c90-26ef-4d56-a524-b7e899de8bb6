package com.turbospaces.rules;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

public enum FieldType {
    TEXT("text", Arrays.asList(
        FieldOperator.EQUALS,
        FieldOperator.NOT_EQUALS,
        FieldOperator.CONTAINS,
        FieldOperator.NOT_CONTAINS,
        FieldOperator.STARTS_WITH,
        FieldOperator.ENDS_WITH,
        FieldOperator.IN,
        FieldOperator.NOT_IN,
        FieldOperator.IS_EMPTY,
        FieldOperator.IS_NOT_EMPTY
    )),
    NUMBER("number", Arrays.asList(
        FieldOperator.EQUALS,
        FieldOperator.NOT_EQUALS,
        FieldOperator.GREATER_THAN,
        FieldOperator.GREATER_THAN_OR_EQUAL,
        FieldOperator.LESS_THAN,
        FieldOperator.LESS_THAN_OR_EQUAL,
        FieldOperator.IN,
        FieldOperator.NOT_IN,
        FieldOperator.BETWEEN
    )),
    BOOLEAN("boolean", Arrays.asList(
        FieldOperator.EQUALS,
        FieldOperator.NOT_EQUALS
    )),
    DATE("date", Arrays.asList(
        FieldOperator.EQUALS,
        FieldOperator.NOT_EQUALS,
        FieldOperator.GREATER_THAN,
        FieldOperator.GREATER_THAN_OR_EQUAL,
        FieldOperator.LESS_THAN,
        FieldOperator.LESS_THAN_OR_EQUAL,
        FieldOperator.BETWEEN,
        FieldOperator.BEFORE,
        FieldOperator.AFTER
    )),
    SELECT("select", Arrays.asList(
        FieldOperator.EQUALS,
        FieldOperator.NOT_EQUALS,
        FieldOperator.IN,
        FieldOperator.NOT_IN
    )),
    MULTISELECT("multiselect", Arrays.asList(
        FieldOperator.CONTAINS,
        FieldOperator.NOT_CONTAINS,
        FieldOperator.IN,
        FieldOperator.NOT_IN,
        FieldOperator.IS_EMPTY,
        FieldOperator.IS_NOT_EMPTY,
        FieldOperator.SIZE_EQUALS,
        FieldOperator.SIZE_GREATER_THAN,
        FieldOperator.SIZE_LESS_THAN
    )),
    MAP("map", Arrays.asList(
        FieldOperator.CONTAINS_KEY,
        FieldOperator.NOT_CONTAINS_KEY,
        FieldOperator.CONTAINS_VALUE,
        FieldOperator.NOT_CONTAINS_VALUE,
        FieldOperator.IS_EMPTY,
        FieldOperator.IS_NOT_EMPTY,
        FieldOperator.SIZE_EQUALS,
        FieldOperator.SIZE_GREATER_THAN,
        FieldOperator.SIZE_LESS_THAN,
        FieldOperator.EQUALS,
        FieldOperator.NOT_EQUALS
    ));

    private final String code;
    private final List<FieldOperator> supportedOperators;

    FieldType(String code, List<FieldOperator> supportedOperators) {
        this.code = code;
        this.supportedOperators = supportedOperators;
    }

    public String getCode() {
        return code;
    }

    public List<FieldOperator> getSupportedOperators() {
        return supportedOperators;
    }

    public static FieldType fromClass(Class<?> type) {
        if (String.class.isAssignableFrom(type)) {
            return TEXT;
        } else if (isNumericType(type)) {
            return NUMBER;
        } else if (isBooleanType(type)) {
            return BOOLEAN;
        } else if (isDateType(type)) {
            return DATE;
        } else if (type.isEnum()) {
            return SELECT;
        } else if (isMapType(type)) {
            return MAP;
        } else if (isListType(type)) {
            return MULTISELECT;
        } else {
            return TEXT;
        }
    }

    private static boolean isNumericType(Class<?> type) {
        if (Number.class.isAssignableFrom(type)) {
            return true;
        }
        return type == byte.class || type == short.class || type == int.class || 
               type == long.class || type == float.class || type == double.class;
    }

    private static boolean isDateType(Class<?> type) {
        return Date.class.isAssignableFrom(type) || 
               LocalDate.class.isAssignableFrom(type) || 
               LocalDateTime.class.isAssignableFrom(type);
    }

    private static boolean isBooleanType(Class<?> type) {
        return type == Boolean.class || type == boolean.class;
    }

    private static boolean isMapType(Class<?> type) {
        return Map.class.isAssignableFrom(type);
    }

    private static boolean isListType(Class<?> type) {
        return List.class.isAssignableFrom(type);
    }
}
