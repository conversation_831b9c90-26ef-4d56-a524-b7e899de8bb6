syntax = "proto3";
package rule.api.v1;

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message FieldDescriptor {
  string path = 1;
  string label = 2;
  string type = 3;
  repeated string operators = 4;
  optional string tooltip = 5;
}

message RuleGroup {
  string name = 1;
  string operator = 2;
  repeated RuleGroup nested = 3;
  repeated RuleCondition conditions = 4;
}

message RuleCondition {
  string field = 1;
  string subpath = 2;
  string operator = 3;
  string value = 4;
}
