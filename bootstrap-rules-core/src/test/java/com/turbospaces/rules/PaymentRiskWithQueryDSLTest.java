package com.turbospaces.rules;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.querydsl.collections.CollQuery;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class PaymentRiskWithQueryDSLTest {
    public static final QPaymentRiskStateEntity Q = QPaymentRiskStateEntity.paymentRiskStateEntity;

    //
    // ~ Field(s)
    //
    public static final String ACCOUNT_ID = "account_id";
    public static final String PROVIDER = "provider";
    public static final String ERROR_CODES = "error_codes";
    public static final String ERROR_MESSAGE = "error_message";
    public static final String TIMESTAMP = "timestamp";
    public static final String TRANSACTION_AMOUNT = "transaction_amount";
    public static final String CURRENCY = "currency";
    public static final String IS_BLOCKED = "is_blocked";
    public static final String TOTAL_ERROR_COUNT = "total_error_count";
    public static final String RECENT_ERROR_COUNT = "recent_error_count";

    @Test
    public void testPaymentRiskRules() throws Exception {
        // User 1: Has SPREEDLY_FISERV error with code 51 (should be blocked)
        var entity1 = PaymentRiskStateEntity.builder()
                .accountId(1001L)
                .provider("SPREEDLY_FISERV")
                .errorCodes(Arrays.asList(51, 20))
                .errorMessage("Insufficient funds")
                .timestamp(new Date())
                .transactionAmount(100.0)
                .currency("USD")
                .isBlocked(false)
                .totalErrorCount(5)
                .recentErrorCount(2)
                .build();

        // User 2: Has SPREEDLY_RAPYD error with code 79 (should be blocked)
        var entity2 = PaymentRiskStateEntity.builder()
                .accountId(1002L)
                .provider("SPREEDLY_RAPYD")
                .errorCodes(Arrays.asList(79, 80))
                .errorMessage("Transaction declined by issuer")
                .timestamp(new Date())
                .transactionAmount(250.0)
                .currency("EUR")
                .isBlocked(false)
                .totalErrorCount(3)
                .recentErrorCount(1)
                .build();

        // User 3: Has SPREEDLY_FISERV but different error code (should NOT be blocked)
        var entity3 = PaymentRiskStateEntity.builder()
                .accountId(1003L)
                .provider("SPREEDLY_FISERV")
                .errorCodes(Arrays.asList(50, 52))
                .errorMessage("Different error")
                .timestamp(new Date())
                .transactionAmount(75.0)
                .currency("USD")
                .isBlocked(false)
                .totalErrorCount(1)
                .recentErrorCount(1)
                .build();

        // User 4: Has different provider with code 51 (should NOT be blocked)
        var entity4 = PaymentRiskStateEntity.builder()
                .accountId(1004L)
                .provider("STRIPE")
                .errorCodes(Arrays.asList(51))
                .errorMessage("Insufficient funds")
                .timestamp(new Date())
                .transactionAmount(300.0)
                .currency("USD")
                .isBlocked(false)
                .totalErrorCount(2)
                .recentErrorCount(1)
                .build();

        // User 5: No errors at all (should NOT be blocked)
        var entity5 = PaymentRiskStateEntity.builder()
                .accountId(1005L)
                .provider("SPREEDLY_FISERV")
                .errorCodes(Arrays.asList())
                .errorMessage("Success")
                .timestamp(new Date())
                .transactionAmount(150.0)
                .currency("GBP")
                .isBlocked(false)
                .totalErrorCount(0)
                .recentErrorCount(0)
                .build();

        var mapper = new CommonObjectMapper();
        var paymentStates = Arrays.asList(entity1, entity2, entity3, entity4, entity5);
        var metadata = new EntityMetadata<>(PaymentRiskStateEntity.class)
                .field(ACCOUNT_ID, Long.class, Q.accountId)
                .field(PROVIDER, String.class, Q.provider)
                .field(ERROR_CODES, Integer.class, (Expression) Q.errorCodes)
                .field(ERROR_MESSAGE, String.class, Q.errorMessage)
                .field(TIMESTAMP, Date.class, Q.timestamp)
                .field(TRANSACTION_AMOUNT, Double.class, Q.transactionAmount)
                .field(CURRENCY, String.class, Q.currency)
                .field(IS_BLOCKED, Boolean.class, Q.isBlocked)
                .field(TOTAL_ERROR_COUNT, Integer.class, Q.totalErrorCount)
                .field(RECENT_ERROR_COUNT, Integer.class, Q.recentErrorCount);

        //
        // ~ Apply the main business rule: Block transactions for specific provider/error combinations
        //
        RuleGroup paymentBlockingRule = RuleGroup.builder()
                .name("Payment Error Blocking Rule")
                .operator(LogicalOperator.OR)
                .nested(List.of(
                        // First condition: SPREEDLY_FISERV with code 51
                        RuleGroup.builder()
                                .operator(LogicalOperator.AND)
                                .conditions(List.of(
                                        RuleCondition.builder()
                                                .field(PROVIDER)
                                                .operator(FieldOperator.EQUALS)
                                                .value("SPREEDLY_FISERV")
                                                .build(),
                                        RuleCondition.builder()
                                                .field(ERROR_CODES)
                                                .operator(FieldOperator.IN)
                                                .value("51")
                                                .build()))
                                .build(),
                        // Second condition: SPREEDLY_RAPYD with code 79
                        RuleGroup.builder()
                                .operator(LogicalOperator.AND)
                                .conditions(List.of(
                                        RuleCondition.builder()
                                                .field(PROVIDER)
                                                .operator(FieldOperator.EQUALS)
                                                .value("SPREEDLY_RAPYD")
                                                .build(),
                                        RuleCondition.builder()
                                                .field(ERROR_CODES)
                                                .operator(FieldOperator.IN)
                                                .value("79")
                                                .build()))
                                .build()))
                .build();

        var blockedUsers = applyRuleAndPrintResults(paymentStates, metadata, paymentBlockingRule, mapper);

        //
        // ~ Verify that only users with specific provider/error combinations are blocked
        //
        assertEquals(2, blockedUsers.size(), "Should block exactly 2 users");
        assertTrue(blockedUsers.contains(entity1), "Should block user with SPREEDLY_FISERV error 51");
        assertTrue(blockedUsers.contains(entity2), "Should block user with SPREEDLY_RAPYD error 79");

        //
        // ~ Apply a rule from external configuration (JSON)
        //
        RuleGroup externalPaymentRule = createExternalPaymentRule(mapper);
        var externalRuleResults = applyRuleAndPrintResults(paymentStates, metadata, externalPaymentRule, mapper);

        // Should have same results as programmatic rule
        assertEquals(2, externalRuleResults.size(), "External rule should also block exactly 2 users");
        assertTrue(externalRuleResults.contains(entity1), "External rule should block user 1");
        assertTrue(externalRuleResults.contains(entity2), "External rule should block user 2");

        //
        // ~ More complex rule: Block high-risk users (multiple recent errors OR high transaction amounts with any error)
        //
        RuleGroup complexRiskRule = RuleGroup.builder()
                .name("Complex Risk Assessment")
                .operator(LogicalOperator.OR)
                .conditions(List.of(
                        // High recent error count
                        RuleCondition.builder()
                                .field(RECENT_ERROR_COUNT)
                                .operator(FieldOperator.GREATER_THAN)
                                .value("1")
                                .build()))
                .nested(List.of(
                        // High transaction amount with any error
                        RuleGroup.builder()
                                .operator(LogicalOperator.AND)
                                .conditions(List.of(
                                        RuleCondition.builder()
                                                .field(TRANSACTION_AMOUNT)
                                                .operator(FieldOperator.GREATER_THAN)
                                                .value("200.0")
                                                .build(),
                                        RuleCondition.builder()
                                                .field(TOTAL_ERROR_COUNT)
                                                .operator(FieldOperator.GREATER_THAN)
                                                .value("0")
                                                .build()))
                                .build()))
                .build();

        var complexRiskResults = applyRuleAndPrintResults(paymentStates, metadata, complexRiskRule, mapper);

        //
        // ~ Should match users with recent errors > 1 OR (high amount > 200 AND any errors)
        //
        assertEquals(3, complexRiskResults.size(), "Complex rule should match 3 users");
        assertTrue(complexRiskResults.contains(entity1), "Should match user 1 (recent errors > 1)");
        assertTrue(complexRiskResults.contains(entity2), "Should match user 2 (high amount + errors)");
        assertTrue(complexRiskResults.contains(entity4), "Should match user 4 (high amount + errors)");

        //
        // ~ Test new List size operations
        //
        RuleGroup listSizeRule = RuleGroup.builder()
                .name("Users with Multiple Error Codes")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.SIZE_GREATER_THAN)
                                .value("1")
                                .build()))
                .build();

        var multipleErrorResults = applyRuleAndPrintResults(paymentStates, metadata, listSizeRule, mapper);
        assertEquals(3, multipleErrorResults.size(), "Should find 3 users with multiple error codes");
        assertTrue(multipleErrorResults.contains(entity1), "Should match user 1 (2 errors)");
        assertTrue(multipleErrorResults.contains(entity2), "Should match user 2 (2 errors)");
        assertTrue(multipleErrorResults.contains(entity3), "Should match user 3 (2 errors)");

        //
        // ~ Test empty list operations
        //
        RuleGroup emptyErrorsRule = RuleGroup.builder()
                .name("Users with No Error Codes")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.IS_EMPTY)
                                .value("")
                                .build()))
                .build();

        var noErrorResults = applyRuleAndPrintResults(paymentStates, metadata, emptyErrorsRule, mapper);
        assertEquals(1, noErrorResults.size(), "Should find 1 user with no error codes");
        assertTrue(noErrorResults.contains(entity5), "Should match user 5 (empty errors)");
    }

    private static List<PaymentRiskStateEntity> applyRuleAndPrintResults(
            List<PaymentRiskStateEntity> data,
            EntityMetadata<PaymentRiskStateEntity> metadata,
            RuleGroup ruleGroup,
            CommonObjectMapper mapper) throws Exception {
        Predicate predicate = QueryDslRuleAdapter.convertToQueryDSL(ruleGroup, metadata, mapper);
        var results = new CollQuery<PaymentRiskStateEntity>().from(Q, data).where(predicate).fetch();

        log.info("rule: {}", ruleGroup.getName());
        log.info("predicate: {}", predicate);
        log.info("matching {} items {}", results.size(), results);

        return results;
    }

    private static RuleGroup createExternalPaymentRule(CommonObjectMapper mapper) throws Exception {
        var valueAsString = """
                {
                    "name": "Payment Error Blocking Rule",
                    "operator": "OR",
                    "nested": [
                        {
                            "operator": "AND",
                            "conditions": [
                                {
                                    "field": "provider",
                                    "operator": "EQUALS",
                                    "value": "SPREEDLY_FISERV"
                                },
                                {
                                    "field": "error_codes",
                                    "operator": "IN",
                                    "value": "51"
                                }
                            ]
                        },
                        {
                            "operator": "AND",
                            "conditions": [
                                {
                                    "field": "provider",
                                    "operator": "EQUALS",
                                    "value": "SPREEDLY_RAPYD"
                                },
                                {
                                    "field": "error_codes",
                                    "operator": "IN",
                                    "value": "79"
                                }
                            ]
                        }
                    ]
                }
                """;
        return mapper.readValue(valueAsString, RuleGroup.class);
    }
}
