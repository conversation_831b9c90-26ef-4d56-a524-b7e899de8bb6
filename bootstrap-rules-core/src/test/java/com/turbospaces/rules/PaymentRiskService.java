package com.turbospaces.rules;

import java.util.Date;
import java.util.List;

import com.querydsl.collections.CollQuery;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * Example service showing how to use the payment risk rules in a real payment processing scenario
 */
@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class PaymentRiskService {
    
    private final EntityMetadata<PaymentRiskStateEntity> metadata;
    private final CommonObjectMapper mapper;
    
    public PaymentRiskService() {
        this.mapper = new CommonObjectMapper();
        this.metadata = new EntityMetadata<>(PaymentRiskStateEntity.class)
                .field("account_id", Long.class, QPaymentRiskStateEntity.paymentRiskStateEntity.accountId)
                .field("provider", String.class, QPaymentRiskStateEntity.paymentRiskStateEntity.provider)
                .field("error_codes", Integer.class, (Expression) QPaymentRiskStateEntity.paymentRiskStateEntity.errorCodes)
                .field("error_message", String.class, QPaymentRiskStateEntity.paymentRiskStateEntity.errorMessage)
                .field("timestamp", Date.class, QPaymentRiskStateEntity.paymentRiskStateEntity.timestamp)
                .field("transaction_amount", Double.class, QPaymentRiskStateEntity.paymentRiskStateEntity.transactionAmount)
                .field("currency", String.class, QPaymentRiskStateEntity.paymentRiskStateEntity.currency)
                .field("is_blocked", Boolean.class, QPaymentRiskStateEntity.paymentRiskStateEntity.isBlocked)
                .field("total_error_count", Integer.class, QPaymentRiskStateEntity.paymentRiskStateEntity.totalErrorCount)
                .field("recent_error_count", Integer.class, QPaymentRiskStateEntity.paymentRiskStateEntity.recentErrorCount);
    }
    
    /**
     * Check if a transaction should be blocked based on payment risk rules
     */
    public boolean shouldBlockTransaction(long accountId, List<PaymentRiskStateEntity> paymentHistory, String ruleJson) {
        try {
            // Parse rule from external configuration (e.g., from Retool admin panel)
            RuleGroup riskRule = mapper.readValue(ruleJson, RuleGroup.class);
            
            // Convert to QueryDSL predicate
            Predicate predicate = QueryDslRuleAdapter.convertToQueryDSL(riskRule, metadata, mapper);
            
            // Apply rule to payment history
            var riskyPayments = new CollQuery<PaymentRiskStateEntity>()
                    .from(QPaymentRiskStateEntity.paymentRiskStateEntity, paymentHistory)
                    .where(predicate)
                    .fetch();
            
            boolean shouldBlock = !riskyPayments.isEmpty();
            
            if (shouldBlock) {
                log.warn("Blocking transaction for account {} due to payment risk rules. Found {} risky payment records.", 
                         accountId, riskyPayments.size());
            } else {
                log.info("Transaction approved for account {}. No risk rules triggered.", accountId);
            }
            
            return shouldBlock;
            
        } catch (Exception e) {
            log.error("Error evaluating payment risk rules for account {}", accountId, e);
            // Fail safe - block if we can't evaluate rules
            return true;
        }
    }
    
    /**
     * Example of the business rule as JSON that could be configured externally
     */
    public static final String DEFAULT_PAYMENT_BLOCKING_RULE = """
            {
                "name": "Payment Error Blocking Rule",
                "operator": "OR",
                "nested": [
                    {
                        "operator": "AND",
                        "conditions": [
                            {
                                "field": "provider",
                                "operator": "EQUALS",
                                "value": "SPREEDLY_FISERV"
                            },
                            {
                                "field": "error_codes",
                                "operator": "IN",
                                "value": "51"
                            }
                        ]
                    },
                    {
                        "operator": "AND",
                        "conditions": [
                            {
                                "field": "provider",
                                "operator": "EQUALS",
                                "value": "SPREEDLY_RAPYD"
                            },
                            {
                                "field": "error_codes",
                                "operator": "IN",
                                "value": "79"
                            }
                        ]
                    }
                ]
            }
            """;
}
