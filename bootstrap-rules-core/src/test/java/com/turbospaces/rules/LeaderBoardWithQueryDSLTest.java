package com.turbospaces.rules;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.querydsl.collections.CollQuery;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LeaderBoardWithQueryDSLTest {
    public static final QLeaderboardStateEntity Q = QLeaderboardStateEntity.leaderboardStateEntity;

    //
    // ~ Field(s)
    //
    public static final String ACCOUNT_ID = "account_id";
    public static final String HIGHEST_MULTIPLIER_IN_HOUR = "highest_multiplier_in_hour";
    public static final String DEBIT = "debit";
    public static final String CREDIT = "credit";
    public static final String WINS_IN_ROW = "wins_in_row";
    public static final String LOSSES_IN_ROW = "losses_in_row";

    @Test
    public void works() throws Exception {
        // Player 1: High multiplier, high wins, high credit
        var entity1 = new LeaderboardStateEntity();
        entity1.setAccountId(1001L);
        entity1.setHighestMultiplierInHour(7.5);
        entity1.setDebit(500.0);
        entity1.setCredit(1200.0);
        entity1.setWinsInRow(4);
        entity1.setLossesInRow(0);

        // Player 2: Low multiplier, high wins, medium credit
        var entity2 = new LeaderboardStateEntity();
        entity2.setAccountId(1002L);
        entity2.setHighestMultiplierInHour(3.2);
        entity2.setDebit(300.0);
        entity2.setCredit(800.0);
        entity2.setWinsInRow(5);
        entity2.setLossesInRow(1);

        // Player 3: High multiplier, medium wins, high credit
        var entity3 = new LeaderboardStateEntity();
        entity3.setAccountId(1003L);
        entity3.setHighestMultiplierInHour(6.1);
        entity3.setDebit(700.0);
        entity3.setCredit(1500.0);
        entity3.setWinsInRow(3);
        entity3.setLossesInRow(0);

        // Player 4: Low multiplier, low wins, medium credit, high losses
        var entity4 = new LeaderboardStateEntity();
        entity4.setAccountId(1004L);
        entity4.setHighestMultiplierInHour(2.8);
        entity4.setDebit(1200.0);
        entity4.setCredit(900.0);
        entity4.setWinsInRow(1);
        entity4.setLossesInRow(3);

        // Player 5: High multiplier, low wins, medium credit
        var entity5 = new LeaderboardStateEntity();
        entity5.setAccountId(1005L);
        entity5.setHighestMultiplierInHour(5.5);
        entity5.setDebit(600.0);
        entity5.setCredit(850.0);
        entity5.setWinsInRow(2);
        entity5.setLossesInRow(1);

        var mapper = new CommonObjectMapper();
        var leaderboards = Arrays.asList(entity1, entity2, entity3, entity4, entity5);
        var metadata = new EntityMetadata<>(LeaderboardStateEntity.class)
                .field(ACCOUNT_ID, Long.class, Q.accountId)
                .field(HIGHEST_MULTIPLIER_IN_HOUR, Double.class, Q.highestMultiplierInHour)
                .field(DEBIT, Double.class, Q.debit)
                .field(CREDIT, Double.class, Q.credit)
                .field(WINS_IN_ROW, Integer.class, Q.winsInRow)
                .field(LOSSES_IN_ROW, Integer.class, Q.lossesInRow);

        //
        // ~ Apply a rule defined programmatically
        //
        RuleGroup highValuePlayersRule = RuleGroup.builder()
                .name("High Value Players")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(HIGHEST_MULTIPLIER_IN_HOUR)
                                .operator(FieldOperator.GREATER_THAN)
                                .value("5.0")
                                .build()))
                .nested(List.of(
                        RuleGroup.builder()
                                .operator(LogicalOperator.OR)
                                .conditions(List.of(
                                        RuleCondition.builder()
                                                .field(WINS_IN_ROW)
                                                .operator(FieldOperator.GREATER_THAN)
                                                .value("3")
                                                .build(),
                                        RuleCondition.builder()
                                                .field(CREDIT)
                                                .operator(FieldOperator.GREATER_THAN)
                                                .value("1000.0")
                                                .build()))
                                .build()))
                .build();
        var highValueResults = applyRuleAndPrintResults(leaderboards, metadata, highValuePlayersRule, mapper);

        //
        // ~ high multiplier (>5.0) AND either high wins (>3) OR high credit (>1000.0)
        //
        assertEquals(2, highValueResults.size(), "High value rule should match exactly 2 players");
        assertTrue(highValueResults.contains(entity1), "Should match player 1 (high multiplier, high wins, high credit)");
        assertTrue(highValueResults.contains(entity3), "Should match player 3 (high multiplier, medium wins, high credit)");

        //
        // ~ Apply a rule from Retool
        //
        RuleGroup externallyConfiguredRule = createExternallyConfiguredRule(mapper);
        var externalRuleResults = applyRuleAndPrintResults(leaderboards, metadata, externallyConfiguredRule, mapper);

        // Assertions for externally configured rule (same logic as high value rule)
        assertEquals(2, externalRuleResults.size(), "External rule should match exactly 2 players");
        assertTrue(externalRuleResults.contains(entity1), "Should match player 1");
        assertTrue(externalRuleResults.contains(entity3), "Should match player 3");

        //
        // ~ Nested complex conditions
        //
        RuleGroup complexNestedRule = RuleGroup.builder()
                .name("Complex Nested Rule")
                .operator(LogicalOperator.AND)
                .nested(List.of(
                        // First nested group: (accountId > 1000 AND winsInRow > 2) OR (credit > 800 AND higestMultiplier > 4)
                        RuleGroup.builder()
                                .operator(LogicalOperator.OR)
                                .nested(List.of(
                                        // Nested: accountId > 1000 AND winsInRow > 2
                                        RuleGroup.builder()
                                                .operator(LogicalOperator.AND)
                                                .conditions(List.of(
                                                        RuleCondition.builder()
                                                                .field(ACCOUNT_ID)
                                                                .operator(FieldOperator.GREATER_THAN)
                                                                .value("1000")
                                                                .build(),
                                                        RuleCondition.builder()
                                                                .field(WINS_IN_ROW)
                                                                .operator(FieldOperator.GREATER_THAN)
                                                                .value("2")
                                                                .build()))
                                                .build(),
                                        // Nested: credit > 800 AND higestMultiplier > 4
                                        RuleGroup.builder()
                                                .operator(LogicalOperator.AND)
                                                .conditions(List.of(
                                                        RuleCondition.builder()
                                                                .field(CREDIT)
                                                                .operator(FieldOperator.GREATER_THAN)
                                                                .value("800")
                                                                .build(),
                                                        RuleCondition.builder()
                                                                .field(HIGHEST_MULTIPLIER_IN_HOUR)
                                                                .operator(FieldOperator.GREATER_THAN)
                                                                .value("4")
                                                                .build()))
                                                .build()))
                                .build()))
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(LOSSES_IN_ROW)
                                .operator(FieldOperator.LESS_THAN)
                                .value("2")
                                .build()))
                .build();
        List<LeaderboardStateEntity> complexRuleResults = applyRuleAndPrintResults(leaderboards, metadata, complexNestedRule, mapper);

        //
        // lossesInRow < 2 AND EITHER (accountId > 1000 AND winsInRow > 2) OR (credit > 800 AND higestMultiplier > 4)
        //
        assertEquals(4, complexRuleResults.size(), "Complex rule should match exactly 4 players");
        assertTrue(complexRuleResults.contains(entity1), "Should match player 1");
        assertTrue(complexRuleResults.contains(entity2), "Should match player 2");
        assertTrue(complexRuleResults.contains(entity3), "Should match player 3");
        assertTrue(complexRuleResults.contains(entity5), "Should match player 5");
    }

    private static List<LeaderboardStateEntity> applyRuleAndPrintResults(
            List<LeaderboardStateEntity> data,
            EntityMetadata<LeaderboardStateEntity> metadata,
            RuleGroup ruleGroup,
            CommonObjectMapper mapper) throws Exception {
        Predicate predicate = QueryDslRuleAdapter.convertToQueryDSL(ruleGroup, metadata, mapper);
        var results = new CollQuery<LeaderboardStateEntity>().from(Q, data).where(predicate).fetch();

        log.info("rule: {}", ruleGroup.getName());
        log.info("predicate: {}", predicate);
        log.info("matching {} items {}", results.size(), results);

        return results;
    }

    private static RuleGroup createExternallyConfiguredRule(CommonObjectMapper mapper) throws Exception {
        var valueAsString = """
                {
                    "name": "High Value Players",
                    "operator": "AND",
                    "conditions": [
                        {
                            "field": "highest_multiplier_in_hour",
                            "operator": "GREATER_THAN",
                            "value": "5.0"
                        }
                    ],
                    "nested": [
                        {
                            "operator": "OR",
                            "conditions": [
                                {
                                    "field": "wins_in_row",
                                    "operator": "GREATER_THAN",
                                    "value": "3"
                                },
                                {
                                    "field": "credit",
                                    "operator": "GREATER_THAN",
                                    "value": "1000.0"
                                }
                            ]
                        }
                    ]
                }
                """;
        return mapper.readValue(valueAsString, RuleGroup.class);
    }
}