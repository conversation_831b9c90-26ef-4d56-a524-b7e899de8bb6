package com.turbospaces.rules;

/**
 * Enhanced Operations Summary for QueryDSL Rule Adapter
 * 
 * This document summarizes all the operations now available for different field types
 * after adding Map operations and enhancing List operations.
 */
public class OperationsSummary {
    
    /**
     * STRING OPERATIONS (8 total):
     * - EQUALS: "value"
     * - NOT_EQUALS: "value" 
     * - CONTAINS: "substring"
     * - NOT_CONTAINS: "substring"
     * - STARTS_WITH: "prefix"
     * - ENDS_WITH: "suffix"
     * - IN: "value1,value2,value3"
     * - NOT_IN: "value1,value2,value3"
     */
    
    /**
     * NUMERIC OPERATIONS (7 total):
     * - EQUALS: "42"
     * - NOT_EQUALS: "42"
     * - GREATER_THAN: "100"
     * - GREATER_THAN_OR_EQUAL: "100"
     * - LESS_THAN: "50"
     * - LESS_THAN_OR_EQUAL: "50"
     * - IN: "10,20,30"
     */
    
    /**
     * B<PERSON><PERSON><PERSON>N OPERATIONS (2 total):
     * - EQUALS: "true"
     * - NOT_EQUALS: "false"
     */
    
    /**
     * DATE OPERATIONS (6 total):
     * - EQUALS: "2023-12-25"
     * - NOT_EQUALS: "2023-12-25"
     * - GREATER_THAN: "2023-01-01" (After)
     * - LESS_THAN: "2023-12-31" (Before)
     * - GREATER_THAN_OR_EQUAL: "2023-01-01" (On or After)
     * - LESS_THAN_OR_EQUAL: "2023-12-31" (On or Before)
     */
    
    /**
     * ENUM OPERATIONS (3 total):
     * - EQUALS: "ACTIVE"
     * - NOT_EQUALS: "INACTIVE"
     * - IN: "PENDING,ACTIVE,COMPLETED"
     */
    
    /**
     * LIST OPERATIONS (9 total) - ENHANCED:
     * - CONTAINS: "value" (check if list contains value)
     * - NOT_CONTAINS: "value" (check if list does NOT contain value)
     * - IN: "value" (alias for CONTAINS)
     * - NOT_IN: "value" (alias for NOT_CONTAINS)
     * - IS_EMPTY: "" (check if list is empty)
     * - IS_NOT_EMPTY: "" (check if list is not empty)
     * - SIZE_EQUALS: "3" (check if list size equals number)
     * - SIZE_GREATER_THAN: "5" (check if list size > number)
     * - SIZE_LESS_THAN: "10" (check if list size < number)
     */
    
    /**
     * MAP OPERATIONS (11 total) - NEW:
     * - CONTAINS_KEY: "keyName" (check if map contains key)
     * - NOT_CONTAINS_KEY: "keyName" (check if map does NOT contain key)
     * - CONTAINS_VALUE: "value" (check if map contains value)
     * - NOT_CONTAINS_VALUE: "value" (check if map does NOT contain value)
     * - IS_EMPTY: "" (check if map is empty)
     * - IS_NOT_EMPTY: "" (check if map is not empty)
     * - SIZE_EQUALS: "3" (check if map size equals number)
     * - SIZE_GREATER_THAN: "5" (check if map size > number)
     * - SIZE_LESS_THAN: "10" (check if map size < number)
     * - EQUALS: "key:value" (check if map has specific key-value pair)
     * - NOT_EQUALS: "key:value" (check if map does NOT have key-value pair)
     */
    
    /**
     * SHARED OPERATIONS BETWEEN COLLECTIONS:
     * 
     * Both List and Map support these size-related operations:
     * - IS_EMPTY
     * - IS_NOT_EMPTY
     * - SIZE_EQUALS
     * - SIZE_GREATER_THAN
     * - SIZE_LESS_THAN
     * 
     * This provides consistent collection handling across different data types.
     */
    
    /**
     * EXAMPLES OF ENHANCED LIST OPERATIONS:
     * 
     * // Check if user has multiple error codes
     * {
     *     "field": "errorCodes",
     *     "operator": "SIZE_GREATER_THAN",
     *     "value": "1"
     * }
     * 
     * // Check if user has empty preferences
     * {
     *     "field": "preferences", 
     *     "operator": "IS_EMPTY",
     *     "value": ""
     * }
     * 
     * // Check if user has exactly 3 tags
     * {
     *     "field": "tags",
     *     "operator": "SIZE_EQUALS", 
     *     "value": "3"
     * }
     */
    
    /**
     * BUSINESS USE CASES:
     * 
     * LIST SIZE OPERATIONS:
     * - Validation: "User must have at least 1 phone number"
     * - Security: "Block users with more than 5 failed login attempts"
     * - Quality: "Premium users must have exactly 3 profile photos"
     * - Analytics: "Find users with empty shopping carts"
     * 
     * MAP OPERATIONS:
     * - Feature flags: "Show feature if user.features.premiumMode = true"
     * - Preferences: "Send notifications if user.settings.notifications = enabled"
     * - Configuration: "Apply timeout if user.config contains timeout key"
     * - Metadata: "Filter by user.metadata.department = engineering"
     */
}
