package com.turbospaces.rules.serialization;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.rules.EntityMetadata;
import com.turbospaces.rules.FieldOperator;
import com.turbospaces.rules.LeaderboardStateEntity;
import com.turbospaces.rules.LogicalOperator;
import com.turbospaces.rules.QLeaderboardStateEntity;
import com.turbospaces.rules.RuleCondition;
import com.turbospaces.rules.RuleGroup;

public class RuleValidationEngineTest {
    
    private CommonObjectMapper mapper;
    private EntityMetadata<LeaderboardStateEntity> metadata;
    
    private static final String ACCOUNT_ID = "account_id";
    private static final String CREDIT = "credit";
    private static final String WINS_IN_ROW = "wins_in_row";
    private static final String HIGHEST_MULTIPLIER = "highest_multiplier_in_hour";
    
    @BeforeEach
    public void setUp() {
        mapper = new CommonObjectMapper();

        QLeaderboardStateEntity Q = QLeaderboardStateEntity.leaderboardStateEntity;
        metadata = new EntityMetadata<>(LeaderboardStateEntity.class)
                .field(ACCOUNT_ID, Long.class, Q.accountId, "Account ID", "Unique player identifier")
                .field(CREDIT, Double.class, Q.credit, "Credit Balance", "Current credit balance")
                .field(WINS_IN_ROW, Integer.class, Q.winsInRow, "Wins in Row", "Consecutive wins")
                .field(HIGHEST_MULTIPLIER, Double.class, Q.highestMultiplierInHour, "Highest Multiplier", "Best multiplier in the last hour");
    }
    
    @Test
    public void testValidConditionValidation() {
        
        RuleCondition validCondition = RuleCondition.builder()
                .field(CREDIT)
                .operator(FieldOperator.GREATER_THAN)
                .value("1000.0")
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(validCondition, metadata, mapper);
        
        assertTrue(report.isValid());
        assertEquals(0, report.getErrors().size());
    }
    
    @Test
    public void testFieldNotFoundValidation() {
        
        RuleCondition invalidCondition = RuleCondition.builder()
                .field("non_existent_field")
                .operator(FieldOperator.GREATER_THAN)
                .value("1000.0")
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(invalidCondition, metadata, mapper);
        
        assertFalse(report.isValid());
        assertEquals(1, report.getErrors().size());
        assertEquals(ValidationError.ErrorType.FIELD_NOT_FOUND, report.getErrors().get(0).getType());
        assertTrue(report.hasFieldErrors());
    }
    
    @Test
    public void testOperatorCompatibilityValidation() {
        
        RuleCondition invalidCondition = RuleCondition.builder()
                .field(CREDIT) 
                .operator(FieldOperator.CONTAINS) 
                .value("1000.0")
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(invalidCondition, metadata, mapper);
        
        assertFalse(report.isValid());
        assertEquals(1, report.getErrors().size());
        assertEquals(ValidationError.ErrorType.OPERATOR_INCOMPATIBLE, report.getErrors().get(0).getType());
        assertTrue(report.hasOperatorErrors());
    }
    
    @Test
    public void testValueTypeValidation() {
        
        RuleCondition invalidCondition = RuleCondition.builder()
                .field(CREDIT) 
                .operator(FieldOperator.GREATER_THAN)
                .value("not_a_number") 
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(invalidCondition, metadata, mapper);
        
        assertFalse(report.isValid());
        assertEquals(1, report.getErrors().size());
        assertEquals(ValidationError.ErrorType.VALUE_TYPE_MISMATCH, report.getErrors().get(0).getType());
        assertTrue(report.hasValueErrors());
    }
    
    @Test
    public void testIntegerFieldValidation() {
        
        RuleCondition validCondition = RuleCondition.builder()
                .field(WINS_IN_ROW) 
                .operator(FieldOperator.EQUALS)
                .value("5")
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(validCondition, metadata, mapper);
        assertTrue(report.isValid());
        
        
        RuleCondition invalidCondition = RuleCondition.builder()
                .field(WINS_IN_ROW) 
                .operator(FieldOperator.EQUALS)
                .value("5.5") 
                .build();
        
        ValidationReport invalidReport = RuleValidationEngine.validateCondition(invalidCondition, metadata, mapper);
        assertFalse(invalidReport.isValid());
        assertTrue(invalidReport.hasValueErrors());
    }
    
    @Test
    public void testLongFieldValidation() {
        
        RuleCondition validCondition = RuleCondition.builder()
                .field(ACCOUNT_ID) 
                .operator(FieldOperator.GREATER_THAN)
                .value("1000")
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(validCondition, metadata, mapper);
        assertTrue(report.isValid());
        
        
        RuleCondition invalidCondition = RuleCondition.builder()
                .field(ACCOUNT_ID) 
                .operator(FieldOperator.GREATER_THAN)
                .value("not_a_long")
                .build();
        
        ValidationReport invalidReport = RuleValidationEngine.validateCondition(invalidCondition, metadata, mapper);
        assertFalse(invalidReport.isValid());
        assertTrue(invalidReport.hasValueErrors());
    }
    
    @Test
    public void testComplexRuleGroupValidation() {
        
        RuleGroup complexRule = RuleGroup.builder()
                .name("Complex Validation Test")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        
                        RuleCondition.builder()
                                .field(CREDIT)
                                .operator(FieldOperator.GREATER_THAN)
                                .value("1000.0")
                                .build(),
                        
                        RuleCondition.builder()
                                .field("invalid_field")
                                .operator(FieldOperator.EQUALS)
                                .value("test")
                                .build(),
                        
                        RuleCondition.builder()
                                .field(WINS_IN_ROW) 
                                .operator(FieldOperator.CONTAINS) 
                                .value("5")
                                .build(),
                        
                        RuleCondition.builder()
                                .field(HIGHEST_MULTIPLIER) 
                                .operator(FieldOperator.GREATER_THAN)
                                .value("not_a_double")
                                .build()))
                .build();
        
        ValidationReport report = RuleValidationEngine.validateRule(complexRule, metadata, mapper);
        
        assertFalse(report.isValid());
        assertEquals(3, report.getErrors().size()); 

        
        assertTrue(report.hasFieldErrors());
        assertTrue(report.hasOperatorErrors());
        assertTrue(report.hasValueErrors());
        
        
        assertEquals(1, report.getErrorsByType(ValidationError.ErrorType.FIELD_NOT_FOUND).size());
        assertEquals(1, report.getErrorsByType(ValidationError.ErrorType.OPERATOR_INCOMPATIBLE).size());
        assertEquals(1, report.getErrorsByType(ValidationError.ErrorType.VALUE_TYPE_MISMATCH).size());
    }
    
    @Test
    public void testNestedRuleGroupValidation() {
        
        RuleGroup nestedRule = RuleGroup.builder()
                .name("Nested Validation Test")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ACCOUNT_ID)
                                .operator(FieldOperator.GREATER_THAN)
                                .value("1000")
                                .build()))
                .nested(List.of(
                        RuleGroup.builder()
                                .name("Nested Group")
                                .operator(LogicalOperator.OR)
                                .conditions(List.of(
                                        
                                        RuleCondition.builder()
                                                .field(CREDIT)
                                                .operator(FieldOperator.LESS_THAN)
                                                .value("5000.0")
                                                .build(),
                                        
                                        RuleCondition.builder()
                                                .field("another_invalid_field")
                                                .operator(FieldOperator.EQUALS)
                                                .value("test")
                                                .build()))
                                .build()))
                .build();
        
        ValidationReport report = RuleValidationEngine.validateRule(nestedRule, metadata, mapper);
        
        assertFalse(report.isValid());
        assertEquals(1, report.getErrors().size());

        
        ValidationError error = report.getErrors().get(0);
        assertTrue(error.getPath().contains("nested"));
        assertEquals(ValidationError.ErrorType.FIELD_NOT_FOUND, error.getType());
    }
    
    @Test
    public void testEmptyValueOperators() {
        
        RuleCondition emptyCondition = RuleCondition.builder()
                .field(CREDIT)
                .operator(FieldOperator.IS_EMPTY)
                .value(null) 
                .build();
        
        ValidationReport report = RuleValidationEngine.validateCondition(emptyCondition, metadata, mapper);
        assertTrue(report.isValid());
        
        
        RuleCondition notEmptyCondition = RuleCondition.builder()
                .field(CREDIT)
                .operator(FieldOperator.IS_NOT_EMPTY)
                .value("") 
                .build();
        
        ValidationReport notEmptyReport = RuleValidationEngine.validateCondition(notEmptyCondition, metadata, mapper);
        assertTrue(notEmptyReport.isValid());
    }

    @Test
    public void testValidationReportToException() {
        
        RuleCondition invalidCondition = RuleCondition.builder()
                .field("non_existent_field")
                .operator(FieldOperator.GREATER_THAN)
                .value("invalid_value")
                .build();

        ValidationReport report = RuleValidationEngine.validateCondition(invalidCondition, metadata, mapper);

        assertFalse(report.isValid());
        assertTrue(report.getErrors().size() > 0);

        
        RuleValidationException exception = report.toException();

        assertNotNull(exception);
        assertEquals(report, exception.getValidationReport());
        assertEquals(report.getErrors(), exception.getErrors());

        
        String message = exception.getMessage();
        assertTrue(message.contains("Rule validation failed"));
        assertTrue(message.contains("error(s)"));
        assertTrue(message.contains("Validation Summary"));
        assertTrue(message.contains("Validation Errors"));
        assertTrue(message.contains("non_existent_field"));
        assertTrue(message.contains("FIELD_NOT_FOUND"));
    }

    @Test
    public void testValidationReportToExceptionWithCustomMessage() {
        
        RuleGroup complexRule = RuleGroup.builder()
                .name("Complex Error Test")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("invalid_field")
                                .operator(FieldOperator.EQUALS)
                                .value("test")
                                .build(),
                        RuleCondition.builder()
                                .field(CREDIT) 
                                .operator(FieldOperator.CONTAINS) 
                                .value("1000.0")
                                .build()))
                .build();

        ValidationReport report = RuleValidationEngine.validateRule(complexRule, metadata, mapper);

        assertFalse(report.isValid());
        assertEquals(2, report.getErrors().size());
    }

    @Test
    public void testValidationReportToExceptionThrowsForValidReport() {
        
        RuleCondition validCondition = RuleCondition.builder()
                .field(CREDIT)
                .operator(FieldOperator.GREATER_THAN)
                .value("1000.0")
                .build();

        ValidationReport validReport = RuleValidationEngine.validateCondition(validCondition, metadata, mapper);

        assertTrue(validReport.isValid());
        assertEquals(0, validReport.getErrors().size());

        
        try {
            validReport.toException();
            fail("Should have thrown IllegalStateException for valid report");
        } catch (IllegalStateException e) {
            assertTrue(e.getMessage().contains("Cannot create exception from valid validation report"));
        }
    }

    @Test
    public void testRuleValidationExceptionDetails() {
        
        RuleGroup complexRule = RuleGroup.builder()
                .name("Comprehensive Error Test")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        
                        RuleCondition.builder()
                                .field("non_existent_field")
                                .operator(FieldOperator.EQUALS)
                                .value("test")
                                .build(),
                        
                        RuleCondition.builder()
                                .field(CREDIT) 
                                .operator(FieldOperator.CONTAINS) 
                                .value("1000.0")
                                .build(),
                        
                        RuleCondition.builder()
                                .field(WINS_IN_ROW) 
                                .operator(FieldOperator.GREATER_THAN)
                                .value("not_a_number") 
                                .build()))
                .build();

        ValidationReport report = RuleValidationEngine.validateRule(complexRule, metadata, mapper);
        RuleValidationException exception = report.toException();

        
        assertEquals(3, exception.getErrors().size());

        
        List<String> errorFieldPaths = exception.getErrorFieldPaths();
        assertTrue(errorFieldPaths.contains("non_existent_field"));
        assertTrue(errorFieldPaths.contains(CREDIT));
        assertTrue(errorFieldPaths.contains(WINS_IN_ROW));

        
        List<ValidationError> fieldErrors = exception.getErrorsByType(ValidationError.ErrorType.FIELD_NOT_FOUND);
        assertEquals(1, fieldErrors.size());
        assertEquals("non_existent_field", fieldErrors.get(0).getFieldPath());

        List<ValidationError> operatorErrors = exception.getErrorsByType(ValidationError.ErrorType.OPERATOR_INCOMPATIBLE);
        assertEquals(1, operatorErrors.size());
        assertEquals(CREDIT, operatorErrors.get(0).getFieldPath());
        assertEquals(FieldOperator.CONTAINS, operatorErrors.get(0).getOperator());
        assertEquals(Double.class, operatorErrors.get(0).getFieldType());

        List<ValidationError> valueErrors = exception.getErrorsByType(ValidationError.ErrorType.VALUE_TYPE_MISMATCH);
        assertEquals(1, valueErrors.size());
        assertEquals(WINS_IN_ROW, valueErrors.get(0).getFieldPath());
        assertEquals("not_a_number", valueErrors.get(0).getValue());
        assertEquals(Integer.class, valueErrors.get(0).getFieldType());
    }

    @Test
    public void testRuleValidationExceptionMessageFormatting() {
        
        RuleGroup testRule = RuleGroup.builder()
                .name("Message Formatting Test")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        
                        RuleCondition.builder()
                                .field("invalid_field")
                                .operator(FieldOperator.EQUALS)
                                .value("test_value")
                                .build(),
                        
                        RuleCondition.builder()
                                .field(CREDIT) 
                                .operator(FieldOperator.CONTAINS) 
                                .value("1000.0")
                                .build()))
                .build();

        ValidationReport report = RuleValidationEngine.validateRule(testRule, metadata, mapper);
        RuleValidationException exception = report.toException();

        String message = exception.getMessage();

        
        assertTrue(message.contains("Validation Summary:"));
        assertTrue(message.contains("Validation Errors:"));

        
        assertTrue(message.contains("Errors: 2"));
        assertTrue(message.contains("FIELD_NOT_FOUND"));
        assertTrue(message.contains("OPERATOR_INCOMPATIBLE"));
        assertTrue(message.contains("invalid_field"));
        assertTrue(message.contains(CREDIT));

        
        assertTrue(message.contains("Field: " + CREDIT));
        assertTrue(message.contains("Operator: CONTAINS"));
        assertTrue(message.contains("Expected Type: Double"));
    }
}
