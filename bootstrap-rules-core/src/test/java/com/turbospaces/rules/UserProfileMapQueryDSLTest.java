package com.turbospaces.rules;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;

import com.querydsl.collections.CollQuery;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class UserProfileMapQueryDSLTest {
    public static final QUserProfileEntity Q = QUserProfileEntity.userProfileEntity;

    //
    // ~ Field(s)
    //
    public static final String USER_ID = "user_id";
    public static final String USERNAME = "username";
    public static final String PREFERENCES = "preferences";
    public static final String SETTINGS = "settings";
    public static final String FEATURES = "features";
    public static final String TOTAL_LOGINS = "total_logins";
    public static final String IS_ACTIVE = "is_active";

    @Test
    public void testMapOperations() throws Exception {
        // User 1: Premium user with dark theme
        Map<String, String> prefs1 = new HashMap<>();
        prefs1.put("theme", "dark");
        prefs1.put("language", "en");
        prefs1.put("timezone", "UTC");

        Map<String, Integer> settings1 = new HashMap<>();
        settings1.put("timeout", 300);
        settings1.put("maxConnections", 10);

        Map<String, Boolean> features1 = new HashMap<>();
        features1.put("premiumFeatures", true);
        features1.put("notifications", true);
        features1.put("analytics", false);

        var user1 = UserProfileEntity.builder()
                .userId(1001L)
                .username("john_doe")
                .preferences(prefs1)
                .settings(settings1)
                .features(features1)
                .totalLogins(150)
                .isActive(true)
                .build();

        // User 2: Basic user with light theme
        Map<String, String> prefs2 = new HashMap<>();
        prefs2.put("theme", "light");
        prefs2.put("language", "es");

        Map<String, Integer> settings2 = new HashMap<>();
        settings2.put("timeout", 60);

        Map<String, Boolean> features2 = new HashMap<>();
        features2.put("premiumFeatures", false);
        features2.put("notifications", false);

        var user2 = UserProfileEntity.builder()
                .userId(1002L)
                .username("jane_smith")
                .preferences(prefs2)
                .settings(settings2)
                .features(features2)
                .totalLogins(25)
                .isActive(true)
                .build();

        // User 3: User with empty preferences
        var user3 = UserProfileEntity.builder()
                .userId(1003L)
                .username("empty_user")
                .preferences(new HashMap<>())
                .settings(new HashMap<>())
                .features(new HashMap<>())
                .totalLogins(5)
                .isActive(false)
                .build();

        var mapper = new CommonObjectMapper();
        var users = Arrays.asList(user1, user2, user3);
        var metadata = new EntityMetadata<>(UserProfileEntity.class)
                .field(USER_ID, Long.class, Q.userId)
                .field(USERNAME, String.class, Q.username)
                .field(PREFERENCES, String.class, (Expression) Q.preferences)
                .field(SETTINGS, Integer.class, Q.settings)
                .field(FEATURES, Boolean.class, Q.features)
                .field(TOTAL_LOGINS, Integer.class, Q.totalLogins)
                .field(IS_ACTIVE, Boolean.class, Q.isActive);

        //
        // ~ Test 1: Users with dark theme preference
        //
        RuleGroup darkThemeRule = RuleGroup.builder()
                .name("Dark Theme Users")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(PREFERENCES)
                                .operator(FieldOperator.EQUALS)
                                .value("theme:dark")
                                .build()))
                .build();

        var darkThemeUsers = applyRuleAndPrintResults(users, metadata, darkThemeRule, mapper);
        assertEquals(1, darkThemeUsers.size(), "Should find 1 user with dark theme");
        assertTrue(darkThemeUsers.contains(user1), "Should match user1 with dark theme");

        //
        // ~ Test 2: Users with premium features enabled
        //
        RuleGroup premiumFeaturesRule = RuleGroup.builder()
                .name("Premium Features Enabled")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(FEATURES)
                                .operator(FieldOperator.EQUALS)
                                .value("premiumFeatures:true")
                                .build()))
                .build();

        var premiumUsers = applyRuleAndPrintResults(users, metadata, premiumFeaturesRule, mapper);
        assertEquals(1, premiumUsers.size(), "Should find 1 premium user");
        assertTrue(premiumUsers.contains(user1), "Should match user1 with premium features");

        //
        // ~ Test 3: Users with timeout setting
        //
        RuleGroup timeoutSettingRule = RuleGroup.builder()
                .name("Users with Timeout Setting")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(SETTINGS)
                                .operator(FieldOperator.CONTAINS_KEY)
                                .value("timeout")
                                .build()))
                .build();

        var usersWithTimeout = applyRuleAndPrintResults(users, metadata, timeoutSettingRule, mapper);
        assertEquals(2, usersWithTimeout.size(), "Should find 2 users with timeout setting");
        assertTrue(usersWithTimeout.contains(user1), "Should match user1");
        assertTrue(usersWithTimeout.contains(user2), "Should match user2");

        //
        // ~ Test 4: Users with empty preferences
        //
        RuleGroup emptyPreferencesRule = RuleGroup.builder()
                .name("Users with Empty Preferences")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(PREFERENCES)
                                .operator(FieldOperator.IS_EMPTY)
                                .value("")
                                .build()))
                .build();

        var usersWithEmptyPrefs = applyRuleAndPrintResults(users, metadata, emptyPreferencesRule, mapper);
        assertEquals(1, usersWithEmptyPrefs.size(), "Should find 1 user with empty preferences");
        assertTrue(usersWithEmptyPrefs.contains(user3), "Should match user3 with empty preferences");

        //
        // ~ Test 5: Complex rule - Premium users with specific language preference
        //
        RuleGroup complexRule = RuleGroup.builder()
                .name("Premium English Users")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(FEATURES)
                                .operator(FieldOperator.EQUALS)
                                .value("premiumFeatures:true")
                                .build(),
                        RuleCondition.builder()
                                .field(PREFERENCES)
                                .operator(FieldOperator.EQUALS)
                                .value("language:en")
                                .build()))
                .build();

        var complexResults = applyRuleAndPrintResults(users, metadata, complexRule, mapper);
        assertEquals(1, complexResults.size(), "Should find 1 premium English user");
        assertTrue(complexResults.contains(user1), "Should match user1");

        //
        // ~ Test 6: External JSON configuration
        //
        RuleGroup externalRule = createExternalMapRule(mapper);
        var externalResults = applyRuleAndPrintResults(users, metadata, externalRule, mapper);
        assertEquals(1, externalResults.size(), "External rule should match 1 user");
        assertTrue(externalResults.contains(user1), "Should match user1");
    }

    private static List<UserProfileEntity> applyRuleAndPrintResults(
            List<UserProfileEntity> data,
            EntityMetadata<UserProfileEntity> metadata,
            RuleGroup ruleGroup,
            CommonObjectMapper mapper) throws Exception {
        Predicate predicate = QueryDslRuleAdapter.convertToQueryDSL(ruleGroup, metadata, mapper);
        var results = new CollQuery<UserProfileEntity>().from(Q, data).where(predicate).fetch();

        log.info("rule: {}", ruleGroup.getName());
        log.info("predicate: {}", predicate);
        log.info("matching {} items {}", results.size(), results);

        return results;
    }

    private static RuleGroup createExternalMapRule(CommonObjectMapper mapper) throws Exception {
        var valueAsString = """
                {
                    "name": "Premium Dark Theme Users",
                    "operator": "AND",
                    "conditions": [
                        {
                            "field": "features",
                            "operator": "EQUALS",
                            "value": "premiumFeatures:true"
                        },
                        {
                            "field": "preferences",
                            "operator": "EQUALS",
                            "value": "theme:dark"
                        }
                    ]
                }
                """;
        return mapper.readValue(valueAsString, RuleGroup.class);
    }
}
