package com.turbospaces.rules;

import java.util.Date;
import java.util.List;

import com.querydsl.core.annotations.QueryEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

@lombok.Data
@QueryEntity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRiskStateEntity {
    public long accountId;
    public String provider;
    public List<Integer> errorCodes;
    public String errorMessage;
    public Date timestamp;
    public double transactionAmount;
    public String currency;
    public boolean isBlocked;
    public int totalErrorCount;
    public int recentErrorCount; // errors in last 24 hours
}
