package com.turbospaces.rules;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Range;
import com.turbospaces.common.EventTimeClock;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.rules.LeaderboardState.WalletSession;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LeaderBoardWithPlainCalcTest {
    @Test
    public void works() {
        var now = new Date();
        var clock = new EventTimeClock();
        try (var state = new LeaderboardState(clock)) {
            var params = LeaderboardState.Params.builder()
                    .debitRange(Range.closed(BigDecimal.valueOf(0.25), BigDecimal.valueOf(10))).debitReward(BigDecimal.valueOf(0.25))
                    .creditRange(Range.closed(BigDecimal.valueOf(0.1), BigDecimal.valueOf(100))).creditReward(BigDecimal.valueOf(1.25))
                    .higestMultiplierReward(BigDecimal.valueOf(18.5))
                    .winsInRow3Reward(BigDecimal.valueOf(55.5))
                    .winsInRow4Reward(BigDecimal.valueOf(155.5))
                    .winsInRow5Reward(BigDecimal.valueOf(1155.5))
                    .lossesInRow3Reward(BigDecimal.valueOf(12.5))
                    .lossesInRow5Reward(BigDecimal.valueOf(32.5))
                    .lossesInRow7Reward(BigDecimal.valueOf(132.5))
                    .lossesInRow11Reward(BigDecimal.valueOf(1132.5))
                    .build();

            // @formatter:off
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -60)).debit(BigDecimal.valueOf(0.1)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -59)).debit(BigDecimal.valueOf(0.25)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -58)).debit(BigDecimal.valueOf(0.35)).credit(BigDecimal.valueOf(0.05)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -57)).debit(BigDecimal.valueOf(10.04)).credit(BigDecimal.valueOf(11.5)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -56)).debit(BigDecimal.valueOf(10.05)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -55)).debit(BigDecimal.valueOf(10.06)).credit(BigDecimal.valueOf(127.5)).build());
            // @formatter:on

            //
            // ~ debit [0.25 .. 10] 30.60 X 0.25 = 7.65
            // ~ credit [0.1 .. 100] 111.50 X 1.25 = 139.375
            //

            Assertions.assertEquals(381.42D, state.total(params).doubleValue());

            // @formatter:off
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -50)).debit(BigDecimal.valueOf(0.01)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -49)).debit(BigDecimal.valueOf(0.02)).credit(BigDecimal.valueOf(0.02)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -48)).debit(BigDecimal.valueOf(0.03)).credit(BigDecimal.valueOf(0.03)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -47)).debit(BigDecimal.valueOf(0.04)).credit(BigDecimal.valueOf(0.04)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -46)).debit(BigDecimal.valueOf(0.05)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -45)).debit(BigDecimal.valueOf(0.06)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -44)).debit(BigDecimal.valueOf(0.07)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -43)).debit(BigDecimal.valueOf(0.08)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -43)).debit(BigDecimal.valueOf(0.09)).credit(BigDecimal.valueOf(0.09)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -40)).debit(BigDecimal.valueOf(0.15)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -39)).debit(BigDecimal.valueOf(0.16)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -38)).debit(BigDecimal.valueOf(0.17)).build());
            state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -37)).debit(BigDecimal.valueOf(0.19)).credit(BigDecimal.valueOf(0.19)).build());
            // @formatter:on

            //
            // ~ wins in row 1 X 55.5 = 55.5
            // ~ losses in row 2 X 12.5 = 25
            //
            Assertions.assertEquals(462.1575, state.total(params).doubleValue());
        }
    }
}
