package com.turbospaces.rules;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.querydsl.collections.CollQuery;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * Documentation and examples for QueryDSL Map operations in the rule adapter.
 * 
 * This class demonstrates all supported Map operations and how to use them
 * in business rules for querying entities with Map fields.
 */
@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class MapOperationsDocumentation {
    
    /**
     * Supported Map Operations in QueryDslRuleAdapter:
     * 
     * 1. CONTAINS_KEY - Check if map contains a specific key
     *    Example: "containsKey":"theme" 
     *    
     * 2. NOT_CONTAINS_KEY - Check if map does NOT contain a specific key
     *    Example: "notContainsKey":"premium"
     *    
     * 3. CONTAINS_VALUE - Check if map contains a specific value
     *    Example: "containsValue":"dark"
     *    
     * 4. NOT_CONTAINS_VALUE - Check if map does NOT contain a specific value
     *    Example: "notContainsValue":"false"
     *    
     * 5. IS_EMPTY - Check if map is empty
     *    Example: "isEmpty":""
     *    
     * 6. IS_NOT_EMPTY - Check if map is not empty
     *    Example: "isNotEmpty":""
     *    
     * 7. SIZE_EQUALS - Check if map size equals specific number
     *    Example: "sizeEquals":"3"
     *    
     * 8. SIZE_GREATER_THAN - Check if map size is greater than specific number
     *    Example: "sizeGreaterThan":"5"
     *    
     * 9. SIZE_LESS_THAN - Check if map size is less than specific number
     *    Example: "sizeLessThan":"10"
     *    
     * 10. EQUALS - Check if map has specific key-value pair
     *     Example: "equals":"theme:dark" (key:value format)
     *     
     * 11. NOT_EQUALS - Check if map does NOT have specific key-value pair
     *     Example: "notEquals":"language:en"
     */

    public static void demonstrateMapOperations() throws Exception {
        // Sample user data
        Map<String, String> userPreferences = new HashMap<>();
        userPreferences.put("theme", "dark");
        userPreferences.put("language", "en");
        userPreferences.put("timezone", "UTC");
        
        var user = UserProfileEntity.builder()
                .userId(1L)
                .username("demo_user")
                .preferences(userPreferences)
                .build();
        
        var mapper = new CommonObjectMapper();
        var metadata = new EntityMetadata<>(UserProfileEntity.class)
                .field("preferences", String.class, (Expression) QUserProfileEntity.userProfileEntity.preferences);
        
        // Example 1: Check if user has theme preference
        RuleGroup hasThemeRule = RuleGroup.builder()
                .name("Users with Theme Preference")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("preferences")
                                .operator(FieldOperator.CONTAINS_KEY)
                                .value("theme")
                                .build()))
                .build();
        
        // Example 2: Check if user prefers dark theme
        RuleGroup darkThemeRule = RuleGroup.builder()
                .name("Dark Theme Users")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("preferences")
                                .operator(FieldOperator.EQUALS)
                                .value("theme:dark")
                                .build()))
                .build();
        
        // Example 3: Check if user has non-empty preferences
        RuleGroup nonEmptyPrefsRule = RuleGroup.builder()
                .name("Users with Preferences")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("preferences")
                                .operator(FieldOperator.IS_NOT_EMPTY)
                                .value("")
                                .build()))
                .build();
        
        // Example 4: Check if user has many preferences (size > 2)
        RuleGroup manyPrefsRule = RuleGroup.builder()
                .name("Users with Many Preferences")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("preferences")
                                .operator(FieldOperator.SIZE_GREATER_THAN)
                                .value("2")
                                .build()))
                .build();
        
        // Example 5: JSON configuration from external system (Retool, admin panel, etc.)
        String externalRuleJson = """
                {
                    "name": "English Dark Theme Users",
                    "operator": "AND",
                    "conditions": [
                        {
                            "field": "preferences",
                            "operator": "EQUALS",
                            "value": "theme:dark"
                        },
                        {
                            "field": "preferences",
                            "operator": "EQUALS",
                            "value": "language:en"
                        }
                    ]
                }
                """;
        
        RuleGroup externalRule = mapper.readValue(externalRuleJson, RuleGroup.class);
        
        // Apply rules and log results
        log.info("=== Map Operations Demo ===");
        
        applyAndLogRule(List.of(user), metadata, hasThemeRule, mapper);
        applyAndLogRule(List.of(user), metadata, darkThemeRule, mapper);
        applyAndLogRule(List.of(user), metadata, nonEmptyPrefsRule, mapper);
        applyAndLogRule(List.of(user), metadata, manyPrefsRule, mapper);
        applyAndLogRule(List.of(user), metadata, externalRule, mapper);
    }
    
    private static void applyAndLogRule(
            List<UserProfileEntity> data,
            EntityMetadata<UserProfileEntity> metadata,
            RuleGroup rule,
            CommonObjectMapper mapper) throws Exception {
        
        Predicate predicate = QueryDslRuleAdapter.convertToQueryDSL(rule, metadata, mapper);
        var results = new CollQuery<UserProfileEntity>()
                .from(QUserProfileEntity.userProfileEntity, data)
                .where(predicate)
                .fetch();
        
        log.info("Rule: {} | Matches: {} | Predicate: {}", 
                rule.getName(), results.size(), predicate);
    }
    
    /**
     * Best Practices for Map Operations:
     * 
     * 1. Key Operations: Use CONTAINS_KEY/NOT_CONTAINS_KEY to check for field presence
     * 2. Value Operations: Use CONTAINS_VALUE for checking if any key has specific value
     * 3. Key-Value Operations: Use EQUALS/NOT_EQUALS with "key:value" format for exact matches
     * 4. Size Operations: Use SIZE_* operations for filtering by map complexity
     * 5. Empty Checks: Use IS_EMPTY/IS_NOT_EMPTY for initialization validation
     * 
     * Common Use Cases:
     * - Feature flags: "features":"premiumMode:true"
     * - User preferences: "preferences":"theme:dark"
     * - Configuration settings: "settings":"timeout:300"
     * - Metadata filtering: "metadata":"category:premium"
     * - Tag systems: "tags":"type:urgent"
     */
}
