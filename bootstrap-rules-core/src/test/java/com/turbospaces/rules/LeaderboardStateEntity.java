package com.turbospaces.rules;

import com.querydsl.core.annotations.QueryEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

@lombok.Data
@QueryEntity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LeaderboardStateEntity {
    public long accountId;
    public double highestMultiplierInHour;
    public double debit, credit;
    public int winsInRow, lossesInRow;
}
