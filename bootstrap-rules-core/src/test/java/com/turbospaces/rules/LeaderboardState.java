package com.turbospaces.rules;

import java.io.Closeable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.UUID;
import java.util.function.Supplier;

import com.google.common.base.Suppliers;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.turbospaces.common.EventTimeClock;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@ToString(onlyExplicitlyIncluded = true)
@Slf4j
public class LeaderboardState implements Closeable {
    @Getter
    @ToString.Include
    private final SortedSet<WalletSession> rounds = new TreeSet<>();

    //
    // ~ sliding window
    //
    private final EventTimeClock clock;
    private final DistributionAccumulator higestMultiplierInHour;

    private final ThreadLocal<Map<Range<BigDecimal>, Supplier<BigDecimal>>> debit = new ThreadLocal<>() {
        @Override
        protected Map<Range<BigDecimal>, Supplier<BigDecimal>> initialValue() {
            return Maps.newHashMap();
        }
    };
    private final ThreadLocal<Map<Range<BigDecimal>, Supplier<BigDecimal>>> credit = new ThreadLocal<>() {
        @Override
        protected Map<Range<BigDecimal>, Supplier<BigDecimal>> initialValue() {
            return Maps.newHashMap();
        }
    };
    private final ThreadLocal<Map<Integer, Supplier<Integer>>> winsInRowSupplier = new ThreadLocal<>() {
        @Override
        public Map<Integer, Supplier<Integer>> initialValue() {
            return Maps.newHashMap();
        }
    };
    private final ThreadLocal<Map<Integer, Supplier<Integer>>> lossesInRowSupplier = new ThreadLocal<>() {
        @Override
        public Map<Integer, Supplier<Integer>> initialValue() {
            return Maps.newHashMap();
        }
    };

    public LeaderboardState(EventTimeClock clock) {
        this.clock = clock;
        higestMultiplierInHour = DistributionAccumulator.create(Duration.ofHours(1), clock);
    }
    public void addRound(WalletSession round) {
        if (getRounds().add(round)) {

        } else {
            //
            // ~ replace because credit amount might be different from original
            //
            getRounds().remove(round);
            getRounds().add(round);
        }

        try {
            clock.current(round.timestamp);
            BigDecimal mult = round.multiplier();
            higestMultiplierInHour.accept(mult);
        } finally {
            invalidate();
            clock.remove();
        }
    }
    public BigDecimal debit(Range<BigDecimal> range) {
        var map = debit.get();
        var supplier = map.computeIfAbsent(range, key -> Suppliers.memoize(new com.google.common.base.Supplier<>() {
            @Override
            public BigDecimal get() {
                BigDecimal toReturn = BigDecimal.ZERO;
                for (WalletSession round : getRounds()) {
                    if (round.getDebit().compareTo(range.lowerEndpoint()) >= 0) {
                        var toAdd = round.getDebit().min(range.upperEndpoint());
                        toReturn = toReturn.add(toAdd);
                    }
                }
                return toReturn;
            }
        }));
        return supplier.get();
    }
    public BigDecimal credit(Range<BigDecimal> range) {
        var map = credit.get();
        var supplier = map.computeIfAbsent(range, key -> Suppliers.memoize(new com.google.common.base.Supplier<>() {
            @Override
            public BigDecimal get() {
                BigDecimal toReturn = BigDecimal.ZERO;
                for (WalletSession round : getRounds()) {
                    if (round.getCredit().compareTo(range.lowerEndpoint()) >= 0) {
                        BigDecimal toAdd = round.getCredit().min(range.upperEndpoint());
                        toReturn = toReturn.add(toAdd);
                    }
                }
                return toReturn;
            }
        }));
        return supplier.get();
    }
    public int winsInRow(int step) {
        var map = winsInRowSupplier.get();
        var supplier = map.computeIfAbsent(step, key -> Suppliers.memoize(new com.google.common.base.Supplier<Integer>() {
            @Override
            public Integer get() {
                int toReturn = 0;

                int currentStreak = 0;
                boolean inSequence = false;
                for (WalletSession round : getRounds()) {
                    if (round.isWin()) {
                        currentStreak++;
                        if (currentStreak >= step && !inSequence) {
                            toReturn++;
                            inSequence = true;
                        }
                    } else {
                        currentStreak = 0;
                        inSequence = false;
                    }
                }

                return toReturn;
            }
        }));
        return supplier.get();
    }
    public int lossesInRow(int step) {
        var map = lossesInRowSupplier.get();
        var supplier = map.computeIfAbsent(step, key -> Suppliers.memoize(new com.google.common.base.Supplier<Integer>() {
            @Override
            public Integer get() {
                int toReturn = 0;

                int currentStreak = 0;
                boolean inSequence = false;
                for (WalletSession round : getRounds()) {
                    if (round.isLoss()) {
                        currentStreak++;
                        if (currentStreak >= step && !inSequence) {
                            toReturn++;
                            inSequence = true;
                        }
                    } else {
                        currentStreak = 0;
                        inSequence = false;
                    }
                }

                return toReturn;
            }
        }));
        return supplier.get();
    }
    public double higestMultiplierInHour() {
        return higestMultiplierInHour.max();
    }
    @Override
    public void close() {
        invalidate();
    }
    public BigDecimal total(Params params) {
        BigDecimal totalDebit = BigDecimal.ZERO;
        BigDecimal totalCredit = BigDecimal.ZERO;

        int winsInRow3 = 0, winsInRow4 = 0, winsInRow5 = 0;
        int currentStreakWinsInRow = 0;
        boolean inSequenceWins3 = false, inSequenceWins4 = false, inSequenceWins5 = false;

        int lossesInRow3 = 0, lossesInRow5 = 0, lossesInRow7 = 0, lossesInRow11 = 0;
        int currentStreakLossesInRow = 0;
        boolean inSequenceLosses3 = false, inSequenceLosses5 = false, inSequenceLosses7 = false, inSequenceLosses11 = false;

        BigDecimal highestMultiplier = BigDecimal.ZERO;

        for (WalletSession ws : getRounds()) {
            if (ws.getDebit().compareTo(params.debitRange.lowerEndpoint()) >= 0) {
                var toAdd = ws.getDebit().min(params.debitRange.upperEndpoint());
                totalDebit = totalDebit.add(toAdd);
            }

            if (ws.getCredit().compareTo(params.creditRange.lowerEndpoint()) >= 0) {
                BigDecimal toAdd = ws.getCredit().min(params.creditRange.upperEndpoint());
                totalCredit = totalCredit.add(toAdd);
            }

            if (ws.isWin()) {
                BigDecimal multiplier = ws.multiplier();
                if (multiplier.compareTo(highestMultiplier) > 0) {
                    highestMultiplier = multiplier;
                }
            }

            if (ws.isWin()) {
                currentStreakWinsInRow++;
                currentStreakLossesInRow = 0;

                if (currentStreakWinsInRow >= 3 && !inSequenceWins3) {
                    winsInRow3++;
                    inSequenceWins3 = true;
                }
                if (currentStreakWinsInRow >= 4 && !inSequenceWins4) {
                    winsInRow4++;
                    inSequenceWins4 = true;
                }
                if (currentStreakWinsInRow >= 5 && !inSequenceWins5) {
                    winsInRow5++;
                    inSequenceWins5 = true;
                }

                inSequenceLosses3 = false;
                inSequenceLosses5 = false;
                inSequenceLosses7 = false;
                inSequenceLosses11 = false;
            } else if (ws.isLoss()) {
                currentStreakLossesInRow++;
                currentStreakWinsInRow = 0;

                if (currentStreakLossesInRow >= 3 && !inSequenceLosses3) {
                    lossesInRow3++;
                    inSequenceLosses3 = true;
                }
                if (currentStreakLossesInRow >= 5 && !inSequenceLosses5) {
                    lossesInRow5++;
                    inSequenceLosses5 = true;
                }
                if (currentStreakLossesInRow >= 7 && !inSequenceLosses7) {
                    lossesInRow7++;
                    inSequenceLosses7 = true;
                }
                if (currentStreakLossesInRow >= 11 && !inSequenceLosses11) {
                    lossesInRow11++;
                    inSequenceLosses11 = true;
                }

                inSequenceWins3 = false;
                inSequenceWins4 = false;
                inSequenceWins5 = false;
            } else {
                currentStreakWinsInRow = 0;
                currentStreakLossesInRow = 0;
                inSequenceWins3 = false;
                inSequenceWins4 = false;
                inSequenceWins5 = false;
                inSequenceLosses3 = false;
                inSequenceLosses5 = false;
                inSequenceLosses7 = false;
                inSequenceLosses11 = false;
            }
        }

        var deltaDebit = totalDebit.multiply(params.debitReward);
        var deltaCredit = totalCredit.multiply(params.creditReward);

        var deltaWinsInRow3Reward = params.winsInRow3Reward.multiply(BigDecimal.valueOf(winsInRow3));
        var deltaWinsInRow4Reward = params.winsInRow4Reward.multiply(BigDecimal.valueOf(winsInRow4));
        var deltaWinsInRow5Reward = params.winsInRow5Reward.multiply(BigDecimal.valueOf(winsInRow5));

        var deltaLossesInRow3Reward = params.lossesInRow3Reward.multiply(BigDecimal.valueOf(lossesInRow3));
        var deltaLossesInRow5Reward = params.lossesInRow5Reward.multiply(BigDecimal.valueOf(lossesInRow5));
        var deltaLossesInRow7Reward = params.lossesInRow7Reward.multiply(BigDecimal.valueOf(lossesInRow7));
        var deltaLossesInRow11Reward = params.lossesInRow11Reward.multiply(BigDecimal.valueOf(lossesInRow11));

        var deltaHighestMultiplierReward = params.higestMultiplierReward.multiply(highestMultiplier);

        return BigDecimal.ZERO
                .add(deltaDebit)
                .add(deltaCredit)
                .add(deltaHighestMultiplierReward)
                .add(deltaWinsInRow3Reward)
                .add(deltaWinsInRow4Reward)
                .add(deltaWinsInRow5Reward)
                .add(deltaLossesInRow3Reward)
                .add(deltaLossesInRow5Reward)
                .add(deltaLossesInRow7Reward)
                .add(deltaLossesInRow11Reward);
    }
    public void invalidate() {
        debit.remove();
        credit.remove();
        winsInRowSupplier.remove();
        lossesInRowSupplier.remove();
    }

    @Getter
    @Setter
    @Builder
    @EqualsAndHashCode(onlyExplicitlyIncluded = true)
    @ToString
    public static class WalletSession implements Comparable<WalletSession> {
        @EqualsAndHashCode.Include
        private UUID tx;
        private long accountId;
        @Builder.Default
        private BigDecimal debit = BigDecimal.ZERO;
        @Builder.Default
        private BigDecimal credit = BigDecimal.ZERO;
        private Date timestamp;

        public BigDecimal multiplier() {
            return getCredit().divide(getDebit(), 2, RoundingMode.HALF_DOWN);
        }
        public boolean isWin() {
            return getCredit().compareTo(BigDecimal.ZERO) > 0;
        }
        public boolean isLoss() {
            return getCredit().compareTo(BigDecimal.ZERO) == 0;
        }
        @Override
        public int compareTo(WalletSession other) {
            int result = getTimestamp().compareTo(other.getTimestamp());
            if (result != 0) {
                return result;
            }
            return getTx().compareTo(other.getTx());
        }
    }

    @AllArgsConstructor
    @Builder
    @ToString
    public static class Params {
        private final Range<BigDecimal> debitRange;
        private final Range<BigDecimal> creditRange;

        private final BigDecimal debitReward, creditReward;

        private final BigDecimal higestMultiplierReward;

        private final BigDecimal winsInRow3Reward, winsInRow4Reward, winsInRow5Reward;
        private final BigDecimal lossesInRow3Reward, lossesInRow5Reward, lossesInRow7Reward, lossesInRow11Reward;
    }
}
