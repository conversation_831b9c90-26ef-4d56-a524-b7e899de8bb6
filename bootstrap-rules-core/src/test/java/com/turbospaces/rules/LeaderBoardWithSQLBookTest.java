package com.turbospaces.rules;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.calcite.DataContext;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.linq4j.AbstractEnumerable;
import org.apache.calcite.linq4j.Enumerable;
import org.apache.calcite.linq4j.Enumerator;
import org.apache.calcite.rel.type.RelDataType;
import org.apache.calcite.rel.type.RelDataTypeFactory;
import org.apache.calcite.schema.ScannableTable;
import org.apache.calcite.schema.SchemaPlus;
import org.apache.calcite.schema.Table;
import org.apache.calcite.schema.impl.AbstractSchema;
import org.apache.calcite.schema.impl.AbstractTable;
import org.apache.calcite.sql.type.SqlTypeName;
import org.junit.jupiter.api.Test;
import org.testcontainers.shaded.com.google.common.collect.Lists;

import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LeaderBoardWithSQLBookTest {
    @Test
    public void works() throws Exception {
        List<LeaderboardStateEntity> data = Arrays.asList(
                LeaderboardStateEntity.builder().accountId(1L).highestMultiplierInHour(25).debit(1000).credit(1001).build(),
                LeaderboardStateEntity.builder().accountId(2L).highestMultiplierInHour(17).debit(1100).credit(3003).build(),
                LeaderboardStateEntity.builder().accountId(3L).highestMultiplierInHour(13).debit(1500).credit(2002).build(),
                LeaderboardStateEntity.builder().accountId(4L).highestMultiplierInHour(79).debit(1600).credit(5005).build(),
                LeaderboardStateEntity.builder().accountId(5L).highestMultiplierInHour(11).debit(1200).credit(4004).build() //
        );

        Properties info = new Properties();
        info.setProperty("lex", "JAVA");
        info.setProperty("caseSensitive", "false");

        Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
        CalciteConnection unwrap = connection.unwrap(CalciteConnection.class);

        SchemaPlus rootSchema = unwrap.getRootSchema();
        rootSchema.add("PUBLIC", new InMemorySchema(data));

        try (var statement = unwrap.prepareStatement("select l.*, l.debit * 3 + l.credit * 2 + (CASE WHEN l.highest_multiplier_in_hour > 25 THEN 0.3 ELSE 0.1 END) as score from public.leaderboards l order by score desc")) {

            ResultSet rs = statement.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            List<String> columns = Lists.newArrayListWithExpectedSize(columnCount);

            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                columns.add(columnName);
            }

            log.info(columns.toString());
            while (rs.next()) {
                List<Object> row = Lists.newArrayListWithExpectedSize(columnCount);
                for (int i = 1; i <= columnCount; i++) {
                    Object object = rs.getObject(i);
                    row.add(object);
                }
                log.info(row.toString());
            }
        }
    }

    public static final class InMemorySchema extends AbstractSchema {
        private final Map<String, Table> map = Maps.newConcurrentMap();

        public InMemorySchema(Collection<LeaderboardStateEntity> data) {
            super();
            map.put("leaderboards", new LeaderboardMemoryTable(data));
        }
        @Override
        protected Map<String, Table> getTableMap() {
            return map;
        }
    }

    public static class LeaderboardMemoryTable extends AbstractTable implements ScannableTable {
        private final Collection<LeaderboardStateEntity> data;

        public LeaderboardMemoryTable(Collection<LeaderboardStateEntity> data) {
            this.data = data;
        }
        @Override
        public RelDataType getRowType(RelDataTypeFactory typeFactory) {
            RelDataTypeFactory.Builder builder = typeFactory.builder();
            builder.add("account_id", SqlTypeName.BIGINT);
            builder.add("highest_multiplier_in_hour", SqlTypeName.DOUBLE);
            builder.add("debit", SqlTypeName.DOUBLE);
            builder.add("credit", SqlTypeName.DOUBLE);
            builder.add("wins_in_row", SqlTypeName.INTEGER);
            builder.add("losses_in_row", SqlTypeName.INTEGER);
            return builder.build();
        }
        @Override
        public Enumerable<Object[]> scan(DataContext root) {
            return new AbstractEnumerable<>() {
                @Override
                public Enumerator<Object[]> enumerator() {
                    return new Enumerator<>() {
                        private Iterator<LeaderboardStateEntity> iterator = data.iterator();
                        private Object[] current;

                        @Override
                        public Object[] current() {
                            return current;
                        }
                        @Override
                        public boolean moveNext() {
                            if (iterator.hasNext()) {
                                LeaderboardStateEntity it = iterator.next();

                                current = new Object[] { it.accountId, it.highestMultiplierInHour, it.debit, it.credit, it.winsInRow, it.lossesInRow };
                                return true;
                            }

                            return false;
                        }
                        @Override
                        public void reset() {
                            iterator = data.iterator();
                        }
                        @Override
                        public void close() {
                            iterator = Collections.emptyIterator();
                        }
                    };
                }
            };
        }
    }
}
