package com.turbospaces.rules.mapper;

import com.turbospaces.rules.FieldOperator;
import com.turbospaces.rules.LogicalOperator;
import com.turbospaces.rules.RuleCondition;
import com.turbospaces.rules.RuleGroup;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class RuleGroupMapperTest {

    private RuleGroupMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new RuleGroupMapper();
    }

    @Test
    void testToProtobuf_SimpleRuleGroup() {
        RuleGroup internal = RuleGroup.builder()
                .name("Test Rule")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("credit")
                                .operator(FieldOperator.GREATER_THAN)
                                .value("1000.0")
                                .build()))
                .build();

        rule.api.v1.RuleGroup protobuf = mapper.toProto(internal);

        assertNotNull(protobuf);
        assertEquals("and", protobuf.getOperator());
        assertEquals(1, protobuf.getConditionsCount());
        assertEquals(0, protobuf.getNestedCount());

        rule.api.v1.RuleCondition condition = protobuf.getConditions(0);
        assertEquals("credit", condition.getField());
        assertEquals("GREATER_THAN", condition.getOperator());
        assertEquals("1000.0", condition.getValue());
    }

    @Test
    void testFromProtobuf_SimpleRuleGroup() {
        rule.api.v1.RuleGroup protobuf = rule.api.v1.RuleGroup.newBuilder()
                .setOperator("or")
                .addConditions(rule.api.v1.RuleCondition.newBuilder()
                        .setField("accountId")
                        .setOperator("EQUALS")
                        .setValue("123")
                        .build())
                .build();

        RuleGroup internal = mapper.fromProto(protobuf);

        assertNotNull(internal);
        assertEquals(LogicalOperator.OR, internal.getOperator());
        assertEquals(1, internal.getConditions().size());
        assertEquals(0, internal.getNested().size());

        RuleCondition condition = internal.getConditions().get(0);
        assertEquals("accountId", condition.getField());
        assertEquals(FieldOperator.EQUALS, condition.getOperator());
        assertEquals("123", condition.getValue());
    }

    @Test
    void testToProtobuf_WithSubPath() {
        RuleGroup internal = RuleGroup.builder()
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("username")
                                .subPath("profile")
                                .operator(FieldOperator.CONTAINS)
                                .value("admin")
                                .build()))
                .build();

        rule.api.v1.RuleGroup protobuf = mapper.toProto(internal);

        assertNotNull(protobuf);
        assertEquals(1, protobuf.getConditionsCount());

        rule.api.v1.RuleCondition condition = protobuf.getConditions(0);
        assertEquals("username", condition.getField());
        assertEquals("profile", condition.getSubpath());
        assertEquals("CONTAINS", condition.getOperator());
        assertEquals("admin", condition.getValue());
    }

    @Test
    void testFromProtobuf_WithSubPath() {
        rule.api.v1.RuleGroup protobuf = rule.api.v1.RuleGroup.newBuilder()
                .setOperator("and")
                .addConditions(rule.api.v1.RuleCondition.newBuilder()
                        .setField("tags")
                        .setSubpath("account")
                        .setOperator("IN")
                        .setValue("vip,premium")
                        .build())
                .build();

        RuleGroup internal = mapper.fromProto(protobuf);

        assertNotNull(internal);
        assertEquals(1, internal.getConditions().size());

        RuleCondition condition = internal.getConditions().get(0);
        assertEquals("account", condition.getSubPath());
        assertEquals("tags", condition.getField());
        assertEquals(FieldOperator.IN, condition.getOperator());
        assertEquals("vip,premium", condition.getValue());
    }

    @Test
    void testToProtobuf_NestedRuleGroups() {
        RuleGroup nested = RuleGroup.builder()
                .operator(LogicalOperator.OR)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("wins")
                                .operator(FieldOperator.GREATER_THAN)
                                .value("10")
                                .build()))
                .build();

        RuleGroup internal = RuleGroup.builder()
                .name("Complex Rule")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("credit")
                                .operator(FieldOperator.GREATER_THAN)
                                .value("1000.0")
                                .build()))
                .nested(List.of(nested))
                .build();

        rule.api.v1.RuleGroup protobuf = mapper.toProto(internal);

        assertNotNull(protobuf);
        assertEquals("and", protobuf.getOperator());
        assertEquals(1, protobuf.getConditionsCount());
        assertEquals(1, protobuf.getNestedCount());

        rule.api.v1.RuleGroup nestedProtobuf = protobuf.getNested(0);
        assertEquals("or", nestedProtobuf.getOperator());
        assertEquals(1, nestedProtobuf.getConditionsCount());
        assertEquals(0, nestedProtobuf.getNestedCount());
    }

    @Test
    void testFromProtobuf_NestedRuleGroups() {
        rule.api.v1.RuleGroup nestedProtobuf = rule.api.v1.RuleGroup.newBuilder()
                .setOperator("or")
                .addConditions(rule.api.v1.RuleCondition.newBuilder()
                        .setField("multiplier")
                        .setOperator("GREATER_THAN")
                        .setValue("5.0")
                        .build())
                .build();

        rule.api.v1.RuleGroup protobuf = rule.api.v1.RuleGroup.newBuilder()
                .setOperator("and")
                .addConditions(rule.api.v1.RuleCondition.newBuilder()
                        .setField("accountId")
                        .setOperator("GREATER_THAN")
                        .setValue("1000")
                        .build())
                .addNested(nestedProtobuf)
                .build();

        RuleGroup internal = mapper.fromProto(protobuf);

        assertNotNull(internal);
        assertEquals(LogicalOperator.AND, internal.getOperator());
        assertEquals(1, internal.getConditions().size());
        assertEquals(1, internal.getNested().size());

        RuleGroup nestedInternal = internal.getNested().get(0);
        assertEquals(LogicalOperator.OR, nestedInternal.getOperator());
        assertEquals(1, nestedInternal.getConditions().size());
        assertEquals(0, nestedInternal.getNested().size());
    }

    @Test
    void testRoundTrip_ComplexRuleGroup() {
        RuleGroup original = RuleGroup.builder()
                .name("Round Trip Test")
                .operator(LogicalOperator.AND)
                .conditions(List.of(
                        RuleCondition.builder()
                                .field("credit")
                                .operator(FieldOperator.GREATER_THAN)
                                .value("1000.0")
                                .build(),
                        RuleCondition.builder()
                                .field("username")
                                .subPath("profile")
                                .operator(FieldOperator.STARTS_WITH)
                                .value("admin")
                                .build()))
                .nested(List.of(
                        RuleGroup.builder()
                                .operator(LogicalOperator.OR)
                                .conditions(List.of(
                                        RuleCondition.builder()
                                                .field("wins")
                                                .operator(FieldOperator.GREATER_THAN)
                                                .value("5")
                                                .build()))
                                .build()))
                .build();

        rule.api.v1.RuleGroup protobuf = mapper.toProto(original);
        RuleGroup roundTrip = mapper.fromProto(protobuf);

        assertNotNull(roundTrip);
        assertEquals(original.getOperator(), roundTrip.getOperator());
        assertEquals(original.getConditions().size(), roundTrip.getConditions().size());
        assertEquals(original.getNested().size(), roundTrip.getNested().size());

        assertEquals(original.getConditions().get(0).getField(), roundTrip.getConditions().get(0).getField());
        assertEquals(original.getConditions().get(0).getOperator(), roundTrip.getConditions().get(0).getOperator());
        assertEquals(original.getConditions().get(0).getValue(), roundTrip.getConditions().get(0).getValue());

        assertEquals(original.getConditions().get(1).getSubPath(), roundTrip.getConditions().get(1).getSubPath());
        assertEquals(original.getConditions().get(1).getField(), roundTrip.getConditions().get(1).getField());
    }

    @Test
    void testInvalidOperatorHandling() {
        rule.api.v1.RuleGroup protobuf = rule.api.v1.RuleGroup.newBuilder()
                .setOperator("invalid_operator")
                .addConditions(rule.api.v1.RuleCondition.newBuilder()
                        .setField("test")
                        .setOperator("INVALID_OPERATOR")
                        .setValue("value")
                        .build())
                .build();

        RuleGroup internal = mapper.fromProto(protobuf);

        assertNotNull(internal);
        assertEquals(LogicalOperator.AND, internal.getOperator());
        assertEquals(1, internal.getConditions().size());
        assertEquals(FieldOperator.EQUALS, internal.getConditions().get(0).getOperator());
    }
}
