package com.turbospaces.rules;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.querydsl.core.types.dsl.Expressions;

class FieldDescriptorTest {

    @Test
    void testMandatoryFieldsEnforced() {
        assertThrows(NullPointerException.class, () ->
            new FieldDescriptor(null, String.class, Expressions.stringPath("test"), "Test Label"));
        assertThrows(NullPointerException.class, () ->
            new FieldDescriptor("test", null, Expressions.stringPath("test"), "Test Label"));
        assertThrows(NullPointerException.class, () ->
            new FieldDescriptor("test", String.class, null, "Test Label"));
        assertThrows(NullPointerException.class, () ->
            new FieldDescriptor("test", String.class, Expressions.stringPath("test"), null));
    }

    @Test
    void testValidFieldDescriptorCreation() {
        FieldDescriptor fieldWithTooltip = new FieldDescriptor(
            "amount",
            BigDecimal.class,
            Expressions.numberPath(BigDecimal.class, "amount"),
            "Amount Field",
            "The transaction amount"
        );

        assertEquals("amount", fieldWithTooltip.getPath());
        assertEquals(BigDecimal.class, fieldWithTooltip.getJavaType());
        assertEquals(FieldType.NUMBER, fieldWithTooltip.getFieldType()); // Auto-detected from BigDecimal.class
        assertEquals("Amount Field", fieldWithTooltip.getLabel());
        assertEquals("The transaction amount", fieldWithTooltip.getTooltip());
        assertNotNull(fieldWithTooltip.getExpression());

        FieldDescriptor fieldWithoutTooltip = new FieldDescriptor(
            "name",
            String.class,
            Expressions.stringPath("name"),
            "Name Field"
        );

        assertEquals("name", fieldWithoutTooltip.getPath());
        assertEquals(String.class, fieldWithoutTooltip.getJavaType());
        assertEquals(FieldType.TEXT, fieldWithoutTooltip.getFieldType()); // Auto-detected from String.class
        assertEquals("Name Field", fieldWithoutTooltip.getLabel());
        assertNull(fieldWithoutTooltip.getTooltip());
    }

    @Test
    void testEnumFieldTypeDetection() {
        FieldDescriptor enumField = new FieldDescriptor(
            "status",
            TestEnum.class,
            Expressions.stringPath("status"),
            "Status Field",
            "The current status"
        );

        assertEquals("status", enumField.getPath());
        assertEquals(TestEnum.class, enumField.getJavaType());
        assertEquals(FieldType.SELECT, enumField.getFieldType()); // Auto-detected from enum
        assertEquals("Status Field", enumField.getLabel());
        assertEquals("The current status", enumField.getTooltip());
    }

    // Test enum for the above test
    enum TestEnum { VALUE1, VALUE2 }

    @Test
    void testAutoFieldTypeDetection() {
        FieldDescriptor stringField = new FieldDescriptor(
            "description",
            String.class,
            Expressions.stringPath("description"),
            "Description Field"
        );

        assertEquals("description", stringField.getPath());
        assertEquals(String.class, stringField.getJavaType());
        assertEquals(FieldType.TEXT, stringField.getFieldType()); // Should auto-detect as TEXT
        assertEquals("Description Field", stringField.getLabel());
        assertNull(stringField.getTooltip());

        FieldDescriptor listField = new FieldDescriptor(
            "tags",
            List.class,
            Expressions.stringPath("tags"), // Use simple path for test
            "Tags Field",
            "List of tags"
        );

        assertEquals(FieldType.MULTISELECT, listField.getFieldType());
    }

    @Test
    void testBackwardCompatibilityGetter() {
        FieldDescriptor field = new FieldDescriptor(
            "amount",
            BigDecimal.class,
            Expressions.numberPath(BigDecimal.class, "amount"),
            "Amount Field"
        );

        assertEquals(BigDecimal.class, field.getType());
        assertEquals(field.getJavaType(), field.getType());
    }

    @Test
    void testOperatorGeneration() {
        FieldDescriptor numberField = new FieldDescriptor(
            "count",
            Integer.class,
            Expressions.numberPath(Integer.class, "count"),
            "Count Field"
        );

        List<OperatorInfo> operators = numberField.get();
        assertNotNull(operators);

        boolean hasEquals = operators.stream().anyMatch(op -> op.getOperator() == FieldOperator.EQUALS);
        boolean hasGreaterThan = operators.stream().anyMatch(op -> op.getOperator() == FieldOperator.GREATER_THAN);
        boolean hasBetween = operators.stream().anyMatch(op -> op.getOperator() == FieldOperator.BETWEEN);

        assertTrue(hasEquals);
        assertTrue(hasGreaterThan);
        assertTrue(hasBetween);
    }

    @Test
    void testEntityMetadataIntegration() {
        EntityMetadata<String> metadata = new EntityMetadata<>(String.class);

        metadata.field("name", String.class, Expressions.stringPath("name"), "Name Field", "The name");

        FieldDescriptor field = metadata.getFieldDescriptor("name");
        assertNotNull(field);
        assertEquals("name", field.getPath());
        assertEquals(String.class, field.getJavaType());
        assertEquals(FieldType.TEXT, field.getFieldType());
        assertEquals("Name Field", field.getLabel());
        assertEquals("The name", field.getTooltip());

        metadata.field("email", String.class, Expressions.stringPath("email"), "Email Address");

        FieldDescriptor emailField = metadata.getFieldDescriptor("email");
        assertNotNull(emailField);
        assertEquals("email", emailField.getPath());
        assertEquals("Email Address", emailField.getLabel());
        assertNull(emailField.getTooltip());

        metadata.field("id", Integer.class, Expressions.numberPath(Integer.class, "id"));

        FieldDescriptor idField = metadata.getFieldDescriptor("id");
        assertNotNull(idField);
        assertEquals("id", idField.getPath());
        assertEquals("Id", idField.getLabel()); // Should be auto-generated
        assertNull(idField.getTooltip());
    }
}
