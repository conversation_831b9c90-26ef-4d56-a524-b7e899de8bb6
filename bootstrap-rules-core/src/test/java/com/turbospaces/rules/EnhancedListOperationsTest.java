package com.turbospaces.rules;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.querydsl.collections.CollQuery;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.turbospaces.json.CommonObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class EnhancedListOperationsTest {
    public static final QPaymentRiskStateEntity Q = QPaymentRiskStateEntity.paymentRiskStateEntity;

    public static final String ERROR_CODES = "error_codes";

    @Test
    public void testEnhancedListOperations() throws Exception {
        // User 1: Multiple error codes
        var entity1 = PaymentRiskStateEntity.builder()
                .accountId(1001L)
                .provider("SPREEDLY_FISERV")
                .errorCodes(Arrays.asList(51, 20, 85))
                .totalErrorCount(5)
                .build();

        // User 2: Two error codes
        var entity2 = PaymentRiskStateEntity.builder()
                .accountId(1002L)
                .provider("SPREEDLY_RAPYD")
                .errorCodes(Arrays.asList(79, 80))
                .totalErrorCount(3)
                .build();

        // User 3: Single error code
        var entity3 = PaymentRiskStateEntity.builder()
                .accountId(1003L)
                .provider("STRIPE")
                .errorCodes(Arrays.asList(50))
                .totalErrorCount(1)
                .build();

        // User 4: Empty error codes
        var entity4 = PaymentRiskStateEntity.builder()
                .accountId(1004L)
                .provider("PAYPAL")
                .errorCodes(Arrays.asList())
                .totalErrorCount(0)
                .build();

        var mapper = new CommonObjectMapper();
        var paymentStates = Arrays.asList(entity1, entity2, entity3, entity4);
        var metadata = new EntityMetadata<>(PaymentRiskStateEntity.class)
                .field(ERROR_CODES, Integer.class, (Expression) Q.errorCodes);

        //
        // ~ Test 1: Lists with exactly 2 error codes
        //
        RuleGroup exactSizeRule = RuleGroup.builder()
                .name("Users with Exactly 2 Error Codes")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.SIZE_EQUALS)
                                .value("2")
                                .build()))
                .build();

        var exactSizeResults = applyRuleAndPrintResults(paymentStates, metadata, exactSizeRule, mapper);
        assertEquals(1, exactSizeResults.size(), "Should find 1 user with exactly 2 error codes");
        assertTrue(exactSizeResults.contains(entity2), "Should match entity2");

        //
        // ~ Test 2: Lists with more than 1 error code
        //
        RuleGroup moreThanOneRule = RuleGroup.builder()
                .name("Users with Multiple Error Codes")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.SIZE_GREATER_THAN)
                                .value("1")
                                .build()))
                .build();

        var multipleErrorResults = applyRuleAndPrintResults(paymentStates, metadata, moreThanOneRule, mapper);
        assertEquals(2, multipleErrorResults.size(), "Should find 2 users with multiple error codes");
        assertTrue(multipleErrorResults.contains(entity1), "Should match entity1 (3 errors)");
        assertTrue(multipleErrorResults.contains(entity2), "Should match entity2 (2 errors)");

        //
        // ~ Test 3: Lists with fewer than 3 error codes
        //
        RuleGroup fewerThanThreeRule = RuleGroup.builder()
                .name("Users with Few Error Codes")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.SIZE_LESS_THAN)
                                .value("3")
                                .build()))
                .build();

        var fewErrorResults = applyRuleAndPrintResults(paymentStates, metadata, fewerThanThreeRule, mapper);
        assertEquals(3, fewErrorResults.size(), "Should find 3 users with < 3 error codes");
        assertTrue(fewErrorResults.contains(entity2), "Should match entity2 (2 errors)");
        assertTrue(fewErrorResults.contains(entity3), "Should match entity3 (1 error)");
        assertTrue(fewErrorResults.contains(entity4), "Should match entity4 (0 errors)");

        //
        // ~ Test 4: Empty error code lists
        //
        RuleGroup emptyListRule = RuleGroup.builder()
                .name("Users with No Error Codes")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.IS_EMPTY)
                                .value("")
                                .build()))
                .build();

        var emptyResults = applyRuleAndPrintResults(paymentStates, metadata, emptyListRule, mapper);
        assertEquals(1, emptyResults.size(), "Should find 1 user with empty error codes");
        assertTrue(emptyResults.contains(entity4), "Should match entity4");

        //
        // ~ Test 5: Non-empty error code lists
        //
        RuleGroup nonEmptyListRule = RuleGroup.builder()
                .name("Users with Any Error Codes")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.IS_NOT_EMPTY)
                                .value("")
                                .build()))
                .build();

        var nonEmptyResults = applyRuleAndPrintResults(paymentStates, metadata, nonEmptyListRule, mapper);
        assertEquals(3, nonEmptyResults.size(), "Should find 3 users with non-empty error codes");
        assertTrue(nonEmptyResults.contains(entity1), "Should match entity1");
        assertTrue(nonEmptyResults.contains(entity2), "Should match entity2");
        assertTrue(nonEmptyResults.contains(entity3), "Should match entity3");

        //
        // ~ Test 6: Traditional CONTAINS operation (still works)
        //
        RuleGroup containsRule = RuleGroup.builder()
                .name("Users with Error Code 51")
                .conditions(List.of(
                        RuleCondition.builder()
                                .field(ERROR_CODES)
                                .operator(FieldOperator.CONTAINS)
                                .value("51")
                                .build()))
                .build();

        var containsResults = applyRuleAndPrintResults(paymentStates, metadata, containsRule, mapper);
        assertEquals(1, containsResults.size(), "Should find 1 user with error code 51");
        assertTrue(containsResults.contains(entity1), "Should match entity1");

        //
        // ~ Test 7: External JSON configuration with new size operations
        //
        RuleGroup externalRule = createExternalListSizeRule(mapper);
        var externalResults = applyRuleAndPrintResults(paymentStates, metadata, externalRule, mapper);
        assertEquals(2, externalResults.size(), "External rule should match 2 users");
        assertTrue(externalResults.contains(entity1), "Should match entity1");
        assertTrue(externalResults.contains(entity2), "Should match entity2");
    }

    private static List<PaymentRiskStateEntity> applyRuleAndPrintResults(
            List<PaymentRiskStateEntity> data,
            EntityMetadata<PaymentRiskStateEntity> metadata,
            RuleGroup ruleGroup,
            CommonObjectMapper mapper) throws Exception {
        Predicate predicate = QueryDslRuleAdapter.convertToQueryDSL(ruleGroup, metadata, mapper);
        var results = new CollQuery<PaymentRiskStateEntity>().from(Q, data).where(predicate).fetch();

        log.info("rule: {}", ruleGroup.getName());
        log.info("predicate: {}", predicate);
        log.info("matching {} items {}", results.size(), results);

        return results;
    }

    private static RuleGroup createExternalListSizeRule(CommonObjectMapper mapper) throws Exception {
        var valueAsString = """
                {
                    "name": "Users with Multiple Errors",
                    "operator": "AND",
                    "conditions": [
                        {
                            "field": "error_codes",
                            "operator": "SIZE_GREATER_THAN",
                            "value": "1"
                        },
                        {
                            "field": "error_codes",
                            "operator": "IS_NOT_EMPTY",
                            "value": ""
                        }
                    ]
                }
                """;
        return mapper.readValue(valueAsString, RuleGroup.class);
    }
}
