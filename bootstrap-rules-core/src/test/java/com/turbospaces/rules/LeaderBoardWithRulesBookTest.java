package com.turbospaces.rules;

import java.math.BigDecimal;
import java.util.Date;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.deliveredtechnologies.rulebook.FactMap;
import com.deliveredtechnologies.rulebook.NameValueReferableTypeConvertibleMap;
import com.deliveredtechnologies.rulebook.Result;
import com.deliveredtechnologies.rulebook.lang.RuleBookBuilder;
import com.deliveredtechnologies.rulebook.lang.RuleBuilder;
import com.deliveredtechnologies.rulebook.model.rulechain.cor.CoRRuleBook;
import com.google.common.collect.Range;
import com.turbospaces.common.EventTimeClock;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.rules.LeaderboardState.WalletSession;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LeaderBoardWithRulesBookTest {
    @Test
    public void works() {
        var now = new Date();
        var clock = new EventTimeClock();
        var state = new LeaderboardState(clock);
        var book = RuleBookBuilder.create(LeaderboardBook.class).withResultType(Double.class).withDefaultResult(0D).build();

        var factMap = new FactMap<>();
        factMap.setValue("data", state);

        // @formatter:off
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -60)).debit(BigDecimal.valueOf(0.1)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -59)).debit(BigDecimal.valueOf(0.25)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -58)).debit(BigDecimal.valueOf(0.35)).credit(BigDecimal.valueOf(0.05)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -57)).debit(BigDecimal.valueOf(10.04)).credit(BigDecimal.valueOf(11.5)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -56)).debit(BigDecimal.valueOf(10.05)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -55)).debit(BigDecimal.valueOf(10.06)).credit(BigDecimal.valueOf(127.5)).build());
        // @formatter:on

        //
        // ~ debit [0.25 .. 10] 30.60 X 0.25 = 7.65
        // ~ credit [0.1 .. 100] 111.50 X 1.25 = 139.375
        //

        book.run(factMap);
        book.getResult().ifPresent(new Consumer<>() {
            @Override
            public void accept(Result<Double> result) {
                log.info("total score with debit/credit: {}", result.getValue());
                log.info("----------------------------------------------------");
                Assertions.assertEquals(381.42D, result.getValue());
            }
        });

        // @formatter:off
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -50)).debit(BigDecimal.valueOf(0.01)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -49)).debit(BigDecimal.valueOf(0.02)).credit(BigDecimal.valueOf(0.02)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -48)).debit(BigDecimal.valueOf(0.03)).credit(BigDecimal.valueOf(0.03)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -47)).debit(BigDecimal.valueOf(0.04)).credit(BigDecimal.valueOf(0.04)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -46)).debit(BigDecimal.valueOf(0.05)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -45)).debit(BigDecimal.valueOf(0.06)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -44)).debit(BigDecimal.valueOf(0.07)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -43)).debit(BigDecimal.valueOf(0.08)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -43)).debit(BigDecimal.valueOf(0.09)).credit(BigDecimal.valueOf(0.09)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -40)).debit(BigDecimal.valueOf(0.15)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -39)).debit(BigDecimal.valueOf(0.16)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -38)).debit(BigDecimal.valueOf(0.17)).build());
        state.addRound(WalletSession.builder().tx(PlatformUtil.randomUUID()).timestamp(DateUtils.addMinutes(now, -37)).debit(BigDecimal.valueOf(0.19)).credit(BigDecimal.valueOf(0.19)).build());
        // @formatter:on

        //
        // ~ wins in row 1 X 55.5 = 55.5
        // ~ losses in row 2 X 12.5 = 25
        //

        book.run(factMap);
        book.getResult().ifPresent(new Consumer<>() {
            @Override
            public void accept(Result<Double> result) {
                log.info("total score with in row logic: {}", result.getValue());
                log.info("----------------------------------------------------");
                Assertions.assertEquals(462.1575, result.getValue());
            }
        });

        //
        // ~ real time number of records in wallet history
        //
        for (int i = 0; i < 1024 * 16; i++) {
            state.addRound(WalletSession.builder()
                    .tx(PlatformUtil.randomUUID())
                    .timestamp(DateUtils.addMinutes(now, -i))
                    .debit(BigDecimal.valueOf(0.1))
                    .credit(BigDecimal.valueOf(0.1 + Math.random())).build());
        }

        int iterations = 1024 * 16;
        StopWatch stopWatch = StopWatch.createStarted();
        for (int i = 0; i < iterations; i++) {
            book.run(factMap);
            // state.invalidate();
        }
        stopWatch.stop();
        log.info("execution time: {} iterations: {} records: {}", stopWatch, iterations, state.getRounds().size());
    }

    @Slf4j
    public static class LeaderboardBook extends CoRRuleBook<Object> {
        @Override
        public void defineRules() {
            debitRule();
            creditRule();
            winsInRowRule();
            lossesInRowRule();
            higestMultiplierInHourRule();
        }
        private void debitRule() {
            BigDecimal lower = BigDecimal.valueOf(0.25);
            BigDecimal upper = BigDecimal.valueOf(10);

            double reward = 0.25;
            Range<BigDecimal> range = Range.closed(lower, upper);

            addRule(RuleBuilder.create()
                    .withFactType(LeaderboardState.class)
                    .withResultType(Double.class)
                    .when(map -> {
                        LeaderboardState state = map.getOne();
                        return state.debit(range).compareTo(BigDecimal.ZERO) > 0;
                    }).then(new BiConsumer<>() {
                        @Override
                        public void accept(NameValueReferableTypeConvertibleMap<LeaderboardState> map, Result<Double> result) {
                            LeaderboardState state = map.getOne();
                            BigDecimal amount = state.debit(range);
                            double delta = amount.multiply(BigDecimal.valueOf(reward)).doubleValue();

                            log.trace("debit rule delta: {}, amount: {}, reward mult: {}", delta, amount, reward);
                            result.setValue(result.getValue() + delta);
                        }
                    }).build());
        }
        private void creditRule() {
            BigDecimal lower = BigDecimal.valueOf(0.1);
            BigDecimal upper = BigDecimal.valueOf(100);

            double reward = 1.25;
            Range<BigDecimal> range = Range.closed(lower, upper);

            addRule(RuleBuilder.create()
                    .withFactType(LeaderboardState.class)
                    .withResultType(Double.class)
                    .when(map -> {
                        LeaderboardState state = map.getOne();
                        return state.credit(range).compareTo(BigDecimal.ZERO) > 0;
                    }).then(new BiConsumer<>() {
                        @Override
                        public void accept(NameValueReferableTypeConvertibleMap<LeaderboardState> map, Result<Double> result) {
                            LeaderboardState state = map.getOne();
                            BigDecimal amount = state.credit(range);
                            double delta = amount.multiply(BigDecimal.valueOf(reward)).doubleValue();

                            log.trace("credit rule delta: {}, amount: {}, reward mult: {}", delta, amount, reward);
                            result.setValue(result.getValue() + delta);
                        }
                    }).build());
        }
        private void winsInRowRule() {
            int step = 3;
            double rewardEach = 55.5;
            addRule(RuleBuilder.create()
                    .withFactType(LeaderboardState.class)
                    .withResultType(Double.class)
                    .when(map -> {
                        LeaderboardState state = map.getOne();
                        return state.winsInRow(step) > 0;
                    }).then(new BiConsumer<>() {
                        @Override
                        public void accept(NameValueReferableTypeConvertibleMap<LeaderboardState> map, Result<Double> result) {
                            LeaderboardState state = map.getOne();
                            int count = state.winsInRow(step);
                            double delta = count * rewardEach;

                            log.trace("wins in row rule delta: {}, count: {}, rewardEach: {}", delta, count, rewardEach);
                            result.setValue(result.getValue() + delta);
                        }
                    }).build());
        }
        private void lossesInRowRule() {
            int step = 3;
            double rewardEach = 12.5;
            addRule(RuleBuilder.create()
                    .withFactType(LeaderboardState.class)
                    .withResultType(Double.class)
                    .when(map -> {
                        LeaderboardState state = map.getOne();
                        return state.lossesInRow(step) > 0;
                    }).then(new BiConsumer<>() {
                        @Override
                        public void accept(NameValueReferableTypeConvertibleMap<LeaderboardState> map, Result<Double> result) {
                            LeaderboardState state = map.getOne();
                            int count = state.lossesInRow(step);
                            double delta = count * rewardEach;

                            result.setValue(result.getValue() + delta);
                            log.trace("losses in row rule delta: {}, count: {}, rewardEach: {}", delta, count, rewardEach);
                        }
                    }).build());
        }
        private void higestMultiplierInHourRule() {
            double reward = 18.5;
            addRule(RuleBuilder.create()
                    .withFactType(LeaderboardState.class)
                    .withResultType(Double.class)
                    .when(map -> {
                        LeaderboardState state = map.getOne();
                        return state.higestMultiplierInHour() > 0;
                    }).then(new BiConsumer<>() {
                        @Override
                        public void accept(NameValueReferableTypeConvertibleMap<LeaderboardState> map, Result<Double> result) {
                            LeaderboardState state = map.getOne();
                            double value = state.higestMultiplierInHour();
                            double delta = value * reward;

                            result.setValue(result.getValue() + delta);
                            log.trace("higest multiplier in hour rule delta: {}, value: {}, reward: {}", delta, value, reward);
                        }
                    }).build());
        }
    }
}
