package com.turbospaces.rules;

import java.util.Map;

import com.querydsl.core.annotations.QueryEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

@lombok.Data
@QueryEntity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileEntity {
    public long userId;
    public String username;
    public Map<String, String> preferences; // User preferences like theme, language, etc.
    public Map<String, Integer> settings;   // Numeric settings like timeout, max connections
    public Map<String, Boolean> features;   // Feature flags
    public int totalLogins;
    public boolean isActive;
}
