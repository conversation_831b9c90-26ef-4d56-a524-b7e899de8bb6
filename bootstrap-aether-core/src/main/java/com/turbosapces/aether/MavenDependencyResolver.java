package com.turbosapces.aether;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.apache.maven.repository.internal.MavenRepositorySystemUtils;
import org.eclipse.aether.DefaultRepositorySystemSession;
import org.eclipse.aether.RepositorySystem;
import org.eclipse.aether.artifact.Artifact;
import org.eclipse.aether.collection.CollectRequest;
import org.eclipse.aether.connector.basic.BasicRepositoryConnectorFactory;
import org.eclipse.aether.graph.Dependency;
import org.eclipse.aether.graph.DependencyFilter;
import org.eclipse.aether.impl.DefaultServiceLocator;
import org.eclipse.aether.impl.DefaultServiceLocator.ErrorHandler;
import org.eclipse.aether.repository.LocalRepository;
import org.eclipse.aether.repository.RemoteRepository;
import org.eclipse.aether.resolution.ArtifactResult;
import org.eclipse.aether.resolution.DependencyRequest;
import org.eclipse.aether.resolution.DependencyResult;
import org.eclipse.aether.spi.connector.RepositoryConnectorFactory;
import org.eclipse.aether.spi.connector.transport.TransporterFactory;
import org.eclipse.aether.transport.file.FileTransporterFactory;
import org.eclipse.aether.transport.http.HttpTransporterFactory;
import org.eclipse.aether.util.artifact.JavaScopes;
import org.eclipse.aether.util.filter.DependencyFilterUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import com.google.common.collect.ImmutableList;

public class MavenDependencyResolver implements DependencyResolver, InitializingBean, DisposableBean {
    public static final String DEFAULT_JAVA_SCOPE = JavaScopes.COMPILE;
    public static final String HOME = System.getenv("HOME");
    public static final File DEFAULT_LOCAL_REPOSITORY = new File(HOME, ".m2/repository");

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ImmutableList<RemoteRepository> repos;
    private RepositorySystem repositorySystem;
    private DefaultRepositorySystemSession session;

    public MavenDependencyResolver() {
        this(Collections.emptyList());
    }
    public MavenDependencyResolver(List<RemoteRepository> repos) {
        ImmutableList.Builder<RemoteRepository> remoteRepos = ImmutableList.builder();
        remoteRepos.add(new RemoteRepository.Builder("central", "default", "https://repo1.maven.org/maven2").build());
        remoteRepos.addAll(repos);
        this.repos = remoteRepos.build();
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        repositorySystem = newRepositorySystem();

        LocalRepository localRepo = new LocalRepository(DEFAULT_LOCAL_REPOSITORY);
        session = MavenRepositorySystemUtils.newSession();
        session.setLocalRepositoryManager(repositorySystem.newLocalRepositoryManager(session, localRepo));
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(session)) {
            session.setReadOnly();
        }
    }
    @Override
    public List<Artifact> resolveDependency(Artifact artifact) throws Exception {
        DependencyFilter classpathFlter = DependencyFilterUtils.classpathFilter(DEFAULT_JAVA_SCOPE);

        CollectRequest collectRequest = new CollectRequest();
        collectRequest.setRoot(new Dependency(artifact, DEFAULT_JAVA_SCOPE));
        collectRequest.setRepositories(repos);

        DependencyRequest dependencyRequest = new DependencyRequest(collectRequest, classpathFlter);
        DependencyResult dependencyResult = repositorySystem.resolveDependencies(session, dependencyRequest);

        ImmutableList.Builder<Artifact> artifacts = ImmutableList.builder();
        for (ArtifactResult artifactResult : dependencyResult.getArtifactResults()) {
            artifacts.add(artifactResult.getArtifact());
        }
        return artifacts.build();
    }
    private RepositorySystem newRepositorySystem() {
        DefaultServiceLocator locator = MavenRepositorySystemUtils.newServiceLocator();
        locator.addService(RepositoryConnectorFactory.class, BasicRepositoryConnectorFactory.class);
        locator.addService(TransporterFactory.class, FileTransporterFactory.class);
        locator.addService(TransporterFactory.class, HttpTransporterFactory.class);
        locator.setErrorHandler(new ErrorHandler() {
            @Override
            public void serviceCreationFailed(Class<?> type, Class<?> impl, Throwable exception) {
                logger.error(exception.getMessage(), exception);
            }
        });
        return locator.getService(RepositorySystem.class);
    }
}
