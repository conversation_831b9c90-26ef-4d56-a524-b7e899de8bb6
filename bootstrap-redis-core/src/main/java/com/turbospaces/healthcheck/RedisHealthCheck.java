package com.turbospaces.healthcheck;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.api.redisnode.RedisNodes;
import org.redisson.config.Config;
import org.springframework.cloud.service.common.RedisServiceInfo;

import com.codahale.metrics.health.HealthCheck;
import com.turbospaces.boot.AbstractHealtchCheck;

public class RedisHealthCheck extends AbstractHealtchCheck {
    private final RedissonClient redisson;

    public RedisHealthCheck(RedisServiceInfo rsi) {
        Config config = new Config();
        config.useSingleServer().setAddress(rsi.getUri());

        this.redisson = Redisson.create(config);
    }

    @Override
    protected Result check() throws Exception {
        try {
            return redisson.getRedisNodes(RedisNodes.SINGLE).pingAll()
                    ? Result.healthy("Redis health check successful")
                    : Result.unhealthy("Redis ping failed");
        } catch (Exception err) {
            logger.warn(err.getMessage(), err);
            return HealthCheck.Result.unhealthy(err);
        }
    }

    @Override
    public void destroy() throws Exception {
        if (redisson != null && !redisson.isShutdown()) {
            redisson.shutdown();
        }
    }

    @Override
    public boolean isBootstrapOnly() {
        return false;
    }
}
