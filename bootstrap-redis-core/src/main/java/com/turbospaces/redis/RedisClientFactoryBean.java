package com.turbospaces.redis;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.NettyHook;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.cloud.service.common.RedisServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.netty4.NettyAllocatorMetrics;
import io.micrometer.core.instrument.binder.netty4.NettyEventExecutorMetrics;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBufAllocatorMetricProvider;
import io.netty.channel.Channel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedisClientFactoryBean extends AbstractFactoryBean<RedissonClient> implements BeanNameAware {
    private final DefaultThreadFactory threadFactory = new DefaultThreadFactory("redisson-event-loop", true, Thread.NORM_PRIORITY);
    private final AtomicBoolean metrics = new AtomicBoolean();
    private final Config config = new Config();
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final NioEventLoopGroup eventLoopGroup;
    private final RedisServiceInfo serviceInfo;
    private final DefaultPlatformExecutorService executor;

    public RedisClientFactoryBean(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RedisServiceInfo si) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.serviceInfo = Objects.requireNonNull(si);
        this.eventLoopGroup = new NioEventLoopGroup(Runtime.getRuntime().availableProcessors(), threadFactory);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }
    @Override
    public Class<?> getObjectType() {
        return RedissonClient.class;
    }
    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
        super.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
        } finally {
            try {
                if (Objects.nonNull(executor)) {
                    executor.destroy();
                }
            } finally {
                if (Objects.nonNull(eventLoopGroup)) {
                    log.debug("shutting event loop: {}", eventLoopGroup.executorCount());
                    eventLoopGroup.shutdownGracefully();
                }
            }
        }
    }
    @Override
    protected RedissonClient createInstance() throws Exception {
        config.setEventLoopGroup(eventLoopGroup);
        config.setExecutor(executor);
        config.setNettyHook(new NettyHook() {
            @Override
            public void afterChannelInitialization(Channel channel) {
                //
                // ~ lazy installation of metrics adapter
                //
                if (metrics.compareAndSet(false, true)) {
                    if (channel.alloc() instanceof ByteBufAllocatorMetricProvider alloc) {
                        new NettyAllocatorMetrics(alloc).bindTo(meterRegistry);
                    }
                }
            }
            @Override
            public void afterBoostrapInitialization(Bootstrap bootstrap) {
                new NettyEventExecutorMetrics(eventLoopGroup).bindTo(meterRegistry);
            }
        });

        SingleServerConfig useSingleServer = config.useSingleServer();
        useSingleServer.setAddress(serviceInfo.getUri());
        useSingleServer.setConnectionMinimumIdleSize(Runtime.getRuntime().availableProcessors());
        useSingleServer.setTcpNoDelay(props.TCP_NO_DELAY.get());
        useSingleServer.setSubscriptionConnectionPoolSize(props.REDIS_SUBSCRIPTION_POOL_CONNECTION_SIZE.get());
        useSingleServer.setSubscriptionsPerConnection(props.REDIS_MAX_SUBSCRIPTIONS_PER_CONNECTION.get());

        return Redisson.create(config);
    }
    @Override
    protected void destroyInstance(RedissonClient instance) throws Exception {
        if (Objects.nonNull(instance)) {
            instance.shutdown();
        }
    }
}
