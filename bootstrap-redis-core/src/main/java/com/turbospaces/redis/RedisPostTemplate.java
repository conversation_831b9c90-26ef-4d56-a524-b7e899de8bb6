package com.turbospaces.redis;

import java.io.IOException;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.slf4j.event.Level;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.cloud.util.StandardUriInfoFactory;
import org.springframework.cloud.util.UriInfo;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.protobuf.Any;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.rpc.TransactionalRequestOutcomePublisher;

import api.v1.AnyMessageWithRoutingKey;
import api.v1.ApiFactory;
import api.v1.CloudEventWithRoutingKey;
import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class RedisPostTemplate implements TransactionalRequestOutcomePublisher<Long> {
    private final StandardUriInfoFactory uriFactory = new StandardUriInfoFactory();
    private final ApplicationProperties props;
    private final ApiFactory apiFactory;
    private final RedissonClient redisson;

    @Override
    public ApiFactory apiFactory() {
        return apiFactory;
    }
    @Override
    public CompletionStage<Long> sendNotify(NotifyQueuePostSpec spec) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC

        var now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var typeUrl = spec.body().getTypeUrl();
        var messageId = spec.messageId();

        AsciiString routingKey = null;
        if (spec.routingKey().isPresent()) {
            routingKey = spec.routingKey().get();
        }

        CloudEventWithRoutingKey payload = new CloudEventWithRoutingKey(now, routingKey, spec);
        RTopic rtopic = redisson.getTopic(qualifier);

        try {
            byte[] data = PlatformUtil.serialize(payload);
            return rtopic.publishAsync(data).whenComplete(new BiConsumer<>() {
                @Override
                public void accept(Long active, Throwable err) {
                    MdcUtil.propagate(mdc);
                    try {
                        if (Objects.isNull(err)) {
                            log.debug("OUT ::: ({}:{}) has been accepted by redis (topic:{}, subscribers:{}, took:{}, size:{})",
                                    typeUrl,
                                    messageId,
                                    qualifier,
                                    active,
                                    System.currentTimeMillis() - now,
                                    FileUtils.byteCountToDisplaySize(data.length));
                        } else {
                            log.error(err.getMessage(), err);
                        }
                    } finally {
                        MDC.clear();
                    }
                }
            });
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    @Override
    public CompletionStage<Long> sendEvent(EventQueuePostSpec spec) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC

        var any = spec.pack(); // ~ not wrapping to request (Any)
        var now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var typeUrl = any.getTypeUrl();

        AsciiString routingKey = null;
        if (spec.routingKey().isPresent()) {
            routingKey = spec.routingKey().get();
        }

        AnyMessageWithRoutingKey payload = new AnyMessageWithRoutingKey(now, routingKey, any);
        RTopic rtopic = redisson.getTopic(qualifier);

        try {
            byte[] data = PlatformUtil.serialize(payload);
            return rtopic.publishAsync(data).whenComplete(new BiConsumer<>() {
                @Override
                public void accept(Long active, Throwable err) {
                    MdcUtil.propagate(mdc);
                    try {
                        if (Objects.isNull(err)) {
                            log.debug("OUT ::: ({}) has been accepted by redis (topic:{}, subscribers:{}, took:{}, size:{})",
                                    typeUrl,
                                    qualifier,
                                    active,
                                    System.currentTimeMillis() - now,
                                    FileUtils.byteCountToDisplaySize(data.length));
                        } else {
                            log.error(err.getMessage(), err);
                        }
                    } finally {
                        MDC.clear();
                    }
                }
            });
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    @Override
    public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
        Map<String, String> mdc = Maps.newHashMap();
        Optional.ofNullable(MDC.getCopyOfContextMap()).ifPresent(new Consumer<>() {
            @Override
            public void accept(Map<String, String> copy) {
                mdc.putAll(copy);
            }
        });

        ResponseWrapperFacade respw = outcome.getReply();
        String typeUrl = respw.body().getTypeUrl();
        long now = System.currentTimeMillis();
        String messageId = respw.headers().getMessageId();
        String traceId = respw.headers().getTraceId();
        String status = StringUtils.lowerCase(respw.status().errorCode().toString()).intern();
        String replyTo;

        try {
            UriInfo uri = uriFactory.createUri(MutableNonPersistentReplyTopic.asRedisReplyTopic(respw.headers().getReplyTo(), uriFactory));
            Preconditions.checkArgument(RedisServiceInfo.REDIS_SCHEME.equals(uri.getScheme()));
            replyTo = uri.getPath();
        } catch (Exception err) {
            return CompletableFuture.failedStage(err);
        }

        mdc.putIfAbsent(MdcTags.MDC_MESSAGE_ID, messageId);
        mdc.putIfAbsent(MdcTags.MDC_ERROR_CODE, status);
        mdc.putIfAbsent(MdcTags.MDC_REPLY_TO, replyTo);
        if (StringUtils.isNotEmpty(traceId)) {
            mdc.putIfAbsent(MdcTags.MDC_TRACE_ID, traceId);
        }

        log.atLevel(props.isDevMode() ? Level.DEBUG : Level.TRACE).log("about to post {} to {} ...", typeUrl, replyTo);

        CloudEventWithRoutingKey payload = new CloudEventWithRoutingKey(now, outcome.getKey(), respw);
        RTopic rtopic = redisson.getTopic(replyTo);

        try {
            byte[] data = PlatformUtil.serialize(payload);
            return rtopic.publishAsync(data).thenApply(new Function<Long, Long>() {
                @Override
                public Long apply(Long subscribers) {
                    Preconditions.checkArgument(subscribers > 0, "message(%s) was not received by reply subscriber by channel(%s)", messageId, replyTo);
                    return subscribers;
                }
            }).whenComplete(new BiConsumer<>() {
                @Override
                public void accept(Long active, Throwable err) {
                    MdcUtil.propagate(mdc);
                    try {
                        if (Objects.isNull(err)) {
                            log.debug("OUT ::: ({}:{}:s-{}) has been accepted by redis (topic:{}, subscribers:{}, took:{}, size:{})",
                                    typeUrl,
                                    messageId,
                                    status,
                                    replyTo,
                                    active,
                                    System.currentTimeMillis() - now,
                                    FileUtils.byteCountToDisplaySize(data.length));
                        }
                    } finally {
                        MDC.clear();
                    }
                }
            });
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    @Override
    public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC

        List<NotificationWrapperFacade> notifications = outcome.getNotifications();
        if (log.isTraceEnabled()) {
            List<String> types = notifications.stream().map(NotificationWrapperFacade::body).map(Any::getTypeUrl).collect(Collectors.toList());
            log.atLevel(props.isDevMode() ? Level.DEBUG : Level.TRACE).log("about to post {} to {} ...", types, topic.name());
        }

        RTopic rtopic = redisson.getTopic(topic.name().toString());
        long now = System.currentTimeMillis();

        CompletableFuture<?>[] list = new CompletableFuture<?>[notifications.size()];
        for (int i = 0; i < notifications.size(); i++) {
            NotificationWrapperFacade notify = notifications.get(i);
            String typeUrl = notify.body().getTypeUrl();
            CloudEventWithRoutingKey payload = new CloudEventWithRoutingKey(now, outcome.getKey(), notify);

            try {
                byte[] data = PlatformUtil.serialize(payload);
                list[i] = rtopic.publishAsync(data).whenComplete(new BiConsumer<>() {
                    @Override
                    public void accept(Long active, Throwable err) {
                        MdcUtil.propagate(mdc);
                        try {
                            if (Objects.isNull(err)) {
                                log.debug("OUT ::: ({}) has been accepted by redis (topic:{}, subscribers:{}, took:{}, size:{})",
                                        typeUrl,
                                        topic.name(),
                                        active,
                                        System.currentTimeMillis() - now,
                                        FileUtils.byteCountToDisplaySize(data.length));
                            } else {
                                log.error(err.getMessage(), err);
                            }
                        } finally {
                            MDC.clear();
                        }
                    }
                }).toCompletableFuture();
            } catch (IOException err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
        return CompletableFuture.allOf(list);
    }
    @Override
    public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC

        List<Any> eventStream = outcome.getEventStream();
        if (log.isTraceEnabled()) {
            var types = eventStream.stream().map(Any::getTypeUrl).collect(Collectors.toList());
            log.atLevel(props.isDevMode() ? Level.DEBUG : Level.TRACE).log("about to post {} to {} ...", types, topic.name());
        }

        RTopic rtopic = redisson.getTopic(topic.name().toString());
        long now = System.currentTimeMillis();

        CompletableFuture<?>[] list = new CompletableFuture<?>[eventStream.size()];
        for (int i = 0; i < eventStream.size(); i++) {
            Any wrapper = eventStream.get(i);
            String typeUrl = wrapper.getTypeUrl();
            AnyMessageWithRoutingKey payload = new AnyMessageWithRoutingKey(now, outcome.getKey(), wrapper);

            try {
                byte[] data = PlatformUtil.serialize(payload);
                list[i] = rtopic.publishAsync(data).whenComplete(new BiConsumer<>() {
                    @Override
                    public void accept(Long active, Throwable err) {
                        MdcUtil.propagate(mdc);
                        try {
                            if (Objects.isNull(err)) {
                                log.debug("OUT ::: ({}) has been accepted by redis (topic:{}, subscribers: {}, took: {}, size: {})",
                                        typeUrl,
                                        topic.name(),
                                        active,
                                        System.currentTimeMillis() - now,
                                        FileUtils.byteCountToDisplaySize(data.length));
                            } else {
                                log.error(err.getMessage(), err);
                            }
                        } finally {
                            MDC.clear();
                        }
                    }
                }).toCompletableFuture();
            } catch (IOException err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
        return CompletableFuture.allOf(list);
    }
}
