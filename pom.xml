<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.turbospaces</groupId>
        <artifactId>turbospaces-parent-pom</artifactId>
        <version>11</version>
    </parent>
    <groupId>com.turbospaces.boot</groupId>
    <artifactId>bootstrap-parent</artifactId>
    <version>2.0.68-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>${project.groupId} ::: ${project.artifactId}</name>
    <description>turbospaces 'bootstrap' library with a various plugins and shared tools</description>
    <url>https://bitbucket.org/andrey_borisov/turbospaces-boot</url>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>
    <distributionManagement>
        <snapshotRepository>
            <id>artifact-registry</id>
            <url>artifactregistry://europe-maven.pkg.dev/patrianna-dev/nexus</url>
        </snapshotRepository>
        <repository>
            <id>artifact-registry</id>
            <url>artifactregistry://europe-maven.pkg.dev/patrianna-dev/nexus</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>artifact-registry-mirror</id>
            <url>artifactregistry://europe-west1-maven.pkg.dev/patrianna-prod/maven-mirror</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <scm>
        <connection>${scm.connection}</connection>
        <developerConnection>${scm.connection}</developerConnection>
        <url>${scm.url}/tree/master</url>
        <tag>v2.0.32</tag>
    </scm>
    <developers>
        <developer>
            <id>andrey</id>
            <name>Andrey Borisov</name>
            <email><EMAIL></email>
            <organization>turbospaces</organization>
            <roles>
                <role>developer</role>
            </roles>
            <timezone>+2</timezone>
        </developer>
    </developers>
    <properties>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.compiler.source>21</maven.compiler.source>
        <jvm.unsafe.options>
            <![CDATA[
                            -Djava.security.manager=allow
                            -XX:+AllowRedefinitionToAddDeleteMethods
                           --add-opens=java.base/java.lang=ALL-UNNAMED
                           --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
                           --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
                           --add-opens=java.base/java.io=ALL-UNNAMED
                           --add-opens=java.base/java.net=ALL-UNNAMED
                           --add-opens=java.base/java.nio=ALL-UNNAMED
                           --add-opens=java.base/java.util=ALL-UNNAMED
                           --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
                           --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
                           --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
                           --add-opens=java.base/sun.nio.cs=ALL-UNNAMED
                           --add-opens=java.base/sun.security.action=ALL-UNNAMED
                           --add-opens=java.base/sun.util.calendar=ALL-UNNAMED
                           --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED]]>
        </jvm.unsafe.options>
        <coverage.exclusions>
            <![CDATA[
                           **/test/**,
                           **/di/**,
                           **/model/**,
                           **/config/**,
                           **/configuration/**,
                           **/*DiModule*,
                           **/*Bean*,
                           **/*Configurer*,
                           **/*Autoconfigurator*,
                           **/*Properties*,
                           /generated-sources]]>
        </coverage.exclusions>
        <!-- -->
        <!-- SCM -->
        <!-- -->
        <scm.provider>git</scm.provider>
        <scm.url>*****************:turbospaces/turbospaces-boot</scm.url>
        <scm.developerConnection>scm:${scm.provider}:${scm.url}.${scm.provider}</scm.developerConnection>
        <!-- -->
        <!-- LOGGING -->
        <!-- -->
        <slf4j.version>2.0.17</slf4j.version>
        <logback.version>1.5.18</logback.version>
        <log4j.version>2.25.1</log4j.version>
        <jboss.logging.version>3.6.1.Final</jboss.logging.version>
        <!-- -->
        <!-- javax -->
        <!-- -->
        <jakarta.inject.version>2.0.1</jakarta.inject.version>
        <jakarta.annotation-api.version>3.0.0</jakarta.annotation-api.version>
        <jakarta.validation-api.version>3.1.1</jakarta.validation-api.version>
        <jakarta.ws.rs-api.version>3.1.0</jakarta.ws.rs-api.version>
        <jakarta.servlet-api.version>5.0.0</jakarta.servlet-api.version>
        <jakarta.mail-api.version>2.1.3</jakarta.mail-api.version>
        <jakarta.el-api.version>6.0.1</jakarta.el-api.version>
        <!-- -->
        <!-- CORE -->
        <!-- -->
        <asm.version>9.8</asm.version>
        <spring.cloud.version>1.2.9.RELEASE</spring.cloud.version>
        <jctools.version>4.0.5</jctools.version>
        <guava.version>33.4.8-jre</guava.version>
        <antlr4.version>4.13.2</antlr4.version>
        <listenablefuture.version>9999.0-empty-to-avoid-conflict-with-guava</listenablefuture.version>
        <jsr305.version>3.0.2</jsr305.version>
        <vavr.version>0.10.7</vavr.version>
        <querydsl.version>5.1.0</querydsl.version>
        <eclipse.collections.version>13.0.0</eclipse.collections.version>
        <okio.version>3.15.0</okio.version>
        <okhttp.version>5.1.0</okhttp.version>
        <netty.version>4.1.123.Final</netty.version>
        <micrometer.version>1.15.2</micrometer.version>
        <reactor.version>2024.0.8</reactor.version>
        <blockhound.version>1.0.13.RELEASE</blockhound.version>
        <jaxb.version>4.0.5</jaxb.version>
        <jackson2.version>2.19.2</jackson2.version>
        <ebean.version>14.11.0</ebean.version>
        <spring.boot.version>3.5.4</spring.boot.version>
        <spring.version>6.2.9</spring.version>
        <spring.kafka.version>3.3.8</spring.kafka.version>
        <spring.integration.version>6.5.1</spring.integration.version>
        <resteasy.version>6.2.12.Final</resteasy.version>
        <resteasy.spring.version>3.2.0.Final</resteasy.spring.version>
        <jetty.version>11.0.25</jetty.version>
        <debezium.version>3.1.3.Final</debezium.version>
        <temporal.version>1.30.1</temporal.version>
        <resilience4j.version>2.3.0</resilience4j.version>
        <protobuf.version>3.25.8</protobuf.version>
        <grpc.version>1.73.0</grpc.version>
        <spring.gcp.version>7.0.0</spring.gcp.version>
        <google.libraries.version>26.64.0</google.libraries.version>
        <google.maps.version>2.2.0</google.maps.version>
        <google.auth.version>1.37.0</google.auth.version>
        <google.oauth.client.version>1.39.0</google.oauth.client.version>
        <google.tink.version>1.18.0</google.tink.version>
        <google.oauth2.version>v2-rev20200213-2.0.0</google.oauth2.version>
        <google.kafka.auth.version>1.0.6</google.kafka.auth.version>
        <zookeeper.version>3.9.3</zookeeper.version>
        <kafka.version>3.9.1</kafka.version>
        <pekko.version>1.1.3</pekko.version>
        <cloudevents.version>4.0.1</cloudevents.version>
        <cloudevents.pubsub.version>1.2</cloudevents.pubsub.version>
        <archaius.version>2.8.2</archaius.version>
        <jjwt.version>0.11.5</jjwt.version>
        <clickhouse.version>0.8.6</clickhouse.version>
        <clickhouse.jdbc.version>${clickhouse.version}</clickhouse.jdbc.version>
        <swagger.version>2.2.31</swagger.version>
        <swagger.parser.version>2.1.27</swagger.parser.version>
        <obfuscator.core.version>1.5</obfuscator.core.version>
        <obfuscator.http.version>1.1</obfuscator.http.version>
        <obfuscator.annotations.version>2.0</obfuscator.annotations.version>
        <jackson2.obfuscator.version>1.2.1</jackson2.obfuscator.version>
        <hikaricp.version>6.3.2</hikaricp.version>
        <sslcontext.kickstart.version>9.1.0</sslcontext.kickstart.version>
        <postgresql.jdbc.version>42.7.7</postgresql.jdbc.version>
        <flyway.version>11.10.3</flyway.version>
        <flyway.postgresql.version>${flyway.version}</flyway.postgresql.version>
        <flyway.spanner.version>${flyway.version}</flyway.spanner.version>
        <flyway.clickhouse.version>10.22.0</flyway.clickhouse.version>
        <dropwizard.metrics.version>4.2.33</dropwizard.metrics.version>
        <commons-lang3.version>3.18.0</commons-lang3.version>
        <commons-collections4.version>4.5.0</commons-collections4.version>
        <commons-net.version>3.11.1</commons-net.version>
        <commons-io.version>2.20.0</commons-io.version>
        <commons-text.version>1.13.1</commons-text.version>
        <commons-beanutils.version>1.11.0</commons-beanutils.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-codec.version>1.19.0</commons-codec.version>
        <commons-math3.version>3.6.1</commons-math3.version>
        <commons-jexl3.version>3.2</commons-jexl3.version>
        <commons-pool2.version>2.12.1</commons-pool2.version>
        <commons-csv.version>1.14.0</commons-csv.version>
        <commons-rng.version>1.6</commons-rng.version>
        <maxmind.sdk.version>4.3.0</maxmind.sdk.version>
        <httpcore.version>4.4.16</httpcore.version>
        <httpclient.version>4.5.14</httpclient.version>
        <httpasyncclient.version>4.1.5</httpasyncclient.version>
        <httpcore5.version>5.3.4</httpcore5.version>
        <httpclient5.version>5.5</httpclient5.version>
        <jgit.version>6.10.1.202505221210-r</jgit.version>
        <threeten.version>1.7.1</threeten.version>
        <hibernate.validation.version>9.0.1.Final</hibernate.validation.version>
        <expressly.version>6.0.0</expressly.version>
        <aspectjweaver.version>1.9.24</aspectjweaver.version>
        <jedis.version>6.0.0</jedis.version>
        <xmemcached.version>2.4.8</xmemcached.version>
        <jnats.version>2.21.4</jnats.version>
        <rulebook.version>0.12</rulebook.version>
        <redisson.version>3.50.0</redisson.version>
        <classmate.version>1.7.0</classmate.version>
        <jgroups.version>5.3.7.Final</jgroups.version>
        <quartz.version>2.5.0</quartz.version>
        <opentracing.version>0.33.0</opentracing.version>
        <jaeger.version>1.8.1</jaeger.version>
        <sentry.version>1.7.30</sentry.version>
        <bouncycastle.version>1.81</bouncycastle.version>
        <curator.version>5.9.0</curator.version>
        <web3j.version>4.14.0</web3j.version>
        <jmail.version>2.0.2</jmail.version>
        <angus.mail.version>2.0.3</angus.mail.version>
        <lombok.version>1.18.38</lombok.version>
        <errorprone.version>2.38.0</errorprone.version>
        <aether.version>1.1.0</aether.version>
        <nv.i18n.version>1.29</nv.i18n.version>
        <uuid.version>5.1.0</uuid.version>
        <calcite.version>1.40.0</calcite.version>
        <scala.config.version>1.4.3</scala.config.version>
        <spark.version>4.0.0</spark.version>
        <scala.binary.version>2.13</scala.binary.version>
        <scala.version>${scala.binary.version}.16</scala.version>
        <msgpack.version>0.9.9</msgpack.version>
        <zeromq.version>0.6.0</zeromq.version>
        <disruptor.version>3.4.4</disruptor.version>
        <tally-core.version>0.14.2</tally-core.version>
        <!-- -->
        <!-- TESTING -->
        <!-- -->
        <jacoco.version>0.8.13</jacoco.version>
        <junit.version>5.13.4</junit.version>
        <mockito.version>5.18.0</mockito.version>
        <h2.version>2.3.232</h2.version>
        <testcontainers.version>1.21.3</testcontainers.version>
        <awaitility.version>4.3.0</awaitility.version>
        <mockserver.version>5.15.0</mockserver.version>
        <cucumber.version>7.26.0</cucumber.version>
        <allure.version>2.29.1</allure.version>
        <assertj.version>3.27.3</assertj.version>
        <logcaptor.version>2.11.0</logcaptor.version>
        <equalsverifier.version>4.0.5</equalsverifier.version>
        <system-stubs.version>2.1.8</system-stubs.version>
        <greenmail.version>2.1.4</greenmail.version>
        <toxiproxy.version>2.1.7</toxiproxy.version>
        <!-- -->
        <!-- extensions -->
        <!-- -->
        <artifactregistry.version>2.2.5</artifactregistry.version>
        <!-- -->
        <!-- plugins -->
        <!-- -->
        <version.protobuf.plugin>3.11.4</version.protobuf.plugin>
        <version.license.plugin>2.5.0</version.license.plugin>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>jakarta.inject</groupId>
                <artifactId>jakarta.inject-api</artifactId>
                <version>${jakarta.inject.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${jakarta.ws.rs-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.el</groupId>
                <artifactId>jakarta.el-api</artifactId>
                <version>${jakarta.el-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.mail</groupId>
                <artifactId>jakarta.mail-api</artifactId>
                <version>${jakarta.mail-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-rng-bom</artifactId>
                <version>${commons-rng.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-bom</artifactId>
                <version>${mockito.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava-bom</artifactId>
                <version>${guava.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio-bom</artifactId>
                <version>${okio.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-bom</artifactId>
                <version>${micrometer.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-bom</artifactId>
                <version>${okhttp.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-bom</artifactId>
                <version>${reactor.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-bom</artifactId>
                <version>${jaxb.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.ebean</groupId>
                <artifactId>ebean-bom</artifactId>
                <version>${ebean.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-bom</artifactId>
                <version>${spring.integration.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-bom</artifactId>
                <version>${protobuf.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>${grpc.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>spring-cloud-gcp-dependencies</artifactId>
                <version>${spring.gcp.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-bom</artifactId>
                <version>${resteasy.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-bom</artifactId>
                <version>${jetty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-bom</artifactId>
                <version>${resilience4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-bom_${scala.binary.version}</artifactId>
                <version>${pekko.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.cloudevents</groupId>
                <artifactId>cloudevents-bom</artifactId>
                <version>${cloudevents.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>libraries-bom</artifactId>
                <version>${google.libraries.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-bom</artifactId>
                <version>${google.auth.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.oauth-client</groupId>
                <artifactId>google-oauth-client-bom</artifactId>
                <version>${google.oauth.client.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.github.hakky54</groupId>
                <artifactId>logcaptor</artifactId>
                <version>${logcaptor.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.cucumber</groupId>
                <artifactId>cucumber-bom</artifactId>
                <version>${cucumber.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.qameta.allure</groupId>
                <artifactId>allure-bom</artifactId>
                <version>${allure.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>eu.rekawek.toxiproxy</groupId>
                <artifactId>toxiproxy-java</artifactId>
                <version>${toxiproxy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-bom</artifactId>
                <version>${assertj.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-bom</artifactId>
                <version>${debezium.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.github.hakky54</groupId>
                <artifactId>sslcontext-kickstart-bom</artifactId>
                <version>${sslcontext.kickstart.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-bom</artifactId>
                <version>${temporal.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcomponents-asyncclient</artifactId>
                <version>${httpasyncclient.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-bom</artifactId>
                <version>${querydsl.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- -->
            <!-- common -->
            <!-- -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-commons</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-tree</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-analysis</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-jvm</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr</artifactId>
                <version>${vavr.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr-match</artifactId>
                <version>${vavr.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr-match-processor</artifactId>
                <version>${vavr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>listenablefuture</artifactId>
                <version>${listenablefuture.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.collections</groupId>
                <artifactId>eclipse-collections</artifactId>
                <version>${eclipse.collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.collections</groupId>
                <artifactId>eclipse-collections-api</artifactId>
                <version>${eclipse.collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.collections</groupId>
                <artifactId>eclipse-collections-forkjoin</artifactId>
                <version>${eclipse.collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.collections</groupId>
                <artifactId>eclipse-collections-testutils</artifactId>
                <version>${eclipse.collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit</artifactId>
                <version>${jgit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit.http.server</artifactId>
                <version>${jgit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>${classmate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-core</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-spring-service-connector</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-localconfig-connector</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jctools</groupId>
                <artifactId>jctools-core</artifactId>
                <version>${jctools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>${antlr4.version}</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor.tools</groupId>
                <artifactId>blockhound</artifactId>
                <version>${blockhound.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>${commons-math3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-jexl3</artifactId>
                <version>${commons-jexl3.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.uuid</groupId>
                <artifactId>java-uuid-generator</artifactId>
                <version>${uuid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.msgpack</groupId>
                <artifactId>msgpack-core</artifactId>
                <version>${msgpack.version}</version>
            </dependency>
            <dependency>
                <groupId>org.zeromq</groupId>
                <artifactId>jeromq</artifactId>
                <version>${zeromq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-core</artifactId>
                <version>${calcite.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-linq4j</artifactId>
                <version>${calcite.version}</version>
            </dependency>
            <dependency>
                <groupId>org.threeten</groupId>
                <artifactId>threetenbp</artifactId>
                <version>${threeten.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons-csv.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>${maxmind.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.xerial.snappy</groupId>
                        <artifactId>snappy-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper-jute</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-metadata</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-server</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-transaction-coordinator</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-group-coordinator</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-group-coordinator-api</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-server-common</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-server-common</artifactId>
                <version>${kafka.version}</version>
                <classifier>test</classifier>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-streams-test-utils</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_${scala.binary.version}</artifactId>
                <version>${kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_${scala.binary.version}</artifactId>
                <version>${kafka.version}</version>
                <classifier>test</classifier>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-api</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-runtime</artifactId>
                <version>${kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-json</artifactId>
                <version>${kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-file</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-transforms</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.archaius</groupId>
                <artifactId>archaius2-api</artifactId>
                <version>${archaius.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.inject</groupId>
                        <artifactId>javax.inject</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.netflix.archaius</groupId>
                <artifactId>archaius2-core</artifactId>
                <version>${archaius.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-jaxrs2-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-jaxrs2-servlet-initializer-v2-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.parser.v3</groupId>
                <artifactId>swagger-parser</artifactId>
                <version>${swagger.parser.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>${httpasyncclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcore.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.core5</groupId>
                <artifactId>httpcore5</artifactId>
                <version>${httpcore5.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.robtimus</groupId>
                <artifactId>obfuscation-annotations</artifactId>
                <version>${obfuscator.annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.robtimus</groupId>
                <artifactId>obfuscation-core</artifactId>
                <version>${obfuscator.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.robtimus</groupId>
                <artifactId>obfuscation-http</artifactId>
                <version>${obfuscator.http.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.xmemcached</groupId>
                <artifactId>xmemcached</artifactId>
                <version>${xmemcached.version}</version>
            </dependency>
            <dependency>
                <groupId>io.nats</groupId>
                <artifactId>jnats</artifactId>
                <version>${jnats.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deliveredtechnologies</groupId>
                <artifactId>rulebook-core</artifactId>
                <version>${rulebook.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.reactivex.rxjava3</groupId>
                        <artifactId>rxjava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.bytebuddy</groupId>
                        <artifactId>byte-buddy</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.robtimus</groupId>
                <artifactId>obfuscation-jackson-databind</artifactId>
                <version>${jackson2.obfuscator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>c3p0</artifactId>
                        <groupId>com.mchange</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>HikariCP-java6</artifactId>
                        <groupId>com.zaxxer</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP-java7</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-core-spi</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy.spring</groupId>
                <artifactId>resteasy-spring</artifactId>
                <version>${resteasy.spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jboss.resteasy</groupId>
                        <artifactId>resteasy-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jakarta.inject</groupId>
                        <artifactId>jakarta.inject-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.maps</groupId>
                <artifactId>google-maps-services</artifactId>
                <version>${google.maps.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.cloud.hosted.kafka</groupId>
                <artifactId>managed-kafka-auth-login-handler</artifactId>
                <version>${google.kafka.auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.crypto.tink</groupId>
                <artifactId>tink</artifactId>
                <version>${google.tink.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-oauth2</artifactId>
                <version>${google.oauth2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${errorprone.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz-jobs</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neovisionaries</groupId>
                <artifactId>nv-i18n</artifactId>
                <version>${nv.i18n.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${jsr305.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-mock</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jaegertracing</groupId>
                <artifactId>jaeger-core</artifactId>
                <version>${jaeger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jaegertracing</groupId>
                <artifactId>jaeger-client</artifactId>
                <version>${jaeger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.annotation</groupId>
                        <artifactId>javax.annotation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.uber.m3</groupId>
                <artifactId>tally-core</artifactId>
                <version>${tally-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry</artifactId>
                <version>${sentry.version}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-logback</artifactId>
                <version>${sentry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jgroups</groupId>
                <artifactId>jgroups</artifactId>
                <version>${jgroups.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpg-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>${scala.config.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.angus</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${angus.mail.version}</version>
            </dependency>
            <dependency>
                <groupId>io.featurehub.cloudevents</groupId>
                <artifactId>cloudevents-google-pubsub</artifactId>
                <version>${cloudevents.pubsub.version}</version>
            </dependency>
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-sdk</artifactId>
                <version>${temporal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-netty-shaded</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-serviceclient</artifactId>
                <version>${temporal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-netty-shaded</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-testing</artifactId>
                <version>${temporal.version}</version>
            </dependency>
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-test-server</artifactId>
                <version>${temporal.version}</version>
            </dependency>
            <!-- -->
            <!-- web3j -->
            <!-- -->
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>core</artifactId>
                <version>${web3j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>utils</artifactId>
                <version>${web3j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>infura</artifactId>
                <version>${web3j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>abi</artifactId>
                <version>${web3j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>crypto</artifactId>
                <version>${web3j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
            <!-- -->
            <!-- metrics -->
            <!-- -->
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-graphite</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-logback</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-httpclient</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jakarta-servlets</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-healthchecks</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jvm</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jmx</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-json</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty12</artifactId>
                <version>${dropwizard.metrics.version}</version>
            </dependency>
            <!-- -->
            <!-- DB -->
            <!-- -->
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>${clickhouse.jdbc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.clickhouse</groupId>
                        <artifactId>clickhouse-http-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.clickhouse</groupId>
                        <artifactId>jdbc-v2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.clickhouse</groupId>
                <artifactId>clickhouse-client</artifactId>
                <version>${clickhouse.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.clickhouse</groupId>
                        <artifactId>clickhouse-http-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.clickhouse</groupId>
                <artifactId>clickhouse-data</artifactId>
                <version>${clickhouse.version}</version>
            </dependency>
            <dependency>
                <groupId>com.clickhouse</groupId>
                <artifactId>client-v2</artifactId>
                <version>${clickhouse.version}</version>
            </dependency>
            <dependency>
                <groupId>com.clickhouse</groupId>
                <artifactId>jdbc-v2</artifactId>
                <version>${clickhouse.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.clickhouse</groupId>
                        <artifactId>clickhouse-http-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-clickhouse</artifactId>
                <version>${flyway.clickhouse.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-postgresql</artifactId>
                <version>${flyway.postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-gcp-spanner</artifactId>
                <version>${flyway.spanner.version}</version>
            </dependency>
            <!-- -->
            <!-- logging -->
            <!-- -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-ext</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${jboss.logging.version}</version>
            </dependency>
            <!-- -->
            <!-- spring boot -->
            <!-- -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-metadata</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.vaadin.external.google</groupId>
                        <artifactId>android-json</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-loader</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-loader-tools</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-properties-migrator</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-neo4j</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-rest</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-solr</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-integration</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jersey</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jetty</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.vaadin.external.google</groupId>
                        <artifactId>android-json</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-tomcat</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-reactor-netty</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-undertow</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web-services</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring.kafka.version}</version>
            </dependency>
            <!-- -->
            <!-- aether -->
            <!-- -->
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-api</artifactId>
                <version>${aether.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-spi</artifactId>
                <version>${aether.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-util</artifactId>
                <version>${aether.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-impl</artifactId>
                <version>${aether.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-connector-basic</artifactId>
                <version>${aether.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-transport-file</artifactId>
                <version>${aether.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.aether</groupId>
                <artifactId>aether-transport-http</artifactId>
                <version>${aether.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- -->
            <!-- spark -->
            <!-- -->
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-core_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j2-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-mllib_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-mllib-local_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql-api_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.spark</groupId>
                        <artifactId>spark-connect-shims_${scala.binary.version}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j2-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-catalyst_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-unsafe_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-common-utils_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j2-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- -->
            <!-- testing -->
            <!-- -->
            <dependency>
                <groupId>com.sanctionco.jmail</groupId>
                <artifactId>jmail</artifactId>
                <version>${jmail.version}</version>
            </dependency>
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>${awaitility.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka-test</artifactId>
                <version>${spring.kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-core</artifactId>
                <version>${mockserver.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-netty</artifactId>
                <version>${mockserver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-client-java</artifactId>
                <version>${mockserver.version}</version>
            </dependency>
            <dependency>
                <groupId>nl.jqno.equalsverifier</groupId>
                <artifactId>equalsverifier</artifactId>
                <version>${equalsverifier.version}</version>
            </dependency>
            <dependency>
                <groupId>uk.org.webcompere</groupId>
                <artifactId>system-stubs-core</artifactId>
                <version>${system-stubs.version}</version>
            </dependency>
            <dependency>
                <groupId>uk.org.webcompere</groupId>
                <artifactId>system-stubs-jupiter</artifactId>
                <version>${system-stubs.version}</version>
            </dependency>
            <dependency>
                <groupId>com.icegreen</groupId>
                <artifactId>greenmail</artifactId>
                <version>${greenmail.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <dependencies>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>${asm.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <release>${maven.compiler.target}</release>
                        <compilerArgs>
                            <arg>-XDcompilePolicy=simple</arg>
                            <arg>--should-stop=ifError=FLOW</arg>
                            <arg>-Xplugin:ErrorProne</arg>
                        </compilerArgs>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>com.google.errorprone</groupId>
                                <artifactId>error_prone_core</artifactId>
                                <version>${errorprone.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>io.ebean</groupId>
                    <artifactId>ebean-maven-plugin</artifactId>
                    <version>${ebean.version}</version>
                    <extensions>true</extensions>
                </plugin>
                <plugin>
                    <groupId>com.github.os72</groupId>
                    <artifactId>protoc-jar-maven-plugin</artifactId>
                    <version>${version.protobuf.plugin}</version>
                    <executions>
                        <execution>
                            <id>generate-proto-stubs</id>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <addProtoSources>all</addProtoSources>
                                <includeMavenTypes>direct</includeMavenTypes>
                                <includeDirectories>src/main/protobuf</includeDirectories>
                                <protocArtifact>com.google.protobuf:protoc:${protobuf.version}</protocArtifact>
                                <outputTargets>
                                    <outputTarget>
                                        <type>java</type>
                                        <outputDirectory>${project.build.directory}/generated-sources/java</outputDirectory>
                                    </outputTarget>
                                </outputTargets>
                            </configuration>
                        </execution>
                        <execution>
                            <id>generate-test-proto-stubs</id>
                            <phase>generate-test-sources</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <addProtoSources>all</addProtoSources>
                                <includeMavenTypes>direct</includeMavenTypes>
                                <inputDirectories>src/test/protobuf</inputDirectories>
                                <protocArtifact>com.google.protobuf:protoc:${protobuf.version}</protocArtifact>
                                <outputTargets>
                                    <outputTarget>
                                        <type>java</type>
                                        <outputDirectory>${project.build.directory}/generated-test-sources/java</outputDirectory>
                                    </outputTarget>
                                    <outputTarget>
                                        <type>grpc-java</type>
                                        <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}</pluginArtifact>
                                        <outputDirectory>${project.build.directory}/generated-test-sources/java</outputDirectory>
                                    </outputTarget>
                                </outputTargets>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${version.license.plugin}</version>
                    <executions>
                        <execution>
                            <id>download-licenses</id>
                            <goals>
                                <goal>download-licenses</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <id>prepare-agent</id>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <propertyName>jacoco.agent.argLine</propertyName>
                    <excludes>
                        <exclude>${coverage.exclusions}</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>${jacoco.agent.argLine} ${jvm.unsafe.options}</argLine>
                    <systemPropertyVariables combine.children="append">
                        <java.awt.headless>true</java.awt.headless>
                        <java.net.preferIPv4Stack>true</java.net.preferIPv4Stack>
                        <jgroups.tcp.address>NON_LOOPBACK</jgroups.tcp.address>
                        <org.springframework.boot.logging.LoggingSystem>none</org.springframework.boot.logging.LoggingSystem>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
        </plugins>
        <extensions>
            <extension>
                <groupId>com.google.cloud.artifactregistry</groupId>
                <artifactId>artifactregistry-maven-wagon</artifactId>
                <version>${artifactregistry.version}</version>
            </extension>
        </extensions>
    </build>
    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <modules>
                <module>bootstrap-api-core</module>
                <module>bootstrap-protobuf-core</module>
                <module>bootstrap-core</module>
                <module>bootstrap-netty-core</module>
                <module>bootstrap-validation-core</module>
                <module>bootstrap-kafka-core</module>
                <module>bootstrap-jdbc-core</module>
                <module>bootstrap-ebean-migration</module>
                <module>bootstrap-ebean-l2cache</module>
                <module>bootstrap-resteasy-core</module>
                <module>bootstrap-httpclient-core</module>
                <module>bootstrap-jetty-core</module>
                <module>bootstrap-web3j-core</module>
                <module>bootstrap-jgroups-core</module>
                <module>bootstrap-gcp-core</module>
                <module>bootstrap-gcp-pubsub-core</module>
                <module>bootstrap-debezium-core</module>
                <module>bootstrap-quartz-core</module>
                <module>bootstrap-locust-core</module>
                <module>bootstrap-logging</module>
                <module>bootstrap-spark-core</module>
                <module>bootstrap-grpc-core</module>
                <module>bootstrap-aether-core</module>
                <module>bootstrap-temporal-core</module>
                <module>bootstrap-geoip</module>
                <module>bootstrap-spanner-core</module>
                <module>bootstrap-zookeeper-core</module>
                <module>bootstrap-clickhouse-core</module>
                <module>bootstrap-redis-core</module>
                <module>bootstrap-memcached-core</module>
                <module>bootstrap-nats-core</module>
                <module>bootstrap-rules-core</module>
                <module>bootstrap-test</module>
                <module>bootstrap-bom</module>
            </modules>
        </profile>
    </profiles>
</project>
