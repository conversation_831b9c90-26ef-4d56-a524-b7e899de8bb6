syntax = "proto3";
package com.turbospaces.api.v1;

import "api/v1/obfuscation.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message GetSystemInfoRequest {
  string username = 1 [(.api.v1.sensitive_mode).sensitive = true];
  bool preserveReply = 2;
}
message GetSystemInfoResponse {
  string remoteIp = 1 [(.api.v1.sensitive_mode).sensitive = true];
  string release = 2;
  bool devMode = 3;
  int64 now = 4;
  bool replyPreserved = 5;
}
