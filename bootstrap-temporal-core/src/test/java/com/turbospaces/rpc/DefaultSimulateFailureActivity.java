package com.turbospaces.rpc;

import api.v1.ApplicationException;
import api.v1.MockResponseStatusFacade;
import lombok.RequiredArgsConstructor;

import java.net.SocketTimeoutException;

@RequiredArgsConstructor
public class DefaultSimulateFailureActivity implements SimulateFailureActivity {
    public static final String SIMULATE_ACTIVITY_APPLICATION_ERROR = "simulateActivityApplicationError";
    public static final String SIMULATE_ACTIVITY_NETWORK_ERROR = "simulateActivityNetworkError";

    @Override
    public void simulateIfApplicable(String userName) throws Throwable {
        if (SIMULATE_ACTIVITY_APPLICATION_ERROR.equals(userName)) {
            throw ApplicationException.of("Simulated activity application error", MockResponseStatusFacade.Code.ERR_BAD_REQUEST);
        }
        if (SIMULATE_ACTIVITY_NETWORK_ERROR.equals(userName)) {
            var cause = new RuntimeException(new SocketTimeoutException());
            throw ApplicationException.rethrowing("Simulated activity application error", cause, MockResponseStatusFacade.Code.ERR_TIMEOUT);
        }
    }
}
