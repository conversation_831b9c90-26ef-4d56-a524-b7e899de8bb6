package com.turbospaces.rpc;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;

import com.turbospaces.api.v1.GetSystemInfoRequest;
import com.turbospaces.api.v1.GetSystemInfoResponse;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.RequestHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.temporal.AbstractSequentialWorkflow;

import api.v1.EnhancedApplicationException;
import api.v1.MockResponseStatusFacade;
import io.temporal.activity.LocalActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.temporal.workflow.Async;
import io.temporal.workflow.Functions.Func;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import io.temporal.workflow.WorkflowInfo;
import jakarta.inject.Inject;

public class DefaultAdminWorkflow extends AbstractSequentialWorkflow<GetSystemInfoRequest, GetSystemInfoResponse.Builder> implements AdminWorkflow {
    public static final String WAIT = "wait";
    public static final String SIMULATE_UNEXPECTED_ERROR = "simulateError";
    public static final String SIMULATE_APPLICATION_ERROR = "simulateApplicationError";
    private final GetRemoteIpActivity getRemoteIpActivity = Workflow.newLocalActivityStub(
            GetRemoteIpActivity.class,
            LocalActivityOptions.newBuilder()
                    .setStartToCloseTimeout(Duration.ofSeconds(15))
                    .setRetryOptions(RetryOptions.newBuilder().setMaximumAttempts(1).build())
                    .build());
    private final GetReleaseVersionActivity getReleaseVersionActivity = Workflow.newLocalActivityStub(
            GetReleaseVersionActivity.class,
            LocalActivityOptions.newBuilder()
                    .setStartToCloseTimeout(Duration.ofSeconds(15))
                    .setRetryOptions(RetryOptions.newBuilder().setMaximumAttempts(1).build())
                    .build());
    private final SimulateFailureActivity simulateFailureActivity = Workflow.newLocalActivityStub(
            SimulateFailureActivity.class,
            LocalActivityOptions.newBuilder()
                    .setStartToCloseTimeout(Duration.ofSeconds(15))
                    .setRetryOptions(RetryOptions.newBuilder().setMaximumAttempts(1).build())
                    .build());

    @Inject
    public DefaultAdminWorkflow(ApplicationProperties props) {
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public GetSystemInfoResponse getInfo(GetSystemInfoRequest request) throws Throwable {
        return apply(
                request,
                GetSystemInfoResponse.newBuilder(),
                new RequestHandler<GetSystemInfoRequest, GetSystemInfoResponse.Builder>() {
                    @Override
                    public boolean actorIsRequired() {
                        return false;
                    }
                    @Override
                    public Class<GetSystemInfoRequest> requestType() {
                        return GetSystemInfoRequest.class;
                    }
                    @Override
                    public boolean isImmediateAcknowledge() {
                        return false;
                    }
                    @Override
                    public void accept(TransactionalRequest<GetSystemInfoRequest, GetSystemInfoResponse.Builder> cmd) throws Throwable {
                        if (request.getPreserveReply()) {
                            cmd.preserveReply();
                            cmd.reply().setReplyPreserved(true);
                        }
                        if (request.getUsername().startsWith(WAIT)) {
                            Workflow.sleep(Long.parseLong(request.getUsername().replace(WAIT, "")));
                            return;
                        }
                        if (request.getUsername().equals(SIMULATE_UNEXPECTED_ERROR)) {
                            throw new RuntimeException("Simulated unexpected error");
                        }
                        if (request.getUsername().equals(SIMULATE_APPLICATION_ERROR)) {
                            throw EnhancedApplicationException.of("Simulated application error",
                                    MockResponseStatusFacade.Code.ERR_BAD_REQUEST,
                                    MockResponseStatusFacade.Reason.SPECIFIED);
                        }
                        cmd.reply().setDevMode(true);
                        simulateFailureActivity.simulateIfApplicable(request.getUsername());
                        Promise<String> f1 = Async.function(new Func<String>() {
                            @Override
                            public String apply() {
                                logger.info("about to retrieve remoteIp ...");
                                return getRemoteIpActivity.userInfo().getRemoteIp().value();
                            }
                        });
                        Promise<String> f2 = Async.function(new Func<String>() {
                            @Override
                            public String apply() {
                                logger.info("about to retrieve release version ...");
                                return getReleaseVersionActivity.releaseVersion();
                            }
                        });

                        Promise.allOf(f1, f2).get();

                        GetSystemInfoResponse.Builder resp = cmd.reply();
                        resp.setDevMode(props.isDevMode());
                        if (Objects.isNull(f1.getFailure())) {
                            resp.setRemoteIp(f1.get());
                        }
                        if (Objects.isNull(f2.getFailure())) {
                            resp.setRelease(f2.get());
                        }

                    }
                }).build();
    }

    @Override
    public Optional<String> getSequentialExecutionKey(WorkflowInfo info, SearchAttributes attributes) {
        return Optional.ofNullable(attributes.get(SearchAttributeKey.forText(MdcTags.MDC_ROUTING_KEY)));
    }
}
