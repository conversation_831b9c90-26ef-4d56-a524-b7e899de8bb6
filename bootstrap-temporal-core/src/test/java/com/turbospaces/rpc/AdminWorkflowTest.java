package com.turbospaces.rpc;

import api.v1.MockApiFactory;
import api.v1.MockResponseStatusFacade;
import api.v1.ObfuscatePrinter;
import com.google.common.base.Suppliers;
import com.google.common.util.concurrent.FutureCallback;
import com.turbospaces.api.v1.GetSystemInfoRequest;
import com.turbospaces.api.v1.GetSystemInfoResponse;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.temporal.ReloadableWorkflowClient;
import com.turbospaces.temporal.WrappedWorkflowPost;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;
import io.temporal.client.WorkflowClient;
import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.vavr.CheckedFunction2;
import lombok.extern.slf4j.Slf4j;
import nl.altindag.log.LogCaptor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;
import org.testcontainers.utility.DockerImageName;

import java.time.Duration;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class AdminWorkflowTest {
    @Test
    public void works() throws Throwable {
        SimpleBootstrap bootstrap = null;
        try (var redis = new GenericContainer<>(DockerImageName.parse("redis:7"))) {
            redis.withNetworkAliases("redis");
            redis.addExposedPort(6379);
            redis.start();
            int port = redis.getMappedPort(6379);

            ApplicationConfig cfg = ApplicationConfig.mock();
            cfg.setDefaultProperty("redis.port", port);
            ApplicationProperties props = new ApplicationProperties(cfg.factory());
            bootstrap = new SimpleBootstrap(props, MocktTemporalInternalTestDiModule.class);
            ApplicationContext ctx = bootstrap.run();
            WorkflowClient client = ctx.getBean(WorkflowClient.class);

            var objectMapper = new CommonObjectMapper();
            var apiFactory = new MockApiFactory(props, objectMapper);
            var post = new WrappedWorkflowPost<>(
                    props,
                    AdminWorkflow.class,
                    apiFactory,
                    new CheckedFunction2<AdminWorkflow, GetSystemInfoRequest, GetSystemInfoResponse>() {
                        @Override
                        public GetSystemInfoResponse apply(AdminWorkflow stub, GetSystemInfoRequest req) throws Throwable {
                            return stub.getInfo(req);
                        }
                    }, GetSystemInfoResponse.getDefaultInstance(),
                    Suppliers.ofInstance(Duration.ofSeconds(30)));

            GetSystemInfoRequest req = GetSystemInfoRequest.newBuilder().setUsername(System.getProperty("user.name")).build();
            ApiResponse<GetSystemInfoResponse> apiResponse = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, req, SearchAttributes.newBuilder());
            ApiResponseEntity<GetSystemInfoResponse> entity = apiResponse.get();
            log.info(ObfuscatePrinter.shortDebugString(entity.unpack().toBuilder()));

            Assertions.assertTrue(entity.unpack().getDevMode());
            Assertions.assertEquals(props.externalIp(), entity.unpack().getRemoteIp());

            GetSystemInfoRequest unexpectedError = GetSystemInfoRequest.newBuilder().setUsername(DefaultAdminWorkflow.SIMULATE_UNEXPECTED_ERROR).build();
            apiResponse = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, unexpectedError, SearchAttributes.newBuilder());
            Assertions.assertTrue(apiResponse.get().status().isSystem());

            try (var temporalChannelCaptor = LogCaptor.forClass(MocktTemporalInternalTestDiModule.MockTemporalChannel.class)) {
                try (var logCaptor = LogCaptor.forClass(ReloadableWorkflowClient.class)) {
                    PlainServiceInfo secret = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.TEMPORAL);
                    bootstrap.addUps(new PlainServiceInfo(UPSs.TEMPORAL, secret.getUri() + "?secret=1234" ));
                    Awaitility.await().untilAsserted(() -> Assertions.assertTrue(logCaptor.getLogEvents()
                            .stream()
                            .anyMatch(e -> e.getMessage().contains("Old temporal client has been shutdown."))));
                    Awaitility.await().untilAsserted(() -> Assertions.assertTrue(temporalChannelCaptor.getLogEvents()
                            .stream()
                            .anyMatch(e -> e.getMessage().contains("Old temporal factory has been destroyed."))));
                    client = ctx.getBean(WorkflowClient.class);
                }
            }

            GetSystemInfoRequest apiError = GetSystemInfoRequest.newBuilder().setUsername(DefaultAdminWorkflow.SIMULATE_APPLICATION_ERROR).build();
            apiResponse = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, apiError, SearchAttributes.newBuilder());
            Assertions.assertTrue(apiResponse.get().status().isBadRequest());
            Assertions.assertEquals(MockResponseStatusFacade.Reason.SPECIFIED.getNumber(), apiResponse.get().status().errorReason().getNumber());

            apiError = GetSystemInfoRequest.newBuilder().setUsername(DefaultAdminWorkflow.SIMULATE_APPLICATION_ERROR).setPreserveReply(true).build();
            apiResponse = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, apiError, SearchAttributes.newBuilder());
            Assertions.assertTrue(apiResponse.get().status().isBadRequest());
            Assertions.assertEquals(MockResponseStatusFacade.Reason.SPECIFIED.getNumber(), apiResponse.get().status().errorReason().getNumber());
            Assertions.assertTrue(apiResponse.get().unpack().getReplyPreserved());

            GetSystemInfoRequest activityAplicationError = GetSystemInfoRequest.newBuilder().setUsername(DefaultSimulateFailureActivity.SIMULATE_ACTIVITY_APPLICATION_ERROR).build();
            apiResponse = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, activityAplicationError, SearchAttributes.newBuilder());
            Assertions.assertTrue(apiResponse.get().status().isBadRequest());
            Assertions.assertEquals("Simulated activity application error", apiResponse.get().status().errorText());

            activityAplicationError = GetSystemInfoRequest.newBuilder().setUsername(DefaultSimulateFailureActivity.SIMULATE_ACTIVITY_NETWORK_ERROR).build();
            apiResponse = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, activityAplicationError, SearchAttributes.newBuilder());
            Assertions.assertTrue(apiResponse.get().status().isTimeout());
            Assertions.assertEquals("Simulated activity application error", apiResponse.get().status().errorText());

            var redisCli = ctx.getBean(RedissonClient.class);
            // simulate sequential execution lock
            List<String> orderOfCompletion = new LinkedList<>();
            var resp1 = sendRequest(props, apiFactory, client, orderOfCompletion, "lock_rk", 100);
            Awaitility.await().untilAsserted(() -> Assertions.assertTrue(redisCli.getFairLock("lock_rk").isLocked()));
            var resp2 = sendRequest(props, apiFactory, client, orderOfCompletion, "lock_rk", 1);
            resp1.thenVerifyOk().get();
            resp2.thenVerifyOk().get();
            Assertions.assertEquals(orderOfCompletion, List.of("100", "1"), "Sequential execution should complete in order");

            // simulate not-sequential execution lock
            orderOfCompletion = new LinkedList<>();
            resp1 = sendRequest(props, apiFactory, client, orderOfCompletion, "lock_rk1", 100);
            resp2 = sendRequest(props, apiFactory, client, orderOfCompletion, "lock_rk2", 1);
            resp1.thenVerifyOk().get();
            resp2.thenVerifyOk().get();
            Assertions.assertEquals(orderOfCompletion, List.of("1", "100"), "Non sequential execution should complete in order");

            Assertions.assertEquals(4, bootstrap.meterRegistry().get("temporal_activity_schedule_to_start_latency")
                    .tag("worker_type", "ActivityWorker")
                    .tag("workflow_type", "AdminWorkflow")
                    .tag("namespace", "test-namespace").timer().count());

        } finally {
            if (bootstrap != null) {
                bootstrap.shutdown();
            }
        }
    }

    private static ApiResponse<GetSystemInfoResponse> sendRequest(
            ApplicationProperties props,
            MockApiFactory apiFactory,
            WorkflowClient client,
            List<String> orderOfCompletion,
            String rk,
            long wait
    ) {
        WrappedWorkflowPost<AdminWorkflow, GetSystemInfoRequest, GetSystemInfoResponse> post;
        SearchAttributes.Builder sequentialAttributes = SearchAttributes.newBuilder().set(SearchAttributeKey.forText(MdcTags.MDC_ROUTING_KEY), rk);
        post = new WrappedWorkflowPost<>(
                props,
                AdminWorkflow.class,
                apiFactory,
                new CheckedFunction2<AdminWorkflow, GetSystemInfoRequest, GetSystemInfoResponse>() {
                    @Override
                    public GetSystemInfoResponse apply(AdminWorkflow stub, GetSystemInfoRequest req) throws Throwable {
                        return stub.getInfo(req);
                    }
                }, GetSystemInfoResponse.getDefaultInstance(),
                Suppliers.ofInstance(Duration.ofSeconds(30)));
        GetSystemInfoRequest sequentialReq1 = GetSystemInfoRequest.newBuilder().setUsername(DefaultAdminWorkflow.WAIT + wait).build();
        var resp = post.execute(client, MocktTemporalInternalTestDiModule.DEFAULT_WORKER, sequentialReq1, sequentialAttributes);
        resp.addCallback(new FutureCallback<ApiResponseEntity<GetSystemInfoResponse>>() {
            @Override
            public void onSuccess(ApiResponseEntity<GetSystemInfoResponse> result) {
                orderOfCompletion.add(String.valueOf(wait));
            }

            @Override
            public void onFailure(Throwable t) {

            }
        });
        return resp;
    }
}
