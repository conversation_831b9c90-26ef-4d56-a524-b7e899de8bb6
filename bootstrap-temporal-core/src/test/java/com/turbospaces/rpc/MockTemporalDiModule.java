package com.turbospaces.rpc;

import static org.mockito.Mockito.mock;

import java.util.concurrent.CompletableFuture;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import com.turbospaces.redis.RedisClientFactoryBean;
import com.turbospaces.temporal.WorkflowLockService;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Import(TestTemporalServerDiModule.class)
@Slf4j
public class MockTemporalDiModule {
    @Bean
    public RedisClientFactoryBean redissonClient() {
        return mock(RedisClientFactoryBean.class);
    }

    @Bean
    @Primary
    public WorkflowLockService mockWorkflowLockService() {
        return new WorkflowLockService() {
            @Override
            public CompletableFuture<Boolean> tryLockAsync(String key, long threadId) {
                return CompletableFuture.completedFuture(true);
            }
            @Override
            public CompletableFuture<Boolean> forceUnlockAsync(String key, long threadId) {
                return CompletableFuture.completedFuture(null);
            }
        };
    }
}
