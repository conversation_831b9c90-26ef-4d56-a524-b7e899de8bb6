package com.turbospaces.rpc;

import com.github.robtimus.obfuscation.Obfuscated;
import com.github.robtimus.obfuscation.annotation.ObfuscateAll;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class UserInfo {
    @ObfuscateAll
    private Obfuscated<String> remoteIp;
    private String userAgent;
}
