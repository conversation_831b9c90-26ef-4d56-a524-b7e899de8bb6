package com.turbospaces.rpc;

import com.turbospaces.api.ServiceApi;
import com.turbospaces.api.v1.GetSystemInfoRequest;
import com.turbospaces.api.v1.GetSystemInfoResponse;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

@WorkflowInterface
public interface AdminWorkflow extends ServiceApi {
    @WorkflowMethod
    GetSystemInfoResponse getInfo(GetSystemInfoRequest request) throws Throwable;
}
