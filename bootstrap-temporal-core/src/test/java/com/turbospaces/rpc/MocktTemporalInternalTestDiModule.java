package com.turbospaces.rpc;

import java.util.List;
import java.util.concurrent.CompletionStage;

import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.redis.RedisClientFactoryBean;
import com.turbospaces.temporal.TemporalChannel;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import api.v1.MockApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.temporal.client.WorkflowClient;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import io.temporal.workflow.Functions;

@Configuration
@Import(TestTemporalServerDiModule.class)
public class MocktTemporalInternalTestDiModule {
    public static String DEFAULT_WORKER = "defaultWorker";

    @Bean
    public CommonObjectMapper objectMapper() {
        return new CommonObjectMapper();
    }
    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, ObjectMapper objectMapper) {
        return new MockApiFactory(props, objectMapper);
    }
    @Bean
    public TemporalChannel temporalChannel(
            ApplicationContext applicationContext,
            DynamicCloud cloud,
            WorkflowClient client,
            ApplicationProperties props,
            ApiFactory apiFactory) {
        return new MockTemporalChannel(
                applicationContext,
                cloud,
                props,
                client,
                apiFactory,
                props.APP_DEV_MODE);
    }

    public static class MockTemporalChannel extends TemporalChannel {

        private final ApplicationContext context;

        public MockTemporalChannel(ApplicationContext context, DynamicCloud cloud, ApplicationProperties props, WorkflowClient workflowClient, ApiFactory apiFactory,
                                   Property<Boolean> enabled) {
            super(props, cloud, workflowClient, apiFactory, enabled);
            this.context = context;
        }

        @Override
        public void accept(WorkerFactory workerFactory) {
            Worker worker = workerFactory.newWorker(MocktTemporalInternalTestDiModule.DEFAULT_WORKER);
            AutowireCapableBeanFactory beanFactory = context.getAutowireCapableBeanFactory();

            worker.registerWorkflowImplementationFactory(AdminWorkflow.class, new Functions.Func<AdminWorkflow>() {
                @Override
                public AdminWorkflow apply() {
                    return beanFactory.createBean(DefaultAdminWorkflow.class);
                }
            });
            registerActivities(worker, List.of(
                    beanFactory.createBean(DefaultGetRemoteIpActivity.class),
                    beanFactory.createBean(DefaultGetReleaseVersionActivity.class),
                    beanFactory.createBean(DefaultSimulateFailureActivity.class)));
        }
    }

    @Bean
    public RedisClientFactoryBean redissonFactory(ApplicationProperties props, MeterRegistry meterRegistry) throws Exception {
        var si = new RedisServiceInfo(UPSs.REDIS, "redis://localhost:" + props.cfg().getInteger("redis.port"));
        return new RedisClientFactoryBean(props, meterRegistry, si);
    }

    @Bean
    public TransactionalRequestOutcomePublisher<?> timedAspect(ApiFactory apiFactory) {
        return new TransactionalRequestOutcomePublisher<>() {
            @Override
            public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
                return null;
            }
            @Override
            public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
                return null;
            }
            @Override
            public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
                return null;
            }
            @Override
            public CompletionStage<Object> sendEvent(EventQueuePostSpec spec) {
                return null;
            }
            @Override
            public CompletionStage<Object> sendNotify(NotifyQueuePostSpec spec) {
                return null;
            }
            @Override
            public ApiFactory apiFactory() {
                return apiFactory;
            }
        };
    }
}
