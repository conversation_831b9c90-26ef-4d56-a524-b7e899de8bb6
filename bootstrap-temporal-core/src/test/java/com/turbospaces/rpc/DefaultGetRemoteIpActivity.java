package com.turbospaces.rpc;

import com.github.robtimus.obfuscation.Obfuscator;
import com.turbospaces.cfg.ApplicationProperties;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultGetRemoteIpActivity implements GetRemoteIpActivity {
    private final ApplicationProperties props;

    @Override
    public UserInfo userInfo() {
        return new UserInfo( Obfuscator.all().obfuscateObject(props.externalIp()), "user-agent");
    }
}
