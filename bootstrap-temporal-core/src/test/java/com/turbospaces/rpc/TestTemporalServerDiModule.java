package com.turbospaces.rpc;

import com.google.common.net.HostAndPort;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.temporal.CustomDataConverter;
import com.turbospaces.temporal.TemporalClientFactoryBean;
import com.turbospaces.temporal.TemporalDiModule;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;
import io.micrometer.core.instrument.MeterRegistry;
import io.temporal.testserver.TestServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Import;

@Configuration
@Import(TemporalDiModule.class)
@Slf4j
public class TestTemporalServerDiModule implements DisposableBean {
    public TestServer.PortBoundTestServer testServer;

    @Bean
    public TestServer.PortBoundTestServer testTemporalServer(DynamicCloud cloud) {
        int port = PlatformUtil.findAvailableTcpPort();
        HostAndPort target = HostAndPort.fromParts("localhost", port);
        var server = TestServer.createPortBoundServer(port);
        cloud.addUps(new PlainServiceInfo(UPSs.TEMPORAL, "tcp://" + target));
        return server;
    }

    @Bean
    @DependsOn("testTemporalServer")
    public TemporalClientFactoryBean temporalClientFactoryBean(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            CustomDataConverter customDataConverter) {
        return new TemporalClientFactoryBean(
                props,
                "test-namespace",
                cloud,
                meterRegistry,
                customDataConverter

        );
    }

    @Override
    public void destroy() throws Exception {
        if (testServer != null) {
            try {
                testServer.close();
            } catch (Exception e) {
                log.error("Error closing Temporal test server: " + e.getMessage());
            }
        }
    }
}
