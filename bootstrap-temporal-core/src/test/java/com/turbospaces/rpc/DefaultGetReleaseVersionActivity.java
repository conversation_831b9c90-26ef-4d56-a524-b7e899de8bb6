package com.turbospaces.rpc;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultGetReleaseVersionActivity implements GetReleaseVersionActivity {
    private final ApplicationProperties props;

    @Override
    public String releaseVersion() {
        return PlatformUtil.version(props.CLOUD_APP_NAME);
    }
}
