package com.turbospaces.rpc;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiConsumer;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;
import org.slf4j.event.Level;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcTags;

import api.v1.ApiFactory;
import api.v1.ReplyUtil;
import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.temporal.common.converter.Values;
import io.temporal.failure.ApplicationFailure;
import io.temporal.failure.TimeoutFailure;
import io.vavr.CheckedConsumer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultWorkflowApiResponse<RESP extends Message> extends AbstractApiResponse<RESP> {
    private final Thread origin;
    private final ApplicationProperties props;
    private final FluentFuture<ApiResponseEntity<RESP>> subject;
    private final RESP prototype;
    private final String workflowId;
    private final SearchAttributes attributes;

    public DefaultWorkflowApiResponse(
            ApplicationProperties props,
            RequestWrapperFacade reqw,
            CompletableFuture<RESP> post,
            ApiFactory apiFactory,
            RESP prototype,
            String workflowId,
            SearchAttributes attributes) {
        super(apiFactory, reqw);
        this.props = Objects.requireNonNull(props);
        this.prototype = Objects.requireNonNull(prototype);
        this.workflowId = Objects.requireNonNull(workflowId);
        this.attributes = Objects.requireNonNull(attributes);
        this.origin = Thread.currentThread();

        var responseMapper = apiFactory.responseMapper();
        var completable = SettableFuture.<ApiResponseEntity<RESP>> create();
        subject = FluentFuture.from(completable);

        var startTime = System.currentTimeMillis();
        post.whenComplete(new BiConsumer<>() {
            @Override
            @SuppressWarnings("unchecked")
            public void accept(RESP result, Throwable err) {
                long took = System.currentTimeMillis() - startTime;
                //
                // ~ MDC fields
                //
                MDC.put(MdcTags.MDC_TRACE_ID, attributes.get(SearchAttributeKey.forText(MdcTags.MDC_TRACE_ID)));
                MDC.put(MdcTags.MDC_ROUTING_KEY, attributes.get(SearchAttributeKey.forText(MdcTags.MDC_ROUTING_KEY)));
                MDC.put(MdcTags.MDC_OPERATION, attributes.get(SearchAttributeKey.forText(MdcTags.MDC_OPERATION)));
                MDC.put(MdcTags.MDC_TOOK, String.valueOf(took));
                try {
                    Class<RESP> clazz = (Class<RESP>) prototype.getClass();
                    ApiResponseEntity<RESP> apiResp;
                    if (Objects.nonNull(result)) {
                        var rwf = responseMapper.toReply(reqw, result, api.v1.CacheControl.getDefaultInstance());
                        apiResp = new DefaultApiResponseEntity<>(rwf, clazz);
                        completable.set(apiResp);
                    } else {
                        String msg = String.format(ReplyUtil.ERROR_FORMAT, reqw.headers().getMessageId());
                        Throwable rootCause = ExceptionUtils.getRootCause(err);
                        if (Objects.isNull(rootCause)) {
                            rootCause = err;
                        }
                        if (rootCause instanceof TimeoutFailure) {
                            var status = apiFactory.toExceptionalTimeoutReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            apiResp = new DefaultApiResponseEntity<>(respw, clazz);
                            completable.set(apiResp);
                        } else if (rootCause instanceof ApplicationFailure app) {
                            var errorCode = app.getType();
                            var errorText = app.getOriginalMessage();
                            var errorReason = 0;

                            Values values = app.getDetails();
                            Map<String, String> details = Maps.newHashMap();
                            RESP preservedReply = null;
                            if (Objects.nonNull(values)) {
                                if (values.getSize() > 0) {
                                    errorReason = values.get(0, int.class);
                                    for (int i = 1; i < values.getSize() - 1; i++) {
                                        String key = values.get(i, String.class);
                                        String value = values.get(i + 1, String.class);
                                        details.put(key, value);
                                        i++;
                                    }
                                    byte[] preservedReplyBytes = values.get(values.getSize() - 1, byte[].class);
                                    if (preservedReplyBytes.length > 0) {
                                        try {
                                            var any = Any.parseFrom(preservedReplyBytes);
                                            if (any.is(clazz)) {
                                                preservedReply = any.unpack(clazz);
                                            }
                                        } catch (InvalidProtocolBufferException e) {
                                            log.error("cannot construct preserved reply", e);
                                        }
                                    }
                                }
                            }

                            var respsw = apiFactory.status(errorCode, errorReason, errorText, details);
                            var message = preservedReply != null ? preservedReply : prototype;
                            var respw = responseMapper.toReply(reqw, message, respsw);
                            apiResp = new DefaultApiResponseEntity<>(respw, clazz);
                            completable.set(apiResp);
                        } else {
                            var status = apiFactory.toExceptionalSystemReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            apiResp = new DefaultApiResponseEntity<>(respw, clazz);
                            completable.set(apiResp);
                        }
                        completable.setException(rootCause);

                        log.debug("IN ::: ({}:{}:{}) workflow reply received with status={} and took={}",
                                apiResp.any().getTypeUrl(),
                                workflowId,
                                attributes.get(SearchAttributeKey.forText(MdcTags.MDC_TOPIC)),
                                apiResp.status().errorCode(),
                                took);
                        if (props.isDevMode() || log.isTraceEnabled()) {
                            try {
                                log.atLevel(props.isDevMode() ? Level.DEBUG : Level.TRACE).log("reply payload: {}", apiResp.unpack());
                            } catch (IOException e) {
                                log.error("cannot unpack workflow reply", e);
                            }
                        }
                    }
                } finally {
                    MDC.remove(MdcTags.MDC_TRACE_ID);
                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                    MDC.remove(MdcTags.MDC_OPERATION);
                    MDC.remove(MdcTags.MDC_TOOK);
                }
            }
        });
    }
    private DefaultWorkflowApiResponse(
            ApplicationProperties props,
            ApiFactory apiFactory,
            RequestWrapperFacade req,
            FluentFuture<ApiResponseEntity<RESP>> subject,
            RESP prototype,
            Thread origin,
            String workflowId,
            SearchAttributes attributes) {
        super(apiFactory, req);
        this.props = Objects.requireNonNull(props);
        this.subject = Objects.requireNonNull(subject);
        this.prototype = Objects.requireNonNull(prototype);
        this.origin = Objects.requireNonNull(origin);
        this.workflowId = Objects.requireNonNull(workflowId);
        this.attributes = Objects.requireNonNull(attributes);
    }
    @Override
    public ApiResponseEntity<RESP> get() throws InterruptedException, ExecutionException {
        return subject.get();
    }
    @Override
    public ApiResponseEntity<RESP> get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return subject.get(timeout, unit);
    }
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return subject.cancel(mayInterruptIfRunning);
    }
    @Override
    public boolean isCancelled() {
        return subject.isCancelled();
    }
    @Override
    public boolean isDone() {
        return subject.isDone();
    }
    @Override
    public void addListener(Runnable listener, Executor executor) {
        subject.addListener(listener, executor);
    }
    @Override
    public void addCallback(FutureCallback<ApiResponseEntity<RESP>> callback, Executor executor) {
        var wrapper = new FutureCallbackWrapper<>(props, callback, origin);
        subject.addCallback(isDone() ? callback : wrapper, executor);
    }
    @Override
    public ApiResponse<RESP> thenVerifyOkAndAccept(CheckedConsumer<RESP> callback, Executor executor) {
        SettableFuture<ApiResponseEntity<RESP>> toReturn = SettableFuture.create();
        subject.addCallback(new FutureCallback<ApiResponseEntity<RESP>>() {
            @Override
            @SuppressWarnings("unchecked")
            public void onSuccess(ApiResponseEntity<RESP> result) {
                boolean isOK = true;

                try {
                    result.verifyOk();
                } catch (Exception err) {
                    toReturn.setException(err);
                    isOK = false;
                }

                if (isOK) {
                    try {
                        RESP body = (RESP) result.any().unpack(prototype.getClass());
                        callback.accept(body);
                        toReturn.set(result);
                    } catch (Throwable err) {
                        toReturn.setException(err);
                    }
                }
            }
            @Override
            public void onFailure(Throwable t) {
                toReturn.setException(t);
            }
        }, executor);

        //
        // ~ new API response by contract
        //
        return new DefaultWorkflowApiResponse<>(props, apiFactory, requestWrapper, FluentFuture.from(toReturn), prototype, origin, workflowId, attributes);
    }
}
