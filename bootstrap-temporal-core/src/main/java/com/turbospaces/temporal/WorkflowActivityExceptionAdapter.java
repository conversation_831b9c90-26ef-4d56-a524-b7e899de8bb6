package com.turbospaces.temporal;

import api.v1.ApiFactory;
import api.v1.ApplicationException;
import io.temporal.common.interceptors.WorkflowOutboundCallsInterceptor;
import io.temporal.common.interceptors.WorkflowOutboundCallsInterceptorBase;
import io.temporal.failure.ActivityFailure;
import io.temporal.failure.ApplicationFailure;
import io.temporal.internal.sync.AsyncInternal;
import lombok.SneakyThrows;

import java.util.Map;

public class WorkflowActivityExceptionAdapter extends WorkflowOutboundCallsInterceptorBase {
    private final WorkflowOutboundCallsInterceptor next;
    private final ApiFactory apiFactory;


    public WorkflowActivityExceptionAdapter(WorkflowOutboundCallsInterceptor next, ApiFactory apiFactory) {
        super(next);
        this.next = next;
        this.apiFactory = apiFactory;
    }

    @Override
    @SneakyThrows
    public <R> ActivityOutput<R> executeActivity(ActivityInput<R> input) {
        try {
            var out = next.executeActivity(input);
            if (!AsyncInternal.isAsync()) {
                out.getResult().get();
            }
            return out;
        } catch (ActivityFailure failure) {
            if (failure.getCause() instanceof ApplicationFailure app && ApplicationException.class.getSimpleName().equals(app.getType())) {
                var errorCode = app.getDetails().get(0, String.class);
                var errorText = app.getOriginalMessage();
                var respsw = apiFactory.status(errorCode, 0, errorText, Map.of());
                throw ApplicationException.of(app.getOriginalMessage(), respsw.errorCode());
            }
            throw failure;
        }
    }

    @Override
    @SneakyThrows
    public <R> LocalActivityOutput<R> executeLocalActivity(LocalActivityInput<R> input) {
        try {
            var out = next.executeLocalActivity(input);
            if (!AsyncInternal.isAsync()) {
                out.getResult().get();
            }
            return out;
        } catch (ActivityFailure failure) {
            if (failure.getCause() instanceof ApplicationFailure app && ApplicationException.class.getSimpleName().equals(app.getType())) {
                var errorCode = app.getDetails().get(0, String.class);
                var errorText = app.getOriginalMessage();
                var respsw = apiFactory.status(errorCode, 0, errorText, Map.of());
                throw ApplicationException.of(app.getOriginalMessage(), respsw.errorCode());
            }
            throw failure;
        }
    }
}
