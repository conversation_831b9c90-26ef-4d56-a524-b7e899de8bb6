package com.turbospaces.temporal;

import io.temporal.api.common.v1.Payload;
import io.temporal.common.context.ContextPropagator;
import io.temporal.common.converter.GlobalDataConverter;
import io.temporal.workflow.Workflow;

import java.util.HashMap;
import java.util.Map;

/**
 * Propagates workflow search attributes to activity header.
 */
public class SearchAttributesContextPropagator implements ContextPropagator {
    @Override
    public String getName() {
        return "activitySearchAttributesPropagator";
    }

    @Override
    public Map<String, Payload> serializeContext(Object context) {
        @SuppressWarnings("unchecked")
        Map<String, Payload> res = (Map<String, Payload>) context;
        return res;
    }

    @Override
    public Object deserializeContext(Map<String, Payload> header) {
        return header;
    }

    @Override
    public Object getCurrentContext() {
        HashMap<String, Payload> context = new HashMap<>();
        Workflow.getTypedSearchAttributes().getUntypedValues().forEach((key, value) ->
                context.put(
                        key.getName(),
                        GlobalDataConverter.get().toPayload(value).orElse(null)
                )
        );
        return context;
    }

    @Override
    public void setCurrentContext(Object context) {
        // do nothing
    }
}