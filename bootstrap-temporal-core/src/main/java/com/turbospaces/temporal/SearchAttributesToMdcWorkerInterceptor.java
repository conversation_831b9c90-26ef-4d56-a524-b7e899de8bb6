package com.turbospaces.temporal;

import java.util.Map;

import org.slf4j.MDC;

import io.temporal.api.common.v1.Payload;
import io.temporal.common.SearchAttributes;
import io.temporal.common.converter.DefaultDataConverter;
import io.temporal.common.interceptors.ActivityInboundCallsInterceptor;
import io.temporal.common.interceptors.ActivityInboundCallsInterceptorBase;
import io.temporal.common.interceptors.WorkerInterceptorBase;
import io.temporal.common.interceptors.WorkflowInboundCallsInterceptor;
import io.temporal.common.interceptors.WorkflowInboundCallsInterceptorBase;
import io.temporal.workflow.Workflow;

public class SearchAttributesToMdcWorkerInterceptor extends WorkerInterceptorBase {
    @Override
    public WorkflowInboundCallsInterceptor interceptWorkflow(WorkflowInboundCallsInterceptor next) {
        return new WorkflowInboundCallsInterceptorBase(next) {
            @Override
            public WorkflowOutput execute(WorkflowInput input) {
                SearchAttributes searchAttributes = Workflow.getTypedSearchAttributes();
                try {
                    searchAttributes.getUntypedValues().forEach((key, payload) -> MDC.put(key.getName(), payload.toString()));
                    return super.execute(input);
                } finally {
                    searchAttributes.getUntypedValues().keySet().forEach(k -> MDC.remove(k.getName()));
                }
            }
        };
    }
    @Override
    public ActivityInboundCallsInterceptor interceptActivity(ActivityInboundCallsInterceptor next) {
        return new ActivityInboundCallsInterceptorBase(next) {
            @Override
            public ActivityOutput execute(ActivityInboundCallsInterceptor.ActivityInput input) {
                Map<String, Payload> searchAttributes = input.getHeader().getValues();
                var dataConverter = DefaultDataConverter.newDefaultInstance();
                try {
                    searchAttributes.forEach((key, payload) -> {
                        var value = dataConverter.fromPayload(payload, String.class, String.class);
                        MDC.put(key, value);
                    });
                    return super.execute(input);
                } finally {
                    searchAttributes.keySet().forEach(MDC::remove);
                }
            }
        };
    }
}
