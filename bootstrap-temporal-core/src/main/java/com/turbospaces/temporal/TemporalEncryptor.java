package com.turbospaces.temporal;

import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;
import io.temporal.payload.context.HasWorkflowSerializationContext;
import io.temporal.payload.context.SerializationContext;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

@Slf4j
public class TemporalEncryptor {
    public static final String ENCRYPTION_META_KEY_PARAM = "payloadVar";

    public static String generateNonce() {
        return PlatformUtil.randomAlphanumeric(16);
    }

    public static String getSecret(SerializationContext context) {
        String worklfowId = "empty";
        if (context instanceof HasWorkflowSerializationContext workflowCtx) {
            worklfowId = workflowCtx.getWorkflowId();
        } else {
            log.error("Not expected. Not able to get workflow id from context");
        }
        return Hashing.sha512().newHasher()
                .putString(worklfowId, StandardCharsets.UTF_8)
                .hash().toString();
    }
}
