package com.turbospaces.temporal;

import api.v1.ApiFactory;
import io.temporal.common.interceptors.WorkerInterceptorBase;
import io.temporal.common.interceptors.WorkflowInboundCallsInterceptor;
import io.temporal.common.interceptors.WorkflowInboundCallsInterceptorBase;
import io.temporal.common.interceptors.WorkflowOutboundCallsInterceptor;

public class WorkflowActivityExceptionAdapterInterceptor extends WorkerInterceptorBase {
    private final ApiFactory apiFactory;

    public WorkflowActivityExceptionAdapterInterceptor(ApiFactory apiFactory) {
        this.apiFactory = apiFactory;
    }

    @Override
    public WorkflowInboundCallsInterceptor interceptWorkflow(WorkflowInboundCallsInterceptor next) {
        return new WorkflowInboundCallsInterceptorBase(next) {
            @Override
            public void init(WorkflowOutboundCallsInterceptor outboundCalls) {
                super.init(new WorkflowActivityExceptionAdapter(outboundCalls, apiFactory));
            }
        };
    }
}
