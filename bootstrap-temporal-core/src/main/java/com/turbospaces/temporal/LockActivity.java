package com.turbospaces.temporal;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ActivityInterface
public interface LockActivity {
    @ActivityMethod
    boolean lock(LockActivityIn in);

    @ActivityMethod
    boolean release(LockActivityIn in);

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class LockActivityIn {
        private String key;
        private long lockThreadId;
    }
}
