package com.turbospaces.temporal;

import com.turbospaces.cfg.ApplicationProperties;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.function.Consumer;

@UtilityClass
public class TemporalOptionsConfigurer {

    private static final RetryOptions NO_RETRY = RetryOptions.newBuilder().setMaximumAttempts(1).build();        
    
    public static <T extends ApplicationProperties> ActivityOptions activityOptions(T props, Consumer<ActivityOptions.Builder> customizer) {
        var options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(props.TEMPORAL_ACTIVITY_START_TO_CLOSE_TIMEOUT.get())
                .setRetryOptions(NO_RETRY)
                .setContextPropagators(List.of(new SearchAttributesContextPropagator()));
        customizer.accept(options);
        return options.build();
    }
    
}
