package com.turbospaces.temporal;

import io.temporal.common.interceptors.ActivityInboundCallsInterceptor;
import io.temporal.common.interceptors.WorkerInterceptorBase;

public class ActivityExceptionAdapterInterceptor extends WorkerInterceptorBase {
    @Override
    public ActivityInboundCallsInterceptor interceptActivity(ActivityInboundCallsInterceptor next) {
        return new ActivityExceptionAdapter(next);
    }
}
