package com.turbospaces.temporal;

import com.turbospaces.cfg.ApplicationProperties;
import io.temporal.client.WorkflowClient;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@RequiredArgsConstructor
public class ReloadableWorkflowClient implements WorkflowClient {
    private final ApplicationProperties props;
    private AtomicReference<WorkflowClient> delegateRef = new AtomicReference<>();

    @Delegate(types = WorkflowClient.class)
    public WorkflowClient getClient() {
        return delegateRef.get();
    }


    public void reload(WorkflowClient newClient) throws Exception {
        var old = this.delegateRef.getAndSet(newClient);
        if (Objects.nonNull(old)) {
            log.info("Channel for temporal client has been replaced.");
            close(old);
            log.info("Old temporal client has been shutdown.");
        }
    }

    public void close() {
        if (delegateRef.get() != null) {
            close(delegateRef.get());
        }
    }

    private void close(WorkflowClient old) {
        old.getWorkflowServiceStubs().shutdown();
        old.getWorkflowServiceStubs().getRawChannel().shutdown();
        old.getWorkflowServiceStubs().awaitTermination(
                props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get().toSeconds(),
                java.util.concurrent.TimeUnit.SECONDS
        );
    }
}