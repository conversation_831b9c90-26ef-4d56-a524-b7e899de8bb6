package com.turbospaces.temporal;

import io.temporal.activity.Activity;
import io.temporal.activity.ActivityExecutionContext;
import io.temporal.client.ActivityCompletionClient;
import io.temporal.payload.context.ActivitySerializationContext;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.Objects;

@Slf4j
@AllArgsConstructor
public class DefaultLockActivity implements LockActivity {
    private WorkflowLockService lockService;
    private ActivityCompletionClient completionClient;

    @Override
    @SneakyThrows
    public boolean lock(LockActivityIn in) {
        ActivityExecutionContext context = Activity.getExecutionContext();
        byte[] taskToken = context.getTaskToken();
        var key = in.getKey();
        var threadId = in.getLockThreadId();

        lockService.tryLockAsync(
                in.getKey(),
                threadId).whenComplete((res, err) -> {
            if (Objects.nonNull(err)) {
                log.error("Error acquiring lock for key: {} threadId: {}", key, threadId, err);
                if (err instanceof Exception exception) {
                    completionClient.withContext(new ActivitySerializationContext(context.getInfo())).completeExceptionally(taskToken, exception);
                } else {
                    completionClient.withContext(new ActivitySerializationContext(context.getInfo())).completeExceptionally(taskToken, new UndeclaredThrowableException(err));
                }
            } else {
                try {
                    log.debug("Lock acquired for key: {} threadId: {}", key, threadId);
                    completionClient.withContext(new ActivitySerializationContext(context.getInfo())).complete(taskToken, res);
                } catch (Exception e) {
                    if (log != null) {
                        log.error("Error completing lock activity for key: {} threadId: {}", key, threadId, e);
                    }
                    lockService.forceUnlockAsync(in.getKey(), in.getLockThreadId());
                    completionClient.withContext(new ActivitySerializationContext(context.getInfo())).completeExceptionally(taskToken, e);
                }
            }
        });
        context.doNotCompleteOnReturn();

        return false;
    }

    @Override
    public boolean release(LockActivityIn in) {
        ActivityExecutionContext context = Activity.getExecutionContext();
        byte[] taskToken = context.getTaskToken();
        var key = in.getKey();
        var threadId = in.getLockThreadId();

        lockService.forceUnlockAsync(key, threadId).whenComplete((res, err) -> {
            if (err != null) {
                log.error("Error releasing lock for key: {} threadId: {}", key, threadId, err);
                if (err instanceof Exception exception) {
                    completionClient.withContext(new ActivitySerializationContext(context.getInfo())).completeExceptionally(taskToken, exception);
                } else {
                    throw new RuntimeException(err);
                }
            }
            log.debug("Lock released for key: {} threadId: {}", key, threadId);
            completionClient.withContext(new ActivitySerializationContext(context.getInfo())).complete(taskToken, true);
        });
        context.doNotCompleteOnReturn();

        return false;
    }
}
