package com.turbospaces.temporal;

import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.ServiceInfoSubscription;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.temporal.client.WorkflowClient;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import io.temporal.worker.WorkerFactoryOptions;
import io.temporal.worker.WorkerOptions;
import io.temporal.worker.WorkflowImplementationOptions;
import io.temporal.workflow.Functions.Func;

public abstract class TemporalChannel implements ApplicationContextAware, InitializingBean, DisposableBean, Consumer<WorkerFactory> {
    public static final String NAMESPACE = "namespace";
    public static final String RETENTION = "retention";

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final WorkflowClient workflowClient;
    protected AtomicReference<WorkerFactory> factory = new AtomicReference<>();
    protected ApplicationContext applicationContext;
    private final DynamicCloud cloud;
    private final ApiFactory apiFactory;
    protected final Property<Boolean> enabled;
    private ServiceInfoSubscription<PlainServiceInfo> temporalSecretSubscription;

    public TemporalChannel(
            ApplicationProperties props,
            DynamicCloud cloud,
            WorkflowClient workflowClient,
            ApiFactory apiFactory,
            Property<Boolean> enabled) {
        this.cloud = Objects.requireNonNull(cloud);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.enabled = Objects.requireNonNull(enabled);
        this.props = Objects.requireNonNull(props);
        this.workflowClient = Objects.requireNonNull(workflowClient);
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        temporalSecretSubscription = ServiceInfoSubscription.of(cloud, UPSs.TEMPORAL, si -> {
            WorkerFactoryOptions factoryOptions = getWorkerFactoryOptions(apiFactory);
            var old = factory.getAndSet(WorkerFactory.newInstance(workflowClient, factoryOptions));
            accept(factory.get());

            if (enabled.get()) {
                logger.debug("about to start temporal factory");
                factory.get().start();
            }
            if (Objects.nonNull(old)) {
                logger.info("Channel for temporal factory has been replaced.");
                destroy(old);
                logger.info("Old temporal factory has been destroyed.");
            }
        });

        //
        // ~ well we might want to pause task(s) polling if necessary
        //
        enabled.subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean value) {
                if (Objects.nonNull(value)) {
                    if (value) {
                        if (BooleanUtils.isFalse(factory.get().isStarted())) {
                            logger.debug("about to start temporal factory");
                            factory.get().start();
                        }
                        logger.info("resuming polling ...");
                        factory.get().resumePolling();
                    } else {
                        if (factory.get().isStarted()) {
                            logger.info("suspending polling ...");
                            factory.get().suspendPolling();
                        }
                    }
                }
            }
        });
    }
    @Override
    public void destroy() throws Exception {
        if (temporalSecretSubscription != null) {
            temporalSecretSubscription.dispose();
        }
        if (Objects.nonNull(factory.get())) {
            destroy(factory.get());
        }
    }
    protected void destroy(WorkerFactory target) throws Exception {
        target.shutdown();
        target.awaitTermination(props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get().toSeconds(), TimeUnit.SECONDS);
    }
    protected Worker newWorker(String topic) {
        var options = WorkerOptions.newBuilder();
        options.setMaxConcurrentNexusExecutionSize(props.TEMPORAL_WORKFLOW_WORKERS_MAX.get());
        options.setMaxConcurrentWorkflowTaskExecutionSize(props.TEMPORAL_WORKFLOW_WORKERS_MAX.get());
        options.setMaxConcurrentActivityExecutionSize(props.TEMPORAL_ACTIVITY_WORKERS_MAX.get());
        options.setMaxConcurrentLocalActivityExecutionSize(props.TEMPORAL_ACTIVITY_WORKERS_MAX.get());
        options.setDefaultDeadlockDetectionTimeout(props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get().toMillis());
        return newWorker(topic, options.build());
    }
    protected Worker newWorker(String topic, WorkerOptions options) {
        return factory.get().newWorker(topic, options);
    }
    protected <T> void registerWorkflowImplementationFactory(Worker worker, Class<T> intf, Class<? extends T> impl) {
        registerWorkflowImplementationFactory(applicationContext, worker, intf, impl);
    }
    protected <T> void registerWorkflowImplementationFactory(Worker worker, Class<T> intf, Class<? extends T> impl, WorkflowImplementationOptions options) {
        registerWorkflowImplementationFactory(applicationContext, worker, intf, impl, options);
    }
    protected void registerActivities(Worker worker, Collection<Object> activities) {
        worker.registerActivitiesImplementations(
                new DefaultLockActivity(applicationContext.getBean(WorkflowLockService.class), workflowClient.newActivityCompletionClient()));
        worker.registerActivitiesImplementations(activities.toArray());
    }

    public static <T> void registerWorkflowImplementationFactory(ApplicationContext appCtx, Worker worker, Class<T> intf, Class<? extends T> impl) {
        worker.registerWorkflowImplementationFactory(intf, new Func<>() {
            @Override
            public T apply() {
                return appCtx.getAutowireCapableBeanFactory().createBean(impl);
            }
        });
    }
    public static <T> void registerWorkflowImplementationFactory(ApplicationContext appCtx, Worker worker, Class<T> intf, Class<? extends T> impl,
            WorkflowImplementationOptions options) {
        worker.registerWorkflowImplementationFactory(intf, new Func<>() {
            @Override
            public T apply() {
                return appCtx.getAutowireCapableBeanFactory().createBean(impl);
            }
        }, options);
    }
    public static WorkerFactoryOptions getWorkerFactoryOptions(ApiFactory apiFactory) {
        return WorkerFactoryOptions.newBuilder()
                .setWorkerInterceptors(
                        new SearchAttributesToMdcWorkerInterceptor(),
                        new ActivityExceptionAdapterInterceptor(),
                        new WorkflowActivityExceptionAdapterInterceptor(apiFactory))
                .build();
    }
}
