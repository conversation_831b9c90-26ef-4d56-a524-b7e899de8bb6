package com.turbospaces.temporal;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import com.google.protobuf.Message;
import com.turbospaces.mdc.MdcTags;

import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.temporal.workflow.WorkflowInfo;

public abstract class AbstractSequentialWorkflow<REQ extends Message, RESP extends Message.Builder> extends AbstractWorkflow<REQ, RESP> {
    @Override public Optional<String> getSequentialExecutionKey(WorkflowInfo info, SearchAttributes attributes) {
        String rk = null;
        if (attributes != null) {
            rk = attributes.get(SearchAttributeKey.forText(MdcTags.MDC_ROUTING_KEY));
        }
        if (StringUtils.isEmpty(rk)) {
            return Optional.empty();
        }
        return Optional.of(workflowInfo.getTaskQueue() + "-" + rk);
    }
}
