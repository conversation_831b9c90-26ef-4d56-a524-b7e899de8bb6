package com.turbospaces.temporal;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.event.Level;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.cloud.DynamicCloud;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.google.common.net.HostAndPort;
import com.google.common.util.concurrent.Uninterruptibles;
import com.google.protobuf.TextFormat;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.ServiceInfoSubscription;
import com.turbospaces.ups.UPSs;
import com.uber.m3.tally.RootScopeBuilder;
import com.uber.m3.tally.StatsReporter;

import io.grpc.ManagedChannel;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.netty.NettyChannelBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.temporal.api.enums.v1.IndexedValueType;
import io.temporal.api.operatorservice.v1.AddSearchAttributesRequest;
import io.temporal.api.operatorservice.v1.ListSearchAttributesRequest;
import io.temporal.api.operatorservice.v1.OperatorServiceGrpc;
import io.temporal.api.workflowservice.v1.DescribeNamespaceRequest;
import io.temporal.api.workflowservice.v1.RegisterNamespaceRequest;
import io.temporal.api.workflowservice.v1.WorkflowServiceGrpc;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowClientOptions;
import io.temporal.common.reporter.MicrometerClientStatsReporter;
import io.temporal.serviceclient.ServiceStubsOptions;
import io.temporal.serviceclient.WorkflowServiceStubs;
import io.temporal.serviceclient.WorkflowServiceStubsOptions;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Accessors(fluent = true)
public class TemporalClientFactoryBean extends AbstractFactoryBean<WorkflowClient> implements BeanNameAware {
    public static final String RETENTION = "retention";
    public static final Map<String, IndexedValueType> DEFAULT_SEARCH_ATTRIBUTES = ImmutableMap.<String, IndexedValueType>builder()
            .put(MdcTags.MDC_BRAND_NAME, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD)
            .put(MdcTags.MDC_TRACE_ID, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD)
            .put(MdcTags.MDC_ROUTING_KEY, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD)
            .put(MdcTags.MDC_TRANSACTION_ID, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD)
            .put(MdcTags.MDC_OPERATION, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD)
            .put(MdcTags.MDC_TOPIC, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD)
            .put(MdcTags.MDC_REMOTE_IP, IndexedValueType.INDEXED_VALUE_TYPE_KEYWORD).build();

    private final ApplicationProperties props;
    private final String namespace;
    private final MeterRegistry meterRegistry;
    private final CustomDataConverter customDataConverter;
    private final DynamicCloud cloud;
    private final PlatformExecutorService executor;
    @Setter
    private Map<String, IndexedValueType> searchAttributes = Maps.newHashMap();
    private ServiceInfoSubscription<PlainServiceInfo> temporalSecretSubscription;
    private final ReloadableWorkflowClient reloadableWorkflowClient;

    public TemporalClientFactoryBean(
            ApplicationProperties props,
            String namespace,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            CustomDataConverter customDataConverter
    ) {
        this.props = Objects.requireNonNull(props);
        this.cloud = Objects.requireNonNull(cloud);
        this.namespace = Objects.requireNonNull(namespace);
        this.meterRegistry = meterRegistry;
        this.customDataConverter = customDataConverter;
        this.reloadableWorkflowClient = new ReloadableWorkflowClient(props);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }

    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
        temporalSecretSubscription = ServiceInfoSubscription.of(cloud, UPSs.TEMPORAL, si -> {
            String target = HostAndPort.fromParts(si.getHost(), si.getPort()).toString();
            reloadClient(target, true);
        });
        super.afterPropertiesSet();
    }

    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
            if (temporalSecretSubscription != null) {
                temporalSecretSubscription.dispose();
            }
            reloadableWorkflowClient.close();
        } finally {
            executor.destroy();
        }
    }

    @Override
    public Class<?> getObjectType() {
        return WorkflowClient.class;
    }

    @Override
    protected WorkflowClient createInstance() throws Exception {
        Optional<PlainServiceInfo> opt = UPSs.findServiceInfoByName(cloud, UPSs.TEMPORAL);
        if (opt.isEmpty()) {
            String target = ServiceStubsOptions.DEFAULT_LOCAL_DOCKER_TARGET;
            reloadClient(target, false);
        }
        return reloadableWorkflowClient;
    }

    private void reloadClient(String target, boolean createSearchAttributes) throws Exception {
        ManagedChannel managedChannel = NettyChannelBuilder.forTarget(target)
                .defaultLoadBalancingPolicy("round_robin")
                .maxInboundMessageSize(props.GRPC_REQUEST_MAX_SIZE.get())
                .maxInboundMetadataSize(props.GRPC_METADATA_MAX_SIZE.get())
                .usePlaintext()
                .executor(executor)
                .idleTimeout(31, TimeUnit.DAYS) // https://github.com/grpc/grpc-java/issues/8714#issuecomment-974389414
                .build();


        WorkflowServiceStubsOptions.Builder stubOptions = WorkflowServiceStubsOptions.newBuilder();
        stubOptions.setChannel(managedChannel);
        StatsReporter reporter = new MicrometerClientStatsReporter(meterRegistry);
        stubOptions.setMetricsScope(new RootScopeBuilder()
                .reporter(reporter)
                .reportEvery(com.uber.m3.util.Duration.ofSeconds(props.APP_METRICS_REPORT_INTERVAL.get().toSeconds())));

        WorkflowClientOptions.Builder clientOptions = WorkflowClientOptions.newBuilder()
                .setDataConverter(customDataConverter);

        if (createSearchAttributes) {
            QueryStringDecoder decoder = new QueryStringDecoder(target);
            Map<String, List<String>> opts = decoder.parameters();

            var serviceStub = WorkflowServiceGrpc.newBlockingStub(managedChannel);
            var operatorStub = OperatorServiceGrpc.newBlockingStub(managedChannel);
            var printer = TextFormat.printer();
            clientOptions = clientOptions.setNamespace(namespace);

            //
            // ~ auto create name space
            //
            if (namespaceExist(serviceStub, namespace)) {
                log.debug("Namespace already exists: {}", namespace);
            } else {
                log.info("About to create namespace: {}", namespace);
                StopWatch stopWatch = StopWatch.createStarted();
                var retention = opts.containsKey(RETENTION) ? Duration.parse(Iterables.getOnlyElement(opts.get(RETENTION))) : Duration.ofDays(7);
                var registerReq = RegisterNamespaceRequest.newBuilder();
                registerReq.setNamespace(namespace);
                registerReq.setWorkflowExecutionRetentionPeriod(com.google.protobuf.Duration.newBuilder().setSeconds(retention.toSeconds()));

                var registerResp = serviceStub.registerNamespace(registerReq.build());
                // Despite blocking call above, namespace is actually created asynchronously within Temporal cluster
                // So waiting until it propagates to prevent "namespace not found" errors down the stack
                log.info("Waiting some time to propagate namespace creation: {}", namespace);
                Uninterruptibles.sleepUninterruptibly(3, TimeUnit.SECONDS); // experimentally, 3 sec is enough
                while (!namespaceExist(serviceStub, namespace)) {
                    Uninterruptibles.sleepUninterruptibly(250, TimeUnit.MILLISECONDS);
                }
                log.info("Registered namespace '{}' in {}ms. Response: {}",
                        namespace,
                        stopWatch.getTime(TimeUnit.MILLISECONDS),
                        printer.printToString(registerResp));
            }

            try {
                var listSearchAttibutesReq = ListSearchAttributesRequest.newBuilder().setNamespace(namespace);
                var addSearchAttributesReq = AddSearchAttributesRequest.newBuilder().setNamespace(namespace);
                try {
                    var listSearchAttibutesResp = operatorStub.listSearchAttributes(listSearchAttibutesReq.build());
                    searchAttributes.putAll(DEFAULT_SEARCH_ATTRIBUTES);
                    searchAttributes.forEach(new BiConsumer<>() {
                        @Override
                        public void accept(String name, IndexedValueType type) {
                            Optional.ofNullable(listSearchAttibutesResp.getCustomAttributesMap().get(name)).ifPresentOrElse(new Consumer<>() {
                                @Override
                                public void accept(IndexedValueType t) {
                                    log.trace("search attribute: {} with type: {} has been registed already", name, t);
                                }
                            }, new Runnable() {
                                @Override
                                public void run() {
                                    addSearchAttributesReq.putSearchAttributes(name, type);
                                }
                            });
                        }
                    });
                } catch (StatusRuntimeException err) {
                    if (err.getStatus().getCode() == Status.Code.UNIMPLEMENTED) {
                        log.warn("ListSearchAttributes is not implemented, will register default search attributes");
                        addSearchAttributesReq.putAllSearchAttributes(DEFAULT_SEARCH_ATTRIBUTES);
                    } else {
                        throw err;
                    }
                }


                if (addSearchAttributesReq.getSearchAttributesCount() > 0) {
                    var searchAttributesResp = operatorStub.addSearchAttributes(addSearchAttributesReq.build());
                    log.info("registed search attributes: {} in: {}, reply: {} ",
                            addSearchAttributesReq.getSearchAttributesMap(),
                            namespace,
                            printer.printToString(searchAttributesResp));
                }
            } catch (StatusRuntimeException err) {
                Status status = err.getStatus();
                switch (status.getCode()) {
                    case UNIMPLEMENTED:
                        log.atLevel(props.isDevMode() ? Level.TRACE : Level.WARN).log(err.getMessage(), err);
                        break;
                    default:
                        log.error(err.getMessage(), err);
                        break;
                }
            }
        }

        WorkflowServiceStubs stubs = WorkflowServiceStubs.newServiceStubs(stubOptions.build());
        reloadableWorkflowClient.reload(WorkflowClient.newInstance(stubs, clientOptions.build()));
    }

    private static boolean namespaceExist(WorkflowServiceGrpc.WorkflowServiceBlockingStub serviceStub, String namespace) {
        try {
            var describeReq = DescribeNamespaceRequest.newBuilder().setNamespace(namespace);
            var describeResp = serviceStub.describeNamespace(describeReq.build());
            if (describeResp.hasNamespaceInfo()) {
                return true;
            }
        } catch (io.grpc.StatusRuntimeException err) {
            log.trace(err.getMessage(), err);
        }
        return false;
    }
}
