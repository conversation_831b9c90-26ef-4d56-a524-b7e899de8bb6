package com.turbospaces.temporal;

import java.lang.reflect.Field;
import java.lang.reflect.InaccessibleObjectException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.function.Function;

import org.apache.commons.lang3.ClassUtils;

import com.github.robtimus.obfuscation.Obfuscated;
import com.github.robtimus.obfuscation.Obfuscator;
import com.google.protobuf.ByteString;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.security.text.SymmetricTextEncryptor;

import io.temporal.api.common.v1.Payload;
import io.temporal.common.converter.DataConverterException;
import io.temporal.common.converter.JacksonJsonPayloadConverter;
import io.temporal.common.converter.PayloadConverter;
import io.temporal.payload.context.SerializationContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SensitiveJacksonJsonPayloadConverter extends JacksonJsonPayloadConverter {
    private final CommonObjectMapper mapper;
    private final SerializationContext serializationContext;

    public SensitiveJacksonJsonPayloadConverter(CommonObjectMapper mapper, SerializationContext serializationContext) {
        super(mapper);
        this.mapper = mapper;
        this.serializationContext = serializationContext;
    }

    public SensitiveJacksonJsonPayloadConverter(CommonObjectMapper mapper) {
        this(mapper, null);
    }

    @Override
    public Optional<Payload> toData(Object value) throws DataConverterException {
        String nonce = TemporalEncryptor.generateNonce();
        if (value != null) {
            String secret = TemporalEncryptor.getSecret(serializationContext);
            visitSensitiveFields(value, str -> {
                try {
                    return SymmetricTextEncryptor.encrypt(str, secret, nonce.getBytes(StandardCharsets.UTF_8));
                } catch (Exception e) {
                    throw new DataConverterException(e);
                }
            });
        }
        return super.toData(value).map(
                p -> p.toBuilder()
                        .putMetadata(
                                TemporalEncryptor.ENCRYPTION_META_KEY_PARAM,
                                ByteString.copyFrom(nonce, StandardCharsets.UTF_8)
                        ).build());
    }

    @Override
    public PayloadConverter withContext(SerializationContext context) {
        return new SensitiveJacksonJsonPayloadConverter(mapper, context);
    }

    @Override public <T> T fromData(Payload content, Class<T> valueClass, Type valueType) throws DataConverterException {

        T result = super.fromData(content, valueClass, valueType);
        if (result != null) {
            visitSensitiveFields(
                    result,
                    str -> {
                        try {
                            return SymmetricTextEncryptor.decrypt(
                                    str,
                                    TemporalEncryptor.getSecret(serializationContext),
                                    content.getMetadataOrThrow(TemporalEncryptor.ENCRYPTION_META_KEY_PARAM)
                                            .toString(StandardCharsets.UTF_8)
                                            .getBytes(StandardCharsets.UTF_8)
                            );
                        } catch (Exception e) {
                            throw new DataConverterException(e);
                        }
                    });
        }
        return result;
    }

    @SuppressWarnings("rawtypes")
    private void visitSensitiveFields(Object object, Function<String, String> mappingFunction) {
        if (object == null) {
            return;
        }
        if (ClassUtils.isPrimitiveOrWrapper(object.getClass()) || object.getClass().equals(String.class)) {
            return;
        }

        try {
            for (Field field : object.getClass().getDeclaredFields()) {
                // Skip static and final fields
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || java.lang.reflect.Modifier.isFinal(field.getModifiers())) {
                    continue;
                }
                try {
                    field.setAccessible(true);
                } catch (SecurityException | InaccessibleObjectException e) {
                    log.warn("Failed to access field {}", field.getName(), e);
                    continue;
                }
                Object fieldValue = field.get(object);
                // only strings supported for now
                if (isSensitive(fieldValue)) {
                    String processedValue = mappingFunction.apply(String.valueOf(((Obfuscated) fieldValue).value()));
                    field.set(object, Obfuscator.all().obfuscateObject(processedValue));
                } else if (fieldValue != null && !ClassUtils.isPrimitiveOrWrapper(fieldValue.getClass())) {
                    visitSensitiveFields(fieldValue, mappingFunction);
                }
            }
        } catch (Throwable e) {
            throw new RuntimeException("Failed to process sensitive fields", e);
        }
    }

    private static boolean isSensitive(Object field) {
        return field instanceof Obfuscated obfuscated && obfuscated.value() instanceof String;
    }
}
