package com.turbospaces.temporal;

import java.time.Duration;
import java.util.function.Supplier;

import com.google.protobuf.Message;
import com.turbospaces.api.ServiceApi;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.ApiResponse;

import api.v1.ApiFactory;
import io.temporal.client.WorkflowClient;
import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.temporal.internal.WorkflowThreadMarker;
import io.vavr.CheckedFunction2;


public class WorkflowPostTemplate {

    private final ApplicationProperties props;
    private final ApiFactory apiFactory;
    private final Supplier<Duration> timeout;
    private final WorkflowClient client;
    private final String namespace; // TODO: Make required

    public WorkflowPostTemplate(ApplicationProperties props, ApiFactory apiFactory, Supplier<Duration> timeout, WorkflowClient client, String namespace) {
        this.props = props;
        this.apiFactory = apiFactory;
        this.timeout = timeout;
        this.client = client;
        this.namespace = namespace;
    }

    public WorkflowPostTemplate(ApplicationProperties props, ApiFactory apiFactory, Supplier<Duration> timeout, WorkflowClient client) {
        this(props, apiFactory, timeout, client, null);
    }

    public <REQ extends Message, REPLY extends Message, W extends ServiceApi> ApiResponse<REPLY> invoke(
            Class<W> workflowClass,
            REQ req,
            REPLY defaultInstance,
            CheckedFunction2<W, REQ, REPLY> stubInvocation,
            Topic taskQueue,
            String routingKey,
            SearchAttributes.Builder attributes) {
        attributes.set(SearchAttributeKey.forText(MdcTags.MDC_ROUTING_KEY), routingKey);
        var post = new WrappedWorkflowPost<>(
                props,
                workflowClass,
                apiFactory,
                stubInvocation,
                defaultInstance,
                timeout);
        String queueName = taskQueue.name().toString();
        if (WorkflowThreadMarker.isWorkflowThread() && namespace != null) {
            return post.executeChildWorkflow(namespace, queueName, req, attributes);
        }
        return post.execute(client, queueName, req, attributes);
    }

}
