package com.turbospaces.temporal;

import api.v1.Obfuscation;
import api.v1.SensitiveMode;
import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Message;
import com.google.protobuf.Message.Builder;
import com.turbospaces.security.text.SymmetricTextEncryptor;
import io.temporal.api.common.v1.Payload;
import io.temporal.common.converter.AbstractProtobufPayloadConverter;
import io.temporal.common.converter.DataConverterException;
import io.temporal.common.converter.PayloadConverter;
import io.temporal.common.converter.ProtobufJsonPayloadConverter;
import io.temporal.payload.context.SerializationContext;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

public class SensetiveProtobufJsonPayloadConverter extends AbstractProtobufPayloadConverter implements PayloadConverter {
    private final ProtobufJsonPayloadConverter defaultPayloadConverter;
    private SerializationContext serializationContext;

    public SensetiveProtobufJsonPayloadConverter(SerializationContext serializationContext) {
        this.defaultPayloadConverter = new ProtobufJsonPayloadConverter();
        this.serializationContext = serializationContext;
    }

    public SensetiveProtobufJsonPayloadConverter() {
        this(null);
    }

    @Override
    public String getEncodingType() {
        return defaultPayloadConverter.getEncodingType();
    }

    @Override
    public Optional<Payload> toData(Object value) throws DataConverterException {
        try {
            Object encryptedValue = value;
            String nonce = TemporalEncryptor.generateNonce();
            if (value instanceof Message message) {
                String secret = TemporalEncryptor.getSecret(serializationContext);
                encryptedValue = visitSensitiveFields(
                        message.toBuilder(),
                        str -> {
                            try {
                                return SymmetricTextEncryptor.encrypt(str, secret, nonce.getBytes(StandardCharsets.UTF_8));
                            } catch (Exception e) {
                                throw new DataConverterException(e);
                            }
                        }
                );
            }
            return defaultPayloadConverter.toData(encryptedValue)
                    .map(
                            p -> p.toBuilder()
                                    .putMetadata(
                                            TemporalEncryptor.ENCRYPTION_META_KEY_PARAM,
                                            ByteString.copyFrom(nonce, StandardCharsets.UTF_8)
                                    ).build());
        } catch (Throwable e) {
            throw new DataConverterException("Failed to convert to payload", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T fromData(Payload content, Class<T> valueClass, Type valueType) throws DataConverterException {
        try {
            T decryptedValue = defaultPayloadConverter.fromData(content, valueClass, valueType);
            if (decryptedValue instanceof Message message) {
                decryptedValue = (T) visitSensitiveFields(
                        message.toBuilder(),
                        str -> {
                            try {
                                return SymmetricTextEncryptor.decrypt(
                                        str,
                                        TemporalEncryptor.getSecret(serializationContext),
                                        content.getMetadataOrThrow(TemporalEncryptor.ENCRYPTION_META_KEY_PARAM)
                                                .toString(StandardCharsets.UTF_8)
                                                .getBytes(StandardCharsets.UTF_8)
                                );
                            } catch (Exception e) {
                                throw new DataConverterException(e);
                            }
                        }
                );
            }
            return decryptedValue;
        } catch (Throwable e) {
            throw new DataConverterException("Failed to convert from payload", e);
        }
    }

    @Override
    public PayloadConverter withContext(SerializationContext context) {
        return new SensetiveProtobufJsonPayloadConverter(context);
    }

    private Message visitSensitiveFields(Builder builder, Function<String, String> mappingFunction) {
        for (Map.Entry<FieldDescriptor, Object> entry : builder.getAllFields().entrySet()) {
            FieldDescriptor field = entry.getKey();
            if (field.isRepeated()) {
                if (field.getJavaType() == FieldDescriptor.JavaType.MESSAGE) {
                    if (field.isMapField()) {

                    } else {
                        for (int i = 0; i < builder.getRepeatedFieldCount(field); ++i) {
                            visitSensitiveFields(builder.getRepeatedFieldBuilder(field, i), mappingFunction);
                        }
                    }
                } else if (isSensitive(field)) {
                    if (field.isMapField()) {

                    } else {
                        for (int i = 0; i < builder.getRepeatedFieldCount(field); ++i) {
                            var f = builder.getRepeatedField(field, i);
                            builder.setRepeatedField(field, i, mappingFunction.apply(f.toString()));
                        }
                    }
                }
            } else {
                if (field.getJavaType() == FieldDescriptor.JavaType.MESSAGE) {
                    visitSensitiveFields(builder.getFieldBuilder(field), mappingFunction);
                } else if (isSensitive(field)) {
                    var f = builder.getField(field);
                    builder.setField(field, mappingFunction.apply(f.toString()));
                }
            }
        }
        return builder.build();
    }


    public static boolean isSensitive(FieldDescriptor field) {
        // Check for your sensitive annotation or option
        SensitiveMode sensitiveMode = field.getOptions().getExtension(Obfuscation.sensitiveMode);
        return sensitiveMode != null && sensitiveMode.getSensitive();
    }
}
