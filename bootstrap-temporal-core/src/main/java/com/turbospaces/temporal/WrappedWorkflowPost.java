package com.turbospaces.temporal;

import java.lang.reflect.UndeclaredThrowableException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import com.google.common.base.Stopwatch;
import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.ServiceApi;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.api.facade.MDCToHeadersContexPropagator;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.rpc.ApiResponse;
import com.turbospaces.rpc.DefaultWorkflowApiResponse;

import api.v1.ApiFactory;
import api.v1.Headers;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.common.RetryOptions;
import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.temporal.workflow.Async;
import io.temporal.workflow.ChildWorkflowOptions;
import io.temporal.workflow.Functions;
import io.temporal.workflow.Functions.Func2;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import io.vavr.CheckedFunction0;
import io.vavr.CheckedFunction2;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public class WrappedWorkflowPost<W extends ServiceApi, REQ extends Message, RESP extends Message> implements Func2<REQ, String, RESP> {
    private final ApplicationProperties props;
    private final Class<W> workflow;
    private final ApiFactory apiFactory;
    private final CheckedFunction2<W, REQ, RESP> action;
    private final RESP prototype;
    private final api.v1.Headers.Builder headers = api.v1.Headers.newBuilder();
    private final String workflowId;
    private final Supplier<Duration> timeout;
    private Any body = Any.getDefaultInstance();
    private W stub;

    public WrappedWorkflowPost(
            ApplicationProperties props,
            Class<W> workflow,
            ApiFactory apiFactory,
            CheckedFunction2<W, REQ, RESP> action,
            RESP prototype,
            Supplier<Duration> timeout) {
        this.props = Objects.requireNonNull(props);
        this.workflow = Objects.requireNonNull(workflow);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.action = Objects.requireNonNull(action);
        this.prototype = Objects.requireNonNull(prototype);
        this.timeout = Objects.requireNonNull(timeout);

        var propagator = new MDCToHeadersContexPropagator();
        propagator.injectContext(headers);

        //
        // ~ preserve message id on the moment of creation
        //
        workflowId = getOrGenerateWorkflowId();
    }
    @Override
    public RESP apply(REQ t1, String topic) {
        try {
            body = Any.pack(t1);
            return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<>() {
                @Override
                public RESP apply() throws Throwable {
                    log.debug("OUT ::: ({}:{}:{}) about to execute temporal workflow",
                            workflow.getSimpleName(),
                            workflowId,
                            topic);
                    return action.apply(stub, t1);
                }
            });
        } catch (Throwable err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public ApiResponse<RESP> execute(WorkflowClient client, String topic, REQ req, SearchAttributes.Builder searchAttributes) {
        RetryOptions retryOptions = RetryOptions.newBuilder().setMaximumAttempts(1).build();
        return execute(client, topic, req, searchAttributes, WorkflowOptions.newBuilder().setRetryOptions(retryOptions));
    }
    public ApiResponse<RESP> execute(WorkflowClient client, String topic, REQ req, SearchAttributes.Builder searchAttributes, RetryOptions retry) {
        return execute(client, topic, req, searchAttributes, WorkflowOptions.newBuilder().setRetryOptions(retry));
    }
    public ApiResponse<RESP> execute(WorkflowClient client, String topic, REQ req, SearchAttributes.Builder searchAttributes, WorkflowOptions.Builder options) {
        fillStandardSearchAttributes(searchAttributes, headers, topic, req);
        var typedSearchAttributes = searchAttributes.build();

        this.stub = client.newWorkflowStub(
                workflow,
                options
                        .setWorkflowId(workflowId)
                        .setTaskQueue(topic)
                        .setTypedSearchAttributes(typedSearchAttributes)
                        .setWorkflowExecutionTimeout(timeout.get())
                        .setContextPropagators(List.of())
                        .build());
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC
        CompletableFuture<RESP> completable = WorkflowClient.execute(new Functions.Func1<REQ, RESP>() {
            @Override
            public RESP apply(REQ t1) {
                MdcUtil.propagate(mdc);
                try {
                    return WrappedWorkflowPost.this.apply(req, topic);
                } finally {
                    if (Objects.nonNull(mdc)) {
                        MDC.clear();
                    }
                }
            }
        }, req);
        var reqw = new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers.build(), body);
        return new DefaultWorkflowApiResponse<>(
                props,
                reqw,
                completable,
                apiFactory,
                prototype,
                workflowId,
                typedSearchAttributes);
    }
    // ~ This is only called from within workflow context
    public ApiResponse<RESP> executeChildWorkflow(String namespace, String queueName, REQ req, SearchAttributes.Builder attributes) {
        WrappedWorkflowPost.fillStandardSearchAttributes(attributes, headers, queueName, req);
        var typedSearchAttributes = attributes.build();

        var unpack = Any.pack(req);
        String id = Workflow.getInfo().getWorkflowId(); // reusing from current (parent)
        ChildWorkflowOptions options = ChildWorkflowOptions.newBuilder()
                .setNamespace(namespace)
                .setWorkflowId(id)
                .setTaskQueue(queueName)
                .setTypedSearchAttributes(typedSearchAttributes)
                .setWorkflowExecutionTimeout(timeout.get())
                .setContextPropagators(List.of())
                .build();
        W w = Workflow.newChildWorkflowStub(workflow, options);
        Promise<RESP> function = Async.function(() -> {
            try {
                Stopwatch sw = Stopwatch.createStarted();
                log.debug("OUT ::: ({}:{}:{}) about to call child temporal workflow",
                        workflow.getSimpleName(),
                        id,
                        queueName);
                RESP result = action.apply(w, req);
                log.info("IN ::: ({}:{}:{}) call to child temporal workflow succeeded in {}ms",
                        workflow.getSimpleName(),
                        id,
                        queueName,
                        sw.elapsed(TimeUnit.MILLISECONDS));
                return result;
            } catch (Throwable e) {
                log.error("IN ::: ({}:{}:{}) call to child temporal workflow failed. Reason: {}",
                        workflow.getSimpleName(),
                        id,
                        queueName,
                        e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
        CompletableFuture<RESP> resultFuture = new CompletableFuture<>();
        function.handle((resp, e) -> {
            if (e != null) {
                resultFuture.completeExceptionally(e);
            } else {
                resultFuture.complete(resp);
            }
            return null;
        });
        var reqw = new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers.build(), unpack);
        return new DefaultWorkflowApiResponse<>(
                props,
                reqw,
                resultFuture,
                apiFactory,
                prototype,
                id,
                typedSearchAttributes);
    }

    private static String getOrGenerateWorkflowId() {
        var messageId = MDC.get(MdcTags.MDC_MESSAGE_ID);
        return StringUtils.isEmpty(messageId) ? ApiFactory.UUID.generate().toString() : messageId;
    }

    static <REQ extends Message> void fillStandardSearchAttributes(
            SearchAttributes.Builder searchAttributes, Headers.Builder headers,
            String topic,
            REQ req) {
        if (StringUtils.isNotEmpty(headers.getBrandName())) {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_BRAND_NAME), headers.getBrandName());
        }
        if (StringUtils.isNotEmpty(headers.getReplyTo())) {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_REPLY_TO), headers.getReplyTo());
        }
        if (StringUtils.isNotEmpty(headers.getTraceId())) {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_TRACE_ID), headers.getTraceId());
        } else {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_TRACE_ID), PlatformUtil.randomUUID().toString());
        }
        if (StringUtils.isNotEmpty(headers.getOperation())) {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_OPERATION), headers.getOperation());
        } else {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_OPERATION), Any.pack(req).getTypeUrl());
        }
        if (StringUtils.isNotEmpty(topic)) {
            searchAttributes.set(SearchAttributeKey.forText(MdcTags.MDC_TOPIC), topic);
        }
    }
}
