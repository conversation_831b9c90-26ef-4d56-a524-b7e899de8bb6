package com.turbospaces.temporal;

import com.turbospaces.cfg.ApplicationProperties;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
public class RedisWorkflowLockService implements WorkflowLockService {
    private final RedissonClient redis;
    private final ApplicationProperties props;

    public RedisWorkflowLockService(RedissonClient redis, ApplicationProperties props) {
        this.redis = redis;
        this.props = props;
    }

    @Override
    public CompletableFuture<Boolean> tryLockAsync(String key, long threadId) {
        log.debug("Acquiring redis lock for key: {} threadId: {}", key, threadId);
        return redis.getFairLock(key).tryLockAsync(
                props.TEMPORAL_LOCK_ACTIVITY_COMPLETION_FROM_SCHEDULE_TIMEOUT.get().to<PERSON><PERSON><PERSON>(),
                props.TEMPORAL_LOCK_ACTIVITY_MAX_LOCK_TIMEOUT.get().toMill<PERSON>(),
                TimeUnit.MILLISECONDS,
                threadId
        ).toCompletableFuture();
    }

    @Override
    public CompletableFuture<Boolean> forceUnlockAsync(String key, long threadId) {
        log.debug("Unlocking redis lock for key: {} threadId: {}", key, threadId);
        return redis.getFairLock(key).forceUnlockAsync().toCompletableFuture();
    }
}
