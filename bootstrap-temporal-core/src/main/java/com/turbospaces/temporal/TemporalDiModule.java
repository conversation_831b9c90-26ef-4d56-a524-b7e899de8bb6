package com.turbospaces.temporal;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.redis.RedisClientFactoryBean;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.Bean;

@Configuration
public class TemporalDiModule {
    @Bean
    public CustomDataConverter customDataConverter(CommonObjectMapper objectMapper) {
        return new CustomDataConverter(objectMapper);
    }

    @Bean
    public WorkflowLockService workflowLockService(RedisClientFactoryBean factory, ApplicationProperties props, MeterRegistry meterRegistry) throws Exception {
        return new RedisWorkflowLockService(factory.getObject(), props);
    }
}
