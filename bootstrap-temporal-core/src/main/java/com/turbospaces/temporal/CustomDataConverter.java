package com.turbospaces.temporal;

import com.turbospaces.json.CommonObjectMapper;
import io.temporal.common.converter.DataConverter;
import io.temporal.common.converter.DefaultDataConverter;
import lombok.experimental.Delegate;

public class CustomDataConverter implements DataConverter {
    @Delegate
    private final DataConverter defaultDataConverter;

    public CustomDataConverter(CommonObjectMapper mapper) {
        this.defaultDataConverter = DefaultDataConverter.newDefaultInstance()
                .withPayloadConverterOverrides(
                        new SensetiveProtobufJsonPayloadConverter(),
                        new SensitiveJacksonJsonPayloadConverter(mapper)
                );
    }
}
