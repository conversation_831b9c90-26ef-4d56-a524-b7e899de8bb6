package com.turbospaces.temporal;

import java.time.Duration;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;

import com.google.common.collect.Lists;
import com.google.common.io.ByteSource;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.NoOpStackTracer;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EmbeddedTransactionalRequest;
import com.turbospaces.dispatch.RequestHandler;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.dispatch.SafeRequestHandler;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.ContextPropagator;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.TransactionalRequestOutcomePublisher;

import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.Tracer.SpanBuilder;
import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import io.temporal.failure.ApplicationFailure;
import io.temporal.failure.ServerFailure;
import io.temporal.workflow.Workflow;
import io.temporal.workflow.WorkflowInfo;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

@Slf4j
public abstract class AbstractWorkflow<REQ extends Message, RESP extends Message.Builder> {
    protected Logger logger;
    protected SearchAttributes searchAttributes;
    protected WorkflowInfo workflowInfo;

    @Inject
    protected TransactionalRequestOutcomePublisher<?> outcomePublisher;
    @Inject
    protected MeterRegistry meterRegistry;
    @Inject
    protected WorkflowLockService workflowLockService;
    @Inject
    protected Tracer tracer;
    @Inject
    protected ApiFactory apiFactory;
    @Inject
    protected ApplicationProperties props;

    public abstract Optional<String> getSequentialExecutionKey(WorkflowInfo info, SearchAttributes attributes);

    protected RESP apply(REQ request, RESP resp, RequestHandler<REQ, RESP> handler) throws Throwable {
        logger = Workflow.getLogger(getClass());
        workflowInfo = Workflow.getInfo();
        searchAttributes = Workflow.getTypedSearchAttributes();
        // Create a deterministic RNG seeded by the Workflow’s history
        Random rng = Workflow.newRandom();
        long lockThreadId = Math.abs(rng.nextLong());
        var stopWatch = StopWatch.createStarted();

        Optional<String> lockMaybe = getSequentialExecutionKey(workflowInfo, searchAttributes);
        boolean locked = false;
        if (lockMaybe.isPresent()) {
            locked = getLockActivity().lock(new LockActivity.LockActivityIn(lockMaybe.get(), lockThreadId));
        }
        if (lockMaybe.isPresent() && !locked) {
            throw new ServerFailure("Request is out of date", true);
        }

        try {
            //
            // ~ adopt the request to the current request logic
            //
            var unit = new WorkUnit() {
                @Override
                public String topic() {
                    return workflowInfo.getTaskQueue();
                }

                @Override
                public long timestamp() {
                    return workflowInfo.getRunStartedTimestampMillis();
                }

                @Override
                public byte[] key() {
                    String routingKey = searchAttributes.get(SearchAttributeKey.forText(MdcTags.MDC_ROUTING_KEY));
                    if (StringUtils.isNotEmpty(routingKey)) {
                        return routingKey.getBytes();
                    }
                    return null;
                }

                @Override
                public ByteSource value() {
                    return ByteSource.wrap(request.toByteArray());
                }
            };
            var spec = RequestQueuePostSpec
                    .newBuilder(request)
                    .setMessageId(UUID.fromString(workflowInfo.getWorkflowId()))
                    .setTopic(new Topic() {
                        @Override
                        public AsciiString name() {
                            return AsciiString.cached(workflowInfo.getTaskQueue());
                        }

                        @Override
                        public void configure(ApplicationConfig cfg) {

                        }
                    }).build();
            var reqw = apiFactory.requestMapper().pack(
                    spec,
                    new ContextPropagator<api.v1.Headers.Builder>() {
                        @Override
                        public void injectContext(api.v1.Headers.Builder into) {
                            into.setMessageId(workflowInfo.getWorkflowId());
                            Optional.ofNullable(searchAttributes.get(SearchAttributeKey.forText(MdcTags.MDC_BRAND_NAME))).ifPresent(into::setBrandName);
                            Optional.ofNullable(searchAttributes.get(SearchAttributeKey.forText(MdcTags.MDC_TRACE_ID))).ifPresent(into::setTraceId);
                        }
                    }, Duration.ofMinutes(1));

            var operation = PlatformUtil.toLowerUnderscore(Any.pack(request).getTypeUrl());
            logger.info("IN ::: ({}:{}:{}) workUnit(key={}, topic={})",
                    workflowInfo.getWorkflowType(),
                    workflowInfo.getWorkflowId(),
                    operation,
                    unit.key(),
                    unit.topic());

            SpanBuilder buildSpan = tracer.buildSpan(operation);
            Span span = buildSpan.start();

            //
            // ~ wait for manual acknowledge and replyWhen condition
            //
            var workflowPromise = Workflow.newPromise();
            Sinks.One<Boolean> ack = Sinks.one();
            Sinks.One<Boolean> replyWhen = Sinks.one();
            Mono<Void> mono = Mono.when(ack.asMono(), replyWhen.asMono()).doOnTerminate(new Runnable() {
                @Override
                public void run() {
                    workflowPromise.complete(true);
                }
            });

            //
            // ~ wrap the request in a infrastructure code
            //
            EmbeddedTransactionalRequest<REQ, RESP> cmd = new EmbeddedTransactionalRequest<>(handler.requestType(), resp, unit, reqw, ack);
            SafeRequestHandler<REQ, RESP> safe = new SafeRequestHandler<>(
                    meterRegistry,
                    tracer,
                    reqw,
                    apiFactory,
                    new NoOpStackTracer(),
                    span,
                    unit,
                    cmd,
                    handler);

            //
            // ~ publish the notifications and events as usual via the outcome publisher
            //
            FluentFuture.from(safe.get()).addCallback(new FutureCallback<>() {
                @Override
                public void onSuccess(TransactionalRequestOutcome result) {
                    try {
                        outcomePublisher.publishNotifications(apiFactory.notifyTopic(), result);
                        outcomePublisher.publishEvents(apiFactory.eventsTopic(), result);
                        replyWhen.tryEmitValue(true);
                    } catch (Exception err) {
                        replyWhen.tryEmitError(err);
                    }
                }

                @Override
                public void onFailure(Throwable err) {
                    replyWhen.tryEmitError(err);
                }
            }, MoreExecutors.directExecutor());

            //
            // ~ execute the request in the current temporal thread
            //
            safe.run();
            mono.block();
            workflowPromise.get();

            var completableTask = safe.get();
            var outcome = completableTask.get();
            var respw = outcome.getReply();
            var status = respw.status();
            stopWatch.stop();

            //
            // ~ we want to propagate the error to the caller with additional information
            //
            if (status.isOK()) {
                log.debug("OUT ::: ({}:{}:{}) workflow execution completed with status=OK, took={}ms",
                        workflowInfo.getWorkflowType(),
                        workflowInfo.getWorkflowId(),
                        operation,
                        stopWatch.getDuration().toMillis());
            } else {
                var errorText = status.errorText();
                var errorCode = status.errorCode().toString().toLowerCase().intern();
                var errorReason = status.errorReason().getNumber();
                var errorDetails = status.errorDetails();

                var details = Lists.newArrayList();
                details.add(errorReason);
                if (MapUtils.isNotEmpty(errorDetails)) {
                    errorDetails.forEach((k, v) -> {
                        details.add(k);
                        details.add(v);
                    });
                }
                details.add(cmd.isPreserveReply() ? respw.body().toByteArray() : new byte[0]);
                log.debug("OUT ::: ({}:{}:{}) workflow execution completed with errorCode={}, errorText={}, took={}ms",
                        workflowInfo.getWorkflowType(),
                        workflowInfo.getWorkflowId(),
                        operation,
                        errorText,
                        errorCode,
                        stopWatch.getDuration().toMillis());
                throw ApplicationFailure.newNonRetryableFailure(errorText, errorCode, details.toArray(new Object[details.size()]));
            }
        } finally {
            if (lockMaybe.isPresent()) {
                try {
                    getLockActivity().release(new LockActivity.LockActivityIn(lockMaybe.get(), lockThreadId));
                } catch (Throwable e) {
                    // make sure lock is released
                    logger.error("Failed to release lock for key: {} using activity", lockMaybe.get(), e);
                    workflowLockService.forceUnlockAsync(lockMaybe.get(), lockThreadId).whenComplete((res, err) -> {
                        if (err != null) {
                            logger.error("Failed to release lock for key: {} using workflow lock service", lockMaybe.get(), err);
                        } else {
                            logger.debug("Lock released for key: {}", lockMaybe.get());
                        }
                    });
                }
            }
        }

        return resp;
    }

    private LockActivity getLockActivity() {
        var activityOptions = TemporalOptionsConfigurer.activityOptions(props, options -> {
            options.setStartToCloseTimeout(props.TEMPORAL_LOCK_ACTIVITY_COMPLETION_FROM_SCHEDULE_TIMEOUT.get());
        });
        return Workflow.newActivityStub(LockActivity.class, activityOptions);
    }
}
