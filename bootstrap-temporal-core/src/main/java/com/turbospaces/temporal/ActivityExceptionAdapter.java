package com.turbospaces.temporal;

import api.v1.ApplicationException;
import io.temporal.activity.ActivityExecutionContext;
import io.temporal.common.interceptors.ActivityInboundCallsInterceptor;
import io.temporal.failure.ApplicationFailure;
import io.temporal.serviceclient.CheckedExceptionWrapper;

public class ActivityExceptionAdapter implements ActivityInboundCallsInterceptor {
    private final ActivityInboundCallsInterceptor next;

    public ActivityExceptionAdapter(ActivityInboundCallsInterceptor next) {
        this.next = next;
    }

    @Override
    public void init(ActivityExecutionContext context) {
        next.init(context);
    }

    @Override
    public ActivityOutput execute(ActivityInput input) {
        try {
            return next.execute(input);
        } catch (CheckedExceptionWrapper ex) {
            Throwable rootCause = ex.getCause();
            if (rootCause instanceof ApplicationException applicationException) {
                throw ApplicationFailure.newNonRetryableFailureWithCause(
                        applicationException.getMessage(),
                        applicationException.getClass().getSimpleName(),
                        applicationException.getCause(),
                        applicationException.getCode()

                );
            }
            throw ex;
        }
    }
}
