package com.turbospaces.spark;

import java.util.Map;

import org.apache.spark.sql.catalyst.InternalRow;
import org.apache.spark.sql.connector.read.InputPartition;
import org.apache.spark.sql.connector.read.PartitionReader;
import org.apache.spark.sql.connector.read.PartitionReaderFactory;

public class EbeanPartitionReaderFactory implements PartitionReaderFactory {
    private final Map<String, String> properties;

    public EbeanPartitionReaderFactory(Map<String, String> properties) {
        this.properties = properties;
    }
    @Override
    public PartitionReader<InternalRow> createReader(InputPartition partition) {
        try {
            return new EbeanQueryReader(properties);
        } catch (Exception err) {
            throw new RuntimeException(err);
        }
    }
}