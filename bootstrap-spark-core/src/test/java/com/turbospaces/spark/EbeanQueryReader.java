package com.turbospaces.spark;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

import org.apache.spark.sql.catalyst.InternalRow;
import org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ups.H2ServiceInfo;
import com.turbospaces.ups.UPSs;

import io.ebean.DatabaseFactory;
import scala.collection.JavaConverters;

public class EbeanQueryReader extends AbstractEbeanPartitionReader<AccountBalance> {
    private final EbeanQueryIterator wrapper;

    public EbeanQueryReader(Map<String, String> options) throws Exception {
        super(new H2ServiceInfo(UPSs.H2_OWNER, options.get(JDBCOptions.JDBC_URL())));

        EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(db, props);

        cfg.addClass(Account.class);
        cfg.addClass(AccountBalance.class);
        cfg.addClass(AccountBalanceId.class);

        this.ebean = DatabaseFactory.create(cfg);
        this.wrapper = new EbeanQueryIterator(ebean, options);
    }
    @Override
    public boolean next() throws IOException {
        return wrapper.hasNext();
    }
    @Override
    public InternalRow get() {
        AccountBalance po = wrapper.next();

        ImmutableList.Builder<Object> builder = ImmutableList.builder();
        builder.add(org.apache.spark.unsafe.types.UTF8String.fromBytes(po.getAccount().getId().toString().getBytes()));
        builder.add(org.apache.spark.unsafe.types.UTF8String.fromBytes(po.getPk().getCurrency().getBytes()));
        builder.add(org.apache.spark.sql.types.Decimal.fromDecimal(po.getAmount()));
        builder.add(po.isCrypto());

        UnmodifiableIterator<Object> iterator = builder.build().iterator();
        return InternalRow.apply(JavaConverters.asScalaIteratorConverter(iterator).asScala().toSeq());
    }
    @Override
    public void close() throws IOException {
        try {
            if (Objects.nonNull(wrapper)) {
                wrapper.close();
            }
        } finally {
            super.close();
        }
    }
}
