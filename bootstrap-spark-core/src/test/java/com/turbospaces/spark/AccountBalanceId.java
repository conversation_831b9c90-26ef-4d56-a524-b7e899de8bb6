package com.turbospaces.spark;

import java.util.UUID;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.Setter;

@Embeddable
@Getter
@Setter
public class AccountBalanceId {
    @Column(nullable = false)
    private UUID accountId;

    @Column(nullable = false)
    private String currency;

    public AccountBalanceId() {}
    public AccountBalanceId(Account account, String currency) {
        this.accountId = account.getId();
        this.currency = currency;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getAccountId()).append(getCurrency()).toHashCode();
    }
    @Override
    public boolean equals(Object obj) {
        AccountBalanceId other = (AccountBalanceId) obj;
        return new EqualsBuilder().append(getAccountId(), other.getAccountId()).append(getCurrency(), other.getCurrency()).isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("account", getAccountId()).append("currency", getCurrency()).build();
    }
}
