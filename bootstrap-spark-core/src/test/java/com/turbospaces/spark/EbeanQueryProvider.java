package com.turbospaces.spark;

import java.util.Map;

import org.apache.spark.sql.connector.catalog.Table;
import org.apache.spark.sql.connector.catalog.TableProvider;
import org.apache.spark.sql.connector.expressions.Transform;
import org.apache.spark.sql.connector.read.Batch;
import org.apache.spark.sql.connector.read.InputPartition;
import org.apache.spark.sql.connector.read.PartitionReaderFactory;
import org.apache.spark.sql.connector.read.Scan;
import org.apache.spark.sql.connector.read.ScanBuilder;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.apache.spark.sql.util.CaseInsensitiveStringMap;

import com.google.common.collect.ImmutableList;

public class EbeanQueryProvider implements TableProvider {
    @Override
    public StructType inferSchema(CaseInsensitiveStringMap options) {
        ImmutableList.Builder<StructField> fields = ImmutableList.builder();

        fields.add(DataTypes.createStructField("account_id", DataTypes.StringType, false));
        fields.add(DataTypes.createStructField("currency", DataTypes.StringType, false));
        fields.add(DataTypes.createStructField("amount", DataTypes.createDecimalType(), false));
        fields.add(DataTypes.createStructField("crypto", DataTypes.BooleanType, false));

        return DataTypes.createStructType(fields.build());
    }
    @Override
    public Table getTable(StructType schema, Transform[] partitioning, Map<String, String> properties) {
        return new AbstractEbeanRead(schema, properties) {
            @Override
            public ScanBuilder newScanBuilder(CaseInsensitiveStringMap options) {
                return new AbstractEbeanQueryScanBuilder(schema, properties, options) {
                    @Override
                    public Scan build() {
                        return new AbstractEbeanScan(schema, properties) {
                            @Override
                            public Batch toBatch() {
                                return new EbeanBatch(schema, properties) {
                                    @Override
                                    public InputPartition[] planInputPartitions() {
                                        return new InputPartition[] { new EbeanInputPartition() };
                                    }
                                    @Override
                                    public PartitionReaderFactory createReaderFactory() {
                                        return new EbeanPartitionReaderFactory(properties);
                                    }
                                };
                            }
                        };
                    }
                };
            }
        };
    }
}