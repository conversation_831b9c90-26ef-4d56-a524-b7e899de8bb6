package com.turbospaces.spark;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "account_balances", schema = "CORE")
@Getter
@Setter
public class AccountBalance {
    @EmbeddedId
    private AccountBalanceId pk;

    @Column
    private BigDecimal amount;

    @Column
    private boolean crypto;

    @ManyToOne(optional = false)
    private Account account;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountBalance() {}
    public AccountBalance(Account player, String currency) {
        AccountBalanceId k = new AccountBalanceId(player, currency);
        setPk(k);
        setAccount(player);
        setAmount(BigDecimal.ZERO);
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getPk()).build();
    }
    @Override
    public boolean equals(Object obj) {
        AccountBalance other = (AccountBalance) obj;
        return new EqualsBuilder().append(getPk(), other.getPk()).isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("currency", getPk().getCurrency()).append("amount", getAmount()).append("version", getVersion()).build();
    }
}
