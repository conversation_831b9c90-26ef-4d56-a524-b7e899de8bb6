package com.turbospaces.spark;

import java.util.Date;
import java.util.UUID;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "account", schema = "CORE")
@Getter
@Setter
public class Account {
    @Id
    private UUID id;

    @Column(unique = true)
    private String username;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Version
    private int version;

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getUsername()).build();
    }
    @Override
    public boolean equals(Object obj) {
        Account other = (Account) obj;
        return new EqualsBuilder().append(getUsername(), other.getUsername()).isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("id", getId()).append("username", getUsername()).append("version", getVersion()).build();
    }
}
