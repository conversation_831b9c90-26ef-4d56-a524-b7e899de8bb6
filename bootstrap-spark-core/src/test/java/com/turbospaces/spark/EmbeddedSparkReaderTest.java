package com.turbospaces.spark;

import static org.apache.spark.sql.functions.col;
import static org.apache.spark.sql.functions.count;
import static org.apache.spark.sql.functions.row_number;
import static org.apache.spark.sql.functions.sum;

import java.math.BigDecimal;
import java.util.List;

import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.VoidFunction;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions;
import org.apache.spark.sql.expressions.Window;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.google.common.collect.Lists;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.ups.H2ServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import scala.Tuple2;

public class EmbeddedSparkReaderTest {
    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        bootstrap.withH2(true, bootstrap.spaceName());
        H2ServiceInfo ownerUps = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.H2_OWNER);

        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, ownerUps, List.of(), "CORE"));
        ConfigurableApplicationContext applicationContext = bootstrap.run();
        List<Tuple2<String, String>> l = Lists.newArrayList();

        try {
            EbeanJpaManager ebean = (EbeanJpaManager) applicationContext.getBean(JpaManager.class);

            try (Transaction tx = ebean.newTransaction()) {
                for (int i = 0; i < 128; i++) {
                    Account account = new Account();
                    account.setId(PlatformUtil.randomUUID());
                    account.setUsername("username_" + account.getId());

                    ebean.save(account, tx);
                    l.add(Tuple2.apply(account.getId().toString(), account.getUsername()));

                    AccountBalance ab1 = new AccountBalance(account, "USD");
                    ab1.setAmount(BigDecimal.ONE.add(BigDecimal.valueOf(ApiFactory.RANDOM.nextInt(12)).add(BigDecimal.valueOf(Math.random()))));
                    ab1.setCrypto(ApiFactory.RANDOM.nextBoolean());
                    ebean.save(ab1, tx);

                    AccountBalance ab2 = new AccountBalance(account, "EUR");
                    ab2.setAmount(BigDecimal.TEN.add(BigDecimal.valueOf(ApiFactory.RANDOM.nextInt(12)).add(BigDecimal.valueOf(Math.random()))));
                    ab2.setCrypto(ApiFactory.RANDOM.nextBoolean());
                    ebean.save(ab2, tx);
                }

                tx.commit();
            }
        } finally {
            bootstrap.shutdown();
        }

        try (SparkSession session = SparkSession.builder()
                .appName("junit")
                .master("local[*]")
                .config("spark.ui.enabled", false)
                .getOrCreate()) {
            try (JavaSparkContext sparkCtx = new JavaSparkContext(session.sparkContext())) {
                JavaRDD<Tuple2<String, String>> usernames = sparkCtx.parallelize(l);

                Dataset<Row> ds = session
                        .read()
                        .format(EbeanQueryProvider.class.getName())
                        .option(JDBCOptions.JDBC_URL(), ownerUps.read())
                        .option(JDBCOptions.JDBC_BATCH_FETCH_SIZE(), 16)
                        .load();

                //
                // ~ same output
                //
                ds.groupBy("crypto").agg(count(col("amount")).alias("count"), sum(col("amount")).alias("total")).show();
                ds.javaRDD().groupBy(v1 -> v1.<Boolean> getAs("crypto")).foreach(new GroupByFunction());

                List<Tuple2<String, Tuple2<Tuple2<String, String>, Iterable<Row>>>> stream = usernames.keyBy(t -> t._1())
                        .join(ds.toJavaRDD().groupBy(r -> r.getAs("account_id")))
                        .collect();

                for (Tuple2<String, Tuple2<Tuple2<String, String>, Iterable<Row>>> it : stream) {
                    it._1();
                    String username = it._2()._1()._2();
                    Iterable<Row> rows = it._2()._2();

                    BigDecimal total = BigDecimal.ZERO;
                    int count = 0;
                    for (Row row : rows) {
                        count++;
                        total = total.add(row.getAs("amount"));
                    }

                    System.out.println(username + " total: " + total + " count: " + count);
                }

                //
                // ~ window example
                //
                ds.withColumn("row_num", row_number().over(Window.partitionBy("currency").orderBy("amount"))).show(1024);
            }
        }
    }

    public static class GroupByFunction implements VoidFunction<Tuple2<Boolean, Iterable<Row>>> {
        private static final long serialVersionUID = 1L;

        @Override
        public void call(Tuple2<Boolean, Iterable<Row>> t) throws Exception {
            BigDecimal total = BigDecimal.ZERO;
            int count = 0;
            for (Row row : t._2()) {
                count++;
                total = total.add(row.getAs("amount"));
            }

            System.out.println("crypto: " + t._1() + " total: " + total + " count: " + count);
        }
    }

    @Configurable
    public static class AppConfig {
        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, MeterRegistry meterRegistry, DynamicCloud cloud) {
            ServiceInfo appUps = UPSs.findRequiredServiceInfoByName(cloud, UPSs.H2_APP);
            return new HikariDataSourceFactoryBean(props, meterRegistry, appUps);
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.addClass(Account.class);
            cfg.addClass(AccountBalance.class);
            cfg.addClass(AccountBalanceId.class);

            return cfg;
        }
        @Bean
        public EbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) throws Exception {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config);
        }
    }
}
