package com.turbospaces.spark;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.AbstractEbeanFactoryBean;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.JpaManager;

import io.ebean.platform.h2.H2Platform;
import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class EbeanFactoryBean extends AbstractEbeanFactoryBean<JpaManager> {
    public EbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
        super(props, meterRegistry, tracer, config);
        config.setDatabasePlatform(new H2Platform());
    }
    @Override
    public Class<?> getObjectType() {
        return JpaManager.class;
    }
    @Override
    protected JpaManager createEbean(SpiEbeanServer db) {
        return new EbeanJpaManager(props, meterRegistry, tracer, db, timer);
    }
}
