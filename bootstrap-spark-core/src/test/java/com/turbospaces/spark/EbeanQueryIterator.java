package com.turbospaces.spark;

import java.util.Iterator;
import java.util.Map;

import org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions;

import com.turbospaces.spark.query.QAccountBalance;

import io.ebean.Database;

public class EbeanQueryIterator extends AbstractEbeanIterator<AccountBalance> {
    public EbeanQueryIterator(Database ebean, Map<String, String> options) {
        super(ebean, options);
    }
    @Override
    protected Iterator<AccountBalance> iterator() {
        QAccountBalance q = q();
        return q.findIterate();
    }
    private QAccountBalance q() {
        QAccountBalance q = new QAccountBalance(ebean);
        q.setBufferFetchSizeHint(Integer.parseInt(options.get(JDBCOptions.JDBC_BATCH_FETCH_SIZE())));
        q.setReadOnly(true);
        q.setDisableLazyLoading(true);
        return q;
    }
}
