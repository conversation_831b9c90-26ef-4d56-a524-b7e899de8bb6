create table core.account (
  id                            uuid not null,
  username                      varchar(255),
  created_at                    timestamp not null,
  modified_at                   timestamp not null,
  version                       integer not null,
  constraint uq_account_username unique (username),
  constraint pk_account primary key (id)
);

create table core.account_balances (
  account_id                    uuid not null,
  currency                      varchar(255) not null,
  amount                        decimal(38),
  crypto                        boolean default false not null,
  version                       integer not null,
  created_at                    timestamp not null,
  modified_at                   timestamp not null,
  constraint pk_account_balances primary key (account_id,currency)
);