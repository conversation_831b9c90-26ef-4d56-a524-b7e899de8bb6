package com.turbospaces.spark;

import java.util.Map;
import java.util.Objects;

import org.apache.spark.sql.connector.read.ScanBuilder;
import org.apache.spark.sql.types.StructType;
import org.apache.spark.sql.util.CaseInsensitiveStringMap;

public abstract class AbstractEbeanQueryScanBuilder implements ScanBuilder {
    protected final StructType schema;
    protected final Map<String, String> properties;
    protected final CaseInsensitiveStringMap options;

    public AbstractEbeanQueryScanBuilder(StructType schema, Map<String, String> properties, CaseInsensitiveStringMap options) {
        this.schema = Objects.requireNonNull(schema);
        this.properties = Objects.requireNonNull(properties);
        this.options = Objects.requireNonNull(options);
    }
}
