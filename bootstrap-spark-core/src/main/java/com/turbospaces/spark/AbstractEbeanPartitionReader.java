package com.turbospaces.spark;

import java.io.Closeable;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

import javax.sql.DataSource;

import org.apache.spark.sql.catalyst.InternalRow;
import org.apache.spark.sql.connector.read.PartitionReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.service.ServiceInfo;

import com.netflix.archaius.config.DefaultCompositeConfig;
import com.netflix.archaius.config.MapConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.jdbc.DatasourceCreator;
import com.turbospaces.jdbc.JdbcPoolServiceConfig;

import io.ebean.Database;

public abstract class AbstractEbeanPartitionReader<T> implements PartitionReader<InternalRow> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final DataSource db;
    protected ApplicationProperties props;
    protected Database ebean;

    public AbstractEbeanPartitionReader(ServiceInfo si) throws Exception {
        this(si, Map.of());
    }
    public AbstractEbeanPartitionReader(ServiceInfo si, Map<String, String> options) throws Exception {
        DefaultCompositeConfig cfg = new DefaultCompositeConfig();
        cfg.addConfig("options", new MapConfig(options));

        DynamicPropertyFactory from = DynamicPropertyFactory.from(cfg);
        props = new ApplicationProperties(from);

        JdbcPoolServiceConfig jdbcCfg = new JdbcPoolServiceConfig(props, false);
        jdbcCfg.setMaxPoolSize(1);

        DatasourceCreator creator = new DatasourceCreator();
        db = creator.create(si, jdbcCfg);
    }
    @Override
    public void close() throws IOException {
        try {
            if (Objects.nonNull(ebean)) {
                ebean.shutdown();
            }
        } finally {
            if (Objects.nonNull(db)) {
                if (db instanceof Closeable closable) {
                    closable.close();
                }
            }
        }
    }
}
