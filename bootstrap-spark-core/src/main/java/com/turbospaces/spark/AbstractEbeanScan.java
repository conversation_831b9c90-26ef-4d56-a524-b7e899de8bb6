package com.turbospaces.spark;

import java.util.Map;

import org.apache.spark.sql.connector.read.Scan;
import org.apache.spark.sql.types.StructType;

public abstract class AbstractEbeanScan implements Scan {
    protected final StructType schema;
    protected final Map<String, String> properties;

    public AbstractEbeanScan(StructType schema, Map<String, String> properties) {
        this.schema = schema;
        this.properties = properties;
    }
    @Override
    public StructType readSchema() {
        return schema;
    }
    @Override
    public String description() {
        return "Ebean Query";
    }
}
