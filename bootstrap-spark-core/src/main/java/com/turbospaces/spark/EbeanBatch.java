package com.turbospaces.spark;

import java.util.Map;

import org.apache.spark.sql.connector.read.Batch;
import org.apache.spark.sql.connector.read.InputPartition;
import org.apache.spark.sql.types.StructType;

public abstract class EbeanBatch implements Batch {
    protected final StructType schema;
    protected final Map<String, String> properties;

    public EbeanBatch(StructType schema, Map<String, String> properties) {
        this.schema = schema;
        this.properties = properties;
    }
    @Override
    public InputPartition[] planInputPartitions() {
        return new InputPartition[] { new EbeanInputPartition() };
    }
}
