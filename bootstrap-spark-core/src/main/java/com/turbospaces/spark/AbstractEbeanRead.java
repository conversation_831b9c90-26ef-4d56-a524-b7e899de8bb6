package com.turbospaces.spark;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.apache.spark.sql.connector.catalog.SupportsRead;
import org.apache.spark.sql.connector.catalog.TableCapability;
import org.apache.spark.sql.types.StructType;

import com.google.common.collect.Sets;

public abstract class AbstractEbeanRead implements SupportsRead {
    protected final StructType schema;
    protected final Map<String, String> properties;

    public AbstractEbeanRead(StructType schema, Map<String, String> properties) {
        this.schema = Objects.requireNonNull(schema);
        this.properties = Objects.requireNonNull(properties);
    }
    @Override
    public String name() {
        return "ebean";
    }
    @Override
    public StructType schema() {
        return schema;
    }
    @Override
    public Set<TableCapability> capabilities() {
        return Sets.newHashSet(TableCapability.BATCH_READ);
    }
}
