package com.turbospaces.spark;

import java.io.Closeable;
import java.io.IOException;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.ebean.Database;

public abstract class AbstractEbeanIterator<T> implements Iterator<T>, Closeable {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final MutableInt totalRows = new MutableInt();
    private final StopWatch stopWatch;
    private final Iterator<T> iterator;
    private T toReturn;
    protected final Database ebean;
    protected final Map<String, String> options;

    public AbstractEbeanIterator(Database ebean, Map<String, String> options) {
        super();
        this.ebean = Objects.requireNonNull(ebean);
        this.options = Objects.requireNonNull(options);
        this.stopWatch = StopWatch.createStarted();
        this.iterator = iterator();
    }
    @Override
    public boolean hasNext() {
        if (iterator.hasNext()) {
            toReturn = iterator.next();
            totalRows.increment();
            return true;
        }

        stopWatch.stop();
        logger.info("totally fetched {} rows in {}", totalRows.get(), stopWatch);
        totalRows.setValue(0);
        return false;
    }
    @Override
    public T next() {
        return toReturn;
    }
    @Override
    public void close() throws IOException {
        if (iterator instanceof Closeable) {
            ((Closeable) iterator).close();
        }
    }
    protected abstract Iterator<T> iterator();
}
