<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.turbospaces.boot</groupId>
        <artifactId>bootstrap-parent</artifactId>
        <version>2.0.68-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>bootstrap-bom</artifactId>
    <packaging>pom</packaging>
    <name>${project.groupId} ::: ${project.artifactId}</name>
    <!-- -->
    <!-- dependency management -->
    <!-- -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-rules-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-guice-governator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-spring-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-api-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-gcp-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-debezium-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-clickhouse-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-netty-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-validation-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-ebean-migration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-kafka-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-gcp-pubsub-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-jdbc-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-resteasy-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-httpclient-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-jetty-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-web3j-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-jgroups-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-ebean-l2cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.turbospaces.boot</groupId>
                <artifactId>bootstrap-redis-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.turbospaces.boot</groupId>
                <artifactId>bootstrap-memcached-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.turbospaces.boot</groupId>
                <artifactId>bootstrap-nats-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-quartz-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-locust-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-spark-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-logging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-aether-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-grpc-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-temporal-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-protobuf-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-geoip</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-spanner-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-zookeeper-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bootstrap-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.ebean</groupId>
                <artifactId>ebean-platform-h2</artifactId>
                <version>${ebean.version}</version>
            </dependency>
            <dependency>
                <groupId>io.ebean</groupId>
                <artifactId>ebean-platform-clickhouse</artifactId>
                <version>${ebean.version}</version>
            </dependency>
            <dependency>
                <groupId>io.ebean</groupId>
                <artifactId>ebean-platform-postgres</artifactId>
                <version>${ebean.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-gcp-spanner</artifactId>
                <version>${flyway.spanner.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-clickhouse</artifactId>
                <version>${flyway.clickhouse.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-postgresql</artifactId>
                <version>${flyway.postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-core</artifactId>
                <version>${mockserver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-netty</artifactId>
                <version>${mockserver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-client-java</artifactId>
                <version>${mockserver.version}</version>
            </dependency>
            <dependency>
                <groupId>nl.jqno.equalsverifier</groupId>
                <artifactId>equalsverifier</artifactId>
                <version>${equalsverifier.version}</version>
            </dependency>
            <dependency>
                <groupId>com.icegreen</groupId>
                <artifactId>greenmail</artifactId>
                <version>${greenmail.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>${classmate.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.crypto.tink</groupId>
                <artifactId>tink</artifactId>
                <version>${google.tink.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-oauth2</artifactId>
                <version>${google.oauth2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>${maxmind.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sanctionco.jmail</groupId>
                <artifactId>jmail</artifactId>
                <version>${jmail.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.uuid</groupId>
                <artifactId>java-uuid-generator</artifactId>
                <version>${uuid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${jsr305.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jctools</groupId>
                <artifactId>jctools-core</artifactId>
                <version>${jctools.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-jaxrs2-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-jaxrs2-servlet-initializer-v2-jakarta</artifactId>
                <version>${swagger.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.expressly</groupId>
                <artifactId>expressly</artifactId>
                <version>${expressly.version}</version>
            </dependency>
            <dependency>
                <groupId>org.threeten</groupId>
                <artifactId>threetenbp</artifactId>
                <version>${threeten.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.angus</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${angus.mail.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-api</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-runtime</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-json</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-file</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-transforms</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-embedded</artifactId>
                <version>${debezium.version}</version>
            </dependency>
            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-core</artifactId>
                <version>${debezium.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
