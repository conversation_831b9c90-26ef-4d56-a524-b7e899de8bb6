package com.turbopsaces.validation;

import java.io.IOException;
import java.util.Iterator;
import java.util.Map.Entry;
import java.util.SortedMap;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.Maps;

public class Checksum {
    public static String chk(ObjectMapper mapper, String json) throws IOException {
        StringBuilder builder = new StringBuilder();
        chk(builder, mapper.readTree(json), false);
        return builder.toString();
    }
    public static String chk(JsonNode node) {
        StringBuilder builder = new StringBuilder();
        chk(builder, node, false);
        return builder.toString();
    }
    private static void chk(StringBuilder str, JsonNode currentNode, boolean debug) {
        if (currentNode.isNull()) {

        } else if (currentNode.isArray()) {
            ArrayNode array = (ArrayNode) currentNode;
            Iterator<JsonNode> it = array.elements();

            //
            // ~ add for debugging purpose only
            //
            if (debug) {
                str.append("[");
            }

            int i = 0;
            while (it.hasNext()) {
                str.append(i).append("=");
                chk(str, it.next(), debug);
                if (it.hasNext()) {
                    str.append("&");
                }
                i++;
            }

            //
            // ~ add for debugging purpose only
            //
            if (debug) {
                str.append("]");
            }
        } else if (currentNode.isObject()) {
            //
            // ~ sort fields
            //
            SortedMap<String, JsonNode> m = Maps.newTreeMap();
            Iterator<String> names = currentNode.fieldNames();
            while (names.hasNext()) {
                String name = names.next();
                JsonNode value = currentNode.get(name);
                m.put(name, value);
            }

            //
            // ~ add for debugging purpose only
            if (debug) {
                str.append("{");
            }
            for (Iterator<Entry<String, JsonNode>> it = m.entrySet().iterator(); it.hasNext();) {
                Entry<String, JsonNode> entry = it.next();
                String name = entry.getKey();
                str.append(name).append("=");
                chk(str, entry.getValue(), debug);
                if (it.hasNext()) {
                    str.append("&");
                }
            }

            //
            // ~ add for debugging purpose only
            //
            if (debug) {
                str.append("}");
            }
        } else if (currentNode.isTextual()) {
            str.append(currentNode.asText());
        } else {
            str.append(currentNode.toString());
        }
    }
}
