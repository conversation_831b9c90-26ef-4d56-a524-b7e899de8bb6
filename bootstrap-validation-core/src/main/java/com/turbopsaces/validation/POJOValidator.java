package com.turbopsaces.validation;

import java.util.Iterator;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Path;
import jakarta.validation.Path.Node;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import jakarta.validation.executable.ExecutableValidator;
import jakarta.validation.metadata.BeanDescriptor;

public class POJOValidator implements Validator {
    private final Validator validator;

    public POJOValidator() {
        HibernateValidatorConfiguration config = Validation.byProvider(HibernateValidator.class).configure();
        try (ValidatorFactory validatorFactory = config.ignoreXmlConfiguration().buildValidatorFactory()) {
            this.validator = validatorFactory.getValidator();
        }
    }
    public POJOValidator(Validator validator) {
        this.validator = Objects.requireNonNull(validator);
    }
    public Set<ConstraintViolation<Object>> validate(Object obj) throws IllegalArgumentException {
        return validator.validate(obj);
    }
    public Set<String> validateWithFormattedOutput(Object obj) {
        return validate(obj).stream().map(POJOValidator::formatViolation).collect(Collectors.toSet());
    }
    public boolean isValid(Object obj) {
        return validate(obj).isEmpty();
    }
    @Override
    public <T> Set<ConstraintViolation<T>> validate(T object, Class<?>... groups) {
        return validator.validate(object, groups);
    }
    @Override
    public <T> Set<ConstraintViolation<T>> validateProperty(T object, String propertyName, Class<?>... groups) {
        return validator.validateProperty(object, propertyName, groups);
    }
    @Override
    public <T> Set<ConstraintViolation<T>> validateValue(Class<T> beanType, String propertyName, Object value, Class<?>... groups) {
        return validator.validateValue(beanType, propertyName, value, groups);
    }
    @Override
    public BeanDescriptor getConstraintsForClass(Class<?> clazz) {
        return validator.getConstraintsForClass(clazz);
    }
    @Override
    public <T> T unwrap(Class<T> type) {
        return validator.unwrap(type);
    }
    @Override
    public ExecutableValidator forExecutables() {
        return validator.forExecutables();
    }
    public static String formatViolation(ConstraintViolation<?> constraintViolation) {
        String beanName = constraintViolation.getRootBeanClass().getSimpleName();
        Iterable<Path.Node> iterable = new Iterable<Node>() {
            @Override
            public Iterator<Node> iterator() {
                return constraintViolation.getPropertyPath().iterator();
            }
        };
        String path = StreamSupport.stream(iterable.spliterator(), false).map(Path.Node::getName).collect(Collectors.joining("."));
        return String.format("%s.%s = invalid value(%s)", beanName, path, String.valueOf(constraintViolation.getInvalidValue()));
    }
}
