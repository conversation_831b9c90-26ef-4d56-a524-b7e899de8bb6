package com.turbospaces.validation;

import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.validation.beanvalidation.SpringValidatorAdapter;

import com.turbopsaces.validation.POJOValidator;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;

public class POJOValidatorTest {
    @Test
    public void works() {
        POJOValidator validator = new POJOValidator();
        Bean bean = new Bean();
        bean.v1 = String.valueOf(System.currentTimeMillis());
        bean.v2 = System.currentTimeMillis();

        SpringValidatorAdapter adapter = new SpringValidatorAdapter(validator);

        Set<ConstraintViolation<Object>> violations = adapter.validate(bean);
        Assertions.assertEquals(1, violations.size());

        for (ConstraintViolation<Object> constraintViolation : violations) {
            System.out.println(POJOValidator.formatViolation(constraintViolation));
        }
    }

    public static class Bean {
        @NotEmpty
        public String v1;
        @Min(1)
        @Max(10)
        public long v2;
    }
}
