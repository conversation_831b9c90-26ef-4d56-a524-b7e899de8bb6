package com.turbospaces.validation;

import java.io.IOException;
import java.math.BigDecimal;

import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbopsaces.validation.Checksum;

import api.v1.ApiFactory;

public class CommonObjectMapperTest {
    @Test
    public void works() throws IOException {
        ObjectMapper mapper = new ObjectMapper();

        Child1 child1 = new Child1();
        child1.a1 = BigDecimal.valueOf(Math.random());
        child1.b2 = Math.random();

        Child2 c21 = new Child2();
        c21.d3 = (float) Math.random();
        c21.t5 = (short) ApiFactory.RANDOM.nextInt();

        Child2 c22 = new Child2();
        c22.d3 = (float) Math.random();
        c22.t5 = (short) ApiFactory.RANDOM.nextInt();

        child1.nested = new Child2[] { c21, c22 };

        Root root = new Root();
        root.i1 = ApiFactory.RANDOM.nextInt();
        root.l2 = ApiFactory.RANDOM.nextLong();
        root.s3Arr = new String[] { "b2", "c2", "d3" };
        root.b4 = ApiFactory.RANDOM.nextBoolean();
        root.s5 = Integer.toString(Math.abs(System.identityHashCode(this)));
        root.child1 = child1;

        System.out.println(Checksum.chk(mapper, mapper.writeValueAsString(root)));
    }

    public static class Root {
        public int i1;
        public long l2;
        public String[] s3Arr;
        public boolean b4;
        public Child1 child1;
        public String s5;
    }

    public static class Child1 {
        public BigDecimal a1;
        public double b2;
        public Child2[] nested;
    }

    public static class Child2 {
        public float d3;
        public short t5;
    }
}
