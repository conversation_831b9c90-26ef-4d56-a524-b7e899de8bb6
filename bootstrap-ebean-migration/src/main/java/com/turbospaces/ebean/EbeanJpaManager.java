package com.turbospaces.ebean;

import java.lang.StackWalker.StackFrame;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.Savepoint;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Stream;

import javax.sql.DataSource;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.DisposableBean;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableList.Builder;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.DevMode;
import com.turbospaces.cache.BlockhoundLoadingCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.RequestHandler;

import io.ebean.AutoTune;
import io.ebean.BackgroundExecutor;
import io.ebean.BeanState;
import io.ebean.CallableSql;
import io.ebean.DatabaseBuilder.Settings;
import io.ebean.DocumentStore;
import io.ebean.DtoQuery;
import io.ebean.DuplicateKeyException;
import io.ebean.ExpressionFactory;
import io.ebean.Filter;
import io.ebean.FutureIds;
import io.ebean.FutureList;
import io.ebean.FutureMap;
import io.ebean.FutureRowCount;
import io.ebean.InsertOptions;
import io.ebean.MergeOptions;
import io.ebean.PagedList;
import io.ebean.PersistenceContextScope;
import io.ebean.Query;
import io.ebean.QueryIterator;
import io.ebean.RowConsumer;
import io.ebean.RowMapper;
import io.ebean.ScriptRunner;
import io.ebean.SqlQuery;
import io.ebean.SqlRow;
import io.ebean.SqlUpdate;
import io.ebean.Transaction;
import io.ebean.TransactionCallback;
import io.ebean.TxScope;
import io.ebean.Update;
import io.ebean.UpdateQuery;
import io.ebean.ValuePair;
import io.ebean.Version;
import io.ebean.annotation.Identity;
import io.ebean.annotation.Platform;
import io.ebean.annotation.TxIsolation;
import io.ebean.bean.BeanCollection;
import io.ebean.bean.BeanLoader;
import io.ebean.bean.CallOrigin;
import io.ebean.bean.EntityBean;
import io.ebean.bean.EntityBeanIntercept;
import io.ebean.cache.ServerCacheManager;
import io.ebean.config.dbplatform.DatabasePlatform;
import io.ebean.config.dbplatform.PlatformIdGenerator;
import io.ebean.event.readaudit.ReadAuditLogger;
import io.ebean.event.readaudit.ReadAuditPrepare;
import io.ebean.meta.MetaInfoManager;
import io.ebean.meta.MetricVisitor;
import io.ebean.platform.postgres.TraceablePlatformIdGenerator;
import io.ebean.plugin.BeanType;
import io.ebean.plugin.Property;
import io.ebean.plugin.SpiServer;
import io.ebean.text.json.JsonContext;
import io.ebeaninternal.api.LoadBeanRequest;
import io.ebeaninternal.api.LoadManyRequest;
import io.ebeaninternal.api.SpiDtoQuery;
import io.ebeaninternal.api.SpiEbeanServer;
import io.ebeaninternal.api.SpiJsonContext;
import io.ebeaninternal.api.SpiLogManager;
import io.ebeaninternal.api.SpiQuery;
import io.ebeaninternal.api.SpiQuery.Type;
import io.ebeaninternal.api.SpiQueryBindCapture;
import io.ebeaninternal.api.SpiQueryPlan;
import io.ebeaninternal.api.SpiSqlQuery;
import io.ebeaninternal.api.SpiSqlUpdate;
import io.ebeaninternal.api.SpiTransaction;
import io.ebeaninternal.api.SpiTransactionManager;
import io.ebeaninternal.api.TransactionEventTable;
import io.ebeaninternal.server.core.SpiResultSet;
import io.ebeaninternal.server.core.timezone.DataTimeZone;
import io.ebeaninternal.server.deploy.BeanDescriptor;
import io.ebeaninternal.server.deploy.BeanProperty;
import io.ebeaninternal.server.query.CQuery;
import io.ebeaninternal.server.transaction.RemoteTransactionEvent;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics;
import io.opentracing.Span;
import io.opentracing.Tracer;
import jakarta.persistence.OptimisticLockException;
import jakarta.persistence.PersistenceException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EbeanJpaManager implements JpaManager, DevMode, DisposableBean, CurrentTransactionProvider {
    public static int SEQ_ALLOCATION_SIZE = 128;
    public static String TAG_READ_ONLY = "readOnly";
    public static String TAG_CLASS_NAME = "className";
    public static String TAG_METHOD_NAME = "methodName";
    public static String TAG_LINE_NUMBER = "lineNumber";

    private final SpiEbeanServer database;
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final Tracer tracer;
    private final ScheduledExecutorService timer;
    private final LoadingCache<Class<? extends AbstractSeq>, PlatformIdGenerator> idGenerators;
    private final LoadingCache<Thread, ImmutableList.Builder<StackFrame>> frames;

    public EbeanJpaManager(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, SpiEbeanServer database, ScheduledExecutorService timer) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.tracer = Objects.requireNonNull(tracer);
        this.database = Objects.requireNonNull(database);
        this.timer = Objects.requireNonNull(timer);
        this.idGenerators = new BlockhoundLoadingCacheWrapper<>(CacheBuilder.newBuilder().build(new CacheLoader<>() {
            @Override
            public PlatformIdGenerator load(Class<? extends AbstractSeq> type) throws Exception {
                BeanProperty idProperty = database.descriptor(type).idProperty();
                String idPropertyName = idProperty.property().name();

                List<Field> fields = Lists.newLinkedList();
                for (Field field : FieldUtils.getAllFields(type)) {
                    if (field.getName().equals(idPropertyName)) {
                        fields.add(field);
                    }
                }

                Field field = Iterables.getOnlyElement(fields);
                Identity identity = field.getAnnotation(Identity.class);
                String sequenceName = identity.sequenceName();

                fields.clear();

                return new TraceablePlatformIdGenerator(
                        database,
                        type,
                        sequenceName,
                        SEQ_ALLOCATION_SIZE //
                );
            }
        }));

        int cacheMaxSize = props.CACHE_DEFAULT_MAX_SIZE.get();
        Duration cacheMaxIdle = props.APP_TIMER_INTERVAL.get();
        Duration cacheMaxTtl = props.CACHE_DEFAULT_MAX_IDLE.get();
        Duration cleanupInterval = props.APP_TIMER_INTERVAL.get();

        this.frames = new BlockhoundLoadingCacheWrapper<>(
                CacheBuilder.newBuilder()
                        .maximumSize(cacheMaxSize)
                        .expireAfterAccess(cacheMaxIdle)
                        .expireAfterWrite(cacheMaxTtl).build(new CacheLoader<>() {
                            @Override
                            public Builder<StackFrame> load(Thread key) throws Exception {
                                return ImmutableList.builder();
                            }
                        }));

        new GuavaCacheMetrics<>(frames, "ebean-stack-tracer", Collections.emptyList()).bindTo(meterRegistry);
        timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                long size = frames.size();
                if (size > 0) {
                    log.debug("about to cleanUp ebean stack frames of {} items ...", size);
                }

                //
                // ~ change to trace after investigation
                //
                try {
                    if (log.isDebugEnabled()) {
                        for (Entry<Thread, Builder<StackFrame>> entry : frames.asMap().entrySet()) {
                            Thread key = entry.getKey();
                            ImmutableList<StackFrame> value = entry.getValue().build();
                            log.debug(key + " ::: " + value);
                        }
                    }
                } finally {
                    frames.cleanUp();
                }
            }
        }, cleanupInterval.toSeconds(), cleanupInterval.toSeconds(), TimeUnit.SECONDS);
    }

    @Override
    public boolean isDevMode() {
        return props.isDevMode();
    }
    @Override
    public <R> PlatformIdGenerator idGenerator(Class<? extends AbstractSeq> type) {
        return idGenerators.getUnchecked(type);
    }
    @Override
    public ImmutableList<StackFrame> frames(Thread orig) {
        return frames.getUnchecked(orig).build();
    }
    @Override
    public void drainFrames(Thread orig) {
        frames.invalidate(orig);
    }
    @Override
    public TracedEbeanTransaction newTransaction() throws Throwable {
        preventTransactionNesting();
        preventNotReadOnly();

        return new TracedEbeanTransaction(() -> {
            StopWatch stopWatch = StopWatch.createStarted();
            SpiTransaction tx = (SpiTransaction) beginTransaction(TxScope.required());
            stopWatch.stop();

            long time = stopWatch.getTime(TimeUnit.MILLISECONDS);
            if (time > 0) {
                log.debug("connection acquiring for txId: {} took: {}", tx.id(), stopWatch);
            }

            return tx;
        }, frames, props, meterRegistry, span(false));
    }
    @Override
    public TracedEbeanTransaction newTransaction(TxScope scope) throws Throwable {
        preventTransactionNesting();
        preventNotReadOnly();

        return new TracedEbeanTransaction(() -> {
            StopWatch stopWatch = StopWatch.createStarted();
            SpiTransaction tx = (SpiTransaction) beginTransaction(scope);
            stopWatch.stop();

            long time = stopWatch.getTime(TimeUnit.MILLISECONDS);
            if (time > 0) {
                log.debug("connection acquiring for txId: {} scope: {} took: {}", tx.id(), scope, stopWatch);
            }

            return tx;
        }, frames, props, meterRegistry, span(false));
    }
    @Override
    public TracedEbeanTransaction newReadOnlyTransaction() throws Throwable {
        preventTransactionNesting();

        return new TracedEbeanTransaction(() -> {
            StopWatch stopWatch = StopWatch.createStarted();
            SpiTransaction tx = (SpiTransaction) beginTransaction(TxScope.required().setReadOnly(true));
            stopWatch.stop();

            long time = stopWatch.getTime(TimeUnit.MILLISECONDS);
            if (time > 0) {
                log.debug("connection acquiring for read-only txId: {} took: {}", tx.id(), stopWatch);
            }

            return tx;
        }, frames, props, meterRegistry, span(true));
    }
    @Override
    public TracedEbeanTransaction newReadOnlyTransaction(TxScope scope) throws Throwable {
        preventTransactionNesting();

        return new TracedEbeanTransaction(() -> {
            StopWatch stopWatch = StopWatch.createStarted();
            SpiTransaction tx = (SpiTransaction) beginTransaction(scope.setReadOnly(true));
            stopWatch.stop();

            long time = stopWatch.getTime(TimeUnit.MILLISECONDS);
            if (time > 0) {
                log.debug("connection acquiring for read-only txId: {} scope: {} took: {}", tx.id(), scope, stopWatch);
            }

            return tx;
        }, frames, props, meterRegistry, span(true));
    }
    @VisibleForTesting
    public ConcurrentMap<Class<? extends AbstractSeq>, PlatformIdGenerator> idGenerators() {
        return idGenerators.asMap();
    }
    @Override
    public void destroy() {
        try {
            shutdown(false, false);
        } finally {
            timer.shutdown();
            idGenerators.invalidateAll();
        }
    }
    @Override
    public <T> T getOrCreateAutonomously(AutonomousLoader<T> loader, Transaction tx) throws Throwable {
        //
        // ~ flush all dirty changes as of now
        // ~ we want to ensure that duplicate key constraint is not caused by next lines of code
        // ~ and if this is a case, immediately propagate exception
        //
        tx.flush();

        //
        // ~ we first attempt to load instantly (assuming in 99.9% it will be present in DB)
        //
        var opt = loader.load();
        if (opt.isPresent()) {
            return opt.get();
        }

        T instance = loader.type().getConstructor().newInstance();
        loader.merge(instance);

        //
        // ~ only PG is natively supported, for rest we will mimic behaviour via save points
        //
        switch (platform()) {
            case POSTGRES:
            case POSTGRES9: {
                //
                // ~ well this will not cause any exception (new feature of framework) so we don't need to create any save points
                //
                insert(instance, loader.insertOptions(), tx); // ~ this will cause concurrent transaction to wait potentially
                break;
            }
            default: {
                //
                // ~ create save-point so that we can later on recover from
                //
                Connection connection = tx.connection();
                Savepoint savepoint = connection.setSavepoint();
                try {
                    save(instance, tx); // ~ this will cause concurrent transaction to wait potentially
                    tx.flush();
                    connection.releaseSavepoint(savepoint); // ~ GC
                } catch (DuplicateKeyException err) {
                    log.warn(err.getMessage(), err);
                    connection.rollback(savepoint); // ~ revert state to save point
                }
                break;
            }
        }

        //
        // ~ since we use insert on conflict we can re-read the data and see the changes already (non-repeatable read in default isolation level)
        //
        return loader.load().orElseThrow(new Supplier<NoSuchElementException>() {
            @Override
            public NoSuchElementException get() {
                String entityName = instance.getClass().getSimpleName();
                EntityBean target = EntityBean.class.cast(instance);
                BeanDescriptor<?> descriptor = descriptor(instance.getClass());
                Map<String, Object> map = Maps.newLinkedHashMap();
                for (BeanProperty prop : descriptor.propertiesAll()) {
                    Object value = prop.getValue(target);
                    map.put(prop.name(), value);
                }
                return new NoSuchElementException("unable to load unique single entity: %s, supplied: %s".formatted(entityName, map));
            }
        });
    }
    @Override
    public <T> void insertOrUpdateAutonomously(AutonomousUpdater<T> loader, Transaction tx) throws Throwable {
        //
        // ~ flush all dirty changes as of now
        // ~ we want to ensure that duplicate key constraint is not caused by next lines of code
        // ~ and if this is a case, immediately propagate exception
        //
        tx.flush();
        T instance = loader.type().getConstructor().newInstance();
        var insertOptions = loader.insertOptions();

        //
        // ~ only PG is natively supported, for rest we will mimic behaviour via save points
        //
        switch (platform()) {
            case POSTGRES:
            case POSTGRES9: {
                //
                // ~ well this will not cause any exception (new feature of framework) so we don't need to create any save points
                //
                loader.merge(instance);
                insert(instance, insertOptions, tx); // ~ this will cause concurrent transaction to wait potentially
                break;
            }
            default: {
                //
                // ~ create save-point so that we can later on recover from
                //
                Connection connection = tx.connection();
                Savepoint savepoint = connection.setSavepoint();
                try {
                    // passing real entity for update applicable only for H2
                    T target = loader.load().orElse(instance);
                    loader.merge(target);
                    save(target, tx); // ~ this will cause concurrent transaction to wait potentially
                    tx.flush();
                    connection.releaseSavepoint(savepoint); // ~ GC
                } catch (DuplicateKeyException err) {
                    log.warn(err.getMessage(), err);
                    connection.rollback(savepoint); // ~ revert state to save point
                }
                break;
            }
        }
    }
    @Override
    public Optional<Object> current() {
        return Optional.ofNullable(database.currentTransaction());
    }
    @Override
    public JsonContext json() {
        return database.json();
    }
    @Override
    public ScriptRunner script() {
        return database.script();
    }
    @Override
    public DocumentStore docStore() {
        return database.docStore();
    }
    @Override
    public Settings config() {
        return database.config();
    }
    @Override
    public DatabasePlatform databasePlatform() {
        return database.databasePlatform();
    }
    @Override
    public List<? extends BeanType<?>> beanTypes() {
        return database.beanTypes();
    }
    @Override
    public BeanLoader beanLoader() {
        return database.beanLoader();
    }
    @Override
    public boolean isDisableL2Cache() {
        return database.isDisableL2Cache();
    }
    @Override
    public <T> BeanType<T> beanType(Class<T> beanClass) {
        return database.beanType(beanClass);
    }
    @Override
    public List<? extends BeanType<?>> beanTypes(String baseTableName) {
        return database.beanTypes(baseTableName);
    }
    @Override
    public BeanType<?> beanTypeForQueueId(String queueId) {
        return database.beanTypeForQueueId(queueId);
    }
    @Override
    public void loadMany(BeanCollection<?> collection, boolean onlyIds) {
        database.loadMany(collection, onlyIds);
    }
    @Override
    public long clockNow() {
        return database.clockNow();
    }
    @Override
    public SpiLogManager log() {
        return database.log();
    }
    @Override
    public SpiJsonContext jsonExtended() {
        return database.jsonExtended();
    }
    @Override
    public boolean isUpdateAllPropertiesInBatch() {
        return database.isUpdateAllPropertiesInBatch();
    }
    @Override
    public Object currentTenantId() {
        return database.currentTenantId();
    }
    @Override
    public CallOrigin createCallOrigin() {
        return database.createCallOrigin();
    }
    @Override
    public void scopedTransactionEnter(TxScope txScope) {
        database.scopedTransactionEnter(txScope);
    }
    @Override
    public void scopedTransactionExit(Object returnOrThrowable, int opCode) {
        database.scopedTransactionExit(returnOrThrowable, opCode);
    }
    @Override
    public <T> SpiQuery<T> createQuery(Class<T> beanType) {
        return database.createQuery(beanType);
    }
    @Override
    public PersistenceContextScope persistenceContextScope(SpiQuery<?> query) {
        return database.persistenceContextScope(query);
    }
    @Override
    public void clearQueryStatistics() {
        database.clearQueryStatistics();
    }
    @Override
    public SpiTransactionManager transactionManager() {
        return database.transactionManager();
    }
    @Override
    public List<BeanDescriptor<?>> descriptors() {
        return database.descriptors();
    }
    @Override
    public <T> BeanDescriptor<T> descriptor(Class<T> type) {
        return database.descriptor(type);
    }
    @Override
    public BeanDescriptor<?> descriptorById(String className) {
        return database.descriptorById(className);
    }
    @Override
    public BeanDescriptor<?> descriptorByQueueId(String queueId) {
        return database.descriptorByQueueId(queueId);
    }
    @Override
    public List<BeanDescriptor<?>> descriptors(String tableName) {
        return database.descriptors(tableName);
    }
    @Override
    public void shutdown() {
        database.shutdown();
    }
    @Override
    public void shutdown(boolean shutdownDataSource, boolean deregisterDriver) {
        database.shutdown(shutdownDataSource, deregisterDriver);
    }
    @Override
    public AutoTune autoTune() {
        return database.autoTune();
    }
    @Override
    public DataSource dataSource() {
        return database.dataSource();
    }
    @Override
    public String name() {
        return database.name();
    }
    @Override
    public DataSource readOnlyDataSource() {
        return database.readOnlyDataSource();
    }
    @Override
    public ExpressionFactory expressionFactory() {
        return database.expressionFactory();
    }
    @Override
    public MetaInfoManager metaInfo() {
        return database.metaInfo();
    }
    @Override
    public int lazyLoadBatchSize() {
        return database.lazyLoadBatchSize();
    }
    @Override
    public Platform platform() {
        return database.platform();
    }
    @Override
    public boolean isSupportedType(java.lang.reflect.Type genericType) {
        return database.isSupportedType(genericType);
    }
    @Override
    public ReadAuditLogger readAuditLogger() {
        return database.readAuditLogger();
    }
    @Override
    public ReadAuditPrepare readAuditPrepare() {
        return database.readAuditPrepare();
    }
    @Override
    public SpiServer pluginApi() {
        return database.pluginApi();
    }
    @Override
    public DataTimeZone dataTimeZone() {
        return database.dataTimeZone();
    }
    @Override
    public void visitMetrics(MetricVisitor visitor) {
        database.visitMetrics(visitor);
    }
    @Override
    public <T> T createEntityBean(Class<T> type) {
        return database.createEntityBean(type);
    }
    @Override
    public void externalModification(TransactionEventTable event) {
        database.externalModification(event);
    }
    @Override
    public ServerCacheManager cacheManager() {
        return database.cacheManager();
    }
    @Override
    public BackgroundExecutor backgroundExecutor() {
        return database.backgroundExecutor();
    }
    @Override
    public void remoteTransactionEvent(RemoteTransactionEvent event) {
        database.remoteTransactionEvent(event);
    }
    @Override
    public SpiQueryBindCapture createQueryBindCapture(SpiQueryPlan queryPlan) {
        return database.createQueryBindCapture(queryPlan);
    }
    @Override
    public void execute(TxScope scope, Runnable runnable) {
        database.execute(scope, runnable);
    }
    @Override
    public void execute(Runnable runnable) {
        database.execute(runnable);
    }
    @Override
    public <T> T executeCall(TxScope scope, Callable<T> callable) {
        return database.executeCall(scope, callable);
    }
    @Override
    public <T> T executeCall(Callable<T> callable) {
        return database.executeCall(callable);
    }
    @Override
    public void markAsDirty(Object bean) {
        database.markAsDirty(bean);
    }

    //
    //
    //
    @Override
    public Transaction createTransaction() {
        return database.createTransaction();
    }
    @Override
    public Transaction createTransaction(TxIsolation isolation) {
        return database.createTransaction(isolation);
    }
    @Override
    public Transaction beginTransaction() {
        return database.beginTransaction();
    }
    @Override
    public Transaction beginTransaction(TxIsolation isolation) {
        return database.beginTransaction(isolation);
    }
    @Override
    public Transaction beginTransaction(TxScope scope) {
        return database.beginTransaction(scope);
    }
    @Override
    public Transaction currentTransaction() {
        return database.currentTransaction();
    }
    @Override
    public void flush() {
        database.flush();
    }
    @Override
    public void endTransaction() {
        database.endTransaction();
    }
    @Override
    public void clearServerTransaction() {
        database.clearServerTransaction();
    }
    @Override
    public SpiTransaction beginServerTransaction() {
        return database.beginServerTransaction();
    }
    @Override
    public SpiTransaction currentServerTransaction() {
        return database.currentServerTransaction();
    }
    @Override
    public SpiTransaction createReadOnlyTransaction(Object tenantId, boolean useMaster) {
        return database.createReadOnlyTransaction(tenantId, useMaster);
    }
    @Override
    public BeanState beanState(Object bean) {
        return database.beanState(bean);
    }
    @Override
    public SqlQuery sqlQuery(String sql) {
        return database.sqlQuery(sql);
    }
    @Override
    public SqlUpdate sqlUpdate(String sql) {
        return database.sqlUpdate(sql);
    }
    @Override
    public void register(TransactionCallback transactionCallback) throws PersistenceException {
        database.register(transactionCallback);
    }
    @Override
    public void externalModification(String tableName, boolean inserted, boolean updated, boolean deleted) {
        database.externalModification(tableName, inserted, updated, deleted);
    }
    @Override
    public <T> Filter<T> filter(Class<T> beanType) {
        return database.filter(beanType);
    }
    @Override
    public void slowQueryCheck(long executionTimeMicros, int rowCount, SpiQuery<?> query) {
        database.slowQueryCheck(executionTimeMicros, rowCount, query);
    }
    @Override
    public Object beanId(Object bean) {
        return database.beanId(bean);
    }
    @Override
    public Object beanId(Object bean, Object id) {
        return database.beanId(bean, id);
    }

    //
    //
    //
    //
    @Override
    public void loadBeanRef(EntityBeanIntercept ebi) {
        database.loadBeanRef(ebi);
    }
    @Override
    public void loadBeanL2(EntityBeanIntercept ebi) {
        database.loadBeanL2(ebi);
    }
    @Override
    public void loadBean(EntityBeanIntercept ebi) {
        database.loadBean(ebi);
    }
    @Override
    public <T> CQuery<T> compileQuery(Type type, SpiQuery<T> query, Transaction transaction) {
        return database.compileQuery(type, query, transaction);
    }
    @Override
    public <A, T> List<A> findIdsWithCopy(SpiQuery<T> query) {
        return database.findIdsWithCopy(query);
    }
    @Override
    public <T> int findCountWithCopy(SpiQuery<T> query) {
        return database.findCountWithCopy(query);
    }
    @Override
    public void loadBean(LoadBeanRequest loadRequest) {
        database.loadBean(loadRequest);
    }
    @Override
    public void loadMany(LoadManyRequest loadRequest) {
        database.loadMany(loadRequest);
    }
    @Override
    public <T> T findSingleAttribute(SpiSqlQuery query, Class<T> cls) {
        return database.findSingleAttribute(query, cls);
    }
    @Override
    public <T> List<T> findSingleAttributeList(SpiSqlQuery query, Class<T> cls) {
        return database.findSingleAttributeList(query, cls);
    }
    @Override
    public <T> void findSingleAttributeEach(SpiSqlQuery query, Class<T> cls, Consumer<T> consumer) {
        database.findSingleAttributeEach(query, cls, consumer);
    }
    @Override
    public <T> T findOneMapper(SpiSqlQuery query, RowMapper<T> mapper) {
        return database.findOneMapper(query, mapper);
    }
    @Override
    public Map<String, ValuePair> diff(Object newBean, Object oldBean) {
        return database.diff(newBean, oldBean);
    }
    @Override
    public <T> List<T> findListMapper(SpiSqlQuery query, RowMapper<T> mapper) {
        return database.findListMapper(query, mapper);
    }
    @Override
    public void findEachRow(SpiSqlQuery query, RowConsumer consumer) {
        database.findEachRow(query, consumer);
    }
    @Override
    public <T> QueryIterator<T> findDtoIterate(SpiDtoQuery<T> query) {
        return database.findDtoIterate(query);
    }
    @Override
    public <T> Stream<T> findDtoStream(SpiDtoQuery<T> query) {
        return database.findDtoStream(query);
    }
    @Override
    public <T> List<T> findDtoList(SpiDtoQuery<T> query) {
        return database.findDtoList(query);
    }
    @Override
    public <T> T findDtoOne(SpiDtoQuery<T> query) {
        return database.findDtoOne(query);
    }
    @Override
    public <T> UpdateQuery<T> update(Class<T> beanType) {
        return database.update(beanType);
    }
    @Override
    public <T> void findDtoEach(SpiDtoQuery<T> query, Consumer<T> consumer) {
        database.findDtoEach(query, consumer);
    }
    @Override
    public <T> void findDtoEach(SpiDtoQuery<T> query, int batch, Consumer<List<T>> consumer) {
        database.findDtoEach(query, batch, consumer);
    }
    @Override
    public <T> void findDtoEachWhile(SpiDtoQuery<T> query, Predicate<T> consumer) {
        database.findDtoEachWhile(query, consumer);
    }
    @Override
    public <D> DtoQuery<D> findDto(Class<D> dtoType, SpiQuery<?> ormQuery) {
        return database.findDto(dtoType, ormQuery);
    }
    @Override
    public SpiResultSet findResultSet(SpiQuery<?> ormQuery) {
        return database.findResultSet(ormQuery);
    }
    @Override
    public <T> Query<T> createNamedQuery(Class<T> beanType, String namedQuery) {
        return database.createNamedQuery(beanType, namedQuery);
    }
    @Override
    public boolean exists(Class<?> beanType, Object beanId, Transaction transaction) {
        return database.exists(beanType, beanId, transaction);
    }
    @Override
    public void addBatch(SpiSqlUpdate defaultSqlUpdate, SpiTransaction transaction) {
        database.addBatch(defaultSqlUpdate, transaction);
    }
    @Override
    public <T> Query<T> createQuery(Class<T> beanType, String ormQuery) {
        return database.createQuery(beanType, ormQuery);
    }
    @Override
    public int[] executeBatch(SpiSqlUpdate defaultSqlUpdate, SpiTransaction transaction) {
        return database.executeBatch(defaultSqlUpdate, transaction);
    }
    @Override
    public int executeNow(SpiSqlUpdate sqlUpdate) {
        return database.executeNow(sqlUpdate);
    }
    @Override
    public <T> boolean exists(SpiQuery<T> ormQuery) {
        return database.exists(ormQuery);
    }
    @Override
    public <T> int findCount(SpiQuery<T> query) {
        return database.findCount(query);
    }
    @Override
    public <A, T> List<A> findIds(SpiQuery<T> query) {
        return database.findIds(query);
    }
    @Override
    public <T> QueryIterator<T> findIterate(SpiQuery<T> query) {
        return database.findIterate(query);
    }
    @Override
    public <T> Stream<T> findStream(SpiQuery<T> query) {
        return database.findStream(query);
    }
    @Override
    public <T> void findEach(SpiQuery<T> query, Consumer<T> consumer) {
        database.findEach(query, consumer);
    }
    @Override
    public <T> void findEach(SpiQuery<T> query, int batch, Consumer<List<T>> consumer) {
        database.findEach(query, batch, consumer);
    }
    @Override
    public <T> void findEachWhile(SpiQuery<T> query, Predicate<T> consumer) {
        database.findEachWhile(query, consumer);
    }
    @Override
    public <T> List<Version<T>> findVersions(SpiQuery<T> query) {
        return database.findVersions(query);
    }
    @Override
    public <T> List<T> findList(SpiQuery<T> query) {
        return database.findList(query);
    }
    @Override
    public <T> FutureRowCount<T> findFutureCount(SpiQuery<T> query) {
        return database.findFutureCount(query);
    }
    @Override
    public <T> FutureIds<T> findFutureIds(SpiQuery<T> query) {
        return database.findFutureIds(query);
    }
    @Override
    public <T> FutureList<T> findFutureList(SpiQuery<T> query) {
        return database.findFutureList(query);
    }
    @Override
    public <K, T> FutureMap<K, T> findFutureMap(SpiQuery<T> query) {
        return database.findFutureMap(query);
    }
    @Override
    public <T> Query<T> find(Class<T> beanType) {
        return database.find(beanType);
    }
    @Override
    public <T> PagedList<T> findPagedList(SpiQuery<T> query) {
        return database.findPagedList(query);
    }
    @Override
    public <T> Set<T> findSet(SpiQuery<T> query) {
        return database.findSet(query);
    }
    @Override
    public <K, T> Map<K, T> findMap(SpiQuery<T> query) {
        return database.findMap(query);
    }
    @Override
    public <A, T> List<A> findSingleAttributeList(SpiQuery<T> query) {
        return database.findSingleAttributeList(query);
    }
    @Override
    public <A, T> Set<A> findSingleAttributeSet(SpiQuery<T> query) {
        return database.findSingleAttributeSet(query);
    }
    @Override
    public <T> T findOne(SpiQuery<T> query) {
        return database.findOne(query);
    }
    @Override
    public <T> Optional<T> findOneOrEmpty(SpiQuery<T> query) {
        return database.findOneOrEmpty(query);
    }
    @Override
    public <T> int delete(SpiQuery<T> query) {
        return database.delete(query);
    }
    @Override
    public <T> int update(SpiQuery<T> query) {
        return database.update(query);
    }
    @Override
    public List<SqlRow> findList(SpiSqlQuery query) {
        return database.findList(query);
    }
    @Override
    public void findEach(SpiSqlQuery query, Consumer<SqlRow> consumer) {
        database.findEach(query, consumer);
    }
    @Override
    public void findEachWhile(SpiSqlQuery query, Predicate<SqlRow> consumer) {
        database.findEachWhile(query, consumer);
    }
    @Override
    public SqlRow findOne(SpiSqlQuery query) {
        return database.findOne(query);
    }
    @Override
    public <T> Query<T> findNative(Class<T> beanType, String nativeSql) {
        return database.findNative(beanType, nativeSql);
    }
    @Override
    public Object nextId(Class<?> beanType) {
        return database.nextId(beanType);
    }
    @Override
    public <T> void sort(List<T> list, String sortByClause) {
        database.sort(list, sortByClause);
    }
    @Override
    public <T> Update<T> createUpdate(Class<T> beanType, String ormUpdate) {
        return database.createUpdate(beanType, ormUpdate);
    }
    @Override
    public <T> DtoQuery<T> findDto(Class<T> dtoType, String sql) {
        return database.findDto(dtoType, sql);
    }
    @Override
    public <T> DtoQuery<T> createNamedDtoQuery(Class<T> dtoType, String namedQuery) {
        return database.createNamedDtoQuery(dtoType, namedQuery);
    }
    @Override
    public CallableSql createCallableSql(String callableSql) {
        return database.createCallableSql(callableSql);
    }
    @Override
    public void refresh(Object bean) {
        database.refresh(bean);
    }
    @Override
    public void refreshMany(Object bean, String propertyName) {
        database.refreshMany(bean, propertyName);
    }
    @Override
    public <T> T find(Class<T> beanType, Object id) {
        return database.find(beanType, id);
    }
    @Override
    public <T> T reference(Class<T> beanType, Object id) {
        return database.reference(beanType, id);
    }
    @Override
    public void save(Object bean) throws OptimisticLockException {
        database.save(bean);
    }
    @Override
    public int saveAll(Collection<?> beans) throws OptimisticLockException {
        return database.saveAll(beans);
    }
    @Override
    public int saveAll(Object... beans) throws OptimisticLockException {
        return database.saveAll(beans);
    }
    @Override
    public boolean delete(Object bean) throws OptimisticLockException {
        return database.delete(bean);
    }
    @Override
    public boolean delete(Object bean, Transaction transaction) throws OptimisticLockException {
        return database.delete(bean, transaction);
    }
    @Override
    public boolean deletePermanent(Object bean) throws OptimisticLockException {
        return database.deletePermanent(bean);
    }
    @Override
    public boolean deletePermanent(Object bean, Transaction transaction) throws OptimisticLockException {
        return database.deletePermanent(bean, transaction);
    }
    @Override
    public int deleteAllPermanent(Collection<?> beans) throws OptimisticLockException {
        return database.deleteAllPermanent(beans);
    }
    @Override
    public int deleteAllPermanent(Collection<?> beans, Transaction transaction) throws OptimisticLockException {
        return database.deleteAllPermanent(beans, transaction);
    }
    @Override
    public int delete(Class<?> beanType, Object id) {
        return database.delete(beanType, id);
    }
    @Override
    public int delete(Class<?> beanType, Object id, Transaction transaction) {
        return database.delete(beanType, id, transaction);
    }
    @Override
    public int deletePermanent(Class<?> beanType, Object id) {
        return database.deletePermanent(beanType, id);
    }
    @Override
    public int deletePermanent(Class<?> beanType, Object id, Transaction transaction) {
        return database.deletePermanent(beanType, id, transaction);
    }
    @Override
    public int deleteAll(Collection<?> beans) throws OptimisticLockException {
        return database.deleteAll(beans);
    }
    @Override
    public int deleteAll(Collection<?> beans, Transaction transaction) throws OptimisticLockException {
        return database.deleteAll(beans, transaction);
    }
    @Override
    public int deleteAll(Class<?> beanType, Collection<?> ids) {
        return database.deleteAll(beanType, ids);
    }
    @Override
    public int deleteAll(Class<?> beanType, Collection<?> ids, Transaction transaction) {
        return database.deleteAll(beanType, ids, transaction);
    }
    @Override
    public int deleteAllPermanent(Class<?> beanType, Collection<?> ids) {
        return database.deleteAllPermanent(beanType, ids);
    }
    @Override
    public int deleteAllPermanent(Class<?> beanType, Collection<?> ids, Transaction transaction) {
        return database.deleteAllPermanent(beanType, ids, transaction);
    }
    @Override
    public int execute(SqlUpdate sqlUpdate) {
        return database.execute(sqlUpdate);
    }
    @Override
    public int execute(Update<?> update) {
        return database.execute(update);
    }
    @Override
    public int execute(Update<?> update, Transaction transaction) {
        return database.execute(update, transaction);
    }
    @Override
    public int execute(CallableSql callableSql) {
        return database.execute(callableSql);
    }
    @Override
    public <T> T find(Class<T> beanType, Object id, Transaction transaction) {
        return database.find(beanType, id, transaction);
    }
    @Override
    public void save(Object bean, Transaction transaction) throws OptimisticLockException {
        database.save(bean, transaction);
    }
    @Override
    public int saveAll(Collection<?> beans, Transaction transaction) throws OptimisticLockException {
        return database.saveAll(beans, transaction);
    }
    @Override
    public Set<Property> checkUniqueness(Object bean) {
        return database.checkUniqueness(bean);
    }
    @Override
    public Set<Property> checkUniqueness(Object bean, Transaction transaction) {
        return database.checkUniqueness(bean, transaction);
    }
    @Override
    public void update(Object bean) throws OptimisticLockException {
        database.update(bean);
    }
    @Override
    public void update(Object bean, Transaction transaction) throws OptimisticLockException {
        database.update(bean, transaction);
    }
    @Override
    public void updateAll(Collection<?> beans) throws OptimisticLockException {
        database.updateAll(beans);
    }
    @Override
    public void updateAll(Collection<?> beans, Transaction transaction) throws OptimisticLockException {
        database.updateAll(beans, transaction);
    }
    @Override
    public void merge(Object bean) {
        database.merge(bean);
    }
    @Override
    public void merge(Object bean, MergeOptions options) {
        database.merge(bean, options);
    }
    @Override
    public void merge(Object bean, MergeOptions options, Transaction transaction) {
        database.merge(bean, options, transaction);
    }
    @Override
    public void insert(Object bean) {
        database.insert(bean);
    }
    @Override
    public void insert(Object bean, InsertOptions insertOptions) {
        database.insert(bean, insertOptions);
    }
    @Override
    public void insert(Object bean, Transaction transaction) {
        database.insert(bean, transaction);
    }
    @Override
    public void insert(Object bean, InsertOptions insertOptions, Transaction transaction) {
        database.insert(bean, insertOptions, transaction);
    }
    @Override
    public void insertAll(Collection<?> beans) {
        database.insertAll(beans);
    }
    @Override
    public void insertAll(Collection<?> beans, InsertOptions options) {
        database.insertAll(beans, options);
    }
    @Override
    public void insertAll(Collection<?> beans, Transaction transaction) {
        database.insertAll(beans, transaction);
    }
    @Override
    public void insertAll(Collection<?> beans, InsertOptions options, Transaction transaction) {
        database.insertAll(beans, options, transaction);
    }
    @Override
    public int execute(SqlUpdate updSql, Transaction transaction) {
        return database.execute(updSql, transaction);
    }
    @Override
    public int execute(CallableSql callableSql, Transaction transaction) {
        return database.execute(callableSql, transaction);
    }
    @Override
    public <T> T publish(Class<T> beanType, Object id, Transaction transaction) {
        return database.publish(beanType, id, transaction);
    }
    @Override
    public <T> T publish(Class<T> beanType, Object id) {
        return database.publish(beanType, id);
    }
    @Override
    public <T> List<T> publish(Query<T> query, Transaction transaction) {
        return database.publish(query, transaction);
    }
    @Override
    public <T> List<T> publish(Query<T> query) {
        return database.publish(query);
    }
    @Override
    public <T> T draftRestore(Class<T> beanType, Object id, Transaction transaction) {
        return database.draftRestore(beanType, id, transaction);
    }
    @Override
    public <T> T draftRestore(Class<T> beanType, Object id) {
        return database.draftRestore(beanType, id);
    }
    @Override
    public <T> List<T> draftRestore(Query<T> query, Transaction transaction) {
        return database.draftRestore(query, transaction);
    }
    @Override
    public <T> List<T> draftRestore(Query<T> query) {
        return database.draftRestore(query);
    }
    @Override
    public <T> Set<String> validateQuery(Query<T> query) {
        return database.validateQuery(query);
    }
    @Override
    public void lock(Object bean) {
        database.lock(bean);
    }
    @Override
    public void truncate(String... tables) {
        database.truncate(tables);
    }
    @Override
    public void truncate(Class<?>... beanTypes) {
        database.truncate(beanTypes);
    }
    private void preventNotReadOnly() {
        //
        // ~ prevent abuses (do not call RW transaction from readOnly operations)
        //
        if (Boolean.TRUE.equals(RequestHandler.READ_ONLY_MARKER.get())) {
            var err = new IllegalStateException("api abuse detected: write transaction in read only handler, check full stacktrace");
            log.error(err.getMessage(), err);

            //
            // ~ should not happen - prevent as much as we can, but not break production
            //
            if (isDevMode()) {
                throw err;
            }
        }
    }
    private void preventTransactionNesting() {
        var manager = database.transactionManager();
        var current = manager.active();
        if (Objects.nonNull(current) && current.isActive()) {
            var err = new IllegalStateException("transaction abuse detected: nested transaction usage");
            log.error(err.getMessage(), err);
            //
            // ~ should not happen - prevent as much as we can, but not break production
            //
            if (isDevMode()) {
                throw err;
            }
        }
    }
    private Span span(boolean readOnly) {
        //
        // ~ capture stack frame from which we are calling new transaction
        //
        var opt = PlatformUtil.currentOperation(getClass());
        Span span = tracer.buildSpan(opt.map(new Function<StackFrame, String>() {
            @Override
            public String apply(StackFrame frame) {
                return String.format("%s:%s", frame.getDeclaringClass().getSimpleName(), frame.getMethodName());
            }
        }).orElse("jdbc_tx_unknown")).start();

        //
        // ~ put debug information at the begging of transaction
        //
        opt.ifPresent(new Consumer<StackFrame>() {
            @Override
            public void accept(StackFrame frame) {
                span.setBaggageItem(TAG_READ_ONLY, Boolean.toString(readOnly));
                span.setBaggageItem(TAG_CLASS_NAME, frame.getDeclaringClass().getSimpleName());
                span.setBaggageItem(TAG_METHOD_NAME, frame.getMethodName());
                span.setBaggageItem(TAG_LINE_NUMBER, Integer.toString(frame.getLineNumber()));

                try {
                    var toAdd = frames.get(Thread.currentThread(), new Callable<>() {
                        @Override
                        public Builder<StackFrame> call() throws Exception {
                            return ImmutableList.builder();
                        }
                    });
                    if (Objects.nonNull(toAdd)) {
                        toAdd.add(frame);
                    }
                } catch (ExecutionException err) {
                    ExceptionUtils.wrapAndThrow(err.getCause());
                }
            }
        });

        //
        // ~ should not happen - prevent as much as we can, but not break production
        //
        if (opt.isEmpty()) {
            if (isDevMode()) {
                Method method = new Object() {}.getClass().getEnclosingMethod();
                throw new IllegalStateException("unable to derive stack information, current method: " + method);
            }
        }
        return span;
    }
}
