package com.turbospaces.ebean;

import javax.sql.DataSource;

import org.apache.commons.lang3.BooleanUtils;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.ebean.config.DatabaseConfig;
import lombok.experimental.Delegate;

public class EbeanDatabaseConfig extends DatabaseConfig implements EbeanCacheConfigurer {
    @Delegate
    private final EbeanCacheConfigurer configurer;
    protected final JsonMapper mapper = new JsonMapper();

    public EbeanDatabaseConfig(DataSource ds, ApplicationProperties props) {
        this.configurer = new DefaultEbeanCacheConfigurer(props);

        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.setSerializationInclusion(Include.NON_NULL);
        mapper.registerModule(new GuavaModule());
        mapper.registerModule(new JavaTimeModule());

        setDefaultServer(props.JDBC_DEFAULT_INSTANCE_REGISTER.get());
        setName("db" + PlatformUtil.randomUUID());
        setDataSource(ds);

        //
        // ~ used for JSON + JSONB mapping
        //
        setObjectMapper(mapper);

        //
        // ~ hack due to deprecated API
        //
        JsonMapper.Builder edit = new JsonMapper.Builder(mapper);
        edit.disable(MapperFeature.CAN_OVERRIDE_ACCESS_MODIFIERS);
        edit.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS);

        setLoadModuleInfo(false); // ~ do not scan module info by default
        setDisableClasspathSearch(true); // ~ do not scan all entities by default
        setNotifyL2CacheInForeground(true); // ~ do not block current thread
        setSkipCacheAfterWrite(true); // ~ do not use l2 cache after any commit

        //
        // ~ batch
        //
        setDatabaseSequenceBatchSize(props.JDBC_LAZY_SEQUENCE_BATCH_SIZE.get());
        setPersistBatchSize(props.JDBC_PERSIST_BATCH_SIZE.get());
        setQueryBatchSize(props.JDBC_QUERY_BATCH_SIZE.get());
        setLazyLoadBatchSize(props.JDBC_LAZY_BATCH_SIZE.get());
        setDumpMetricsOnShutdown(false);

        //
        // ~ cache
        //
        setDisableL2Cache(BooleanUtils.isFalse(props.CACHE_ENABLED.get()));
        setCacheMaxIdleTime((int) props.CACHE_DEFAULT_MAX_IDLE.get().toSeconds());
        setCacheMaxSize(props.CACHE_DEFAULT_MAX_SIZE.get());
        setQueryCacheMaxTimeToLive((int) props.CACHE_DEFAULT_MAX_TTL.get().toSeconds());
        setQueryCacheMaxIdleTime((int) props.CACHE_DEFAULT_MAX_IDLE.get().toSeconds());
        setQueryCacheMaxSize(props.CACHE_DEFAULT_MAX_SIZE.get());

        if (props.JDBC_SLOW_QUERY_LOGGER_ENABLE.get()) {
            setSlowQueryMillis(props.JDBC_SLOW_QUERY_LOGGER_MILLIS.get());
            setSlowQueryListener(new EbeanSlowQueryListener());
        }
    }
}
