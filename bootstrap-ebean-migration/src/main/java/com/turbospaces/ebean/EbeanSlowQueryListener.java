package com.turbospaces.ebean;

import java.util.Arrays;

import org.slf4j.MDC;

import com.turbospaces.mdc.MdcTags;

import io.ebean.config.SlowQueryEvent;
import io.ebean.config.SlowQueryListener;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EbeanSlow<PERSON>ueryListener implements SlowQueryListener {
    @Override
    public void process(SlowQueryEvent event) {
        String prevTook = MDC.get(MdcTags.MDC_TOOK);
        MDC.put(MdcTags.MDC_TOOK, String.valueOf(event.getTimeMillis()));
        try {
            String caller = Arrays.stream(Thread.currentThread().getStackTrace())
                    .filter(v -> v.getClassName().endsWith("Repo"))
                    .findFirst()
                    .map(v -> v.getClassName() + "_" + v.getMethodName())
                    .orElse(event.getOriginNode().origin().beanType());

            MDC.put(MdcTags.MDC_QUERY_LABEL, caller);

            log.warn("Slow query warning - millis:{} rows:{} caller[{}] sql[{}] ",
                    event.getTimeMillis(), event.getRowCount(), caller,
                    event.getSql());
        } finally {
            if (prevTook != null) {
                MDC.put(MdcTags.MDC_TOOK, prevTook);
            } else {
                MDC.remove(MdcTags.MDC_TOOK);
            }

            MDC.remove(MdcTags.MDC_QUERY_LABEL);
        }
    }
}
