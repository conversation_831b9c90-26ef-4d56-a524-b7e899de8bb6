package com.turbospaces.ebean;

import java.io.Serializable;
import java.util.Date;

import io.ebean.annotation.WhenCreated;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
public abstract class AbstractSeq implements Serializable {
    @Column
    private long startInterval;

    @Column
    private long endInterval;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    public abstract long getId();
    public abstract void setId(long id);
}
