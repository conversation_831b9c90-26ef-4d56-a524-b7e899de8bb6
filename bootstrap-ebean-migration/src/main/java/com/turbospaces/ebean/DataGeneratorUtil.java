package com.turbospaces.ebean;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import jakarta.persistence.Table;
import lombok.SneakyThrows;

public class DataGeneratorUtil {
    @SneakyThrows(SQLException.class)
    public static void clearH2Db(EbeanJpaManager ebean, String[] schemas) {
        ebean.cacheManager().clearAll();
        try (var conn = ebean.dataSource().getConnection()) {
            conn.createStatement().executeUpdate("SET REFERENTIAL_INTEGRITY FALSE");
            for (String schema : schemas) {
                ResultSet rs = conn.createStatement().executeQuery(String.format(
                        "SELECT table_name FROM information_schema.tables WHERE table_schema = '%s'",
                        schema.toUpperCase()));
                while (rs.next()) {
                    String tableName = rs.getString(1);
                    if (tableName.endsWith("WITH_HISTORY")) {
                        // dirty hack, those are tables generated by flyway for history tracking, error when trying to truncate
                        continue;
                    }
                    conn.createStatement().executeUpdate("SET SCHEMA " + schema.toUpperCase());
                    conn.createStatement().executeUpdate("TRUNCATE TABLE \"" + tableName + "\" RESTART IDENTITY");
                }
            }
            conn.createStatement().executeUpdate("SET REFERENTIAL_INTEGRITY TRUE");
        }
    }

    @SneakyThrows(SQLException.class)
    public static void clearPostgresDb(EbeanJpaManager ebean, List<Class<?>> entities) {
        ebean.cacheManager().clearAll();
        try (Connection connection = ebean.dataSource().getConnection()) {
            for (Class<?> clazz : entities) {
                Table table = clazz.getAnnotation(Table.class);
                String query = String.format("truncate table %s.%s cascade", table.schema(), table.name());
                try (PreparedStatement stmt = connection.prepareStatement(query)) {
                    stmt.execute();
                }
            }
            connection.commit();
        } finally {
            ebean.cacheManager().clearAll();
        }
    }
}
