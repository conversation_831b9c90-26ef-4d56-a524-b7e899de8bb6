package com.turbospaces.ebean;

import io.ebean.Database;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.MeterBinder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class EbeanMetrics implements MeterBinder {
    public static final String EBEAN_QUERY_METRIC_NAME = "ebean-query";
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final Database db;
    private final ScheduledExecutorService scheduler;
    private final Duration interval;

    public EbeanMetrics(Database db, ScheduledExecutorService scheduler, Duration interval) {
        this.db = Objects.requireNonNull(db);
        this.scheduler = Objects.requireNonNull(scheduler);
        this.interval = Objects.requireNonNull(interval);
    }

    @Override
    public void bindTo(MeterRegistry registry) {
        scheduler.scheduleWithFixedDelay(() -> {
            QueryMetricVisitor queryMetricVisitor = new QueryMetricVisitor();
            // very important line, if collecting all metrics, all will be cleared, which will conflict with cache metrics
            db.metaInfo().visitMetrics(queryMetricVisitor);
            queryMetricVisitor.queryMetrics().forEach(metric -> {
                List<Tag> tags = Arrays.asList(
                        tag("name", metric.name()),
                        tag("label", metric.label()),
                        tag("hash", metric.hash()),
                        tag("type", type(metric.sql())));
                var len = metric.count();
                // record max
                if (len > 0) {
                    registry.summary(EBEAN_QUERY_METRIC_NAME, tags).record(metric.max());
                    len--;
                }
                // record mean
                if (len > 0) {
                    registry.summary(EBEAN_QUERY_METRIC_NAME, tags).record(metric.mean());
                    len--;
                }
                // record other counters as mean
                for (int i = 0; i < len; i++) {
                    registry.summary(EBEAN_QUERY_METRIC_NAME, tags).record(metric.mean());
                }

                logger.info("ebean query [label: {}, hash: {}, count: {}, mean: {}, max: {}, total: {}]: {}",
                        metric.label(),
                        metric.hash(),
                        metric.count(),
                        metric.mean(),
                        metric.max(),
                        metric.total(),
                        metric.sql());
            });
        }, 0, interval.toMillis(), TimeUnit.MILLISECONDS);
    }

    private static Tag tag(String name, String value) {
        return Tag.of(name, StringUtils.isEmpty(value) ? "none" : value);
    }

    private static String type(String sql) {
        return sql.split(" ", 2)[0]; // ~ TODO magic ???
    }
}
