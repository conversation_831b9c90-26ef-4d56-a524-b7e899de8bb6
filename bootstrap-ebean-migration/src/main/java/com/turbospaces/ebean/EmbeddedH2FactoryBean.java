package com.turbospaces.ebean;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Objects;

import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

public class EmbeddedH2FactoryBean extends AbstractFactoryBean<HikariDataSource> {
    public static final String NAMESPACE_JUNIT = "junit";
    private final String namespace;
    private final String[] schemas;

    public EmbeddedH2FactoryBean(String namespace, String[] schemas) {
        this.schemas = Objects.requireNonNull(schemas);
        this.namespace = namespace;
    }
    public EmbeddedH2FactoryBean(String[] schemas) {
        this.schemas = Objects.requireNonNull(schemas);
        this.namespace = NAMESPACE_JUNIT;
    }
    @Override
    public Class<?> getObjectType() {
        return HikariDataSource.class;
    }
    @Override
    protected HikariDataSource createInstance() throws Exception {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:h2:mem:" + namespace + ";DB_CLOSE_DELAY=0;LOCK_MODE=0");
        config.setDriverClassName("org.h2.Driver");

        HikariDataSource ds = new HikariDataSource(config);
        createSchemas(ds);

        return ds;
    }
    @Override
    protected void destroyInstance(HikariDataSource instance) throws Exception {
        if (Objects.nonNull(instance)) {
            instance.close();
        }
    }
    public void createSchemas() throws Exception {
        createSchemas(getObject());
    }
    private void createSchemas(HikariDataSource ds) throws Exception {
        if (ds.isRunning()) {
            try (Connection conn = ds.getConnection()) {
                conn.setAutoCommit(false);

                for (String schema : schemas) {
                    try (Statement stmt = conn.createStatement()) {
                        stmt.execute(String.format("create schema if not exists %s", schema));
                    }
                }

                conn.commit();
            }
        }
    }
}
