package com.turbospaces.ebean;

import java.util.Objects;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.cache.ServerCacheType;
import io.ebean.typequery.TQAssocBean;

public class DefaultEbeanCacheConfigurer implements EbeanCacheConfigurer {
    private final ApplicationProperties props;

    public DefaultEbeanCacheConfigurer(ApplicationProperties props) {
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public void setReplicated(Class<?> clazz) {
        String beanPrefix = clazz.getName() + ServerCacheType.BEAN.code();
        String naturalKeyPrefix = clazz.getName() + ServerCacheType.NATURAL_KEY.code();

        props.cfg().setDefaultProperty(beanPrefix + "." + CACHE_MODE_LOCAL, false);
        props.cfg().setDefaultProperty(naturalKeyPrefix + "." + CACHE_MODE_LOCAL, false);
    }
    @Override
    public void setLocal(Class<?> clazz) {
        String beanPrefix = clazz.getName() + ServerCacheType.BEAN.code();
        String naturalKeyPrefix = clazz.getName() + ServerCacheType.NATURAL_KEY.code();

        props.cfg().setDefaultProperty(beanPrefix + "." + CACHE_MODE_LOCAL, true);
        props.cfg().setDefaultProperty(naturalKeyPrefix + "." + CACHE_MODE_LOCAL, true);
    }
    @Override
    public void setReplicated(Class<?> clazz, TQAssocBean<?, ?, ?> assoc) {
        String prefix = clazz.getName() + "." + assoc.toString() + ServerCacheType.COLLECTION_IDS.code();
        props.cfg().setDefaultProperty(prefix + "." + CACHE_MODE_LOCAL, false);
    }
    @Override
    public void setLocal(Class<?> clazz, TQAssocBean<?, ?, ?> assoc) {
        String prefix = clazz.getName() + "." + assoc.toString() + ServerCacheType.COLLECTION_IDS.code();
        props.cfg().setDefaultProperty(prefix + "." + CACHE_MODE_LOCAL, true);
    }
    @Override
    public void setMaxSize(Class<?> clazz, int value) {
        String beanPrefix = clazz.getName() + ServerCacheType.BEAN.code();
        String naturalKeyPrefix = clazz.getName() + ServerCacheType.NATURAL_KEY.code();

        props.cfg().setDefaultProperty(beanPrefix + "." + MAX_SIZE, value);
        props.cfg().setDefaultProperty(naturalKeyPrefix + "." + MAX_SIZE, value);
    }
    @Override
    public void setMaxTTL(Class<?> clazz, int value) {
        String prefix = clazz.getName() + ServerCacheType.BEAN.code();
        props.cfg().setDefaultProperty(prefix + "." + MAX_TTL, value);
    }
    @Override
    public void setMaxIdle(Class<?> clazz, int value) {
        String prefix = clazz.getName() + ServerCacheType.BEAN.code();
        props.cfg().setDefaultProperty(prefix + "." + MAX_IDLE, value);
    }
    @Override
    public void setMaxSize(Class<?> clazz, TQAssocBean<?, ?, ?> assoc, int value) {
        String prefix = clazz.getName() + "." + assoc.toString() + ServerCacheType.COLLECTION_IDS.code();
        props.cfg().setDefaultProperty(prefix + "." + MAX_SIZE, value);
    }
    @Override
    public void setMaxTTL(Class<?> clazz, TQAssocBean<?, ?, ?> assoc, int value) {
        String prefix = clazz.getName() + "." + assoc.toString() + ServerCacheType.COLLECTION_IDS.code();
        props.cfg().setDefaultProperty(prefix + "." + MAX_TTL, value);
    }
    @Override
    public void setMaxIdle(Class<?> clazz, TQAssocBean<?, ?, ?> assoc, int value) {
        String prefix = clazz.getName() + "." + assoc.toString() + ServerCacheType.COLLECTION_IDS.code();
        props.cfg().setDefaultProperty(prefix + "." + MAX_IDLE, value);
    }
    @Override
    public void setMaxSizeQuery(Class<?> clazz, int value) {
        String prefix = clazz.getName() + ServerCacheType.QUERY.code();
        props.cfg().setDefaultProperty(prefix + "." + MAX_SIZE, value);
    }
}
