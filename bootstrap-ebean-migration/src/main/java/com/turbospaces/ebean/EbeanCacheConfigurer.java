package com.turbospaces.ebean;

import io.ebean.typequery.TQAssocBean;

public interface EbeanCacheConfigurer {
    String MAX_SIZE = "max-size";
    String MAX_TTL = "max-ttl";
    String MAX_IDLE = "max-idle";
    String CACHE_MODE_LOCAL = "cache-mode-local";

    void setLocal(Class<?> clazz);
    void setReplicated(Class<?> clazz);
    void setMaxSize(Class<?> clazz, int value);
    void setMaxTTL(Class<?> clazz, int value);
    void setMaxIdle(Class<?> clazz, int value);

    void setReplicated(Class<?> clazz, TQAssocBean<?, ?, ?> assoc);
    void setLocal(Class<?> clazz, TQAssocBean<?, ?, ?> assoc);
    void setMaxSize(Class<?> clazz, TQAssocBean<?, ?, ?> assoc, int value);
    void setMaxTTL(Class<?> clazz, TQAssocBean<?, ?, ?> assoc, int value);
    void setMaxIdle(Class<?> clazz, TQAssocBean<?, ?, ?> assoc, int value);

    void setMaxSizeQuery(Class<?> clazz, int value);
}
