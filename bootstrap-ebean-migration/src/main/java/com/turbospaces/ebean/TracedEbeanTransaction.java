package com.turbospaces.ebean;

import java.lang.StackWalker.StackFrame;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.cache.Cache;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableList.Builder;
import com.google.common.hash.Hashing;
import com.turbospaces.boot.DevMode;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.PlatformThread;
import com.turbospaces.mdc.MdcTags;

import io.ebean.ProfileLocation;
import io.ebean.TransactionCallback;
import io.ebean.annotation.DocStoreMode;
import io.ebean.event.changelog.BeanChange;
import io.ebean.event.changelog.ChangeSet;
import io.ebeaninternal.api.SpiPersistenceContext;
import io.ebeaninternal.api.SpiProfileTransactionEvent;
import io.ebeaninternal.api.SpiTransaction;
import io.ebeaninternal.api.TransactionEvent;
import io.ebeaninternal.server.core.PersistDeferredRelationship;
import io.ebeaninternal.server.core.PersistRequestBean;
import io.ebeaninternal.server.persist.BatchControl;
import io.ebeaninternal.server.transaction.ProfileStream;
import io.ebeanservice.docstore.api.DocStoreTransaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import io.opentracing.Scope;
import io.opentracing.Span;
import jakarta.persistence.PersistenceException;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public final class TracedEbeanTransaction implements SpiTransaction, Scope, DevMode {
    private final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    private final Supplier<SpiTransaction> supplier;
    private final Cache<Thread, Builder<StackFrame>> frames;
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final Span span;
    private final StopWatch stopWatch;
    private final Thread originThread;
    private boolean revertToSavepoint;
    private String id;

    public TracedEbeanTransaction(
            java.util.function.Supplier<SpiTransaction> original,
            Cache<Thread, Builder<StackFrame>> frames,
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            Span span) {
        this.frames = Objects.requireNonNull(frames);
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.span = Objects.requireNonNull(span);
        this.stopWatch = StopWatch.createStarted();
        this.originThread = Thread.currentThread();
        this.supplier = Suppliers.memoize(new Supplier<SpiTransaction>() {
            @Override
            public SpiTransaction get() {
                SpiTransaction tx = original.get();
                id = tx.id();
                if (StringUtils.isEmpty(id)) {
                    id = Hashing.murmur3_32_fixed().newHasher().putBytes(originThread.getName().getBytes()).putInt(tx.hashCode()).hash().toString();
                }
                log.debug("begin tx[{}] ...", id);
                return tx;
            }
        });

        nonBlocking.set(true);
    }
    @Override
    public boolean isDevMode() {
        return props.isDevMode();
    }
    @Override
    public void close() {
        MDC.put(MdcTags.MDC_TOOK, String.valueOf(stopWatch.getDuration().toMillis()));

        Thread currentThread = Thread.currentThread();

        try {
            var tx = get();
            if (StringUtils.isEmpty(id)) {
                id = Hashing.murmur3_32_fixed().newHasher().putBytes(originThread.getName().getBytes()).putInt(tx.hashCode()).hash().toString();
            }

            //
            // ~ delegate to ebean transaction which closes underlying JDBC connection
            //
            tx.close();

            if (stopWatch.isStarted()) {
                stopWatch.stop();
            }
            span.finish();

            if (log.isDebugEnabled()) {
                String tag = span.getBaggageItem(EbeanJpaManager.TAG_READ_ONLY);

                //
                // ~ attempt to pretty print transaction information
                //
                if (StringUtils.isNotEmpty(tag)) {
                    Boolean readOnly = Boolean.parseBoolean(tag);
                    String classNameFrom = span.getBaggageItem(EbeanJpaManager.TAG_CLASS_NAME);
                    String methodNameFrom = span.getBaggageItem(EbeanJpaManager.TAG_METHOD_NAME);
                    int lineNumberFrom = Integer.parseInt(span.getBaggageItem(EbeanJpaManager.TAG_LINE_NUMBER));
                    String debugFrom = String.format("%s.%s:%d", classNameFrom, methodNameFrom, lineNumberFrom);

                    Optional<StackFrame> opt = PlatformUtil.currentOperation(getClass());
                    opt.ifPresent(new Consumer<StackFrame>() {
                        @Override
                        public void accept(StackFrame frame) {
                            String classNameTo = frame.getDeclaringClass().getSimpleName();
                            String methodNameTo = frame.getMethodName();
                            int lineNumberTo = frame.getLineNumber();
                            String debugTo = String.format("%s.%s:%d", classNameTo, methodNameTo, lineNumberTo);

                            log.debug("closed ebean transaction tx({}):({} => {}), readOnly: {}, took: {}",
                                    id,
                                    debugFrom,
                                    debugTo,
                                    readOnly,
                                    stopWatch);
                        }
                    });

                    //
                    // ~ should not happen - prevent as much as we can, but not break production
                    //
                    if (opt.isEmpty()) {
                        if (isDevMode()) {
                            Method method = new Object() {}.getClass().getEnclosingMethod();
                            throw new IllegalStateException("unable to derive stack information, current method: " + method);
                        }
                    }
                } else {
                    log.debug("closed ebean transaction txId: {}, took: {}", id, stopWatch);
                }
            }

            //
            // ~ well we are not very interested in non platform threads
            //
            if (BooleanUtils.isFalse(currentThread instanceof PlatformThread)) {
                frames.invalidate(currentThread);
            }

            reportMetrics();
        } finally {
            MDC.remove(MdcTags.MDC_TOOK);
            nonBlocking.set(revertToSavepoint);
        }
    }
    private void reportMetrics() {
        ImmutableList.Builder<Tag> tags = ImmutableList.builder();
        String operation = MDC.get(MdcTags.MDC_OPERATION);

        if (StringUtils.isNotEmpty(operation)) {
            tags.add(Tag.of(MdcTags.MDC_OPERATION, operation));
        }

        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                Timer timer = meterRegistry.timer("tx.time", tags.build());
                timer.record(stopWatch.getDuration());
            }
        });
    }
    @Override
    public String label() {
        return get().label();
    }
    @Override
    public boolean isLogSql() {
        return get().isLogSql();
    }
    @Override
    public boolean isLogSummary() {
        return get().isLogSummary();
    }
    @Override
    public void logSql(String msg, Object... args) {
        get().logSql(msg, args);
    }
    @Override
    public void logSummary(String msg, Object... args) {
        get().logSummary(msg, args);
    }
    @Override
    public void logTxn(String msg, Object... args) {
        get().logTxn(msg, args);
    }
    @Override
    public void registerDeferred(PersistDeferredRelationship derived) {
        get().registerDeferred(derived);
    }
    @Override
    public void register(TransactionCallback callback) {
        get().register(callback);
    }
    @Override
    public void registerDeleteBean(Integer hash) {
        get().registerDeleteBean(hash);
    }
    @Override
    public void setAutoPersistUpdates(boolean autoPersistUpdates) {
        get().setAutoPersistUpdates(autoPersistUpdates);
    }
    @Override
    public boolean isRegisteredDeleteBean(Integer hash) {
        return get().isRegisteredDeleteBean(hash);
    }
    @Override
    public void unregisterBeans() {
        get().unregisterBeans();
    }
    @Override
    public void setLabel(String label) {
        get().setLabel(label);
    }
    @Override
    public boolean isRegisteredBean(Object bean) {
        return get().isRegisteredBean(bean);
    }
    @Override
    public boolean isReadOnly() {
        return get().isReadOnly();
    }
    @Override
    public void setReadOnly(boolean readOnly) {
        get().setReadOnly(readOnly);
    }
    @Override
    public void commitAndContinue() {
        get().commitAndContinue();
    }
    @Override
    public String id() {
        return id;
    }
    @Override
    public Instant startTime() {
        return get().startTime();
    }
    @Override
    public Boolean isUpdateAllLoadedProperties() {
        return get().isUpdateAllLoadedProperties();
    }
    @Override
    public DocStoreMode docStoreMode() {
        return get().docStoreMode();
    }
    @Override
    public void commit() {
        get().commit();
    }
    @Override
    public int getDocStoreBatchSize() {
        return get().getDocStoreBatchSize();
    }
    @Override
    public int getBatchSize() {
        return get().getBatchSize();
    }
    @Override
    public Boolean getBatchGetGeneratedKeys() {
        return get().getBatchGetGeneratedKeys();
    }
    @Override
    public void depth(int diff) {
        get().depth(diff);
    }
    @Override
    public void rollback() throws PersistenceException {
        get().rollback();
    }
    @Override
    public void depthDecrement() {
        get().depthDecrement();
    }
    @Override
    public void rollback(Throwable e) throws PersistenceException {
        get().rollback(e);
    }
    @Override
    public void depthReset() {
        get().depthReset();
    }
    @Override
    public int depth() {
        return get().depth();
    }
    @Override
    public void rollbackAndContinue() {
        get().rollbackAndContinue();
    }
    @Override
    public boolean isAutoPersistUpdates() {
        return get().isAutoPersistUpdates();
    }
    @Override
    public boolean isExplicit() {
        return get().isExplicit();
    }
    @Override
    public TransactionEvent event() {
        return get().event();
    }
    @Override
    public boolean isPersistCascade() {
        return get().isPersistCascade();
    }
    @Override
    public boolean isBatchThisRequest() {
        return get().isBatchThisRequest();
    }
    @Override
    public void setNestedUseSavepoint() {
        get().setNestedUseSavepoint();
    }
    @Override
    public BatchControl batchControl() {
        return get().batchControl();
    }
    @Override
    public void setBatchControl(BatchControl control) {
        get().setBatchControl(control);
    }
    @Override
    public SpiPersistenceContext persistenceContext() {
        return get().persistenceContext();
    }
    @Override
    public void setRollbackOnly() {
        get().setRollbackOnly();
    }
    @Override
    public boolean isRollbackOnly() {
        return get().isRollbackOnly();
    }
    @Override
    public void end() {
        get().end();
    }
    @Override
    public void setPersistenceContext(SpiPersistenceContext context) {
        get().setPersistenceContext(context);
    }
    @Override
    public boolean isActive() {
        return get().isActive();
    }
    @Override
    public void setDocStoreMode(DocStoreMode mode) {
        get().setDocStoreMode(mode);
    }
    @Override
    public Connection internalConnection() {
        return get().internalConnection();
    }
    @Override
    public void setDocStoreBatchSize(int batchSize) {
        get().setDocStoreBatchSize(batchSize);
    }
    @Override
    public boolean isSaveAssocManyIntersection(String intersectionTable, String beanName) {
        return get().isSaveAssocManyIntersection(intersectionTable, beanName);
    }
    @Override
    public boolean checkBatchEscalationOnCascade(PersistRequestBean<?> request) {
        return get().checkBatchEscalationOnCascade(request);
    }
    @Override
    public void setPersistCascade(boolean persistCascade) {
        get().setPersistCascade(persistCascade);
    }
    @Override
    public void flushBatchOnCascade() {
        get().flushBatchOnCascade();
    }
    @Override
    public void flushBatchOnRollback() {
        get().flushBatchOnRollback();
    }
    @Override
    public PersistenceException translate(String message, SQLException cause) {
        return get().translate(message, cause);
    }
    @Override
    public void markNotQueryOnly() {
        get().markNotQueryOnly();
    }
    @Override
    public void setUpdateAllLoadedProperties(boolean updateAllLoadedProperties) {
        get().setUpdateAllLoadedProperties(updateAllLoadedProperties);
    }
    @Override
    public void checkBatchEscalationOnCollection() {
        get().checkBatchEscalationOnCollection();
    }
    @Override
    public void flushBatchOnCollection() {
        get().flushBatchOnCollection();
    }
    @Override
    public void addBeanChange(BeanChange beanChange) {
        get().addBeanChange(beanChange);
    }
    @Override
    public void sendChangeLog(ChangeSet changeSet) {
        get().sendChangeLog(changeSet);
    }
    @Override
    public void setSkipCache(boolean skipCache) {
        get().setSkipCache(skipCache);
    }
    @Override
    public DocStoreTransaction docStoreTransaction() {
        return get().docStoreTransaction();
    }
    @Override
    public void setTenantId(Object tenantId) {
        get().setTenantId(tenantId);
    }
    @Override
    public Object tenantId() {
        return get().tenantId();
    }
    @Override
    public long profileOffset() {
        return get().profileOffset();
    }
    @Override
    public void profileEvent(SpiProfileTransactionEvent event) {
        get().profileEvent(event);
    }
    @Override
    public void setProfileStream(ProfileStream profileStream) {
        get().setProfileStream(profileStream);
    }
    @Override
    public ProfileStream profileStream() {
        return get().profileStream();
    }
    @Override
    public void setProfileLocation(ProfileLocation profileLocation) {
        get().setProfileLocation(profileLocation);
    }
    @Override
    public ProfileLocation profileLocation() {
        return get().profileLocation();
    }
    @Override
    public boolean isNestedUseSavepoint() {
        return get().isNestedUseSavepoint();
    }
    @Override
    public boolean isSkipCacheExplicit() {
        return get().isSkipCacheExplicit();
    }
    @Override
    public void preCommit() {
        get().preCommit();
    }
    @Override
    public void postCommit() {
        get().postCommit();
    }
    @Override
    public void postRollback(Throwable cause) {
        get().postRollback(cause);
    }
    @Override
    public void deactivateExternal() {
        get().deactivateExternal();
    }
    @Override
    public boolean isSkipCache() {
        return get().isSkipCache();
    }
    @Override
    public void setBatchMode(boolean useBatch) {
        get().setBatchMode(useBatch);
    }
    @Override
    public boolean isBatchMode() {
        return get().isBatchMode();
    }
    @Override
    public void setBatchOnCascade(boolean batchMode) {
        get().setBatchOnCascade(batchMode);
    }
    @Override
    public boolean isBatchOnCascade() {
        return get().isBatchOnCascade();
    }
    @Override
    public void setBatchSize(int batchSize) {
        get().setBatchSize(batchSize);
    }
    @Override
    public void setGetGeneratedKeys(boolean getGeneratedKeys) {
        get().setGetGeneratedKeys(getGeneratedKeys);
    }
    @Override
    public void setFlushOnMixed(boolean batchFlushOnMixed) {
        get().setFlushOnMixed(batchFlushOnMixed);
    }
    @Override
    public void setFlushOnQuery(boolean batchFlushOnQuery) {
        get().setFlushOnQuery(batchFlushOnQuery);
    }
    @Override
    public boolean isFlushOnQuery() {
        return get().isFlushOnQuery();
    }
    @Override
    public void flush() throws PersistenceException {
        get().flush();
    }
    @Override
    public Connection connection() {
        return get().connection();
    }
    @Override
    public void addModification(String tableName, boolean inserts, boolean updates, boolean deletes) {
        get().addModification(tableName, inserts, updates, deletes);
    }
    @Override
    public void putUserObject(String name, Object value) {
        get().putUserObject(name, value);
    }
    @Override
    public Object getUserObject(String name) {
        return get().getUserObject(name);
    }
    private SpiTransaction get() {
        var currentThread = Thread.currentThread();
        var toReturn = supplier.get();

        if (originThread.equals(currentThread)) {
            //
            // ~ well it is for sure not the way how we want to manage transactions and abuse
            //
        } else {
            var msg = "transaction sharing between threads has been detected, origin: %s, current: %s".formatted(originThread, currentThread);
            if (props.isProdMode()) {
                log.error(msg);
            } else {
                throw new IllegalStateException(msg);
            }
        }

        return toReturn;
    }
}
