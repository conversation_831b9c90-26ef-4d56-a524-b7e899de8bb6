package com.turbospaces.ebean;

import java.util.Optional;
import java.util.Set;

import com.google.common.collect.ImmutableSet;
import com.turbospaces.api.jpa.JpaStackTracer;

import io.ebean.InsertOptions;
import io.ebean.Transaction;
import io.ebean.TxScope;
import io.ebean.config.dbplatform.PlatformIdGenerator;
import io.ebean.plugin.BeanType;
import io.ebeaninternal.api.SpiEbeanServer;
import io.ebeaninternal.api.SpiTransaction;
import io.ebeaninternal.server.deploy.BeanDescriptor;
import io.ebeaninternal.server.deploy.BeanPropertyAssocMany;
import io.ebeaninternal.server.deploy.BeanPropertyAssocOne;

public interface JpaManager extends SpiEbeanServer, JpaStackTracer {
    <R> PlatformIdGenerator idGenerator(Class<? extends AbstractSeq> type);

    SpiTransaction newTransaction() throws Throwable;
    SpiTransaction newTransaction(TxScope scope) throws Throwable;
    SpiTransaction newReadOnlyTransaction() throws Throwable;
    SpiTransaction newReadOnlyTransaction(TxScope scope) throws Throwable;

    <T> T getOrCreateAutonomously(AutonomousLoader<T> loader, Transaction tx) throws Throwable;
    <T> void insertOrUpdateAutonomously(AutonomousUpdater<T> loader, Transaction tx) throws Throwable;

    default Set<BeanType<?>> dependentQueryCacheTables(BeanType<?> root) {
        ImmutableSet.Builder<BeanType<?>> toReturn = ImmutableSet.builder();
        for (BeanType<?> beanType : beanTypes()) {
            BeanDescriptor<?> desc = (BeanDescriptor<?>) beanType;
            for (BeanPropertyAssocMany<?> many : desc.propertiesMany()) {
                if (many.targetDescriptor().equals(root)) {
                    toReturn.add(beanType);
                }
            }
            for (BeanPropertyAssocOne<?> one : desc.propertiesOne()) {
                if (one.targetDescriptor().equals(root)) {
                    toReturn.add(beanType);
                }
            }
            for (BeanPropertyAssocMany<?> manyToMany : desc.propertiesManyToMany()) {
                if (manyToMany.targetDescriptor().equals(root)) {
                    toReturn.add(beanType);
                }
            }
        }
        return toReturn.build();
    }

    interface AutonomousLoader<T> extends AutonomousUpdater<T> {
        InsertOptions insertOptions();
    }

    interface AutonomousUpdater<T> {
        Class<T> type();
        Optional<T> load() throws Throwable;
        void merge(T target) throws Throwable;

        default InsertOptions insertOptions() {
            return InsertOptions.ON_CONFLICT_UPDATE;
        }
    }
}
