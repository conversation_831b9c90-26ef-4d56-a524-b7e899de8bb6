package com.turbospaces.ebean;

import java.io.Closeable;
import java.io.IOException;

import javax.sql.DataSource;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.jdbc.DatasourceCreator;
import com.turbospaces.jdbc.JdbcPoolServiceConfig;

import io.ebean.DatabaseFactory;
import io.ebean.config.DatabaseConfig;
import io.ebean.config.EncryptKey;
import io.ebean.config.EncryptKeyManager;
import io.ebean.dbmigration.DbMigration;
import io.ebean.platform.postgres.PostgresPlatform;
import io.ebeaninternal.dbmigration.DefaultDbMigration;
import io.ebeaninternal.server.cache.DefaultServerCachePlugin;

public class GeneratePostgresMigration {
    public static void generate(Iterable<Class<?>> entities, ApplicationProperties props) throws Exception {
        getMigration(entities, props).generateMigration();
    }

    public static void generateAllPendingDrops(Iterable<Class<?>> entities, ApplicationProperties props) throws Exception {
        DbMigration migration = getMigration(entities, props);
        for (String drop : migration.getPendingDrops()) {
            migration.setGeneratePendingDrop(drop);
            migration.generateMigration();
        }
    }

    public static DbMigration getMigration(Iterable<Class<?>> entities, ApplicationProperties props) throws Exception {
        PostgresqlServiceInfo info = new PostgresqlServiceInfo("owner", props.cfg().getString("service.postgres-owner.uri"));
        return getMigration(entities, props, info);
    }

    public static DbMigration getMigration(Iterable<Class<?>> entities, ApplicationProperties props, ServiceInfo info) throws Exception {
        JdbcPoolServiceConfig jdbcCfg = new JdbcPoolServiceConfig(props, false);
        DatasourceCreator creator = new DatasourceCreator();

        DataSource db = creator.create(info, jdbcCfg);

        try {
            DatabaseConfig sc = new EbeanDatabaseConfig(db, props);
            sc.setDataSource(db);
            sc.setServerCachePlugin(new DefaultServerCachePlugin());
            //
            // mock, not needed for migration generate
            //
            sc.setEncryptKeyManager(new EncryptKeyManager() {
                @Override
                public EncryptKey getEncryptKey(String tableName, String columnName) {
                    return new EncryptKey() {
                        @Override
                        public String getStringValue() {
                            throw new UnsupportedOperationException();
                        }
                    };
                }
            });

            entities.forEach(sc::addClass);

            DefaultDbMigration migration = new DefaultDbMigration();

            migration.setStrictMode(false);
            migration.setServer(DatabaseFactory.create(sc));
            migration.setApplyPrefix("V");
            migration.setMigrationPath(props.APP_DB_MIGRATION_PATH.get());
            migration.setPlatform(new PostgresPlatform());
            return migration;
        } finally {
            if (db instanceof Closeable closeable) {
                try {
                    closeable.close();
                } catch (IOException err) {
                    ExceptionUtils.wrapAndThrow(err);
                }
            }
        }
    }
}
