package com.turbospaces.ebean;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.Location;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.flywaydb.core.api.output.MigrateResult;

import com.turbospaces.common.PlatformUtil;

import io.ebeaninternal.api.SpiEbeanServer;
import io.ebeaninternal.dbmigration.DefaultDbMigration;

public class FlywayUberRunner {
    public static MigrateResult run(SpiEbeanServer ebean, String... schemas) throws IOException {
        return run(ebean, Map.of(), schemas);
    }
    public static MigrateResult run(SpiEbeanServer ebean, Map<String, String> placeholders, String... schemas) throws IOException {
        File f = File.createTempFile("junit", StringUtils.EMPTY);
        f.delete();
        f.mkdir();

        try {
            String migrationPath = PlatformUtil.randomUUID().toString();

            DefaultDbMigration migration = new DefaultDbMigration();
            migration.setStrictMode(false);
            migration.setServer(ebean);
            migration.setApplyPrefix("V");
            migration.setMigrationPath(migrationPath);
            migration.setPathToResources(f.getPath());
            migration.setPlatform(ebean.databasePlatform());
            migration.setIncludeBuiltInPartitioning(false);
            migration.generateMigration();

            Path path = Paths.get(f.getPath(), migrationPath);

            FluentConfiguration fluentConfiguration = Flyway.configure();
            fluentConfiguration.dataSource(ebean.dataSource());
            fluentConfiguration.mixed(true);
            fluentConfiguration.encoding(StandardCharsets.UTF_8);
            fluentConfiguration.locations(Location.FILESYSTEM_PREFIX + path.toString());
            fluentConfiguration.placeholders(placeholders);

            if (ArrayUtils.isNotEmpty(schemas)) {
                fluentConfiguration.schemas(schemas);
                fluentConfiguration.createSchemas(true);
            }

            Flyway flyway = fluentConfiguration.load();
            return flyway.migrate();
        } finally {
            f.delete();
        }
    }
}
