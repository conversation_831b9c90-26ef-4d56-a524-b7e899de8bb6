package com.turbospaces.ebean;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.ebean.DatabaseFactory;
import io.ebean.annotation.Cache;
import io.ebean.cache.ServerCacheType;
import io.ebean.plugin.BeanType;
import io.ebean.plugin.Property;
import io.ebeaninternal.api.SpiEbeanServer;
import io.ebeaninternal.server.deploy.BeanProperty;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractEbeanFactoryBean<T extends JpaManager> extends AbstractFactoryBean<T> {
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final Tracer tracer;
    protected final EbeanDatabaseConfig config;
    protected final ScheduledExecutorService timer;

    public AbstractEbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.tracer = Objects.requireNonNull(tracer);
        this.config = Objects.requireNonNull(config);

        //
        // ~ just one thread
        //
        this.timer = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        });
    }
    @Override
    @SuppressWarnings("unchecked")
    protected T createInstance() throws Exception {
        SpiEbeanServer db = (SpiEbeanServer) DatabaseFactory.create(config);

        Duration interval = props.APP_METRICS_REPORT_INTERVAL.get();
        new EbeanMetrics(db, timer, interval).bindTo(meterRegistry);

        T toReturn = (T) createEbean(db);

        //
        // ~ ensure that any replicated cache beans have a @Version property in it
        //
        for (BeanType<?> beanType : toReturn.beanTypes()) {
            Class<?> type = beanType.type();
            if (Objects.nonNull(type.getAnnotation(Cache.class))) {
                String beanPrefix = type.getName() + ServerCacheType.BEAN.code();
                boolean local = props.cfg().getBoolean(beanPrefix + "." + EbeanCacheConfigurer.CACHE_MODE_LOCAL, true);

                log.trace("beanType: {}, local: {}", type.getName(), local);

                if (BooleanUtils.negate(local)) {
                    boolean versioned = false;
                    for (Property it : beanType.allProperties()) {
                        if (it instanceof BeanProperty beanProperty) {
                            if (beanProperty.isVersion()) {
                                versioned = true;
                                break;
                            }
                        }
                    }

                    if (versioned) {
                        log.debug("{} has @Version and configured as replicated", type.getName());
                    } else {
                        var msg = "%s has no @Version, but cache is configured as replicated, this may lead to stale data".formatted(type.getName());
                        log.error(msg);
                        throw new IllegalStateException(msg);
                    }
                }
            }
        }
        return toReturn;
    }
    @Override
    protected void destroyInstance(T instance) throws Exception {
        try {
            if (Objects.nonNull(instance)) {
                StopWatch stopWatch = StopWatch.createStarted();
                log.info("about to close ebean({}) now ...", instance.name());
                if (instance instanceof DisposableBean) {
                    ((DisposableBean) instance).destroy();
                }
                stopWatch.stop();
                log.info("closed ebean({}) in {}", instance.name(), stopWatch);
            }
        } finally {
            PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
        }
    }
    protected abstract JpaManager createEbean(SpiEbeanServer db) throws Exception;
}
