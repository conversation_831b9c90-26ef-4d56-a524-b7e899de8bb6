package io.ebean.platform.postgres;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.NavigableSet;
import java.util.Objects;
import java.util.TreeSet;
import java.util.concurrent.locks.ReentrantLock;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.Lists;
import com.turbospaces.ebean.AbstractSeq;

import io.ebean.BackgroundExecutor;
import io.ebean.Database;
import io.ebean.Transaction;
import io.ebean.config.dbplatform.PlatformIdGenerator;
import io.ebean.config.dbplatform.SimpleSequenceIdGenerator;
import io.vavr.Function0;
import jakarta.persistence.PersistenceException;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class TraceablePlatformIdGenerator implements PlatformIdGenerator {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final Database ebean;
    private final String name;
    private final String seqName;
    private final int allocationSize;
    private final ReentrantLock lock = new ReentrantLock();
    private final NavigableSet<Long> idList = new TreeSet<>();

    public TraceablePlatformIdGenerator(Database ebean, Class<? extends AbstractSeq> clazz, String seqName, int allocationSize) {
        this.ebean = Objects.requireNonNull(ebean);
        this.name = clazz.getSimpleName();
        this.seqName = Objects.requireNonNull(seqName);
        this.allocationSize = allocationSize;
    }
    @Override
    public Object nextId(Transaction tx) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public Object apply() {
                lock.lock();
                try {
                    int size = idList.size();
                    if (size > 0) {
                        long id = idList.pollFirst();
                        logger.debug("{}: nextId immediately: {}", getName(), id);
                        return id;
                    }

                    loadMore(tx);
                    long id = idList.pollFirst();
                    logger.debug("{}: nextId after loadMore: {}", getName(), id);
                    return id;
                } finally {
                    lock.unlock();
                }
            }
        });

    }
    @Override
    public String getName() {
        return name;
    }
    @Override
    public boolean isDbSequence() {
        return true;
    }
    @Override
    public void preAllocateIds(int allocateSize) {

    }
    private void loadMore(Transaction tx) {
        BackgroundExecutor backgroundExecutor = ebean.backgroundExecutor(); // ~ not needed actually (NPE prevention)
        DataSource dataSource = ebean.dataSource(); // ~ not needed actually (NPE prevention)
        List<Long> newIds = Lists.newArrayListWithExpectedSize(allocationSize);

        switch (ebean.platform()) {
            case POSTGRES:
            case POSTGRES9: {
                PostgresSequenceIdGenerator idGen = new PostgresSequenceIdGenerator(backgroundExecutor, dataSource, seqName, allocationSize);
                String sql = idGen.getSql(allocationSize);

                Connection connection = tx.connection(); // ~ do not close it here
                try {
                    try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
                        try (ResultSet rset = pstmt.executeQuery()) {
                            while (rset.next()) {
                                newIds.add(rset.getLong(1));
                            }
                        }
                    }
                } catch (SQLException e) {
                    throw new PersistenceException("Error getting sequence nextval", e);
                }

                break;
            }
            default: {
                String sql = "select nextval('" + seqName + "')";
                SimpleSequenceIdGenerator simpleGen = new SimpleSequenceIdGenerator(dataSource, sql, seqName);

                for (int i = 0; i < allocationSize; i++) {
                    Number nextId = (Number) simpleGen.nextId(tx);
                    newIds.add(nextId.longValue());
                }

                break;
            }
        }

        logger.info("{}: pre-loaded new ids: {}", getName(), newIds);
        idList.addAll(newIds);
        newIds.clear();
    }
}
