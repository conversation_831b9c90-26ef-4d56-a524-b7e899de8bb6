package com.turbospaces.ebean;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.RequestHandler;

import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import lombok.extern.slf4j.Slf4j;

public class ReadOnlyTransactionAspectTest {
    @Test
    void happyPath() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            ServiceApi serviceApi = applicationContext.getBean(ServiceApi.class);
            serviceApi.callMe(PlatformUtil.randomUUID().toString());
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void testWriteInReadOnly() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            ServiceApi serviceApi = applicationContext.getBean(ServiceApi.class);
            RequestHandler.READ_ONLY_MARKER.set(true);
            Assertions.assertThrows(IllegalStateException.class, () -> serviceApi.callMe(PlatformUtil.randomUUID().toString()));
        } finally {
            RequestHandler.READ_ONLY_MARKER.remove();
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public EmbeddedH2FactoryBean datasource() {
            return new EmbeddedH2FactoryBean(new String[] {});
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, EmbeddedH2FactoryBean ds) throws Exception {
            return new EbeanDatabaseConfig(ds.getObject(), props);
        }
        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config);
        }
        @Bean
        public ServiceApi serviceApi(EbeanJpaManager ebean) {
            return new DefaultServiceApi(ebean);
        }
    }

    public interface ServiceApi {
        void callMe(String message) throws Throwable;
    }

    @Slf4j
    public static class DefaultServiceApi implements ServiceApi {
        private final EbeanJpaManager ebean;

        public DefaultServiceApi(EbeanJpaManager ebean) {
            this.ebean = ebean;
        }

        @Override
        public void callMe(String message) throws Throwable {
            ebean.newTransaction();
            log.debug(message);
        }
    }
}
