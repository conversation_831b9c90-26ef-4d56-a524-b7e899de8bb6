package com.turbospaces.ebean;

import java.util.concurrent.ScheduledExecutorService;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class MockEbeanJpaManager extends EbeanJpaManager {
    public MockEbeanJpaManager(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            Tracer tracer,
            SpiEbeanServer database,
            ScheduledExecutorService timer) {
        super(props, meterRegistry, tracer, database, timer);
    }
}
