package com.turbospaces.ebean;

import java.util.List;

import javax.sql.DataSource;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class RollbackTransactionTest {
    static final String SCHEMA = "test";

    @Test
    void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            MockEbeanJpaManager ebean = applicationContext.getBean(MockEbeanJpaManager.class);
            Model1 model11 = new Model1(1);
            Model1 model12 = new Model1(2);
            Model2 model21 = new Model2(1);
            Model2 model22 = new Model2(2);

            try (Transaction tx = ebean.beginTransaction()) {
                ebean.save(model11, tx);
                ebean.save(model21, tx);
                // If there is no current transaction one will be created. But there is!
                // Therefore this save will be rolled back together with all the previous
                // (because they belong to the same transaction)
                ebean.save(model22);
                tx.rollback();
                // No active transaction. New one will be created and commited immediately
                ebean.save(model12);

                Exception e;
                e = Assertions.assertThrows(IllegalStateException.class, tx::commit);
                Assertions.assertEquals("Transaction is Inactive", e.getMessage());
                e = Assertions.assertThrows(NullPointerException.class, ebean::flush);
                Assertions.assertEquals("Cannot invoke \"io.ebean.Transaction.flush()\" because the return value of \"io.ebeaninternal.server.core.DefaultServer.currentTransaction()\" is null", e.getMessage());
            }

            Assertions.assertNull(ebean.find(Model2.class, 1)); // rolled back
            Assertions.assertNull(ebean.find(Model2.class, 2)); // rolled back
            Assertions.assertNull(ebean.find(Model1.class, 1)); // rolled back
            Assertions.assertNotNull(ebean.find(Model1.class, 2)); // persisted
        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public EmbeddedH2FactoryBean datasource() {
            return new EmbeddedH2FactoryBean(new String[]{SCHEMA});
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, EmbeddedH2FactoryBean ds) throws Exception {
            return new EbeanConfiguration(ds.getObject(), props);
        }
        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config) {
                @Override
                protected MockEbeanJpaManager createInstance() throws Exception {
                    MockEbeanJpaManager ebean = super.createInstance();
                    FlywayUberRunner.run(ebean, SCHEMA);
                    return ebean;
                }
            };
        }
    }

    public static class EbeanConfiguration extends EbeanDatabaseConfig {
        public EbeanConfiguration(DataSource ds, ApplicationProperties props) {
            super(ds, props);
            addAll(List.of(Model1.class, Model2.class));
        }
    }
}
