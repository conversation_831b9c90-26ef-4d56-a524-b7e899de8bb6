package com.turbospaces.ebean;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;

import javax.sql.DataSource;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

@Configuration
class EbeanMetricsTest {

    @Test
    void testMetricReporting() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        cfg.setLocalProperty(props.APP_METRICS_REPORT_INTERVAL.getKey(), Duration.ofMillis(10));
        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        EbeanJpaManager ebeanJpaManager = applicationContext.getBean(MockEbeanJpaManager.class);
        try (TracedEbeanTransaction tx = ebeanJpaManager.newTransaction()) {
            ebeanJpaManager.save(new Model3(1, "desc", BigDecimal.ONE), tx);
            tx.commit();
        }
        AppConfig.Repo repo = applicationContext.getBean(AppConfig.Repo.class);


        // check multiple reported correctly
        repo.findModel(1);
        repo.findModel(1);
        repo.findModel(1);

        Awaitility.await().untilAsserted(() -> {
            var summary = bootstrap.meterRegistry()
                    .get(EbeanMetrics.EBEAN_QUERY_METRIC_NAME)
                    .tag("name", "orm.Model3.byId")
                    .summary();
            Assertions.assertEquals(3, summary.count());
            Assertions.assertTrue(summary.mean() > 0);
            Assertions.assertTrue(summary.max() > 0);
        });

        // check one reported correctly
        repo.findList();

        Awaitility.await().untilAsserted(() -> {
            var summary = bootstrap.meterRegistry()
                    .get(EbeanMetrics.EBEAN_QUERY_METRIC_NAME)
                    .tag("name", "orm.Model3.findList")
                    .summary();
            Assertions.assertEquals(1, summary.count());
            Assertions.assertTrue(summary.mean() > 0);
            Assertions.assertTrue(summary.max() > 0);
        });
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public EmbeddedH2FactoryBean datasource() {
            return new EmbeddedH2FactoryBean(new String[]{EbeanSlowQueryListenerTest.SCHEMA});
        }

        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, EmbeddedH2FactoryBean ds) throws Exception {
            return new EbeanConfiguration(ds.getObject(), props);
        }

        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config) {
                @Override
                protected MockEbeanJpaManager createInstance() throws Exception {
                    MockEbeanJpaManager ebean = super.createInstance();
                    FlywayUberRunner.run(ebean, EbeanSlowQueryListenerTest.SCHEMA);
                    return ebean;
                }
            };
        }

        @Bean
        public Repo repo(MockEbeanJpaManager ebean) {
            return new Repo(ebean);
        }

        public static class EbeanConfiguration extends EbeanDatabaseConfig {
            public EbeanConfiguration(DataSource ds, ApplicationProperties props) {
                super(ds, props);
                addAll(List.of(Model3.class));
            }
        }

        public static class Repo {
            private final EbeanJpaManager ebean;

            public Repo(EbeanJpaManager ebean) {
                this.ebean = ebean;
            }

            public Model3 findModel(int id) {
                return ebean.find(Model3.class, id);
            }

            public List<Model3> findList() throws Exception {
                return ebean.find(Model3.class).findList();
            }
        }
    }
}