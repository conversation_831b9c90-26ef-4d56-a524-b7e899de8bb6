package com.turbospaces.ebean;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.util.List;

import javax.sql.DataSource;

import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import nl.altindag.log.LogCaptor;

class EbeanSlowQueryListenerTest {

    static final String SCHEMA = "slow";

    @Test
    void slowQuery() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props, EbeanSlowQueryListenerTest.AppConfig.class);
        cfg.setLocalProperty(props.JDBC_SLOW_QUERY_LOGGER_ENABLE.getKey(), true);
        cfg.setLocalProperty(props.JDBC_SLOW_QUERY_LOGGER_MILLIS.getKey(), 1);
        ConfigurableApplicationContext applicationContext = bootstrap.run();
        MockEbeanJpaManager ebean = applicationContext.getBean(MockEbeanJpaManager.class);
        ebean.deleteAll(Model3.class, ebean.find(Model3.class).findIds());
        try (var logCaptor = LogCaptor.forClass(EbeanSlowQueryListener.class)) {
            for (int i = 1; i <= 5000; i++) {
                var row = new Model3(i, "desc-" + i, BigDecimal.TEN);
                ebean.save(row);
            }
            try (Transaction tx = ebean.newTransaction()) {
                for (var model : ebean.find(Model3.class).forUpdate().findList()) {
                    ebean.save(model);
                    tx.flush();
                }
                tx.commit();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            List<String> logs = logCaptor.getWarnLogs();
            assertFalse(logs.isEmpty());
            String loggedMessage = logs.iterator().next();
            assertTrue(loggedMessage.contains("Slow query warning"));
            assertTrue(loggedMessage.contains("rows:5000"));
        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public EmbeddedH2FactoryBean datasource() {
            return new EmbeddedH2FactoryBean(new String[] { SCHEMA });
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, EmbeddedH2FactoryBean ds) throws Exception {
            return new EbeanSlowQueryListenerTest.EbeanConfiguration(ds.getObject(), props);
        }
        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config) {
                @Override
                protected MockEbeanJpaManager createInstance() throws Exception {
                    MockEbeanJpaManager ebean = super.createInstance();
                    FlywayUberRunner.run(ebean, SCHEMA);
                    return ebean;
                }
            };
        }
    }

    public static class EbeanConfiguration extends EbeanDatabaseConfig {
        public EbeanConfiguration(DataSource ds, ApplicationProperties props) {
            super(ds, props);
            addAll(List.of(Model3.class));
        }
    }
}
