package com.turbospaces.ebean;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

@Configuration
class EbeanJpaManagerTest {
    @Test
    public void testNestedTransactionPrevented() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), ReadOnlyTransactionAspectTest.AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        EbeanJpaManager ebeanJpaManager = applicationContext.getBean(EbeanJpaManager.class);
        try (TracedEbeanTransaction tx = ebeanJpaManager.newTransaction()) {
            tx.connection();

            var err = Assertions.assertThrows(
                    IllegalStateException.class,
                    () -> {
                        try (TracedEbeanTransaction nested = ebeanJpaManager.newTransaction()) {
                            nested.startTime();
                        } finally {
                            tx.commit();
                        }
                    }
            );
            assertEquals("transaction abuse detected: nested transaction usage", err.getMessage());
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public EmbeddedH2FactoryBean datasource() {
            return new EmbeddedH2FactoryBean(new String[]{});
        }

        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, EmbeddedH2FactoryBean ds) throws Exception {
            return new EbeanDatabaseConfig(ds.getObject(), props);
        }

        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config);
        }
    }
}