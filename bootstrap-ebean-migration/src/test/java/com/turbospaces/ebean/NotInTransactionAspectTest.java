package com.turbospaces.ebean;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpResponse.BodyHandlers;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicReference;

import org.flywaydb.core.internal.database.h2.H2DatabaseType;
import org.flywaydb.database.postgresql.PostgreSQLDatabaseType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.HttpStatusCode;
import org.mockserver.model.MediaType;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.annotation.Bean;
import org.testcontainers.containers.PostgreSQLContainer;

import com.google.common.base.Stopwatch;
import com.google.common.io.ByteSource;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.PlatformTransactionalThread;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.ups.UPSs;

import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import io.vavr.CheckedRunnable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public class NotInTransactionAspectTest {
    public static final ThreadLocal<Boolean> NON_BLOCKING = DefaultBlockHoundIntegration.FLAG;
    public static final String USERNAME = "test";
    public static final String PASSWORD = "test";
    public static final String DB = "defaultdb";
    public static final String SCHEMA = "test";

    @Test
    void works() throws Throwable {
        try (var pg = new PostgreSQLContainer<>("postgres:latest")) {
            pg.withDatabaseName(DB);
            pg.withUsername(USERNAME);
            pg.withPassword(PASSWORD);
            pg.start();

            var pgUri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                    PostgresqlServiceInfo.POSTGRES_SCHEME,
                    pg.getHost(),
                    pg.getFirstMappedPort(),
                    pg.getUsername(),
                    pg.getPassword(),
                    pg.getDatabaseName()).getUriString();

            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            cfg.setDefaultProperty(PlatformUtil.randomUUID().toString(), PostgreSQLDatabaseType.class.getName());
            cfg.setDefaultProperty(PlatformUtil.randomUUID().toString(), H2DatabaseType.class.getName());
            cfg.setLocalProperty(props.APP_BLOCKHOUND_ENABLED.getKey(), true);

            SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.addUps(new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri));
            var applicationContext = bootstrap.run();

            var ebean = applicationContext.getBean(EbeanJpaManager.class);

            var tfb = new ThreadFactoryBuilder();
            tfb.setDaemon(false);
            tfb.setNameFormat("local-test-%d");
            tfb.setThreadFactory(new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    return new PlatformTransactionalThread(props, ebean, r);
                }
            });

            try (var pool = Executors.newCachedThreadPool(tfb.build())) {
                var worker = new ThreadPoolContextWorker(props, bootstrap.meterRegistry(), pool, false);
                worker.setBeanName(PlatformUtil.randomUUID().toString());
                worker.afterPropertiesSet();

                try {
                    var serviceApi = applicationContext.getBean(ServiceApi.class);
                    var context = worker.actor(new WorkUnit() {
                        @Override
                        public String topic() {
                            return "junit";
                        }
                        @Override
                        public long timestamp() {
                            return System.currentTimeMillis();
                        }
                        @Override
                        public byte[] key() {
                            return topic().getBytes();
                        }
                        @Override
                        public ByteSource value() {
                            return ByteSource.empty();
                        }
                    });

                    //
                    // ~ should fail due to HTTP call
                    //
                    for (var callback : new CheckedRunnable[] {
                            new CheckedRunnable() {
                                @Override
                                public void run() throws Throwable {
                                    serviceApi.callMeHttp(PlatformUtil.randomUUID().toString());
                                }
                            }
                    }) {
                        var failure = new AtomicReference<Throwable>();
                        var latch = new CountDownLatch(1);
                        var submit = context.submit(callback);
                        Futures.addCallback(submit, new FutureCallback<Object>() {
                            @Override
                            public void onSuccess(Object result) {
                                latch.countDown();
                            }
                            @Override
                            public void onFailure(Throwable t) {
                                failure.set(t);
                                latch.countDown();
                            }
                        }, MoreExecutors.directExecutor());

                        latch.await();
                        Assertions.assertInstanceOf(Error.class, failure.get());
                        log.error("unexpected error", failure.get());
                    }

                    for (var callback : new CheckedRunnable[] {
                            new CheckedRunnable() {
                                @Override
                                public void run() throws Throwable {
                                    serviceApi.callMeJdbc(PlatformUtil.randomUUID().toString());
                                }
                            }
                    }) {
                        var failure = new AtomicReference<Throwable>();
                        var latch = new CountDownLatch(1);
                        var submit = context.submit(callback);
                        Futures.addCallback(submit, new FutureCallback<Object>() {
                            @Override
                            public void onSuccess(Object result) {
                                latch.countDown();
                            }
                            @Override
                            public void onFailure(Throwable t) {
                                failure.set(t);
                                latch.countDown();
                            }
                        }, MoreExecutors.directExecutor());

                        latch.await();
                        if(Objects.nonNull(failure.get())) {
                            log.error("unexpected error", failure.get());
                        }
                        Assertions.assertNull(failure.get());
                    }
                } finally {
                    bootstrap.shutdown();
                    worker.destroy();
                }
            }
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public HikariDataSourceFactoryBean postgresDatasource(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                ApplicationConfig cfg) {
            PostgresqlServiceInfo si = UPSs.findRequiredScopedServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
            return new HikariDataSourceFactoryBean(props, meterRegistry, si);
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean ds) throws Exception {
            var toReturn = new EbeanDatabaseConfig(ds.getObject(), props);
            toReturn.addAll(List.of(Model1.class, Model2.class));
            return toReturn;
        }
        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config) {
                @Override
                protected MockEbeanJpaManager createInstance() throws Exception {
                    MockEbeanJpaManager ebean = super.createInstance();
                    FlywayUberRunner.run(ebean, SCHEMA);
                    return ebean;
                }
            };
        }
        @Bean
        public ServiceApi serviceApi(MockEbeanJpaManager ebean) {
            return new DefaultServiceApi(ebean);
        }
    }

    public interface ServiceApi {
        void callMeHttp(String message) throws Throwable;
        void callMeJdbc(String message) throws Throwable;
    }

    @Slf4j
    @RequiredArgsConstructor
    public static class DefaultServiceApi implements ServiceApi {
        private final MockEbeanJpaManager ebean;

        @Override
        public void callMeHttp(String message) throws Throwable {
            log.debug(message);
            log.debug(Thread.currentThread().getName());

            try (ClientAndServer server = ClientAndServer.startClientAndServer(0)) {
                server.when(HttpRequest.request("/call"))
                        .respond(new HttpResponse()
                                .withStatusCode(HttpStatusCode.OK_200.code())
                                .withReasonPhrase(HttpStatusCode.OK_200.reasonPhrase())
                                .withContentType(MediaType.TEXT_PLAIN)
                                .withBody(PlatformUtil.randomUUID().toString()));

                try (HttpClient httpClient = HttpClient.newBuilder().build()) {
                    try (var tx = ebean.newReadOnlyTransaction()) {
                        log.debug(NON_BLOCKING.get().toString());

                        var connection = tx.connection();

                        log.debug(NON_BLOCKING.get().toString());
                        log.debug(connection.toString());
                        log.debug("current http = {}", ((PlatformTransactionalThread) Thread.currentThread()).current().isPresent());

                        //
                        // ~ this must be allowed
                        //
                        log.debug("model1: {}", ebean.find(Model1.class).usingTransaction(tx).findCount());
                        log.debug("model2: {}", ebean.find(Model2.class).usingTransaction(tx).findCount());

                        log.debug(NON_BLOCKING.get().toString());

                        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
                            @Override
                            public void run() throws Throwable {
                                Stopwatch stopwatch = Stopwatch.createStarted();
                                Thread.sleep(1);
                                stopwatch.stop();
                                log.info("managed to sleep for {}", stopwatch.toString());
                            }
                        });

                        //
                        // ~ known blocking IO call to OS
                        //
                        log.debug(NON_BLOCKING.get().toString());
                        var request = java.net.http.HttpRequest.newBuilder().uri(new URI("http://localhost:%d/call".formatted(server.getPort()))).GET().build();
                        java.net.http.HttpResponse<String> resp = httpClient.send(request, BodyHandlers.ofString());
                        log.debug(resp.body());
                    }
                }
            }
        }
        @Override
        public void callMeJdbc(String message) throws Throwable {
            log.debug(message);
            log.debug(Thread.currentThread().getName());

            try (var tx = ebean.newReadOnlyTransaction()) {
                var connection = tx.connection();
                log.debug(connection.toString());
                log.debug("current jdbc = {}", ((PlatformTransactionalThread) Thread.currentThread()).current().isPresent());

                //
                // ~ known blocking to
                //
                log.debug("model1: {}", ebean.find(Model1.class).usingTransaction(tx).findCount());
                log.debug("model2: {}", ebean.find(Model2.class).usingTransaction(tx).findCount());
            }
        }
    }
}
