package com.turbospaces.ebean;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class MockEbeanFactoryBean extends AbstractEbeanFactoryBean<MockEbeanJpaManager> {
    public MockEbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
        super(props, meterRegistry, tracer, config);
    }
    @Override
    public Class<?> getObjectType() {
        return MockEbeanJpaManager.class;
    }
    @Override
    protected MockEbeanJpaManager createEbean(SpiEbeanServer db) {
        return new MockEbeanJpaManager(props, meterRegistry, tracer, db, timer);
    }
}
