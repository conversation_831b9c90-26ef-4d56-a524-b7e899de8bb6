package com.turbospaces.ebean;

import java.math.BigDecimal;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Entity
@Table(schema = EbeanSlowQueryListenerTest.SCHEMA, name = "model3")
@AllArgsConstructor
@Getter
public class Model3 {
    @Id
    private final Integer id;
    private final String description;
    private final BigDecimal price;
}