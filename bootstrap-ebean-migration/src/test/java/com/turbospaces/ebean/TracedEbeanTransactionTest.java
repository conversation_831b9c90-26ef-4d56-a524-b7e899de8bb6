package com.turbospaces.ebean;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcTags;

import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

@Configuration
class TracedEbeanTransactionTest {
    @Test
    public void testTxTime() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), ReadOnlyTransactionAspectTest.AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        EbeanJpaManager ebeanJpaManager = applicationContext.getBean(EbeanJpaManager.class);
        MDC.put(MdcTags.MDC_OPERATION, "test-operation");
        try (TracedEbeanTransaction tx = ebeanJpaManager.newTransaction()) {
            tx.commit();
        }
        Meter meter = bootstrap.meterRegistry().find("tx.time").meter();
        Assertions.assertNotNull(meter);
        Assertions.assertEquals("test-operation", meter.getId().getTag("operation"));
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public EmbeddedH2FactoryBean datasource() {
            return new EmbeddedH2FactoryBean(new String[] {});
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, EmbeddedH2FactoryBean ds) throws Exception {
            return new EbeanDatabaseConfig(ds.getObject(), props);
        }
        @Bean
        public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new MockEbeanFactoryBean(props, meterRegistry, tracer, config);
        }
    }
}