package com.turbospaces.plugins;

import java.util.List;
import java.util.Objects;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.ups.UPSs;

import io.ebean.Database;
import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import jakarta.inject.Inject;

public class EbeanServerBootstrapPluginTest {
    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        bootstrap.withH2(true, bootstrap.spaceName());

        ServiceInfo ownerUps = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.H2_OWNER);

        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, ownerUps, List.of()));
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            Foo1 bean1 = applicationContext.getBean(Foo1.class);
            Foo2 bean2 = applicationContext.getBean(Foo2.class);
            bean1.regular();
            bean1.readOnly();

            bean2.db.autoTune();
        } finally {
            bootstrap.shutdown();
        }
    }

    public static class Foo1 {
        private final JpaManager ebean;

        @Inject
        public Foo1(JpaManager ebean) {
            this.ebean = ebean;
        }
        public void regular() throws Throwable {
            try (Transaction tx = ebean.newTransaction()) {
                tx.commit();
            }
        }
        public void readOnly() throws Throwable {
            try (Transaction tx = ebean.newReadOnlyTransaction()) {
                tx.commit();
            }
        }
    }

    public static class Foo2 {
        private final Database db;

        @Inject
        public Foo2(Database db) {
            this.db = Objects.requireNonNull(db);
        }
    }

    @Configurable
    public static class AppConfig {
        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, MeterRegistry meterRegistry, DynamicCloud cloud) {
            ServiceInfo appUps = UPSs.findRequiredServiceInfoByName(cloud, UPSs.H2_APP);
            return new HikariDataSourceFactoryBean(props, meterRegistry, appUps);
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            return new EbeanDatabaseConfig(factory.getObject(), props);
        }
        @Bean
        public EbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config);
        }
        @Bean
        public Foo1 foo1(JpaManager db) {
            return new Foo1(db);
        }
        @Bean
        public Foo2 foo2(Database db) {
            return new Foo2(db);
        }
    }
}
