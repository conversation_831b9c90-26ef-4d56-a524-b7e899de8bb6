package com.turbospaces.jgroups;

import java.util.Objects;

import org.jgroups.JChannel;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.turbospaces.cfg.ApplicationProperties;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class JGroupsFactoryBean extends AbstractFactoryBean<JChannel> {
    private final ApplicationProperties props;

    @Override
    public Class<?> getObjectType() {
        return JChannel.class;
    }
    @Override
    protected JChannel createInstance() throws Exception {
        JChannel channel = props.isDevMode() ? new JChannel() : new DNSPingChannel(props);
        channel.setDiscardOwnMessages(true);
        channel.connect(props.CLOUD_APP_SPACE_NAME.get());
        return channel;
    }
    @Override
    protected void destroyInstance(JChannel instance) throws Exception {
        if (Objects.nonNull(instance)) {
            instance.close();
        }
    }
}
