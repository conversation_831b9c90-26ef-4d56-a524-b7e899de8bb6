package com.turbospaces.jgroups;

import java.io.InputStream;
import java.net.URL;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jgroups.JChannel;
import org.jgroups.conf.ConfiguratorFactory;
import org.jgroups.conf.ProtocolConfiguration;
import org.jgroups.conf.XmlConfigurator;
import org.jgroups.protocols.dns.DNS_PING;
import org.jgroups.protocols.dns.TypeADNSResolver;
import org.jgroups.stack.Protocol;
import org.jgroups.stack.ProtocolHook;
import org.jgroups.stack.ProtocolStack;
import org.springframework.util.ResourceUtils;

import com.turbospaces.cfg.ApplicationProperties;

public class DNSPingChannel extends JChannel {
    public DNSPingChannel(ApplicationProperties props) throws Exception {
        super(false);

        this.prot_stack = new ProtocolStack(this) {
            @Override
            public void initProtocolStack(List<ProtocolConfiguration> configs, ProtocolHook afterCreationHook) throws Exception {
                // ~ DNS query
                String query = props.APP_DNS_QUERY.get();
                if (StringUtils.isEmpty(query)) {
                    query = props.CLOUD_APP_ID.get();
                }

                DNS_PING ping = findProtocol(DNS_PING.class);
                ping.setValue("dns_query", query);
                ping.setValue("dns_record_type", "A");
                ping.setValue("dns_resolver", new TypeADNSResolver());
                super.initProtocolStack(configs, afterCreationHook);
            }
        };

        URL url = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "dns-ping-jgroups-tcp.xml");
        try (InputStream in = url.openStream()) {
            XmlConfigurator xml = (XmlConfigurator) ConfiguratorFactory.getStackConfigurator(in);
            List<ProtocolConfiguration> configs = xml.getProtocolStack();
            for (ProtocolConfiguration cfg : configs) {
                cfg.substituteVariables();
            }
            prot_stack.setup(configs, new ProtocolHook() {
                @Override
                public void afterCreation(Protocol prot) throws Exception {

                }
            });
        }
    }
}
