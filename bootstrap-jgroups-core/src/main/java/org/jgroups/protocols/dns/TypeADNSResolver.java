package org.jgroups.protocols.dns;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;

import org.jgroups.Address;
import org.jgroups.stack.IpAddress;

import com.google.common.collect.ImmutableList;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TypeADNSResolver implements DNSResolver {
    public static final int DEFAULT_COMMUNICATION_PORT = 6800;

    @Override
    public List<Address> resolveIps(String dnsQuery, DNSRecordType recordType) {
        String bindPort = System.getProperty("jgroups.bind_port", Integer.toString(DEFAULT_COMMUNICATION_PORT));
        ImmutableList.Builder<Address> addresses = ImmutableList.builder();

        try {
            InetAddress[] inetAddresses = InetAddress.getAllByName(dnsQuery);
            for (InetAddress address : inetAddresses) {
                addresses.add(new IpAddress(address.getHostAddress(), Integer.parseInt(bindPort)));
            }

            log.debug("DNS lookup: {}", Arrays.toString(inetAddresses));
        } catch (UnknownHostException ex) {
            log.atWarn().setMessage("failed to resolve DNS query: " + dnsQuery).setCause(ex).log();
        }

        return addresses.build();
    }
}
