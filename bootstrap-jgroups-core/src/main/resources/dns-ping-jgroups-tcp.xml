<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="urn:org:jgroups"
        xsi:schemaLocation="urn:org:jgroups http://www.jgroups.org/schema/jgroups.xsd"
        >
    <TCP bind_addr="${jgroups.bind_addr:site_local}"
         bind_port="${jgroups.bind_port:6800}"
         external_addr="${jgroups.external_addr}"
         external_port="${jgroups.external_port}"
         thread_pool.min_threads="${jgroups.thread_pool.min_threads:16}"
         thread_pool.max_threads="${jgroups.thread_pool.max_threads:64}"
         thread_pool.keep_alive_time="30000"/>
    <RED/>

    <dns.DNS_PING/>
    <MERGE3  min_interval="10000" max_interval="30000"/>
    <FD_SOCK2/>
    <FD_ALL3 timeout="40000" interval="5000"/>
    <VERIFY_SUSPECT2 timeout="1500"/>
    <BARRIER/>
    <pbcast.NAKACK2 use_mcast_xmit="false"/>
    <UNICAST3/>
    <pbcast.STABLE desired_avg_gossip="50000" max_bytes="4M"/>
    <pbcast.GMS print_local_addr="true" join_timeout="${jgroups.join_timeout:2000}"/>
    <UFC max_credits="2M" min_threshold="0.4"/>
    <MFC max_credits="2M" min_threshold="0.4"/>
    <FRAG2 frag_size="60K"/>
    <pbcast.STATE_TRANSFER/>
</config>