package com.turbospaces.infinispan;

import org.jgroups.JChannel;
import org.jgroups.blocks.ReplicatedHashMap;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.jgroups.JGroupsFactoryBean;

public class JgroupsPluginTest {
    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();

        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            JChannel channel = applicationContext.getBean(JChannel.class);

            try (ReplicatedHashMap<String, Object> cache = new ReplicatedHashMap<>(channel)) {
                channel.connect(getClass().getSimpleName());

                cache.start(30 * 10000);
                cache.put("1", 1);
                cache.put("2", 2);
                cache.clear();
            }
        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public JGroupsFactoryBean jgroups(ApplicationProperties props) {
            return new JGroupsFactoryBean(props);
        }
    }
}
