package com.turbospaces.boot.test;

import java.util.Objects;
import java.util.UUID;

import javax.annotation.OverridingMethodsMustInvokeSuper;

import org.apache.commons.lang3.time.StopWatch;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.testcontainers.containers.Network;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.rpc.CompletableRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
public class KafkaPGBootRuleContext implements AutoCloseable {
    private Network network;

    private CompletableRequestReplyMapper<UUID, ResponseWrapperFacade> reqReplyMapper;
    private KafkaTemplate<byte[], byte[]> kafkaTemplate;
    private KafkaAcceptorChannel kafkaChannel;
    private EmbeddedKafkaBroker kafka;

    private PostgresqlServiceInfo posi, pasi;
    private KafkaServiceInfo ksi;

    @Override
    @OverridingMethodsMustInvokeSuper
    public void close() throws Exception {
        if (Objects.nonNull(kafkaTemplate)) {
            log.info("stopping embedded kafka template: {} ...", kafkaTemplate);
            kafkaTemplate.destroy();
        }

        if (Objects.nonNull(kafkaChannel)) {
            log.info("stopping embedded request-reply channel: {} ...", kafkaTemplate);
            kafkaChannel.stop();
        }

        if (Objects.nonNull(kafka)) {
            StopWatch stopWatch = StopWatch.createStarted();
            log.info("stopping embedded Kafka: ...");
            kafka.destroy();
            stopWatch.stop();
            log.info("stopped embedded Kafka in {} ...", stopWatch.toString());
        }

        if (Objects.nonNull(reqReplyMapper)) {
            reqReplyMapper.destroy();
        }
    }
}
