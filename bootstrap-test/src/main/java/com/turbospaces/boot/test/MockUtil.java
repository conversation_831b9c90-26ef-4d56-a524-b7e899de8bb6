package com.turbospaces.boot.test;

import static org.mockito.Mockito.mock;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.mockito.Mockito;
import org.mockito.verification.VerificationMode;

import com.google.common.io.ByteSource;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.protobuf.Empty;
import com.google.protobuf.Message;
import com.google.protobuf.Timestamp;
import com.turbospaces.api.ServiceApi;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.EmbeddedTransactionalRequest;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.ContextPropagator;
import com.turbospaces.rpc.DefaultApiResponse;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.rpc.RequestReplyTimeout;

import api.v1.ApiFactory;
import io.netty.util.AsciiString;
import io.vavr.CheckedConsumer;
import jakarta.inject.Inject;
import reactor.core.publisher.Sinks;

@SuppressWarnings("unchecked")
public class MockUtil {
    private final Timestamp foo = Timestamp.getDefaultInstance();
    private final ApplicationProperties props;
    private final ApiFactory apiFactory;
    private final Topic topic = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("embedded");
        }
        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };

    @Inject
    public MockUtil(ApplicationProperties props, ApiFactory apiFactory) {
        this.props = props;
        this.apiFactory = Objects.requireNonNull(apiFactory);
    }
    public <T extends Message, V> MockCall mockServiceCall(V service, CheckedConsumer<V> action, T resp) throws Throwable {
        RequestQueuePostSpec spec = RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build();
        Duration timeout = Duration.ofSeconds(30);
        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                spec,
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, timeout);
        ResponseWrapperFacade replyWrapper = apiFactory.responseMapper().toReply(requestWrapper, resp, api.v1.CacheControl.getDefaultInstance());
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFuture(replyWrapper);
        DefaultApiResponse<T> apiResponse = new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                (Class<T>) resp.getClass(),
                requestWrapper);

        var mock = Mockito.doReturn(apiResponse).when(service);
        action.accept(mock);
        return new MockCall(mode -> verifyServiceCall(service, action, mode));
    }

    public <T extends Message, V> MockCall mockServiceCall(V api, CheckedConsumer<V> action, T resp, T... nextResp) throws Throwable {
        RequestQueuePostSpec spec = RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build();
        Duration timeout = Duration.ofSeconds(30);

        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                spec,
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                },
                timeout);

        var mock = Mockito.doReturn(toApiResponse(resp, requestWrapper), Arrays.stream(nextResp).map(r -> toApiResponse(r, requestWrapper)).toArray())
                .when(api);
        action.accept(mock);
        return new MockCall(mode -> verifyServiceCall(api, action, mode));
    }

    public <T extends Message, V> MockCall mockServiceCall(V api, CheckedConsumer<V> action, List<DefaultApiResponse<T>> responses) throws Throwable {
        var mock = Mockito.doReturn(responses.get(0), responses.subList(1, responses.size()).toArray()).when(api);
        action.accept(mock);
        return new MockCall(mode -> verifyServiceCall(api, action, mode));
    }

    private <T extends Message> DefaultApiResponse<T> toApiResponse(T resp, RequestWrapperFacade requestWrapper) {
        ResponseWrapperFacade replyWrapper = apiFactory.responseMapper().toReply(requestWrapper, resp, api.v1.CacheControl.getDefaultInstance());
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFuture(replyWrapper);
        return new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                (Class<T>) resp.getClass(),
                requestWrapper);
    }

    public <T extends Message> DefaultApiResponse<T> toApiResponse(T resp, ResponseStatusFacade status) {
        RequestQueuePostSpec spec = RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build();
        Duration timeout = Duration.ofSeconds(30);
        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                spec,
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                },
                timeout);

        ResponseWrapperFacade replyWrapper = apiFactory.responseMapper().toReply(requestWrapper, resp, status);
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFuture(replyWrapper);

        return new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                (Class<T>) resp.getClass(),
                requestWrapper);
    }

    public <T extends Message, V> MockCall mockServiceCallWithTimeout(V api, CheckedConsumer<V> action, Class<T> respClass) throws Throwable {
        Duration timeout = Duration.ofSeconds(30);
        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build(),
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, timeout);
        String messageId = requestWrapper.headers().getMessageId();
        RequestReplyTimeout replyTimeout = new RequestReplyTimeout(timeout, messageId, false);
        ResponseStatusFacade fault = apiFactory.toExceptional(messageId, replyTimeout);
        ResponseWrapperFacade replyWrapper = apiFactory.responseMapper().toReply(requestWrapper, Empty.getDefaultInstance(), fault);
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFuture(replyWrapper);
        DefaultApiResponse<T> apiResponse = new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                respClass,
                requestWrapper);

        var mock = Mockito.doReturn(apiResponse).when(api);
        action.accept(mock);
        return new MockCall(mode -> {
            try {
                verifyServiceCall(api, action, mode);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        });
    }
    public <T extends Message, V> MockCall mockServiceCall(
            V api,
            CheckedConsumer<V> action,
            T prototype,
            Throwable err) throws Throwable {
        Duration timeout = Duration.ofSeconds(30);
        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build(),
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, timeout);

        String messageId = requestWrapper.headers().getMessageId();
        ResponseStatusFacade fault = apiFactory.toExceptional(messageId, err);
        ResponseWrapperFacade replyWrapper = apiFactory.responseMapper().toReply(requestWrapper, prototype, fault);
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFuture(replyWrapper);
        DefaultApiResponse<T> apiResponse = new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                (Class<T>) prototype.getClass(),
                requestWrapper);

        var mock = Mockito.doReturn(apiResponse).when(api);
        action.accept(mock);
        return new MockCall(mode -> verifyServiceCall(api, action, mode));
    }
    public <T extends Message, V extends ServiceApi> MockCall mockServiceCall(
            V api,
            CheckedConsumer<V> action,
            T resp,
            ResponseStatusFacade err) throws Throwable {
        RequestQueuePostSpec spec = RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build();
        Duration timeout = Duration.ofSeconds(30);

        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                spec,
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, timeout);

        ResponseWrapperFacade replyWrapper = apiFactory.responseMapper().toReply(requestWrapper, resp, err);
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFuture(replyWrapper);
        DefaultApiResponse<T> apiResponse = new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                (Class<T>) resp.getClass(),
                requestWrapper);

        var mock = Mockito.doReturn(apiResponse).when(api);
        action.accept(mock);
        return new MockCall(mode -> verifyServiceCall(api, action, mode));
    }

    public <T extends Message, V> MockCall mockServiceCallWithExceptionResponse(V api, CheckedConsumer<V> action, Class<T> respClass, Throwable exception) throws Throwable {
        Duration timeout = Duration.ofSeconds(30);
        RequestWrapperFacade requestWrapper = apiFactory.requestMapper().pack(
                RequestQueuePostSpec.newBuilder(foo).setTopic(topic).build(),
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, timeout);
        ListenableFuture<ResponseWrapperFacade> completable = Futures.immediateFailedFuture(exception);
        DefaultApiResponse<T> apiResponse = new DefaultApiResponse<>(
                props,
                completable,
                apiFactory,
                respClass,
                requestWrapper);

        var mock = Mockito.doReturn(apiResponse).when(api);
        action.accept(mock);
        return new MockCall(mode -> {
            try {
                verifyServiceCall(api, action, mode);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        });
    }


    public MockCall mockSinkEvent(QueuePostTemplate<?> postTemplate, Consumer<QueuePostTemplate<?>> action) {
        //
        // ~ do not confuse with guava's (it might cause crazy miss-leading errors)
        //
        var mock = Mockito.doReturn(mock(CompletableFuture.class)).when(postTemplate);
        action.accept(mock);
        return new MockCall(mode -> verifyPostCall(postTemplate, action, mode));
    }
    private static void verifyPostCall(QueuePostTemplate<?> postTemplate, Consumer<QueuePostTemplate<?>> action, VerificationMode verification) {
        QueuePostTemplate<?> mock = Mockito.verify(postTemplate, verification);
        action.accept(mock);
    }
    private static <V> void verifyServiceCall(V api, CheckedConsumer<V> action, VerificationMode verification) throws Throwable {
        V mock = Mockito.verify(api, verification);
        action.accept(mock);
    }
    public <REQ extends Message, RESP extends Message.Builder> TransactionalRequest<REQ, RESP> toTransactionalRequest(
            Class<REQ> reqType,
            REQ req,
            RESP resp) {
        long now = System.currentTimeMillis();
        RequestWrapperFacade reqw = apiFactory.requestMapper().pack(
                RequestQueuePostSpec.newBuilder(req).setTopic(topic).build(),
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, Duration.ofSeconds(30));

        return new EmbeddedTransactionalRequest<>(
                reqType,
                resp,
                new WorkUnit() {
                    @Override
                    public String topic() {
                        return topic.name().toString();
                    }
                    @Override
                    public long timestamp() {
                        return now;
                    }
                    @Override
                    public byte[] key() {
                        return null;
                    }
                    @Override
                    public ByteSource value() {
                        return ByteSource.empty();
                    }
                },
                reqw,
                Sinks.one());
    }
    public <REQ extends Message, RESP extends Message.Builder> TransactionalRequest<REQ, RESP> toTransactionalRequest(
            Class<REQ> reqType,
            REQ req,
            RESP resp,
            AsciiString hash) {
        long now = System.currentTimeMillis();
        RequestWrapperFacade reqw = apiFactory.requestMapper().pack(
                RequestQueuePostSpec.newBuilder(req).setTopic(topic).setRoutingKey(hash).build(),
                new ContextPropagator<api.v1.Headers.Builder>() {
                    @Override
                    public void injectContext(api.v1.Headers.Builder into) {

                    }
                }, Duration.ofSeconds(30));

        return new EmbeddedTransactionalRequest<>(
                reqType,
                resp,
                new WorkUnit() {
                    @Override
                    public String topic() {
                        return topic.name().toString();
                    }
                    @Override
                    public long timestamp() {
                        return now;
                    }
                    @Override
                    public byte[] key() {
                        return hash.toString().getBytes();
                    }
                    @Override
                    public ByteSource value() {
                        return ByteSource.empty();
                    }
                }, reqw,
                Sinks.one());
    }

    public static class MockCall {
        private final CheckedConsumer<VerificationMode> verifyCallback;

        public MockCall(CheckedConsumer<VerificationMode> verifyCallback) {
            this.verifyCallback = Objects.requireNonNull(verifyCallback);
        }
        public void verify() throws Throwable {
            verifyCallback.accept(Mockito.times(1));
        }
        public void verify(VerificationMode verificationMode) throws Throwable {
            verifyCallback.accept(verificationMode);
        }
    }
}
