package com.turbospaces.boot.test;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import io.ebean.event.BeanQueryAdapter;
import io.ebean.event.BeanQueryRequest;

public class EbeanSqlQueryCounter implements BeanQueryAdapter {

    private static int selectCount = 0;
    private static int updateCount = 0;
    private static int deleteCount = 0;
    private static int queryCount = 0;

    //
    // ~ Note that a BeanQueryAdapter should be thread safe
    //
    private static final ReadWriteLock lock = new ReentrantReadWriteLock();
    private static final Lock write = lock.writeLock();
    private static final Lock read = lock.readLock();

    @Override
    public boolean isRegisterFor(Class<?> cls) {
        return true;
    }

    @Override
    public int getExecutionOrder() {
        return 0;
    }

    @Override
    public void preQuery(BeanQueryRequest<?> request) {
        atomicUpdate(() -> {
            switch (request.query().getQueryType()) {
                case FIND -> selectCount += 1;
                case UPDATE -> updateCount += 1;
                case DELETE -> deleteCount += 1;
                default -> throw new IllegalArgumentException("Unexpected value: " + request.query().getQueryType());
            }
            queryCount += 1;
        });
    }

    public static void reset() {
        atomicUpdate(() -> {
            queryCount = 0;
            selectCount = 0;
            updateCount = 0;
            deleteCount = 0;
        });
    }

    public static void assertQueryCount(int expected) {
        read(() -> {
            if (expected != queryCount) {
                throw new AssertionError("Expected " + expected + " queries, but got " + queryCount);
            }
        });
    }

    public static void assertSelectCount(int expected) {
        read(() -> {
            if (expected != selectCount) {
                throw new AssertionError("Expected " + expected + " queries, but got " + selectCount);
            }
        });
    }

    public static void assertUpdateCount(int expected) {
        read(() -> {
            if (expected != updateCount) {
                throw new AssertionError("Expected " + expected + " queries, but got " + updateCount);
            }
        });
    }

    public static void assertDeleteCount(int expected) {
        read(() -> {
            if (expected != deleteCount) {
                throw new AssertionError("Expected " + expected + " queries, but got " + deleteCount);
            }
        });
    }

    private static void atomicUpdate(Runnable action) {
        write.lock();
        try {
            action.run();
        } finally {
            write.unlock();
        }
    }

    private static void read(Runnable action) {
        read.lock();
        try {
            action.run();
        } finally {
            read.unlock();
        }
    }

}
