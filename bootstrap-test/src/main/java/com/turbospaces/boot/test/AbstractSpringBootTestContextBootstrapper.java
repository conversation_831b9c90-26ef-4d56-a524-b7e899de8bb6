package com.turbospaces.boot.test;

import java.lang.reflect.UndeclaredThrowableException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.concurrent.ConcurrentException;
import org.apache.commons.lang3.concurrent.LazyInitializer;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.SpringBootContextLoader;
import org.springframework.boot.test.context.SpringBootTestContextBootstrapper;
import org.springframework.cloud.SmartCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.UriBasedServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaZKBroker;
import org.springframework.kafka.test.core.BrokerAddress;
import org.springframework.test.context.ContextConfigurationAttributes;
import org.springframework.test.context.ContextLoader;
import org.testcontainers.clickhouse.ClickHouseContainer;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

import com.google.common.collect.Iterables;
import com.google.common.net.HostAndPort;
import com.turbospaces.boot.AbstractBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.ClickhouseServiceInfo;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractSpringBootTestContextBootstrapper<T extends ApplicationProperties>
        extends SpringBootTestContextBootstrapper
        implements SmartCloud {
    public static final int PG_PORT = 5432;
    public static final int CLICKHOUSE_PORT = 8123;
    public static final int REDIS_PORT = 6379;
    public static final int MEMCACHED_PORT = 11211;

    public static final String DB = "defaultdb";
    public static final String NAMESPACE = "junit";
    public static final String USERNAME = "test";
    public static final String PASSWORD = "test";

    static {
        System.setProperty("ebean.registerShutdownHook", Boolean.FALSE.toString());
        System.setProperty("org.springframework.boot.logging.LoggingSystem", "none");
    }

    private final Map<String, ServiceInfo> services = new HashMap<>();
    private final LazyInitializer<T> props = new LazyInitializer<>() {
        @Override
        protected T initialize() throws ConcurrentException {
            try {
                return createProps();
            } catch (Exception err) {
                throw new ConcurrentException(err);
            }
        }
    };
    private PostgreSQLContainer<?> pgContainer;
    private GenericContainer<?> redisContainer;
    private GenericContainer<?> memcachedContainer;
    private ClickHouseContainer clickhouseContainer;

    @Override
    protected ContextLoader resolveContextLoader(Class<?> testClass, List<ContextConfigurationAttributes> configAttributesList) {
        return new SpringBootContextLoader() {
            @Override
            protected SpringApplication getSpringApplication() {
                try {
                    T delegate = props.get();

                    //
                    // ~ pass as property and let cloud connector to pick up it via property
                    //
                    for (ServiceInfo info : services.values()) {
                        String uri = null;
                        if (info instanceof UriBasedServiceInfo) {
                            uri = ((UriBasedServiceInfo) info).getUri();
                        } else if (info instanceof RawServiceInfo raw) {
                            uri = raw.toByteSource().asCharSource(StandardCharsets.UTF_8).read();
                        } else {
                            throw new IllegalArgumentException();
                        }
                        delegate.cfg().setLocalProperty(String.format("service.%s.uri", info.getId()), uri);
                    }

                    ConfigurableApplicationBootstrap<T> boostrap = new ConfigurableApplicationBootstrap<>(delegate) {
                        @Override
                        public void shutdown() throws Exception {
                            try {
                                super.shutdown();
                            } finally {
                                afterShutdown(delegate, this);
                            }
                        }
                    };
                    beforeBoostrap(delegate, boostrap);
                    return boostrap;
                } catch (Throwable err) {
                    throw new UndeclaredThrowableException(err);
                }
            }
        };
    }
    @Override
    public void addUps(ServiceInfo info) {
        services.put(info.getId(), info);
    }
    @Override
    public boolean removeUps(String id) {
        if (props.isInitialized()) {
            try {
                T delegate = props.get();
                ApplicationConfig cfg = delegate.cfg();
                cfg.clearLocalProperty(String.format("service.%s.uri", id));
                return Objects.nonNull(services.remove(id));
            } catch (ConcurrentException err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
        return false;
    }
    protected abstract T createProps() throws Exception;
    protected void beforeBoostrap(T properties, AbstractBootstrap<T> bootstrap) throws Exception {

    }
    protected void afterShutdown(T properties, AbstractBootstrap<T> bootstrap) throws Exception {
        //
        // ~ PG
        //
        try {
            if (Objects.nonNull(pgContainer)) {
                pgContainer.close();
            }
        } catch (Exception err) {
            log.error(err.getMessage(), err);
        }

        //
        // ~ redis
        //
        try {
            if (Objects.nonNull(redisContainer)) {
                redisContainer.close();
            }
        } catch (Exception err) {
            log.error(err.getMessage(), err);
        }

        //
        // ~ memcached
        //
        try {
            if (Objects.nonNull(memcachedContainer)) {
                memcachedContainer.close();
            }
        } catch (Exception err) {
            log.error(err.getMessage(), err);
        }

        //
        // ~ Clickhouse
        //
        try {
            if (Objects.nonNull(clickhouseContainer)) {
                clickhouseContainer.close();
            }
        } catch (Exception err) {
            log.error(err.getMessage(), err);
        }
    }
    protected PostgresqlServiceInfo withPG(AbstractBootstrap<T> bootstrap) {
        pgContainer = new PostgreSQLContainer<>("postgres:latest");
        pgContainer.withUsername(USERNAME);
        pgContainer.withPassword(PASSWORD);
        pgContainer.withDatabaseName(DB);
        pgContainer.addExposedPorts(PG_PORT);
        pgContainer.setCommand("postgres", "-c", "wal_level=logical");
        pgContainer.start();

        var pgUri = UPSs.toUriInfo(
                UPSs.POSTGRES_OWNER,
                PostgresqlServiceInfo.POSTGRES_SCHEME,
                pgContainer.getHost(),
                pgContainer.getMappedPort(PG_PORT),
                pgContainer.getUsername(),
                pgContainer.getPassword(),
                pgContainer.getDatabaseName()).getUriString();

        var ups = new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri);
        bootstrap.addUps(ups);
        return ups;
    }
    protected ClickhouseServiceInfo withClickhouse(AbstractBootstrap<T> bootstrap) {
        clickhouseContainer = new ClickHouseContainer("clickhouse/clickhouse-server:latest");
        clickhouseContainer.withUsername(USERNAME);
        clickhouseContainer.withPassword(PASSWORD);
        clickhouseContainer.withDatabaseName(DB);
        clickhouseContainer.withExposedPorts(CLICKHOUSE_PORT);
        clickhouseContainer.start();

        var clickhouseUri = UPSs.toUriInfo(
                UPSs.CLICKHOUSE,
                ClickhouseServiceInfo.CLICKHOUSE_SCHEME,
                clickhouseContainer.getHost(),
                clickhouseContainer.getMappedPort(CLICKHOUSE_PORT),
                clickhouseContainer.getUsername(),
                clickhouseContainer.getPassword(),
                clickhouseContainer.getDatabaseName(),
                Map.of("clickhouse.jdbc.v1", Boolean.TRUE.toString(), "autoCommit", Boolean.FALSE.toString())).getUriString();

        var ups = new ClickhouseServiceInfo(UPSs.CLICKHOUSE, clickhouseUri);
        bootstrap.addUps(ups);
        return ups;
    }
    protected RedisServiceInfo withRedis(AbstractBootstrap<T> bootstrap) {
        redisContainer = new GenericContainer<>(DockerImageName.parse("redis:latest"));
        redisContainer.withNetworkAliases("redis");
        redisContainer.addExposedPort(REDIS_PORT);
        redisContainer.start();

        var ups = new RedisServiceInfo(UPSs.REDIS, "localhost", redisContainer.getMappedPort(REDIS_PORT), null);
        bootstrap.addUps(ups);
        return ups;
    }
    protected MemcachedServiceInfo withMemcached(AbstractBootstrap<T> bootstrap) {
        memcachedContainer = new GenericContainer<>(DockerImageName.parse("memcached:latest"));
        memcachedContainer.withNetworkAliases("redis");
        memcachedContainer.addExposedPort(MEMCACHED_PORT);
        memcachedContainer.start();

        var ups = new MemcachedServiceInfo(UPSs.MEMCACHED, "localhost", memcachedContainer.getMappedPort(MEMCACHED_PORT));
        bootstrap.addUps(ups);
        return ups;
    }

    protected static class ConfigurableApplicationBootstrap<T extends ApplicationProperties> extends AbstractBootstrap<T> {
        protected ConfigurableApplicationBootstrap(T props) throws Throwable {
            super(props);
        }
        @Override
        protected void applyInitializers(ConfigurableApplicationContext context) {
            super.applyInitializers(context);

            if (context.containsBean(EmbeddedKafkaBroker.BEAN_NAME)) {
                Map<String, EmbeddedKafkaZKBroker> mzk = context.getBeanFactory().getBeansOfType(EmbeddedKafkaZKBroker.class);
                if (MapUtils.isNotEmpty(mzk)) {
                    EmbeddedKafkaZKBroker broker = Iterables.getOnlyElement(mzk.values());
                    if (Objects.nonNull(broker)) {
                        if (Objects.nonNull(broker.getBrokerAddresses())) {
                            BrokerAddress address = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses()));
                            KafkaServiceInfo ksi = new KafkaServiceInfo(HostAndPort.fromParts(address.getHost(), address.getPort()));
                            addUps(ksi);
                        }
                    }
                }

                Map<String, EmbeddedKafkaBroker> mraft = context.getBeanFactory().getBeansOfType(EmbeddedKafkaBroker.class);
                if (MapUtils.isNotEmpty(mraft)) {
                    EmbeddedKafkaBroker broker = Iterables.getOnlyElement(mraft.values());
                    if (Objects.nonNull(broker)) {
                        KafkaServiceInfo ksi = new KafkaServiceInfo("kafka://" + broker.getBrokersAsString());
                        addUps(ksi);
                    }
                }
            }
        }
    }
}
