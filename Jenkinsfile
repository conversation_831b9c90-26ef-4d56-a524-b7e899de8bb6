pipeline {
  agent {
    node { label 'patrianna-dev' }
  }
  triggers { pollSCM('H/2 * * * *') }

  environment {
    NEXUS_OSS_REPO = 'sonatype-nexus-snapshots'
    MAVEN_ARGS = '-Dmaven.wagon.rto=60000 -Dmaven.wagon.http.retryHandler.count=3 -Dmaven.wagon.httpconnectionManager.ttlSeconds=30 -Daether.connector.http.connectionMaxTtl=30'
  }
  options {
    disableConcurrentBuilds()
    timeout(time: 1, unit: 'HOURS')
  }
  tools {
    maven 'mvn3'
    jdk 'jdk21'
  }
  stages {
	stage ('Build') {
      when {
        not { changelog '.*\\[maven-release-plugin\\].*' }
      }
      steps {
        withCredentials([usernamePassword(credentialsId: 'nexus-oss-registry-auth', usernameVariable: 'NEXUS_OSS_USER', passwordVariable: 'NEXUS_OSS_CREDENTIALS')]) {
          sh 'mvn -s settings.xml clean deploy -U'
          sh 'mvn org.codehaus.mojo:build-helper-maven-plugin:remove-project-artifact -Dbuildhelper.removeAll=true'
        }
      }
	}
  } 
}