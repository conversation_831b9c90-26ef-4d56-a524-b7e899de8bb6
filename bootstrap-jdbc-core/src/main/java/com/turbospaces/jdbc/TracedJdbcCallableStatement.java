package com.turbospaces.jdbc;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Date;
import java.sql.NClob;
import java.sql.Ref;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLType;
import java.sql.SQLXML;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Map;

public class TracedJdbcCallableStatement extends TracedJdbcPreparedStatement implements CallableStatement {
    private final CallableStatement statement;

    public TracedJdbcCallableStatement(CallableStatement statement) {
        super(statement);
        this.statement = statement;
    }
    @Override
    public void registerOutParameter(int parameterIndex, int sqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterIndex, sqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(int parameterIndex, int sqlType, int scale) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterIndex, sqlType, scale);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean wasNull() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.wasNull();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getString(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getString(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean getBoolean(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBoolean(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public byte getByte(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getByte(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public short getShort(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getShort(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public int getInt(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getInt(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public long getLong(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getLong(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public float getFloat(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getFloat(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public double getDouble(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getDouble(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    @SuppressWarnings("deprecation")
    public BigDecimal getBigDecimal(int parameterIndex, int scale) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBigDecimal(parameterIndex, scale);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public byte[] getBytes(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBytes(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Date getDate(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getDate(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Time getTime(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTime(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Timestamp getTimestamp(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTimestamp(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Object getObject(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getObject(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public BigDecimal getBigDecimal(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBigDecimal(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Object getObject(int parameterIndex, Map<String, Class<?>> map) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getObject(parameterIndex, map);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Ref getRef(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getRef(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Blob getBlob(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBlob(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Clob getClob(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getClob(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Array getArray(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getArray(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Date getDate(int parameterIndex, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getDate(parameterIndex, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Time getTime(int parameterIndex, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTime(parameterIndex, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Timestamp getTimestamp(int parameterIndex, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTimestamp(parameterIndex, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(int parameterIndex, int sqlType, String typeName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterIndex, sqlType, typeName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(String parameterName, int sqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterName, sqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(String parameterName, int sqlType, int scale) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterName, sqlType, scale);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(String parameterName, int sqlType, String typeName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterName, sqlType, typeName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public URL getURL(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getURL(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setURL(String parameterName, URL val) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setURL(parameterName, val);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNull(String parameterName, int sqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNull(parameterName, sqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBoolean(String parameterName, boolean x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBoolean(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setByte(String parameterName, byte x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setByte(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setShort(String parameterName, short x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setShort(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setInt(String parameterName, int x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setInt(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setLong(String parameterName, long x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setLong(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setFloat(String parameterName, float x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setFloat(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setDouble(String parameterName, double x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setDouble(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBigDecimal(String parameterName, BigDecimal x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBigDecimal(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setString(String parameterName, String x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setString(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBytes(String parameterName, byte[] x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBytes(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setDate(String parameterName, Date x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setDate(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setTime(String parameterName, Time x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTime(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setTimestamp(String parameterName, Timestamp x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTimestamp(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setAsciiStream(String parameterName, InputStream x, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setAsciiStream(parameterName, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBinaryStream(String parameterName, InputStream x, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBinaryStream(parameterName, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setObject(String parameterName, Object x, int targetSqlType, int scale) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterName, x, targetSqlType, scale);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setObject(String parameterName, Object x, int targetSqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterName, x, targetSqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setObject(String parameterName, Object x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setCharacterStream(String parameterName, Reader reader, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCharacterStream(parameterName, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setDate(String parameterName, Date x, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setDate(parameterName, x, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setTime(String parameterName, Time x, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTime(parameterName, x, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setTimestamp(String parameterName, Timestamp x, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTimestamp(parameterName, x, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNull(String parameterName, int sqlType, String typeName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNull(parameterName, sqlType, typeName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getString(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getString(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean getBoolean(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBoolean(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public byte getByte(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getByte(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public short getShort(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getShort(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public int getInt(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getInt(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public long getLong(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getLong(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public float getFloat(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getFloat(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public double getDouble(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getDouble(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public byte[] getBytes(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBytes(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Date getDate(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getDate(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Time getTime(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTime(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Timestamp getTimestamp(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTimestamp(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Object getObject(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getObject(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public BigDecimal getBigDecimal(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBigDecimal(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Object getObject(String parameterName, Map<String, Class<?>> map) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getObject(parameterName, map);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Ref getRef(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getRef(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Blob getBlob(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getBlob(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Clob getClob(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getClob(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Array getArray(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getArray(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Date getDate(String parameterName, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getDate(parameterName, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Time getTime(String parameterName, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTime(parameterName, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Timestamp getTimestamp(String parameterName, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getTimestamp(parameterName, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public URL getURL(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getURL(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public RowId getRowId(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getRowId(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public RowId getRowId(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getRowId(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setRowId(String parameterName, RowId x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setRowId(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNString(String parameterName, String value) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNString(parameterName, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNCharacterStream(String parameterName, Reader value, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNCharacterStream(parameterName, value, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNClob(String parameterName, NClob value) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNClob(parameterName, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setClob(String parameterName, Reader reader, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setClob(parameterName, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBlob(String parameterName, InputStream inputStream, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBlob(parameterName, inputStream, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNClob(String parameterName, Reader reader, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNClob(parameterName, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public NClob getNClob(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getNClob(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public NClob getNClob(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getNClob(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setSQLXML(String parameterName, SQLXML xmlObject) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setSQLXML(parameterName, xmlObject);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public SQLXML getSQLXML(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getSQLXML(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public SQLXML getSQLXML(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getSQLXML(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getNString(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getNString(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getNString(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getNString(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Reader getNCharacterStream(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getNCharacterStream(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Reader getNCharacterStream(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getNCharacterStream(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Reader getCharacterStream(int parameterIndex) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getCharacterStream(parameterIndex);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Reader getCharacterStream(String parameterName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getCharacterStream(parameterName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBlob(String parameterName, Blob x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBlob(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setClob(String parameterName, Clob x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setClob(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setAsciiStream(String parameterName, InputStream x, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setAsciiStream(parameterName, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBinaryStream(String parameterName, InputStream x, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBinaryStream(parameterName, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setCharacterStream(String parameterName, Reader reader, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCharacterStream(parameterName, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setAsciiStream(String parameterName, InputStream x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setAsciiStream(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBinaryStream(String parameterName, InputStream x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBinaryStream(parameterName, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setCharacterStream(String parameterName, Reader reader) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCharacterStream(parameterName, reader);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNCharacterStream(String parameterName, Reader value) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNCharacterStream(parameterName, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setClob(String parameterName, Reader reader) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setClob(parameterName, reader);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setBlob(String parameterName, InputStream inputStream) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBlob(parameterName, inputStream);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNClob(String parameterName, Reader reader) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNClob(parameterName, reader);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public <T> T getObject(int parameterIndex, Class<T> type) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getObject(parameterIndex, type);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public <T> T getObject(String parameterName, Class<T> type) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getObject(parameterName, type);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setObject(String parameterName, Object x, SQLType targetSqlType, int scaleOrLength) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterName, x, targetSqlType, scaleOrLength);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setObject(String parameterName, Object x, SQLType targetSqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterName, x, targetSqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(int parameterIndex, SQLType sqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterIndex, sqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(int parameterIndex, SQLType sqlType, int scale) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterIndex, sqlType, scale);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(int parameterIndex, SQLType sqlType, String typeName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterIndex, sqlType, typeName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(String parameterName, SQLType sqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterName, sqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(String parameterName, SQLType sqlType, int scale) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterName, sqlType, scale);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void registerOutParameter(String parameterName, SQLType sqlType, String typeName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.registerOutParameter(parameterName, sqlType, typeName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
}
