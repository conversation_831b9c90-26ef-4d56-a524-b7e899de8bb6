package com.turbospaces.jdbc;

import java.io.Closeable;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.Objects;

import javax.sql.DataSource;

import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.zaxxer.hikari.HikariDataSource;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.db.PostgreSQLDatabaseMetrics;
import io.vavr.CheckedFunction0;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HikariDataSourceFactoryBean extends AbstractFactoryBean<DataSource> {
    private final ServiceInfo si;
    private final MeterRegistry meterRegistry;
    private final JdbcPoolServiceConfig jdbcCfg;

    public HikariDataSourceFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, ServiceInfo si) {
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.si = Objects.requireNonNull(si);
        this.jdbcCfg = new JdbcPoolServiceConfig(props, false);
    }
    public HikariDataSourceFactoryBean(ServiceInfo appInfo, MeterRegistry meterRegistry, JdbcPoolServiceConfig jdbcCfg) {
        this.meterRegistry = meterRegistry;
        this.si = Objects.requireNonNull(appInfo);
        this.jdbcCfg = Objects.requireNonNull(jdbcCfg);
    }
    @Override
    public Class<?> getObjectType() {
        return DataSource.class;
    }
    @Override
    protected DataSource createInstance() throws Exception {
        var creator = new DatasourceCreator(meterRegistry);
        var db = (HikariDataSource) creator.create(si, jdbcCfg);

        if (si instanceof PostgresqlServiceInfo psi) {
            new PostgreSQLDatabaseMetrics(db, psi.getPath()).bindTo(meterRegistry);
        }

        return new DataSource() {
            @Override
            public TracedJdbcConnection getConnection(String username, String password) throws SQLException {
                return new TracedJdbcConnection(new CheckedFunction0<Connection>() {
                    @Override
                    public Connection apply() throws Throwable {
                        return db.getConnection(username, password);
                    }
                }, db);
            }
            @Override
            public TracedJdbcConnection getConnection() throws SQLException {
                return new TracedJdbcConnection(new CheckedFunction0<Connection>() {
                    @Override
                    public Connection apply() throws Throwable {
                        return db.getConnection();
                    }
                }, db);
            }
            @Override
            public <T> T unwrap(Class<T> iface) throws SQLException {
                return db.unwrap(iface);
            }
            @Override
            public boolean isWrapperFor(Class<?> iface) throws SQLException {
                return db.isWrapperFor(iface);
            }
            @Override
            public java.util.logging.Logger getParentLogger() throws SQLFeatureNotSupportedException {
                return db.getParentLogger();
            }
            @Override
            public void setLoginTimeout(int seconds) throws SQLException {
                db.setLoginTimeout(seconds);
            }
            @Override
            public void setLogWriter(PrintWriter out) throws SQLException {
                db.setLogWriter(out);
            }
            @Override
            public int getLoginTimeout() throws SQLException {
                return db.getLoginTimeout();
            }
            @Override
            public PrintWriter getLogWriter() throws SQLException {
                return db.getLogWriter();
            }
        };
    }
    @Override
    protected void destroyInstance(DataSource instance) throws Exception {
        if (Objects.nonNull(instance)) {
            log.info("about to close JDBC pool now ...");
            StopWatch stopWatch = StopWatch.createStarted();
            if (instance instanceof Closeable) {
                ((Closeable) instance).close();
            }
            stopWatch.stop();
            log.info("closed JDBC pool in {}", stopWatch);
        }
    }
}
