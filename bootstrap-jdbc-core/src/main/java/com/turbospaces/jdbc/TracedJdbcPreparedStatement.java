package com.turbospaces.jdbc;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Date;
import java.sql.NClob;
import java.sql.ParameterMetaData;
import java.sql.PreparedStatement;
import java.sql.Ref;
import java.sql.ResultSetMetaData;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLType;
import java.sql.SQLXML;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;

public class TracedJdbcPreparedStatement extends TracedJdbcStatement implements PreparedStatement {
    private final PreparedStatement statement;

    public TracedJdbcPreparedStatement(PreparedStatement statement) {
        super(statement);
        this.statement = statement;
    }
    @Override
    public final TracedResultSet executeQuery() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedResultSet(statement.executeQuery());
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int executeUpdate() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeUpdate();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNull(int parameterIndex, int sqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNull(parameterIndex, sqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBoolean(int parameterIndex, boolean x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBoolean(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setByte(int parameterIndex, byte x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setByte(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setShort(int parameterIndex, short x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setShort(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setInt(int parameterIndex, int x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setInt(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setLong(int parameterIndex, long x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setLong(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setFloat(int parameterIndex, float x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setFloat(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setDouble(int parameterIndex, double x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setDouble(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBigDecimal(int parameterIndex, BigDecimal x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBigDecimal(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setString(int parameterIndex, String x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setString(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBytes(int parameterIndex, byte[] x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBytes(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setDate(int parameterIndex, Date x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setDate(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setTime(int parameterIndex, Time x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTime(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setTimestamp(int parameterIndex, Timestamp x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTimestamp(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setAsciiStream(int parameterIndex, InputStream x, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setAsciiStream(parameterIndex, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    @SuppressWarnings("deprecation")
    public final void setUnicodeStream(int parameterIndex, InputStream x, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setUnicodeStream(parameterIndex, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBinaryStream(int parameterIndex, InputStream x, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBinaryStream(parameterIndex, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void clearParameters() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.clearParameters();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setObject(int parameterIndex, Object x, int targetSqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterIndex, x, targetSqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setObject(int parameterIndex, Object x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean execute() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.execute();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void addBatch() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.addBatch();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setCharacterStream(int parameterIndex, Reader reader, int length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCharacterStream(parameterIndex, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setRef(int parameterIndex, Ref x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setRef(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBlob(int parameterIndex, Blob x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBlob(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setClob(int parameterIndex, Clob x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setClob(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setArray(int parameterIndex, Array x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setArray(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final ResultSetMetaData getMetaData() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getMetaData();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setDate(int parameterIndex, Date x, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setDate(parameterIndex, x, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setTime(int parameterIndex, Time x, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTime(parameterIndex, x, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setTimestamp(int parameterIndex, Timestamp x, Calendar cal) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setTimestamp(parameterIndex, x, cal);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNull(int parameterIndex, int sqlType, String typeName) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNull(parameterIndex, sqlType, typeName);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setURL(int parameterIndex, URL x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setURL(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final ParameterMetaData getParameterMetaData() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getParameterMetaData();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setRowId(int parameterIndex, RowId x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setRowId(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNString(int parameterIndex, String value) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNString(parameterIndex, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNCharacterStream(int parameterIndex, Reader value, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNCharacterStream(parameterIndex, value, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNClob(int parameterIndex, NClob value) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNClob(parameterIndex, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setClob(int parameterIndex, Reader reader, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setClob(parameterIndex, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBlob(int parameterIndex, InputStream inputStream, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBlob(parameterIndex, inputStream, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNClob(int parameterIndex, Reader reader, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNClob(parameterIndex, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setSQLXML(int parameterIndex, SQLXML xmlObject) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setSQLXML(parameterIndex, xmlObject);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setObject(int parameterIndex, Object x, int targetSqlType, int scaleOrLength) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterIndex, x, targetSqlType, scaleOrLength);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setAsciiStream(int parameterIndex, InputStream x, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setAsciiStream(parameterIndex, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBinaryStream(int parameterIndex, InputStream x, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBinaryStream(parameterIndex, x, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setCharacterStream(int parameterIndex, Reader reader, long length) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCharacterStream(parameterIndex, reader, length);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setAsciiStream(int parameterIndex, InputStream x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setAsciiStream(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBinaryStream(int parameterIndex, InputStream x) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBinaryStream(parameterIndex, x);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setCharacterStream(int parameterIndex, Reader reader) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCharacterStream(parameterIndex, reader);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNCharacterStream(int parameterIndex, Reader value) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNCharacterStream(parameterIndex, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setClob(int parameterIndex, Reader reader) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setClob(parameterIndex, reader);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setBlob(int parameterIndex, InputStream inputStream) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setBlob(parameterIndex, inputStream);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setNClob(int parameterIndex, Reader reader) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setNClob(parameterIndex, reader);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setObject(int parameterIndex, Object x, SQLType targetSqlType, int scaleOrLength) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterIndex, x, targetSqlType, scaleOrLength);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setObject(int parameterIndex, Object x, SQLType targetSqlType) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setObject(parameterIndex, x, targetSqlType);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long executeLargeUpdate() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeLargeUpdate();
        } finally {
            nonBlocking.set(toReset);
        }
    }
}
