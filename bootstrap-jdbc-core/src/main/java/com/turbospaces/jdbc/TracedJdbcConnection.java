package com.turbospaces.jdbc;

import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.NClob;
import java.sql.SQLClientInfoException;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Savepoint;
import java.sql.ShardingKey;
import java.sql.Statement;
import java.sql.Struct;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.Executor;

import org.apache.commons.lang3.concurrent.ConcurrentException;
import org.apache.commons.lang3.concurrent.LazyInitializer;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.mutable.MutableObject;

import com.zaxxer.hikari.HikariDataSource;

import io.vavr.CheckedFunction0;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public final class TracedJdbcConnection implements Connection {
    private final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    private final MutableObject<Connection> cached = new MutableObject<>();
    private final LazyInitializer<Connection> supplier;
    private boolean autoCommit;

    public TracedJdbcConnection(CheckedFunction0<Connection> original, HikariDataSource ds) {
        this.autoCommit = ds.isAutoCommit();
        this.supplier = new LazyInitializer<>() {
            protected Connection initialize() throws ConcurrentException {
                try {
                    var toReturn = original.apply();
                    log.trace("connection init: {}", toReturn);
                    return toReturn;
                } catch (Throwable err) {
                    throw new ConcurrentException(err);
                }
            }
        };
    }
    @Override
    public boolean getAutoCommit() throws SQLException {
        return autoCommit;
    }
    @Override
    public Statement createStatement() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcStatement(getOrCreate().createStatement());
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcStatement(getOrCreate().createStatement(resultSetType, resultSetConcurrency));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcStatement(getOrCreate().createStatement(resultSetType, resultSetConcurrency, resultSetHoldability));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcPreparedStatement prepareStatement(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcPreparedStatement(getOrCreate().prepareStatement(sql));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcPreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcPreparedStatement(getOrCreate().prepareStatement(sql, resultSetType, resultSetConcurrency));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcPreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcPreparedStatement(getOrCreate().prepareStatement(sql, resultSetType, resultSetConcurrency, resultSetHoldability));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcPreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcPreparedStatement(getOrCreate().prepareStatement(sql, autoGeneratedKeys));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcPreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcPreparedStatement(getOrCreate().prepareStatement(sql, columnIndexes));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcPreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcPreparedStatement(getOrCreate().prepareStatement(sql, columnNames));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcCallableStatement prepareCall(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcCallableStatement(getOrCreate().prepareCall(sql));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcCallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcCallableStatement(getOrCreate().prepareCall(sql, resultSetType, resultSetConcurrency));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public TracedJdbcCallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedJdbcCallableStatement(getOrCreate().prepareCall(sql, resultSetType, resultSetConcurrency, resultSetHoldability));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Savepoint setSavepoint() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().setSavepoint();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Savepoint setSavepoint(String name) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().setSavepoint(name);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void rollback(Savepoint savepoint) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().rollback(savepoint);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void releaseSavepoint(Savepoint savepoint) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().releaseSavepoint(savepoint);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setAutoCommit(boolean autoCommit) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setAutoCommit(autoCommit);
            this.autoCommit = autoCommit;
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().unwrap(iface);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().isWrapperFor(iface);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String nativeSQL(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().nativeSQL(sql);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public DatabaseMetaData getMetaData() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getMetaData();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setReadOnly(boolean readOnly) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setReadOnly(readOnly);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean isReadOnly() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().isReadOnly();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setCatalog(String catalog) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setCatalog(catalog);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getCatalog() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getCatalog();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setTransactionIsolation(int level) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setTransactionIsolation(level);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public int getTransactionIsolation() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getTransactionIsolation();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public SQLWarning getWarnings() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getWarnings();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void clearWarnings() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().clearWarnings();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Map<String, Class<?>> getTypeMap() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getTypeMap();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setTypeMap(Map<String, Class<?>> map) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setTypeMap(map);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setHoldability(int holdability) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setHoldability(holdability);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public int getHoldability() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getHoldability();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Clob createClob() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().createClob();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Blob createBlob() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().createBlob();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public NClob createNClob() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().createNClob();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public SQLXML createSQLXML() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().createSQLXML();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean isValid(int timeout) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().isValid(timeout);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getClientInfo(String name) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getClientInfo(name);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Properties getClientInfo() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getClientInfo();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Array createArrayOf(String typeName, Object[] elements) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().createArrayOf(typeName, elements);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Struct createStruct(String typeName, Object[] attributes) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().createStruct(typeName, attributes);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setSchema(String schema) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setSchema(schema);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String getSchema() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getSchema();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void abort(Executor executor) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().abort(executor);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setNetworkTimeout(executor, milliseconds);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public int getNetworkTimeout() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().getNetworkTimeout();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void beginRequest() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().beginRequest();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void endRequest() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().endRequest();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean setShardingKeyIfValid(ShardingKey shardingKey, ShardingKey superShardingKey, int timeout) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().setShardingKeyIfValid(shardingKey, superShardingKey, timeout);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public boolean setShardingKeyIfValid(ShardingKey shardingKey, int timeout) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return getOrCreate().setShardingKeyIfValid(shardingKey, timeout);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setShardingKey(ShardingKey shardingKey, ShardingKey superShardingKey) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setShardingKey(shardingKey, superShardingKey);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setShardingKey(ShardingKey shardingKey) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            getOrCreate().setShardingKey(shardingKey);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void commit() throws SQLException {
        var opt = Optional.ofNullable(cached.get());
        if (opt.isPresent()) {
            boolean toReset = nonBlocking.get();
            try {
                nonBlocking.set(false);
                var connection = opt.get();
                connection.commit();
            } finally {
                nonBlocking.set(toReset);
            }
        }
    }
    @Override
    public void rollback() throws SQLException {
        var opt = Optional.ofNullable(cached.get());
        if (opt.isPresent()) {
            boolean toReset = nonBlocking.get();
            try {
                nonBlocking.set(false);
                var connection = opt.get();
                connection.rollback();
            } finally {
                nonBlocking.set(toReset);
            }
        }
    }
    @Override
    public boolean isClosed() throws SQLException {
        var opt = Optional.ofNullable(cached.get());
        if (opt.isPresent()) {
            boolean toReset = nonBlocking.get();
            try {
                nonBlocking.set(false);
                var connection = opt.get();
                return connection.isClosed();
            } finally {
                nonBlocking.set(toReset);
            }
        }
        return false;
    }
    @Override
    public void close() throws SQLException {
        var opt = Optional.ofNullable(cached.get());
        if (opt.isPresent()) {
            boolean toReset = nonBlocking.get();
            try {
                nonBlocking.set(false);
                try (var connection = opt.get()) {
                    log.trace("connection close: {}", connection);
                }
            } finally {
                nonBlocking.set(toReset);
            }
        }
    }
    @Override
    public void setClientInfo(String name, String value) throws SQLClientInfoException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            supplier.get().setClientInfo(name, value);
        } catch (ConcurrentException e) {
            throw new SQLClientInfoException();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void setClientInfo(Properties properties) throws SQLClientInfoException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            supplier.get().setClientInfo(properties);
        } catch (ConcurrentException e) {
            throw new SQLClientInfoException();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    private Connection getOrCreate() throws SQLException {
        try {
            if (Objects.isNull(cached.get())) {
                cached.setValue(supplier.get());
            }
            return cached.get();
        } catch (ConcurrentException err) {
            Throwable cause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(cause)) {
                cause = err;
            }
            throw new SQLException(cause);
        }
    }
}
