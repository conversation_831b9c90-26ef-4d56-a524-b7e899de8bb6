package com.turbospaces.jdbc;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.cloud.service.ServiceConnectorConfig;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RelationalServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.H2ServiceInfo;
import com.zaxxer.hikari.HikariConfig;

public class JdbcPoolServiceConfig implements ServiceConnectorConfig {
    private final Map<String, Object> datasourceProperties = new HashMap<>();
    private boolean autoCommit = false;
    private boolean registerMBeans = true;
    private Integer minPoolSize;
    private Integer maxPoolSize;
    private Duration connnectionTimeout;
    private Duration leakDetectionTimeout;

    public JdbcPoolServiceConfig(ApplicationProperties props, boolean readOnly) {
        int min = readOnly ? props.JDBC_READ_ONLY_POOL_MIN_SIZE.get() : props.JDBC_POOL_MIN_SIZE.get();
        int max = readOnly ? props.JDBC_READ_ONLY_POOL_MAX_SIZE.get() : props.JDBC_POOL_MAX_SIZE.get();

        if (readOnly) {
            setAutoCommit(true);
        }

        connnectionTimeout = props.JDBC_CONNECTION_TIMEOUT.get();
        leakDetectionTimeout = props.JDBC_LEAK_DETECTION_TIMEOUT.get();

        setMinPoolSize(min);
        setMaxPoolSize(max);
    }
    public void setAutoCommit(boolean autoCommit) {
        this.autoCommit = autoCommit;
    }
    public void setRegisterMBeans(boolean registerMBeans) {
        this.registerMBeans = registerMBeans;
    }
    public void setMinPoolSize(Integer minPoolSize) {
        this.minPoolSize = minPoolSize;
    }
    public void setMaxPoolSize(Integer maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
    }
    public void setConnnectionTimeout(Duration connnectionTimeout) {
        this.connnectionTimeout = connnectionTimeout;
    }
    public void setLeakDetectionTimeout(Duration leakDetectionTimeout) {
        this.leakDetectionTimeout = leakDetectionTimeout;
    }
    public void addDatasourceProperty(String key, Object value) {
        datasourceProperties.put(key, value);
    }
    public HikariConfig toHikariConfig(ServiceInfo si) {
        HikariConfig cfg = new HikariConfig();

        cfg.setPoolName(String.format("%s-pool", si.getId()));
        cfg.setRegisterMbeans(registerMBeans);
        cfg.setAutoCommit(autoCommit);

        if (autoCommit) {
            cfg.setPoolName(String.format("%s-autocommit-pool", si.getId()));
        }
        if (minPoolSize != null) {
            cfg.setMinimumIdle(minPoolSize);
        }
        if (maxPoolSize != null) {
            cfg.setMaximumPoolSize(maxPoolSize);
        }
        if (connnectionTimeout != null) {
            cfg.setConnectionTimeout(connnectionTimeout.toMillis());
        }
        if (leakDetectionTimeout != null) {
            cfg.setLeakDetectionThreshold(leakDetectionTimeout.toMillis());
        }
        for (Entry<String, Object> entry : datasourceProperties.entrySet()) {
            cfg.addDataSourceProperty(entry.getKey(), entry.getValue());
        }

        if (si instanceof RelationalServiceInfo rsi) {
            cfg.setUsername(rsi.getUserName());
            cfg.setPassword(rsi.getPassword());

            //
            // ~ important adjustment for PostreSQL
            //
            if (rsi instanceof PostgresqlServiceInfo) {
                cfg.setDataSourceClassName("org.postgresql.ds.PGSimpleDataSource");
                cfg.addDataSourceProperty("serverName", rsi.getHost());
                cfg.addDataSourceProperty("portNumber", rsi.getPort());
                cfg.addDataSourceProperty("databaseName", rsi.getPath());
            } else {
                cfg.setJdbcUrl(rsi.getJdbcUrl());
            }
        } else if (si instanceof H2ServiceInfo h2si) {
            cfg.setJdbcUrl(h2si.getJdbcUrl());
        }

        return cfg;
    }
}
