package com.turbospaces.jdbc;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.sql.Statement;

import lombok.RequiredArgsConstructor;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@RequiredArgsConstructor
public class TracedJdbcStatement implements Statement {
    protected final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    private final Statement statement;

    @Override
    public final <T> T unwrap(Class<T> iface) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.unwrap(iface);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean isWrapperFor(Class<?> iface) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.isWrapperFor(iface);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final TracedResultSet executeQuery(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedResultSet(statement.executeQuery(sql));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int executeUpdate(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeUpdate(sql);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void close() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.close();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getMaxFieldSize() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getMaxFieldSize();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setMaxFieldSize(int max) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setMaxFieldSize(max);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getMaxRows() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getMaxRows();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setMaxRows(int max) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setMaxRows(max);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setEscapeProcessing(boolean enable) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setEscapeProcessing(enable);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getQueryTimeout() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getQueryTimeout();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setQueryTimeout(int seconds) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setQueryTimeout(seconds);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void cancel() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.cancel();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final SQLWarning getWarnings() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getWarnings();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void clearWarnings() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.clearWarnings();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setCursorName(String name) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setCursorName(name);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean execute(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.execute(sql);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final TracedResultSet getResultSet() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedResultSet(statement.getResultSet());
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getUpdateCount() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getUpdateCount();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean getMoreResults() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getMoreResults();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setFetchDirection(int direction) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setFetchDirection(direction);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getFetchDirection() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getFetchDirection();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setFetchSize(int rows) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setFetchSize(rows);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getFetchSize() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getFetchSize();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getResultSetConcurrency() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getResultSetConcurrency();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getResultSetType() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getResultSetType();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void addBatch(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.addBatch(sql);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void clearBatch() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.clearBatch();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int[] executeBatch() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeBatch();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final Connection getConnection() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getConnection();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean getMoreResults(int current) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getMoreResults(current);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final TracedResultSet getGeneratedKeys() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return new TracedResultSet(statement.getGeneratedKeys());
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeUpdate(sql, autoGeneratedKeys);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeUpdate(sql, columnIndexes);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int executeUpdate(String sql, String[] columnNames) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeUpdate(sql, columnNames);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.execute(sql, autoGeneratedKeys);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean execute(String sql, int[] columnIndexes) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.execute(sql, columnIndexes);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean execute(String sql, String[] columnNames) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.execute(sql, columnNames);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final int getResultSetHoldability() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getResultSetHoldability();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean isClosed() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.isClosed();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setPoolable(boolean poolable) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setPoolable(poolable);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean isPoolable() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.isPoolable();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void closeOnCompletion() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.closeOnCompletion();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean isCloseOnCompletion() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.isCloseOnCompletion();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long getLargeUpdateCount() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getLargeUpdateCount();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final void setLargeMaxRows(long max) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            statement.setLargeMaxRows(max);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long getLargeMaxRows() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.getLargeMaxRows();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long[] executeLargeBatch() throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeLargeBatch();
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long executeLargeUpdate(String sql) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeLargeUpdate(sql);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long executeLargeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeLargeUpdate(sql, autoGeneratedKeys);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long executeLargeUpdate(String sql, int[] columnIndexes) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeLargeUpdate(sql, columnIndexes);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final long executeLargeUpdate(String sql, String[] columnNames) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.executeLargeUpdate(sql, columnNames);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final String enquoteLiteral(String val) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.enquoteLiteral(val);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final String enquoteIdentifier(String identifier, boolean alwaysQuote) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.enquoteIdentifier(identifier, alwaysQuote);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final boolean isSimpleIdentifier(String identifier) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.isSimpleIdentifier(identifier);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public final String enquoteNCharLiteral(String val) throws SQLException {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return statement.enquoteNCharLiteral(val);
        } finally {
            nonBlocking.set(toReset);
        }
    }
}
