<?xml version="1.0" encoding="UTF-8"?>
<included>
    <sequenceNumberGenerator class="ch.qos.logback.core.spi.BasicSequenceNumberGenerator" />
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
    <!--  -->
    <!-- specific packages -->
    <!--  -->
    <logger name="com.netflix" level="INFO" />
    <logger name="okhttp3" level="INFO" />
    <logger name="org.apache.spark" level="INFO" />
    <logger name="org.apache.parquet" level="INFO" />
    <logger name="org.apache.hadoop" level="INFO" />
    <logger name="org.codehaus.janino" level="INFO" />
    <logger name="com.datastax.driver" level="INFO" />
    <logger name="org.redisson.connection" level="DEBUG" />
    <logger name="org.redisson" level="INFO" />
    <logger name="com.google.code.yanf4j" level="INFO" />
    <logger name="net.rubyeye.xmemcached" level="INFO" />
    <logger name="org.apache.kafka" level="INFO" />
    <logger name="org.apache.flink" level="INFO" />
    <logger name="org.apache.http" level="INFO" />
    <logger name="org.apache.hc.client5.http" level="INFO" />
    <logger name="org.apache.cassandra" level="INFO" />
    <logger name="org.apache.curator" level="INFO" />
    <logger name="org.apache.zookeeper" level="INFO" />
    <logger name="org.apache.zookeeper.server" level="INFO" />
    <logger name="org.apache.zookeeper.ClientCnxn" level="INFO" />
    <logger name="kafka" level="INFO" />
    <logger name="akka" level="INFO" />
    <logger name="io.netty" level="INFO" />
    <logger name="org.eclipse.jetty" level="INFO" />
    <logger name="com.codahale.metrics.graphite" level="INFO" />
    <logger name="org.eclipse.jgit" level="INFO" />
    <logger name="org.hibernate.validator" level="INFO" />
    <logger name="org.jgroups" level="DEBUG" />
    <logger name="org.jboss.resteasy.resteasy_jaxrs.i18n" level="INFO" />
    <logger name="io.opencensus" level="INFO" />
    <logger name="io.grpc" level="INFO" />
    <logger name="com.google.api.client.http" level="WARN" />
    <logger name="com.clickhouse.client.http" level="WARN" />
    <logger name="io.debezium.embedded.EmbeddedEngine" level="INFO" />
    <logger name="io.debezium" level="INFO" />
    <!--  -->
    <!-- specific classes -->
    <!--  -->
    <logger name="java.lang.ProcessBuilder" level="INFO" />
    <logger name="org.redisson.client.handler.ConnectionWatchdog" level="DEBUG" />
    <logger name="org.springframework.beans.factory.support.DisposableBeanAdapter" level="TRACE" />
    <logger name="org.redisson.connection.DNSMonitor" level="INFO" />
    <logger name="org.springframework.kafka.listener.KafkaMessageListenerContainer" level="INFO" />
    <!--  -->
    <!--  -->
    <!--  -->
    <logger name="com.turbospaces.cfg" level="INFO" />
    <!--  -->
    <!-- ebean  -->
    <!--  -->
    <logger name="io.ebean.SQL" level="DEBUG" />
    <logger name="io.ebean.TXN" level="DEBUG" />
    <logger name="io.ebean.SUM" level="INFO" />
    <logger name="io.ebean.cache.TABLEMOD" level="DEBUG" />
    <logger name="io.ebean.cache.QUERY" level="DEBUG" />
    <logger name="io.ebean.cache.BEAN" level="DEBUG" />
    <logger name="io.ebean.cache.COLL" level="DEBUG" />
    <logger name="io.ebean.cache.NATKEY" level="DEBUG" />
    <logger name="io.ebean.internal" level="TRACE" />
    <logger name="io.ebean.core" level="INFO" />
    <!--  -->
    <!-- testing -->
    <!--  -->
    <logger name="org.testcontainers" level="INFO" />
    <logger name="com.github.dockerjava" level="INFO" />
</included>