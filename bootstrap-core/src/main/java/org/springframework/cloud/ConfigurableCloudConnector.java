package org.springframework.cloud;

import static com.turbospaces.common.EnvUtil.ENV_CACERT_PREFIX;
import static com.turbospaces.common.EnvUtil.ENV_HOSTNAME;
import static com.turbospaces.common.EnvUtil.ENV_SPACE_NAME;
import static com.turbospaces.common.EnvUtil.ENV_UPS_PREFIX;
import static com.turbospaces.common.EnvUtil.INDEX;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.openssl.PEMParser;
import org.jctools.maps.NonBlockingHashMap;
import org.springframework.cloud.app.ApplicationInstanceInfo;
import org.springframework.cloud.app.BasicApplicationInstanceInfo;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.MysqlServiceInfo;
import org.springframework.cloud.service.common.OracleServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.cloud.util.StandardUriInfoFactory;
import org.springframework.cloud.util.UriInfo;
import org.springframework.cloud.util.UriInfoFactory;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.net.HostAndPort;
import com.netflix.archaius.api.Config;
import com.netflix.archaius.api.config.CompositeConfig;
import com.turbospaces.api.AsFlux;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.CloudOptions;
import com.turbospaces.cfg.TypedPropertyFactory;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ssl.SSL;
import com.turbospaces.ups.ClickhouseServiceInfo;
import com.turbospaces.ups.FileServiceInfo;
import com.turbospaces.ups.H2ServiceInfo;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.NatsServiceInfo;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.retry.RetryRegistry;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.core.publisher.Sinks.Many;

@Slf4j
public class ConfigurableCloudConnector implements SmartCloudConnector, AsFlux<Map<String, ServiceInfo>> {
    public static final String RANDOM = "random";
    public static final String SVC = "svc.cluster.local";
    public static final String NAMESPACE = "/var/run/secrets/kubernetes.io/serviceaccount/namespace";
    public static final String FILE_SUFFIX = "_FILE";

    private final Many<Map<String, ServiceInfo>> services = Sinks.many().replay().latestOrDefault(new NonBlockingHashMap<>());
    private Optional<String> namespace = Optional.empty();
    private ApplicationProperties props;
    private BasicApplicationInstanceInfo instanceInfo;
    private String spaceName;
    private String appId;
    private String instanceId;

    private Disposable subscribe;

    @Override
    public void load(ApplicationProperties cfg, KeyStore keyStore, RetryRegistry retryRegistry) throws Exception {
        this.props = Objects.requireNonNull(cfg);
        Map<String, Object> cloudProps = new HashMap<>();

        configureInstance();
        configureAddress(cloudProps);
        configureSlot(cloudProps);
        configureSiteName(cloudProps);
        configureAppName(cloudProps);

        subscribe = readServices(retryRegistry).subscribe(new Consumer<Map<String, ServiceInfo>>() {
            @Override
            public void accept(Map<String, ServiceInfo> map) {
                EmitResult emitResult = services.tryEmitNext(map);
                if (emitResult.isFailure()) {
                    log.error("unable to emit services: {}", map.keySet());
                }
            }
        });

        try {
            namespace = Optional.of(new String(Files.readAllBytes(Paths.get(NAMESPACE))).trim());
            log.info("k8s namespace: {}", namespace.get());
        } catch (Exception err) {
            log.trace(err.getMessage(), err);
        }

        instanceInfo = new BasicApplicationInstanceInfo(instanceId, appId, cloudProps);
        SSL.addCertificates(keyStore, readCerts());

        for (ServiceInfo si : getServiceInfos()) {
            log.debug("ups('{}')={}", si.getId(), si);
        }
    }
    @Override
    public boolean isInMatchingCloud() {
        boolean isMatching = StringUtils.isNotEmpty(spaceName) && StringUtils.isNotEmpty(appId);
        log.info("isMatching(spaceName={}, appId={}) = [{}]", spaceName, appId, isMatching);
        return isMatching;
    }
    @Override
    public String namespace() {
        return namespace.orElse(spaceName);
    }
    @Override
    public HostAndPort toFQSN(int port) {
        String index = props.CLOUD_APP_INSTANCE_INDEX.get();
        String query = props.APP_DNS_QUERY.get();
        return HostAndPort.fromParts(spaceName.equals(namespace()) ? "localhost" : Joiner.on('.').join(new String[] { index, query, namespace(), ConfigurableCloudConnector.SVC }), port);
    }
    @Override
    public ApplicationInstanceInfo getApplicationInstanceInfo() {
        return instanceInfo;
    }
    @Override
    public List<ServiceInfo> getServiceInfos() {
        return ImmutableList.copyOf(services.asFlux().blockFirst().values());
    }
    @Override
    public Flux<Map<String, ServiceInfo>> asFlux() {
        return services.asFlux();
    }
    @Override
    public void dispose() {
        if (Objects.nonNull(subscribe)) {
            if (BooleanUtils.isFalse(subscribe.isDisposed())) {
                subscribe.dispose();
            }
        }
    }
    private void configureAddress(Map<String, Object> cloudProps) {
        CompositeConfig cfg = props.factory().getConfig();

        String hostname = PlatformUtil.detectIp();
        if (cfg.containsKey(CloudOptions.CLOUD_APP_HOST)) {
            hostname = cfg.getString(CloudOptions.CLOUD_APP_HOST);
        }

        //
        // ~ primary port
        //
        int primaryPort = TypedPropertyFactory.PRIMARY_PORT;
        if (cfg.containsKey(CloudOptions.CLOUD_APP_PORT)) {
            //
            // ~ random port condition
            //
            if (RANDOM.equalsIgnoreCase(cfg.getString(CloudOptions.CLOUD_APP_PORT))) {
                primaryPort = PlatformUtil.findAvailableTcpPort();
            } else {
                primaryPort = cfg.getInteger(CloudOptions.CLOUD_APP_PORT);
            }
        }

        //
        // ~ secondary port
        //
        int secondaryPort = TypedPropertyFactory.SECONDARY_PORT;
        if (cfg.containsKey(CloudOptions.CLOUD_APP_SECONDARY_PORT)) {
            //
            // ~ random port condition
            //
            if (RANDOM.equalsIgnoreCase(cfg.getString(CloudOptions.CLOUD_APP_SECONDARY_PORT))) {
                secondaryPort = PlatformUtil.findAvailableTcpPort();
            } else {
                secondaryPort = cfg.getInteger(CloudOptions.CLOUD_APP_SECONDARY_PORT);
            }
        }

        //
        // ~ tertiary port
        //
        int tertiaryPort = TypedPropertyFactory.TERTIARY_PORT;
        if (cfg.containsKey(CloudOptions.CLOUD_APP_TERTIARY_PORT)) {
            //
            // ~ random port condition
            //
            if (RANDOM.equalsIgnoreCase(cfg.getString(CloudOptions.CLOUD_APP_TERTIARY_PORT))) {
                tertiaryPort = PlatformUtil.findAvailableTcpPort();
            } else {
                tertiaryPort = cfg.getInteger(CloudOptions.CLOUD_APP_TERTIARY_PORT);
            }
        }

        //
        // ~ k8s or docker SWARM
        //
        if (StringUtils.isNotEmpty(System.getenv(ENV_HOSTNAME))) {
            hostname = System.getenv(ENV_HOSTNAME);
        }

        addCloudProp(CloudOptions.CLOUD_APP_HOST, hostname, cloudProps);
        addCloudProp(CloudOptions.CLOUD_APP_PORT, primaryPort, cloudProps);
        addCloudProp(CloudOptions.CLOUD_APP_SECONDARY_PORT, secondaryPort, cloudProps);
        addCloudProp(CloudOptions.CLOUD_APP_TERTIARY_PORT, tertiaryPort, cloudProps);
    }
    private void configureSlot(Map<String, Object> cloudProps) {
        CompositeConfig cfg = props.factory().getConfig();
        String slot = null;

        if (cfg.containsKey(CloudOptions.CLOUD_APP_INSTANCE_INDEX)) {
            slot = cfg.getString(CloudOptions.CLOUD_APP_INSTANCE_INDEX);
            //
            // ~ random application index / slot condition
            //
            if (RANDOM.equalsIgnoreCase(slot)) {
                slot = Long.toString(ProcessHandle.current().pid());
            }
        }

        //
        // ~ k8s or docker SWARM
        //
        if (StringUtils.isNotEmpty(System.getenv(INDEX))) {
            slot = System.getenv(INDEX);
        }

        if (StringUtils.isEmpty(slot)) {
            slot = String.valueOf(0);
        }

        addCloudProp(CloudOptions.CLOUD_APP_INSTANCE_INDEX, slot, cloudProps);
    }
    private void configureSiteName(Map<String, Object> cloudProps) {
        CompositeConfig cfg = props.factory().getConfig();
        spaceName = System.getenv(ENV_SPACE_NAME);

        if (cfg.containsKey(CloudOptions.CLOUD_APP_SPACE_NAME)) {
            spaceName = cfg.getString(CloudOptions.CLOUD_APP_SPACE_NAME);
        }
        if (StringUtils.isNotEmpty(spaceName)) {
            addCloudProp(CloudOptions.CLOUD_APP_SPACE_NAME, spaceName, cloudProps);
        }
    }
    private void configureInstance() {
        CompositeConfig cfg = props.factory().getConfig();
        if (cfg.containsKey(CloudOptions.CLOUD_APP_ID)) {
            appId = cfg.getString(CloudOptions.CLOUD_APP_ID);
        }
        instanceId = PlatformUtil.randomUUID().toString();
    }
    private void configureAppName(Map<String, Object> cloudProps) {
        CompositeConfig cfg = props.factory().getConfig();
        if (cfg.containsKey(CloudOptions.CLOUD_APP_NAME)) {
            String appName = cfg.getString(CloudOptions.CLOUD_APP_NAME);
            addCloudProp(CloudOptions.CLOUD_APP_NAME, appName, cloudProps);
        }
    }
    protected Flux<Map<String, ServiceInfo>> readServices(RetryRegistry retryRegistry) throws Exception {
        ImmutableMap.Builder<String, ServiceInfo> map = ImmutableMap.builder();

        CompositeConfig cfg = props.factory().getConfig();
        Config prefixedView = cfg.getPrefixedView("service");
        for (String key : prefixedView.keys()) {
            int idx = key.indexOf(".uri");
            if (idx > 0) {
                String serviceId = key.substring(0, idx);
                String value = prefixedView.getRawProperty(key).toString();
                addServiceInfo(serviceId, StringUtils.trim(value), map);
            }
        }

        for (Entry<String, String> entry : System.getenv().entrySet()) {
            if (entry.getKey().startsWith(ENV_UPS_PREFIX)) {
                String key = entry.getKey();
                String value = entry.getValue();
                String id = key.substring(ENV_UPS_PREFIX.length()).trim().toLowerCase();

                //
                // docker mounts secrets to /run/secrets/${secret}
                //
                if (key.endsWith(FILE_SUFFIX)) {
                    File f = new File(value);
                    byte[] encoded = Files.readAllBytes(f.toPath());
                    value = new String(encoded, StandardCharsets.UTF_8);
                    id = id.substring(0, id.length() - FILE_SUFFIX.length());
                    log.debug("secret={} has been loaded from={} ...", id, f);
                }

                addServiceInfo(id, StringUtils.trim(value), map);
            }
        }

        return Flux.just(map.build());
    }
    protected Collection<Certificate> readCerts() throws Exception {
        Collection<Certificate> cacerts = Lists.newLinkedList();

        for (Entry<String, String> entry : System.getenv().entrySet()) {
            if (entry.getKey().startsWith(ENV_CACERT_PREFIX)) {
                File pem = new File(entry.getValue()); // ~ PEM
                byte[] bytes = Files.readAllBytes(pem.toPath());
                String raw = new String(bytes, StandardCharsets.UTF_8);
                try (StringReader reader = new StringReader(raw)) {
                    try (PEMParser parser = new PEMParser(reader)) {
                        org.bouncycastle.cert.X509CertificateHolder keyInfo = (org.bouncycastle.cert.X509CertificateHolder) parser.readObject();

                        try (ByteArrayInputStream io = new ByteArrayInputStream(keyInfo.getEncoded())) {
                            CertificateFactory fact = CertificateFactory.getInstance("X.509");
                            X509Certificate cer = (X509Certificate) fact.generateCertificate(io);
                            cacerts.add(cer);
                        }
                    }
                }
            }
        }

        return cacerts;
    }
    protected void addServiceInfo(String id, String value, ImmutableMap.Builder<String, ServiceInfo> m) {
        switch (id) {
            case UPSs.CFG:
            case UPSs.SENTRY:
            case UPSs.INFLUX:
            case UPSs.JAEGER:
            case UPSs.TEMPORAL:
            case UPSs.ELASTIC_SEARCH: {
                m.put(id, new PlainServiceInfo(id, value));
                break;
            }
            case UPSs.KAFKA:
            case UPSs.CDC_KAFKA: {
                m.put(id, new KafkaServiceInfo(id, value));
                break;
            }
            case UPSs.POSTGRES_OWNER:
            case UPSs.POSTGRES_APP: {
                m.put(id, new PostgresqlServiceInfo(id, value));
                break;
            }
            case UPSs.H2_OWNER:
            case UPSs.H2_APP: {
                m.put(id, new H2ServiceInfo(id, value));
                break;
            }
            case UPSs.CLICKHOUSE: {
                m.put(id, new ClickhouseServiceInfo(id, value));
                break;
            }
            case UPSs.REDIS: {
                m.put(id, new RedisServiceInfo(id, value));
                break;
            }
            case UPSs.MEMCACHED: {
                m.put(id, new MemcachedServiceInfo(id, value));
                break;
            }
            case UPSs.NATS: {
                m.put(id, new NatsServiceInfo(id, value));
                break;
            }

            default: {
                ServiceInfo si = new RawServiceInfo(id, value.getBytes());

                try {
                    UriInfoFactory uriFactory = new StandardUriInfoFactory();
                    UriInfo uriInfo = uriFactory.createUri(value);
                    String scheme = uriInfo.getScheme();

                    //
                    // ~ important condition
                    //
                    if (StringUtils.isNotEmpty(scheme)) {
                        switch (scheme) {
                            case OracleServiceInfo.ORACLE_SCHEME: {
                                si = new OracleServiceInfo(id, value);
                                break;
                            }
                            case PostgresqlServiceInfo.POSTGRES_SCHEME: {
                                si = new PostgresqlServiceInfo(id, value);
                                break;
                            }
                            case ClickhouseServiceInfo.CLICKHOUSE_SCHEME: {
                                si = new ClickhouseServiceInfo(id, value);
                                break;
                            }
                            case MysqlServiceInfo.MYSQL_SCHEME: {
                                si = new MysqlServiceInfo(id, value);
                                break;
                            }
                            case RedisServiceInfo.REDIS_SCHEME: {
                                si = new RedisServiceInfo(id, value);
                                break;
                            }
                            case MemcachedServiceInfo.MEMCACHED_SCHEME: {
                                si = new MemcachedServiceInfo(id, value);
                                break;
                            }
                            case NatsServiceInfo.NATS_SCHEME: {
                                si = new NatsServiceInfo(id, value);
                                break;
                            }
                            case KafkaServiceInfo.KAFKA_SCHEME: {
                                si = new KafkaServiceInfo(id, value);
                                break;
                            }
                            case FileServiceInfo.FILE_SCHEME: {
                                si = new FileServiceInfo(id, value);
                                break;
                            }
                            case PlainServiceInfo.HTTP_SCHEME:
                            case PlainServiceInfo.HTTPS_SCHEME:
                            case PlainServiceInfo.DNS_SCHEME: {
                                si = new PlainServiceInfo(id, value);
                                break;
                            }
                            default: {
                                throw new IllegalArgumentException("unknown schema: " + scheme);
                            }
                        }
                    }
                } catch (IllegalArgumentException err) {
                    log.trace(err.getMessage(), err);
                }

                m.put(id, si);
            }
        }
    }
    protected void addCloudProp(String key, Object value, Map<String, Object> map) {
        log.debug("adding {}={}", key, value);
        map.put(key, Objects.requireNonNull(value));
    }
}
