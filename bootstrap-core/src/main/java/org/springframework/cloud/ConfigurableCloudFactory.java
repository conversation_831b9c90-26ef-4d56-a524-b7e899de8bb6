package org.springframework.cloud;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import com.netflix.archaius.api.Property;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.InitCommand;
import org.eclipse.jgit.api.PullCommand;
import org.eclipse.jgit.api.TransportConfigCallback;
import org.eclipse.jgit.lib.ConfigConstants;
import org.eclipse.jgit.lib.Constants;
import org.eclipse.jgit.lib.StoredConfig;
import org.eclipse.jgit.transport.HttpTransport;
import org.eclipse.jgit.transport.RefSpec;
import org.eclipse.jgit.transport.RemoteConfig;
import org.eclipse.jgit.transport.TagOpt;
import org.eclipse.jgit.transport.Transport;
import org.eclipse.jgit.transport.URIish;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.slf4j.event.Level;
import org.springframework.cloud.service.ServiceInfo;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.netflix.archaius.api.Config;
import com.netflix.archaius.api.config.CompositeConfig;
import com.netflix.archaius.config.DefaultCompositeConfig;
import com.netflix.archaius.config.DefaultSettableConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.CloudMapConfig;
import com.turbospaces.cfg.CloudOptions;
import com.turbospaces.cfg.DynamicCompositeConfig;
import com.turbospaces.cfg.ScopedProperty;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.core.functions.CheckedRunnable;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.Many;

@Slf4j
public class ConfigurableCloudFactory extends CloudFactory implements CheckedRunnable, Disposable {
    private static final String[] PROPERTY_FILE_EXTENSIONS = { "properties", "props" };

    private final Many<String> logging = Sinks.many().replay().latest();
    private final Object mutex = new Object();
    private final DynamicCloud cloud;
    private final ApplicationProperties props;
    private final ScheduledExecutorService timer;
    private ScheduledFuture<?> subscription;

    public ConfigurableCloudFactory(
            SmartCloudConnector connector,
            ApplicationProperties props,
            RetryRegistry retryRegistry,
            Consumer<String> loggingWatcher) throws Exception {
        this.props = props;
        registerCloudConnector(connector);

        //
        // ~ if matching cloud
        //
        if (connector.isInMatchingCloud()) {
            this.cloud = new DynamicCloud(connector, getServiceCreators());
        } else {
            throw new CloudException("No suitable cloud connector found");
        }

        this.timer = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        });

        //
        // ~ subscribe to changes (distinct by content)
        //
        logging.asFlux().distinctUntilChanged().subscribe(loggingWatcher);

        // reading props with default values before git pull
        Map<String, Pair<Property<?>, Object>> map = props.readFieldAsPropMap()
                .entrySet().stream()
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), Pair.of(e.getValue(), e.getValue().get())), HashMap::putAll);;
        for (ServiceInfo si : connector.getServiceInfos()) {
            if (si.getId().equals(UPSs.CFG)) {
                //
                // ~ trigger immediately on current thread
                //
                run();
                var cfgName = DynamicCompositeConfig.GIT_CFG_NAME;
                check(props, cfgName, props.cfg().getConfig(cfgName), map);

                //
                // ~ periodically poll for changes
                //
                if (props.APP_CFG_DYNAMIC_POLLING_ENABLED.get()) {
                    Runnable task = new Runnable() {
                        @Override
                        public void run() {
                            //
                            // ~ retry several times (with exponential back-off) before alerting to sentry
                            //
                            Retry retry = retryRegistry.retry("cfg-refresh-action");
                            CheckedRunnable runnable = Retry.decorateCheckedRunnable(retry, ConfigurableCloudFactory.this);

                            try {
                                runnable.run();
                            } catch (InterruptedException err) {
                                log.warn(err.getMessage(), err);
                                Thread.currentThread().interrupt();
                            } catch (Throwable t) {
                                log.error(t.getMessage(), t);
                            }
                        }
                    };

                    long seconds = props.APP_TIMER_INTERVAL.get().toSeconds();
                    subscription = timer.scheduleWithFixedDelay(task, seconds, seconds, TimeUnit.SECONDS);
                }
            }
        }

        //
        // ~ replace configuration with real implementation
        //
        CloudMapConfig cfc = new CloudMapConfig(connector.getApplicationInstanceInfo());
        DefaultSettableConfig config = (DefaultSettableConfig) props.cfg().getConfig(DynamicCompositeConfig.CLOUD_CFG_NAME);
        config.setProperties(cfc);
    }
    @Override
    public void dispose() {
        if (Objects.nonNull(subscription)) {
            subscription.cancel(true);
        }
        PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
    }

    static void check(ApplicationProperties props, String name, Config config, Map<String, Pair<Property<?>, Object>> map) {
        List<String> unknown = new ArrayList<>();
        List<String> same = new ArrayList<>();
        config.forEachProperty((k, v) -> {
            String key = k;
            if (props.DYNAMIC_PROPERTY_KEYS.get().contains(key)) {
                log.debug("marking dynamic property: {} as known", key);
                return;
            }
            if (props.undeclaredDynamicProperties().contains(key)) {
                log.debug("whitelisted property: {} skipping", key);
                return;
            }
            if (!map.containsKey(key)) {
                // try to remove first 'xxxx.' section - possible scope
                var noPrefix = key.substring(key.indexOf('.') + 1);
                if (map.containsKey(noPrefix) && map.get(noPrefix).getLeft() instanceof ScopedProperty<?>) {
                    return;
                }
                // try to find possible scope
                String[] parts = key.split("\\.");
                for (int i = 0; i < parts.length; i++) {
                    String[] copy = parts.clone();
                    copy[i] = ScopedProperty.SCOPE_PLACEHOLDER;
                    String maybeScoped = String.join(".", copy);
                    if (map.containsKey(maybeScoped) && map.get(maybeScoped).getLeft() instanceof ScopedProperty<?>) {
                        return;
                    }
                }

                // if still doesn't contain, OR property is NOT scoped
                unknown.add(k);
                return;
            }
            // check values
            if (Objects.equals(String.valueOf(map.get(key).getRight()), v)) {
                same.add(k);
            }
        });
        if (!unknown.isEmpty()) {
            log.atLevel(Level.valueOf(props.LOGGER_LEVEL_CFG_ABUSE.get())).log("unknown properties: {} (loaded from '{}')", unknown, name);
        }
        if (!same.isEmpty()) {
            log.atLevel(Level.valueOf(props.LOGGER_LEVEL_CFG_ABUSE.get())).log("properties have the same values: {} (loaded from '{}')", same, name);
        }
    }
    @Override
    public DynamicCloud getCloud() {
        return cloud; // ~ safe to call many times
    }
    @Override
    public void run() throws Exception {
        synchronized (mutex) {
            for (ServiceInfo si : cloud.getServiceInfos()) {
                if (si.getId().equals(UPSs.CFG)) {
                    PlainServiceInfo info = (PlainServiceInfo) si;

                    File toFile = Files.createTempDirectory("cfg").toFile();
                    InitCommand initCmd = new InitCommand().setBare(false).setDirectory(toFile);
                    log.debug("cloning git repo from={}", info);
                    URL logFile = null;

                    try (Git git = initCmd.call()) {
                        applyConfig(git, info);

                        //
                        // ~ pull command with 1 minute timeout
                        //
                        int timeout = (int) TimeUnit.MINUTES.toSeconds(1);

                        PullCommand pullCommand = git.pull().setRemote(Constants.DEFAULT_REMOTE_NAME).setTagOpt(TagOpt.NO_TAGS);
                        pullCommand.setRemoteBranchName(Constants.MASTER);
                        pullCommand.setTimeout(timeout);
                        pullCommand.setTransportConfigCallback(new TransportConfigCallback() {
                            @Override
                            public void configure(Transport transport) {
                                if (transport instanceof HttpTransport http) {
                                    http.setTimeout(timeout);
                                }
                            }
                        });

                        if (StringUtils.isNotEmpty(info.getUserName()) && StringUtils.isNotEmpty(info.getPassword())) {
                            pullCommand.setCredentialsProvider(new UsernamePasswordCredentialsProvider(info.getUserName(), info.getPassword()));
                        }
                        pullCommand.call();

                        Map<String, Object> cloudProps = cloud.getApplicationInstanceInfo().getProperties();
                        String spaceName = cloudProps.get(CloudOptions.CLOUD_APP_SPACE_NAME).toString();
                        String appId = cloud.getApplicationInstanceInfo().getAppId();

                        log.debug("looking for property files in {}:{}", spaceName, appId);
                        File dspace = new File(toFile, spaceName);
                        if (dspace.exists() && dspace.isDirectory()) {
                            //
                            // ~ try load space shared properties
                            //
                            Collection<File> dprops = FileUtils.listFiles(dspace, PROPERTY_FILE_EXTENSIONS, false);
                            if (Objects.nonNull(dprops)) {
                                for (File f : dprops) {
                                    if (f.isFile()) {
                                        load(f);
                                    }
                                }
                            }

                            File dapp = new File(dspace, appId);
                            if (dapp.exists() && dapp.isDirectory()) {
                                //
                                // ~ try load application properties
                                //
                                Collection<File> aprops = FileUtils.listFiles(dapp, PROPERTY_FILE_EXTENSIONS, false);
                                if (Objects.nonNull(aprops)) {
                                    for (File f : aprops) {
                                        if (f.isFile()) {
                                            load(f);
                                        }
                                    }
                                }

                                String filename = "logging.xml";
                                File fapp = new File(dapp, filename);
                                if (fapp.exists() && fapp.isFile()) {
                                    logFile = fapp.toURI().toURL();
                                }
                            }
                        }
                    } finally {
                        if (Objects.nonNull(logFile)) {
                            logging.tryEmitNext(IOUtils.toString(logFile, StandardCharsets.UTF_8));
                        }

                        if (toFile.exists()) {
                            FileUtils.deleteDirectory(toFile);
                        }
                    }
                }
            }
        }
    }
    private void load(File f) throws Exception {
        CompositeConfig cfg = props.cfg();

        Properties orig = new Properties();
        try (FileInputStream io = new FileInputStream(f)) {
            log.debug("loading space shared properties from: {}", f.getAbsolutePath());
            orig.load(io);
        }

        String name = Paths.get(f.getParentFile().getName(), f.getName()).toString();

        DefaultCompositeConfig gitComposeConfig = (DefaultCompositeConfig) cfg.getConfig(DynamicCompositeConfig.GIT_CFG_NAME);
        DefaultSettableConfig fcfg = (DefaultSettableConfig) gitComposeConfig.getConfig(name);
        if (Objects.isNull(fcfg)) {
            fcfg = new DefaultSettableConfig();
            gitComposeConfig.addConfig(name, fcfg);
        }

        Set<String> toRemove = new HashSet<>();
        fcfg.forEachProperty((k, v) -> {
            if (!orig.containsKey(k)) {
                toRemove.add(k);
            }
        });

        if (!toRemove.isEmpty()) {
            log.debug("found orphaned properties: {}", toRemove);
            for (String prop : toRemove) {
                fcfg.clearProperty(prop);
            }
        }

        //
        // ~ trim all properties by default
        //
        Properties data = new Properties();
        ImmutableMap<String, String> map = Maps.fromProperties(orig); // ~ enforce no duplicate(s)
        for (Entry<String, String> entry : map.entrySet()) {
            data.setProperty(StringUtils.trim(entry.getKey()), StringUtils.trim(entry.getValue()));
        }

        log.debug("setting git cfg props={}", data);
        fcfg.setProperties(data);
    }
    private void applyConfig(Git git, PlainServiceInfo info) throws URISyntaxException, IOException {
        log.debug("patching git config ...");

        StoredConfig config = git.getRepository().getConfig();
        config.setBoolean(ConfigConstants.CONFIG_GC_SECTION, null, ConfigConstants.CONFIG_KEY_AUTODETACH, false);
        if (props.APP_DEV_MODE.get()) {
            config.setBoolean("http", null, "sslVerify", false);
        }

        URIish urish = new URIish(Strings.CS.removeEnd(info.getUri(), "/"));
        RemoteConfig remoteConfig = new RemoteConfig(config, Constants.DEFAULT_REMOTE_NAME);
        remoteConfig.addURI(urish);

        RefSpec refSpec = new RefSpec();
        refSpec = refSpec.setForceUpdate(true);
        refSpec = refSpec.setSourceDestination(Constants.R_HEADS + "*", Constants.R_REMOTES + remoteConfig.getName() + "/" + "*");
        remoteConfig.addFetchRefSpec(refSpec);
        remoteConfig.update(config);

        config.save();
    }
}
