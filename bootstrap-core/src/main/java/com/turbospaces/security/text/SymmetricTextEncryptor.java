package com.turbospaces.security.text;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.PBEParametersGenerator;
import org.bouncycastle.crypto.engines.AESEngine;
import org.bouncycastle.crypto.generators.PKCS5S2ParametersGenerator;
import org.bouncycastle.crypto.modes.GCMBlockCipher;
import org.bouncycastle.crypto.modes.GCMModeCipher;
import org.bouncycastle.crypto.params.AEADParameters;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.util.encoders.Base64;

import java.nio.charset.StandardCharsets;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class SymmetricTextEncryptor {
    private static final int MAC_SIZE_BITS = 128;
    private static final int KEY_DERIVE_ITERATIONS = 8 * 1024;

    public static String encrypt(String plaintext, String secretKey, byte[] nonce) throws Exception {
        GCMModeCipher cipher = GCMBlockCipher.newInstance(AESEngine.newInstance());

        byte[] secretKeyBytes = deriveKeyWithFixedKeyLenght(secretKey, nonce, MAC_SIZE_BITS);
        byte[] plaintextBytes = plaintext.getBytes(StandardCharsets.UTF_8);

        CipherParameters parameters = new AEADParameters(new KeyParameter(secretKeyBytes), MAC_SIZE_BITS, nonce);
        cipher.init(true, parameters);

        byte[] cipherBytes = new byte[cipher.getOutputSize(plaintextBytes.length)];

        int len = cipher.processBytes(plaintextBytes, 0, plaintextBytes.length, cipherBytes, 0);
        cipher.doFinal(cipherBytes, len);

        return new String(Base64.encode(cipherBytes), StandardCharsets.UTF_8);
    }
    public static String decrypt(String encryptedText, String secretKey, byte[] nonce) throws Exception {
        GCMModeCipher cipher = GCMBlockCipher.newInstance(AESEngine.newInstance());

        byte[] secretKeyBytes = deriveKeyWithFixedKeyLenght(secretKey, nonce, MAC_SIZE_BITS);
        CipherParameters parameters = new AEADParameters(new KeyParameter(secretKeyBytes), MAC_SIZE_BITS, nonce);
        cipher.init(false, parameters);

        byte[] cipherBytes = Base64.decode(encryptedText.getBytes(StandardCharsets.UTF_8));
        byte[] plaintBytes = new byte[cipher.getOutputSize(cipherBytes.length)];

        int len = cipher.processBytes(cipherBytes, 0, cipherBytes.length, plaintBytes, 0);
        cipher.doFinal(plaintBytes, len);

        return new String(plaintBytes, StandardCharsets.UTF_8);
    }
    private static byte[] deriveKeyWithFixedKeyLenght(String password, byte[] salt, int keyLength) {
        PKCS5S2ParametersGenerator generator = new PKCS5S2ParametersGenerator();
        generator.init(PBEParametersGenerator.PKCS5PasswordToUTF8Bytes(password.toCharArray()), salt, KEY_DERIVE_ITERATIONS);
        KeyParameter keyParameter = (KeyParameter) generator.generateDerivedMacParameters(keyLength);
        return keyParameter.getKey();
    }
}
