package com.turbospaces.ssl;

import java.math.BigInteger;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.Callable;

import org.apache.commons.lang3.time.DateUtils;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;

import com.turbospaces.cfg.ApplicationProperties;

public class SelfSignedCertificateGenerator implements Callable<KeyStore> {
    public static String PASSWORD = "changeit";

    private final ApplicationProperties props;
    private final String password;

    public SelfSignedCertificateGenerator(ApplicationProperties props) {
        this(props, PASSWORD);
    }
    public SelfSignedCertificateGenerator(ApplicationProperties props, String password) {
        this.props = Objects.requireNonNull(props);
        this.password = Objects.requireNonNull(password);
    }
    @Override
    public KeyStore call() throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG", "SUN");
        keyGen.initialize(2048, random);
        KeyPair pair = keyGen.generateKeyPair();

        String issuerString = "C=UA, O=turbospaces, OU=dev";
        String subjectString = String.format("C=UA, O=public, OU=%s", props.CLOUD_APP_ID.get());
        X500Name issuer = new X500Name(issuerString);
        BigInteger serial = BigInteger.ONE;
        Date notBefore = new Date();
        Date notAfter = DateUtils.addYears(notBefore, 1);
        X500Name subject = new X500Name(subjectString);
        PublicKey publicKey = pair.getPublic();
        JcaX509v3CertificateBuilder v3 = new JcaX509v3CertificateBuilder(issuer, serial, notBefore, notAfter, subject, publicKey);

        String providerName = BouncyCastleProvider.PROVIDER_NAME;
        X509CertificateHolder certHldr = v3.build(new JcaContentSignerBuilder("SHA1WithRSA").setProvider(providerName).build(pair.getPrivate()));
        X509Certificate cert = new JcaX509CertificateConverter().setProvider(providerName).getCertificate(certHldr);
        cert.checkValidity(new Date());
        cert.verify(pair.getPublic());

        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(null, null);
        ks.setKeyEntry(props.CLOUD_APP_ID.get(), pair.getPrivate(), password.toCharArray(), new X509Certificate[] { cert });

        return ks;
    }
}
