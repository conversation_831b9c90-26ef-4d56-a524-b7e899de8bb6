package com.turbospaces.cfg;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Objects;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.lib.Repository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.turbospaces.common.PlatformUtil;

public class JGitRepository implements GitRepository {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final Repository repository;
    private final ApplicationConfig cfg;

    public JGitRepository(Repository repository, ApplicationConfig cfg) {
        this.repository = Objects.requireNonNull(repository);
        this.cfg = Objects.requireNonNull(cfg);
    }
    @Override
    public void readme() throws Exception {
        try (Git git = new Git(repository)) {
            File touch = new File(repository.getDirectory().getParent(), "readme");
            FileUtils.touch(touch);

            git.add().addFilepattern(touch.getName()).call();
            git.commit().setMessage(PlatformUtil.randomUUID().toString() + ":" + touch.getName()).call();
        }
    }
    @Override
    public void spaceProps(String name, Properties props) throws Exception {
        try (Git git = new Git(repository)) {
            String spaceName = cfg.getString(CloudOptions.CLOUD_APP_SPACE_NAME);

            File space = new File(repository.getDirectory().getParent(), spaceName);
            File touch = new File(space, name);

            FileUtils.touch(touch);
            try (FileOutputStream out = new FileOutputStream(touch)) {
                props.store(out, spaceName);
                logger.info("stored space props: {} into: {}", props, touch);
            }

            git.add().addFilepattern(".").call();
            git.commit().setMessage(PlatformUtil.randomUUID().toString() + ":" + touch.getName()).call();
        }
    }
    @Override
    public void appProps(String name, Properties props) throws Exception {
        try (Git git = new Git(repository)) {
            String spaceName = cfg.getString(CloudOptions.CLOUD_APP_SPACE_NAME);
            String appId = cfg.getString(CloudOptions.CLOUD_APP_ID);

            File space = new File(repository.getDirectory().getParent(), spaceName);
            File app = new File(space, appId);
            File touch = new File(app, name);

            FileUtils.touch(touch);
            try (FileOutputStream out = new FileOutputStream(touch)) {
                props.store(out, spaceName);
                logger.info("stored app props: {} into: {}", props, touch);
            }

            git.add().addFilepattern(".").call();
            git.commit().setMessage(PlatformUtil.randomUUID().toString() + ":" + touch.getName()).call();
        }
    }
}
