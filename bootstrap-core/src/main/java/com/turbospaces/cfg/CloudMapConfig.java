package com.turbospaces.cfg;

import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.cloud.app.ApplicationInstanceInfo;

import com.netflix.archaius.config.MapConfig;

public class CloudMapConfig extends MapConfig {
    public CloudMapConfig(ApplicationInstanceInfo info) {
        super(info.getProperties().entrySet().stream().collect(Collectors.toMap(new Function<>() {
            @Override
            public String apply(Entry<String, Object> entry) {
                return entry.getKey();
            }
        }, new Function<>() {
            @Override
            public String apply(Entry<String, Object> entry) {
                return entry.getValue().toString();
            }
        })));
    }
}
