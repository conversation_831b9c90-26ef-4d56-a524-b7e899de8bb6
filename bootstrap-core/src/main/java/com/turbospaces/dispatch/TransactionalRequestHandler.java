package com.turbospaces.dispatch;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import com.google.common.base.Function;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Message;
import com.netflix.archaius.api.Property;

import reactor.core.publisher.Sinks;

public abstract class TransactionalRequestHandler<REQ extends Message, RESP extends Message.Builder> implements RequestHandler<REQ, RESP>, ApplicationContextAware {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    protected ApplicationContext applicationContext;
    private final Class<REQ> reqType;
    private final Class<RESP> replyClass;

    @SuppressWarnings("unchecked")
    public TransactionalRequestHandler() {
        //
        // ~ by convention (derive request / reply types)
        //
        ParameterizedType parameterizedType = (ParameterizedType) this.getClass().getGenericSuperclass();
        this.reqType = (Class<REQ>) parameterizedType.getActualTypeArguments()[0];
        this.replyClass = (Class<RESP>) parameterizedType.getActualTypeArguments()[1];
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    @Override
    public final void accept(TransactionalRequest<REQ, RESP> cmd) throws Throwable {
        apply(cmd);
    }
    //
    // ~ adapter method (we don't want to rewrite all handlers and change signature)
    //
    public abstract void apply(TransactionalRequest<REQ, RESP> cmd) throws Throwable;

    public <I extends Message, O extends Message.Builder, T extends TransactionalRequestHandler<I, O>>
            void
            invokeSync(
                    Class<T> handler,
                    I req,
                    O resp,
                    Property<Integer> prop,
                    TransactionalRequest<?, ?> cmd) throws Throwable {
        FluentFuture<?> future = invoke(handler, req, resp, cmd);
        future.get(prop.get(), TimeUnit.SECONDS);
    }
    public <I extends Message, O extends Message.Builder, T extends TransactionalRequestHandler<I, O>>
            FluentFuture<O> invoke(
                    Class<T> handler,
                    I req,
                    O resp,
                    FutureCallback<O> callback,
                    TransactionalRequest<?, ?> cmd) throws Throwable {
        Executor directExecutor = MoreExecutors.directExecutor();
        FluentFuture<O> fluentFuture = invoke(handler, req, resp, cmd)
                .transform(new Function<Object, O>() {
                    @Override
                    public O apply(Object input) {
                        logger.trace("forwarding call to direct executor with arg: {}", resp);
                        return resp;
                    }
                }, directExecutor);
        fluentFuture.addCallback(callback, directExecutor);
        return fluentFuture;
    }
    @SuppressWarnings("unchecked")
    public <I extends Message, O extends Message.Builder, T extends TransactionalRequestHandler<I, O>>
            FluentFuture<?>
            invoke(
                    Class<T> handler,
                    I req,
                    O resp,
                    TransactionalRequest<?, ?> cmd) throws Throwable {
        T target = applicationContext.getBean(handler);
        Class<I> type = (Class<I>) req.getClass();
        EmbeddedTransactionalRequest<I, O> nested = new EmbeddedTransactionalRequest<>(
                type,
                resp,
                cmd.record(),
                cmd.wrapper().repack(req),
                Sinks.one());

        if (cmd.isPreserveNotifications()) {
            nested.preserveNotifications();
        }
        if (cmd.isPreserveEventStream()) {
            nested.preserveEventStream();
        }

        target.accept(nested); // same thread

        ListenableFuture<?> future = nested.replyCondition();
        future.addListener(new Runnable() {
            @Override
            public void run() {
                for (Message.Builder msg : nested.notifications()) {
                    cmd.notify(msg);
                }
                for (Message.Builder msg : nested.eventStreaming()) {
                    cmd.eventStream(msg);
                }
            }
        }, MoreExecutors.directExecutor());
        return FluentFuture.from(future);
    }
    public Class<REQ> requestType() {
        return reqType;
    }
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public RESP newReply() {
        try {
            Class response = Class.forName(replyClass.getName()).getDeclaringClass();
            Method newBuilderMethod = response.getMethod("newBuilder");
            Message.Builder builder = (Message.Builder) newBuilderMethod.invoke(null);
            return (RESP) builder;
        } catch (Exception err) {
            throw new IllegalStateException("Error when building reply", err);
        }
    }
}
