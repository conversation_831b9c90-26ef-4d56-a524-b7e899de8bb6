package com.turbospaces.dispatch;

import java.lang.StackWalker.StackFrame;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collections;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;
import org.springframework.cloud.util.StandardUriInfoFactory;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Any;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Message;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.api.StackTracer;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.jpa.CompositeStackTracer;
import com.turbospaces.api.jpa.JpaStackTracer;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.TransactionalRequestOutcome.TransactionalRequestOutcomeBuilder;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.metrics.MetricTags;

import api.v1.ApiFactory;
import api.v1.ApplicationException;
import api.v1.Headers;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.vavr.CheckedRunnable;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public class SafeRequestHandler<REQ extends Message, RESP extends Message.Builder> implements CheckedRunnable, Supplier<WorkerCompletableTask> {
    public static final String METRIC_NAME = "dispatcher";
    public static final ImmutableSet<String> TAGS_TO_EXCLUDE = ImmutableSet.of(
            MdcTags.MDC_MESSAGE_ID,
            MdcTags.MDC_TRACE_ID,
            MdcTags.MDC_ROUTING_KEY,
            MdcTags.MDC_OPERATION,
            MdcTags.MDC_ASN,
            MdcTags.MDC_ASN_ORG,
            MdcTags.MDC_TIMEOUT,
            MdcTags.MDC_REPLY_TO,
            MdcTags.MDC_TOOK);

    private final Object mutex = new Object();
    private final StandardUriInfoFactory uriFactory = new StandardUriInfoFactory();
    private final SettableFuture<TransactionalRequestOutcome> future = SettableFuture.create();
    private final AtomicReference<Map<StackTracer, ImmutableList<StackFrame>>> stacktraces = new AtomicReference<>(Collections.emptyMap());
    private final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    private final WorkerCompletableTask toReturn;
    private final RequestHandler<REQ, RESP> action;
    @Getter
    private final TransactionalRequest<REQ, RESP> transaction;
    private final RequestWrapperFacade reqw;
    private final MeterRegistry meterRegistry;
    private final Tracer tracer;
    private final ApiFactory apiFactory;
    private final CompositeStackTracer stackTracer;
    private final Span span;
    private final StopWatch stopWatch;
    private final String operationName;
    private final Set<Tag> tags;

    public SafeRequestHandler(
            MeterRegistry meterRegistry,
            Tracer tracer,
            RequestWrapperFacade reqw,
            ApiFactory apiFactory,
            CompositeStackTracer stackTracer,
            Span span,
            WorkUnit record,
            TransactionalRequest<REQ, RESP> transaction,
            RequestHandler<REQ, RESP> action) {
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.tracer = Objects.requireNonNull(tracer);
        this.span = Objects.requireNonNull(span);
        this.reqw = Objects.requireNonNull(reqw);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.stackTracer = Objects.requireNonNull(stackTracer);
        this.transaction = Objects.requireNonNull(transaction);
        this.toReturn = new WorkerCompletableTask(future, record, reqw.headers().getMessageId());
        this.action = Objects.requireNonNull(action);

        this.stopWatch = StopWatch.createStarted();
        this.operationName = PlatformUtil.toLowerUnderscore(reqw.body().getTypeUrl());
        this.tags = Sets.newLinkedHashSet();
    }
    @Override
    public WorkerCompletableTask get() {
        return toReturn;
    }
    @Override
    public void run() throws Throwable {
        var thread = Thread.currentThread();
        var oldName = thread.getName();
        var newName = oldName + "|" + operationName;
        thread.setName(newName);

        //
        // ~ add tags
        //
        tags.add(Tag.of(MetricTags.OPERATION, operationName));

        //
        // ~ add universally all possible tags from headers (excluding messageId/traceId/etc)
        //
        Headers headers = reqw.headers();
        Map<FieldDescriptor, Object> data = headers.getAllFields();
        Map<String, Object> additionaTags = data.entrySet().stream().collect(Collectors.toMap(e -> e.getKey().getName(), Map.Entry::getValue));
        Field[] fields = FieldUtils.getAllFields(MdcTags.class);
        for (Field f : fields) {
            if (Modifier.isPublic(f.getModifiers()) && BooleanUtils.isFalse(Modifier.isStatic(f.getModifiers()))) {
                try {
                    String value = (String) FieldUtils.readStaticField(f);
                    if (additionaTags.containsKey(value)) {
                        Object toPut = additionaTags.get(value);
                        if (Objects.nonNull(toPut)) {
                            if (BooleanUtils.isFalse(TAGS_TO_EXCLUDE.contains(value))) {
                                tags.add(Tag.of(value, String.valueOf(toPut)));
                            }
                        }
                    }
                } catch (IllegalAccessException err) {
                    ExceptionUtils.wrapAndThrow(err);
                }
            }
        }

        log.trace("({}):(m-{}) captured tags: {}", operationName, headers.getMessageId(), tags);

        //
        // ~ execute actual business logic
        //
        var toReset = nonBlocking.get(); // ~ capture current value
        try (Scope activate = tracer.activateSpan(span)) {
            Objects.requireNonNull(tracer.activeSpan());

            // nonBlocking.set(true); // ~ prohibit any blocking operation
            action.accept(transaction);
            captureStackTraces(thread);

            //
            // ~ allow to set reply asynchronously, but at the same time acknowledge in advance so that we can consume next
            //
            ListenableFuture<?> replyCondition = transaction.replyCondition();
            replyCondition.addListener(new Runnable() {
                @Override
                public void run() {
                    TransactionalRequestOutcomeBuilder outcome = null;

                    try {
                        Object get = replyCondition.get();
                        if (Objects.nonNull(get)) {
                            log.trace("completed with reply: {}", get);
                        }

                        //
                        // ~ store as MDC field
                        //
                        long took = System.currentTimeMillis() - stopWatch.getStartInstant().toEpochMilli();
                        MDC.put(MdcTags.MDC_TOOK, String.valueOf(took));

                        var reply = transaction.reply().build();
                        var responseMapper = apiFactory.responseMapper();
                        var respw = responseMapper.toReply(reqw, reply, transaction.cacheControl());

                        outcome = TransactionalRequestOutcome.builder();
                        outcome.key(transaction.routingKey());
                        outcome.reply(respw);

                        registerNotifications(outcome);
                        registerEventStream(outcome);
                    } catch (ExecutionException err) {
                        captureFailure(err.getCause());
                    } catch (Exception err) {
                        captureFailure(err);
                    } finally {
                        try {
                            reportMetrics(thread);

                            //
                            // ~ capture number of JDBC transactions involved if possible
                            //
                            MutableInt jdbcTxCount = new MutableInt();

                            try {
                                for (Entry<StackTracer, ImmutableList<StackFrame>> entry : stacktraces.get().entrySet()) {
                                    StackTracer stack = entry.getKey();
                                    ImmutableList<StackFrame> frames = entry.getValue();
                                    if (stack instanceof JpaStackTracer) {
                                        if (CollectionUtils.isNotEmpty(frames)) {
                                            jdbcTxCount.add(frames.size());
                                        }
                                    }
                                }
                            } finally {
                                stacktraces.set(Collections.emptyMap());
                            }

                            //
                            // ~ only then complete and return control to high level dispatcher
                            //
                            if (Objects.nonNull(outcome)) {
                                future.set(outcome.build());
                            }

                            //
                            // ~ it is actually soft/weak reference so might be cleared by GC
                            //
                            if (jdbcTxCount.intValue() > 0) {
                                log.debug("finished {} : {}, jdbc.tx(count): {}", operationName, stopWatch, jdbcTxCount.get());
                            } else {
                                log.debug("finished {} : {}", operationName, stopWatch);
                            }
                        } finally {
                            try {
                                removeFrames(thread);
                            } finally {
                                removeMDC();
                            }
                        }
                    }
                }
            }, MoreExecutors.directExecutor());
        } catch (Throwable err) {
            captureFailure(err);
            reportMetrics(thread);
            removeFrames(thread);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            try {
                if (transaction.isAckOrNack()) {

                } else {
                    transaction.ack(); // ~ we mark to acknowledge as soon as possible
                }
            } finally {
                nonBlocking.set(toReset); // ~ simply reset to previous value
                thread.setName(oldName);
                removeMDC();
                transaction.clearMDCTags();
            }
        }
    }

    private void removeFrames(Thread originThread) {
        stackTracer.forEach(new Consumer<StackTracer>() {
            @Override
            public void accept(StackTracer t) {
                try {
                    t.drainFrames(originThread);
                } finally {
                    t.drainFrames(Thread.currentThread());
                }
            }
        });
    }

    private static void removeMDC() {
        MDC.remove(MdcTags.MDC_TOOK);
        MDC.remove(MdcTags.MDC_ERROR_CODE);
        MDC.remove(MdcTags.MDC_ERROR_CLASS);
    }
    private void addTags() {
        synchronized (mutex) {
            try {
                transaction.tags().forEach(new BiConsumer<String, String>() {
                    @Override
                    public void accept(String k, String v) {
                        if (StringUtils.isNotEmpty(v)) {
                            tags.add(Tag.of(k, v));
                        }
                    }
                });
            } catch (Exception err) {
                log.error("Error when adding tags", err);
            }
        }
    }
    private void captureStackTraces(Thread currentThread) {
        synchronized (mutex) {
            boolean isError = tags.stream().anyMatch(t -> t.getKey().equals(MetricTags.ERROR));
            //
            // ~ capture number of JDBC transactions involved
            //
            ImmutableMap.Builder<StackTracer, ImmutableList<StackFrame>> m = ImmutableMap.builder();
            stackTracer.forEach(new Consumer<StackTracer>() {
                @Override
                public void accept(StackTracer t) {
                    try {
                        ImmutableList<StackFrame> frames = t.frames(currentThread);
                        m.put(t, frames);
                        if (t instanceof JpaStackTracer) {
                            if (CollectionUtils.isNotEmpty(frames)) {
                                if (BooleanUtils.isFalse(isError)) {
                                    var txTags = ImmutableList.of(Tag.of(MetricTags.OPERATION, operationName));
                                    DistributionSummary summary = meterRegistry.summary(MetricTags.DB_TX_COUNT, txTags);
                                    summary.record(frames.size());
                                }
                            }
                        }
                    } finally {
                        t.drainFrames(currentThread);
                    }
                }
            });
            stacktraces.set(m.build());
        }
    }
    private void reportMetrics(Thread currentThread) {
        synchronized (mutex) {
            addTags();
            boolean isError = tags.stream().anyMatch(t -> t.getKey().equals(MetricTags.ERROR));
            if (BooleanUtils.isFalse(isError)) {
                tags.add(Tag.of(MetricTags.ERROR, "none"));
            }
            var replyTo = reqw.headers().getReplyTo();
            if (StringUtils.isNotEmpty(replyTo)) {
                tags.add(Tag.of(MetricTags.REPLY_TO, MutableNonPersistentReplyTopic.asPersistentReplyTo(replyTo, uriFactory)));
            }
            stopWatch.stop();

            Timer timer = meterRegistry.timer(METRIC_NAME, tags);
            timer.record(stopWatch.getDuration());
        }
    }
    private void captureFailure(Throwable err) {
        synchronized (mutex) {
            if (BooleanUtils.negate(toReturn.isDone())) {
                Throwable cause = ExceptionUtils.getRootCause(err);
                if (Objects.isNull(cause)) {
                    cause = err;
                }

                ApplicationException app = null;
                if (cause instanceof ApplicationException ae) {
                    app = ae;
                } else if (err instanceof ApplicationException ae) {
                    app = ae;
                }

                tags.add(Tag.of(MetricTags.ERROR, cause.getClass().getSimpleName()));

                if (app != null) {
                    tags.add(Tag.of(MetricTags.ERROR_CODE, String.valueOf(app.getCode().toString())));

                    MDC.put(MdcTags.MDC_ERROR_CLASS, app.getClass().getSimpleName());
                    MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(app.getCode().toString()));
                }

                Message.Builder reply = transaction.reply();

                if (transaction.isPreserveReply()) {
                    //
                    // ~ NO-OP we want to keep the state populated by handler before the exception occurred
                    //
                } else {
                    reply.clear();
                }

                var fault = apiFactory.toExceptional(reqw.headers().getMessageId(), app != null ? app : cause);
                var respw = apiFactory.responseMapper().toReply(reqw, reply.build(), fault);

                //
                // ~ we don't want to bombard sentry in unnecessary cases
                //
                boolean logAsFailure = false;
                if (respw.status().isSystem()) {
                    logAsFailure = true;
                } else if (respw.status().isTimeout()) {
                    logAsFailure = true;
                }

                //
                // ~ so we post as error only system/timeout by default, subject for later changes
                //
                if (app != null) {
                    if (logAsFailure) {
                        log.atError().setCause(cause).log();
                    } else {
                        log.atWarn().setCause(cause).log();
                    }
                } else {
                    log.atError().setCause(err).log();
                }

                var outcome = TransactionalRequestOutcome.builder();
                outcome.key(transaction.routingKey());
                outcome.reply(respw);

                //
                // ~ in some cases we would like to preserve notifications
                //
                if (transaction.isPreserveNotifications()) {
                    registerNotifications(outcome);
                }
                if (transaction.isPreserveEventStream()) {
                    registerEventStream(outcome);
                }

                future.set(outcome.build());
            }
        }
    }
    private void registerNotifications(TransactionalRequestOutcomeBuilder outcome) {
        synchronized (mutex) {
            outcome.notifications(transaction
                    .notifications()
                    .stream()
                    .map(it -> apiFactory.notificationMapper().pack(it))
                    .toList());
        }
    }
    private void registerEventStream(TransactionalRequestOutcomeBuilder outcome) {
        synchronized (mutex) {
            outcome.eventStream(transaction
                    .eventStreaming()
                    .stream()
                    .map(it -> Any.pack(it.build()))
                    .toList());
        }
    }
    @Override
    public String toString() {
        return reqw.body().getTypeUrl();
    }
}
