package com.turbospaces.dispatch;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeoutException;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;

import com.google.common.util.concurrent.FutureCallback;
import com.google.protobuf.Empty;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.ApiResponse;

import api.v1.ApiFactory;
import io.vavr.CheckedConsumer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractSafeResponseConsumer implements Runnable, FutureCallback<ResponseWrapperFacade>, CheckedConsumer<ResponseWrapperFacade> {
    private final ApiResponse<?> apiResponse;
    private final ApiFactory apiFactory;

    public AbstractSafeResponseConsumer(ApiResponse<?> post, ApiFactory apiFactory) {
        this.apiResponse = Objects.requireNonNull(post);
        this.apiFactory = Objects.requireNonNull(apiFactory);
    }
    @Override
    public final void run() {
        ResponseWrapperFacade respw = null;
        try {
            var input = apiResponse.get();
            respw = apiResponse.toReply(input.unpack(), input.status());
        } catch (Exception err) {
            Throwable cause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(cause)) {
                cause = err;
            }
            if (cause instanceof TimeoutException) {
                log.warn(cause.getMessage(), cause); // ~ just warning

                //
                // ~ accept wrapped timeout exception properly
                //
                try {
                    var messageId = Optional.ofNullable(MDC.get(MdcTags.MDC_MESSAGE_ID)).orElse(ApiFactory.UUID.generate().toString());
                    var fault = apiFactory.toExceptional(messageId, cause);
                    onSuccess(apiFactory.responseMapper().toReply(apiResponse.wrapper(), Empty.getDefaultInstance(), fault));
                } catch (Exception nested) {
                    log.error(nested.getMessage(), nested); // ~ raise sentry alert at least
                }
            } else {
                onFailure(cause);
            }
        }

        if (Objects.nonNull(respw)) {
            onSuccess(respw);
        }
    }
    @Override
    public final void onSuccess(ResponseWrapperFacade result) {
        try {
            accept(result);
        } catch (Throwable t) {
            onFailure(t);
        }
    }
    @Override
    public void onFailure(Throwable t) {
        Throwable rootCause = ExceptionUtils.getRootCause(t);
        if (Objects.isNull(rootCause)) {
            rootCause = t;
        }
        log.error(rootCause.getMessage(), rootCause); // ~ raise sentry alert at least
    }
}
