package com.turbospaces.dispatch;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import org.apache.commons.lang3.BooleanUtils;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.protobuf.Message;
import com.google.protobuf.Message.Builder;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.executor.WorkUnit;

import io.netty.util.AsciiString;

public interface TransactionalRequest<REQ extends Message, RESP extends Message.Builder> {
    RequestWrapperFacade wrapper();
    REQ request() throws Exception;
    RESP reply();
    Date timestamp();
    LocalDate at();
    AsciiString routingKey();
    void setRoutingKey(AsciiString hash);
    WorkUnit record();

    String addMetricTag(String k, String v);
    Map<String, String> tags();
    void addMDCTag(String key, String value);
    void clearMDCTags();

    boolean ack();
    boolean nack();
    boolean isAcked();
    boolean isNacked();
    default boolean isAckOrNack() {
        return BooleanUtils.or(new boolean[] { isAcked(), isNacked() });
    }

    void cache(Duration duration);
    void cache(api.v1.CacheControl.Builder control);
    api.v1.CacheControl cacheControl();
    default void cache(Supplier<Duration> supplier) {
        cache(supplier.get());
    }

    void preserveReply();
    void preserveNotifications();
    void preserveEventStream();
    boolean isPreserveReply();
    boolean isPreserveNotifications();
    boolean isPreserveEventStream();

    ListenableFuture<?> replyCondition();
    void replyWhen(ListenableFuture<?> task);

    <T extends Builder> T notify(T msg);
    <T extends Builder> T eventStream(T msg);

    List<Message.Builder> notifications();
    List<Message.Builder> eventStreaming();

    void clear();
}
