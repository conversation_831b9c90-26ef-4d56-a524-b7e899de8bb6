package com.turbospaces.dispatch;

import com.google.protobuf.Message;
import com.turbospaces.api.ReadOnlyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.api.TopicRegistry;
import com.turbospaces.executor.WorkUnit;

import io.vavr.CheckedConsumer;
import io.vavr.CheckedRunnable;

public interface RequestHandler<REQ extends Message, RESP extends Message.Builder> extends CheckedConsumer<TransactionalRequest<REQ, RESP>> {
    boolean actorIsRequired();
    Class<REQ> requestType();
    boolean isImmediateAcknowledge();
    default CheckedRunnable decorate(TopicRegistry registry, WorkUnit workUnit, SafeRequestHandler<REQ, RESP> delegate) {
        return new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                Topic topic = registry.forName(workUnit.topic());

                if (topic instanceof ReadOnlyTopic) {
                    READ_ONLY_MARKER.set(Boolean.TRUE);
                }

                try {
                    delegate.run();
                } finally {
                    READ_ONLY_MARKER.remove();
                }
            }
        };
    }

    ThreadLocal<Boolean> READ_ONLY_MARKER = new ThreadLocal<>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };
}
