package com.turbospaces.dispatch;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Empty;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.jpa.CompositeStackTracer;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.MdcUtil;

import api.v1.ApiFactory;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;

public abstract class AbstractServerRequestConsumer implements ServiceCallAsyncDispatcher {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final ApiFactory apiFactory;
    protected final CompositeStackTracer stackTracer;
    protected Duration timeout;

    protected AbstractServerRequestConsumer(
            ApplicationProperties props,
            ApiFactory apiFactory,
            CompositeStackTracer stackTracer) {
        this.props = Objects.requireNonNull(props);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.stackTracer = Objects.requireNonNull(stackTracer);
        this.timeout = props.BATCH_COMPLETION_TIMEOUT.get();
    }
    protected Optional<ListenableFuture<TransactionalRequestOutcome>> logAndAccept(
            RequestWrapperFacade reqw,
            WorkUnit unit,
            Sinks.One<Boolean> latch) throws Throwable {
        String typeUrl = reqw.body().getTypeUrl();

        //
        // ~ set corresponding MDC values
        //
        String operation = PlatformUtil.toLowerUnderscore(typeUrl);
        ApiFactory.setMdc(unit, operation, reqw.headers());

        try {
            logger.info("IN ::: ({}) workUnit: {}", typeUrl, unit);

            //
            // ~ maybe time-outed already
            //
            int origin = reqw.headers().getTimeout();
            boolean outdated = false;
            if (origin > 0) {
                long delta = System.currentTimeMillis() - unit.timestamp();
                if (delta > TimeUnit.SECONDS.toMillis(origin)) {
                    outdated = true;
                }
            }

            if (outdated) {
                EmitResult emitResult = latch.tryEmitValue(true);
                logger.warn("({}) request - outdated", typeUrl);
                if (emitResult.isFailure()) {

                }
            } else {
                return Optional.ofNullable(schedule(unit, reqw, latch));
            }
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            throw err;
        } finally {
            MdcUtil.clearMdc(unit);
        }

        return Optional.empty();
    }
    protected WorkerCompletableTask convertUnhandledException(RequestWrapperFacade facade, WorkUnit unit, Throwable err) {
        var fault = apiFactory.toExceptional(facade.headers().getMessageId(), err);
        var respw = apiFactory.responseMapper().toReply(facade, Empty.getDefaultInstance(), fault);

        var delegate = SettableFuture.<TransactionalRequestOutcome> create();
        var completable = new WorkerCompletableTask(delegate, unit, respw.headers().getMessageId());
        var outcome = TransactionalRequestOutcome.builder().reply(respw);
        unit.ifKeyPresent(outcome::key);
        delegate.set(outcome.build());
        return completable;
    }
}
