package com.turbospaces.dispatch;

import com.google.common.util.concurrent.ListenableFuture;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.executor.WorkUnit;

import reactor.core.publisher.Sinks;

public interface ServiceCallAsyncDispatcher {
    ListenableFuture<TransactionalRequestOutcome> schedule(WorkUnit unit, RequestWrapperFacade reqw, Sinks.One<Boolean> latch) throws Throwable;
}
