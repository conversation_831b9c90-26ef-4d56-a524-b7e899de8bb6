package com.turbospaces.dispatch;

import java.time.Duration;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.concurrent.LazyInitializer;
import org.slf4j.MDC;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.WorkUnit;

import api.v1.CacheControl;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;

@Slf4j
public abstract class AbstractTransactionalRequest<REQ extends Message, RESP extends Message.Builder> implements TransactionalRequest<REQ, RESP> {
    //
    // ~ reply builder
    //
    protected RESP reply;

    //
    // ~ additional tags to be reported to metrics collector
    //
    protected final Map<String, String> tags = new HashMap<>();
    protected final Set<String> mdcTags = new HashSet<>();

    //
    // 1) post back notifications to UI via web-sockets
    // 2) post event to long-running storage
    // 3) post to any queue
    //
    protected final List<Message.Builder> notifications = Lists.newLinkedList();
    protected final List<Message.Builder> eventStream = Lists.newLinkedList();

    //
    // check if the message has been acknowledged by handler
    //
    protected final AtomicBoolean ack = new AtomicBoolean();

    //
    // check if the message has been negative acknowledged by handler
    //
    protected final AtomicBoolean nack = new AtomicBoolean();

    //
    // ~ Kafka/PubSub work unit
    //
    protected WorkUnit record;

    //
    // ~ request wrapper similar to HTTP (status line + body)
    //
    protected RequestWrapperFacade wrapper;

    //
    // ~ if this is a batch, we want to count-down (Kafka)
    //
    protected Sinks.One<Boolean> latch;

    //
    // ~ ordering key
    //
    protected AsciiString routingKey;

    //
    // ~ lazy decoder of incoming request
    //
    protected LazyInitializer<REQ> req;

    //
    // ~ allow to acknowledge message consumption earlier and send reply to client later on (after say remote HTTP call)
    //
    protected List<ListenableFuture<?>> replyConditions = Lists.newLinkedList();

    //
    // ~ in case of exception, do we want to preserve the state of replies and added notification (crucial in some cases)
    //
    protected boolean preserveReply;
    protected boolean preserveNotifications;
    protected boolean preserveEventStream;

    //
    // ~ allow low level control of caching which has to be applied to HTTP responses
    //
    protected api.v1.CacheControl cacheControl = api.v1.CacheControl.getDefaultInstance();

    protected AbstractTransactionalRequest(RESP prototype, WorkUnit record, RequestWrapperFacade wrapper, Sinks.One<Boolean> latch) {
        this.reply = Objects.requireNonNull(prototype);
        this.record = Objects.requireNonNull(record);
        this.wrapper = Objects.requireNonNull(wrapper);
        this.latch = Objects.requireNonNull(latch);
        if (Objects.nonNull(record.key())) {
            this.routingKey = new AsciiString(record.key());
        }
    }
    @Override
    public RequestWrapperFacade wrapper() {
        return wrapper;
    }
    @Override
    public RESP reply() {
        return reply;
    }
    @Override
    public LocalDate at() {
        return PlatformUtil.toLocalUTCDate(timestamp());
    }
    @Override
    public AsciiString routingKey() {
        return routingKey;
    }
    @Override
    public void setRoutingKey(AsciiString routingKey) {
        this.routingKey = Objects.requireNonNull(routingKey);
    }
    @Override
    public boolean isPreserveReply() {
        return preserveReply;
    }
    @Override
    public boolean isPreserveNotifications() {
        return preserveNotifications;
    }
    @Override
    public boolean isPreserveEventStream() {
        return preserveEventStream;
    }
    @Override
    public WorkUnit record() {
        return record;
    }
    @Override
    public ListenableFuture<?> replyCondition() {
        return Futures.allAsList(replyConditions);
    }
    @Override
    public Map<String, String> tags() {
        return tags;
    }
    @Override
    public void addMDCTag(String key, String value) {
        mdcTags.add(key);
        MDC.put(key, value);
    }
    @Override
    public void clearMDCTags() {
        mdcTags.forEach(MDC::remove);
        mdcTags.clear();
    }
    @Override
    public <T extends Message.Builder> T notify(T msg) {
        notifications.add(msg);
        return msg;
    }
    @Override
    public <T extends Message.Builder> T eventStream(T msg) {
        eventStream.add(msg);
        return msg;
    }
    @Override
    public List<Message.Builder> notifications() {
        return notifications;
    }
    @Override
    public List<Message.Builder> eventStreaming() {
        return eventStream;
    }
    @Override
    public void clear() {
        notifications.clear();
        eventStream.clear();
    }
    @Override
    public boolean isAcked() {
        return ack.get();
    }
    @Override
    public boolean isNacked() {
        return nack.get();
    }
    @Override
    public void cache(Duration duration) {
        int seconds = (int) duration.toSeconds();
        cache(api.v1.CacheControl.newBuilder()
                .setPrivate(false)
                .setNoCache(false)
                .setNoStore(false)
                .setNoTransform(false)
                .setProxyRevalidate(false)
                .setMustRevalidate(false)
                .setMaxAge(seconds)
                .setSMaxAge(seconds));
    }
    @Override
    public void cache(api.v1.CacheControl.Builder control) {
        this.cacheControl = control.build();
    }
    @Override
    public CacheControl cacheControl() {
        return cacheControl;
    }
    @Override
    public boolean ack() {
        Preconditions.checkArgument(BooleanUtils.isFalse(nack.get()));

        boolean success = ack.compareAndSet(false, true);
        if (success) {
            EmitResult emitResult = latch.tryEmitValue(true);
            if (emitResult.isFailure()) {

            }
        } else {
            log.error("attempting to double ack: {}", record); // ~ protect from API abuse (raise sentry alert)
        }
        return success;
    }
    @Override
    public boolean nack() {
        Preconditions.checkArgument(BooleanUtils.isFalse(ack.get()));

        boolean success = nack.compareAndSet(false, true);
        if (success) {
            EmitResult emitResult = latch.tryEmitValue(true);
            if (emitResult.isFailure()) {

            }
        } else {
            log.error("attempting to double nack: {}", record); // ~ protect from API abuse (raise sentry alert)
        }
        return success;
    }
    @Override
    public void preserveReply() {
        this.preserveReply = true;
    }
    @Override
    public void preserveNotifications() {
        this.preserveNotifications = true;
    }
    @Override
    public void preserveEventStream() {
        this.preserveEventStream = true;
    }
    @Override
    public String addMetricTag(String k, String v) {
        return tags.put(k, v);
    }
    @Override
    public void replyWhen(ListenableFuture<?> task) {
        replyConditions.add(Objects.requireNonNull(task));
    }
}
