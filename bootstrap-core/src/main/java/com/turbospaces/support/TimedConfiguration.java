package com.turbospaces.support;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;

@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
@RequiredArgsConstructor
public class TimedConfiguration {
    private final MeterRegistry meterRegistry;

    @Bean
    public TimedAspect timedAspect() {
        return new TimedAspect(meterRegistry);
    }
}
