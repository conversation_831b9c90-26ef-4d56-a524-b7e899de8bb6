package com.turbospaces.healthchecks;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import com.turbospaces.boot.AbstractHealtchCheck;
import com.turbospaces.cfg.ApplicationProperties;

public class HttpHealthCheck extends AbstractHealtchCheck {
    private static final int OK = 200;

    private final boolean bootstrapOnly;
    private final Set<Integer> okCodes;
    private final ApplicationProperties props;
    private final URI uri;

    public HttpHealthCheck(ApplicationProperties props, URI uri) {
        this(props, uri, false);
    }
    public HttpHealthCheck(ApplicationProperties props, URI uri, boolean bootstrapOnly) {
        this(props, uri, Collections.singleton(OK), bootstrapOnly);
    }
    public HttpHealthCheck(ApplicationProperties props, URI uri, Collection<Integer> okCodes, boolean bootstrapOnly) {
        if (okCodes.isEmpty()) {
            throw new IllegalArgumentException();
        }

        this.props = Objects.requireNonNull(props);
        this.uri = Objects.requireNonNull(uri);
        this.okCodes = new HashSet<>(okCodes);
        this.bootstrapOnly = bootstrapOnly;
    }
    @Override
    protected Result check() throws Exception {
        try {
            logger.debug("checking connectivity {} ...", uri);

            HttpURLConnection conn = (HttpURLConnection) uri.toURL().openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout((int) props.TCP_CONNECTION_TIMEOUT.get().toMillis());
            conn.setReadTimeout((int) props.TCP_SOCKET_TIMEOUT.get().toMillis());

            String userInfo = uri.getUserInfo();
            if (userInfo != null && !userInfo.isEmpty()) {
                String auth = Base64.getEncoder().encodeToString(userInfo.getBytes());
                conn.setRequestProperty("Authorization", "Basic " + auth);
            }

            boolean okCode = okCodes.contains(conn.getResponseCode());

            StringBuilder sb = new StringBuilder();
            if (okCode) {
                try (InputStream io = conn.getInputStream()) {
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(io))) {
                        String output;
                        while ( (output = br.readLine()) != null ) {
                            sb.append(output);
                        }
                    }
                }
            }

            logger.debug("{} response: {}", uri, conn.getResponseCode());
            return okCode ? Result.healthy(sb.toString()) : Result.unhealthy("unexpected response code: " + conn.getResponseCode());
        } catch (Exception err) {
            logger.warn(err.getMessage(), err);
            return Result.unhealthy(err);
        }
    }
    @Override
    public void destroy() throws Exception {

    }
    @Override
    public boolean isBootstrapOnly() {
        return bootstrapOnly;
    }
}
