package com.turbospaces.healthchecks;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URI;
import java.net.URL;
import java.util.Objects;

import org.springframework.cloud.service.UriBasedServiceInfo;

import com.codahale.metrics.health.HealthCheck;
import com.turbospaces.boot.AbstractHealtchCheck;
import com.turbospaces.cfg.ApplicationProperties;

public class SocketHealthCheck extends AbstractHealtchCheck {
    private final boolean bootstrapOnly;
    private final ApplicationProperties props;
    private final UriBasedServiceInfo serviceInfo;

    public SocketHealthCheck(ApplicationProperties props, UriBasedServiceInfo serviceInfo) {
        this(props, serviceInfo, false);
    }
    public SocketHealthCheck(ApplicationProperties props, UriBasedServiceInfo serviceInfo, boolean bootstrapOnly) {
        this.props = Objects.requireNonNull(props);
        this.serviceInfo = Objects.requireNonNull(serviceInfo);
        this.bootstrapOnly = bootstrapOnly;
    }
    @Override
    protected Result check() throws Exception {
        String host = serviceInfo.getHost();
        int port = serviceInfo.getPort();

        if (port > 0) {} else {
            switch (serviceInfo.getScheme()) {
                case "http":
                case "https": {
                    URL url = new URI(serviceInfo.getUri()).toURL();
                    port = url.getDefaultPort();
                    break;
                }
                default:
                    break;
            }
        }

        logger.debug("checking TCP connection on {}:{} ...", host, port);

        try (Socket socket = new Socket()) {
            InetSocketAddress addr = new InetSocketAddress(host, port);

            socket.setTcpNoDelay(props.TCP_NO_DELAY.get());
            socket.setKeepAlive(props.TCP_KEEP_ALIVE.get());
            socket.setSoTimeout((int) props.TCP_SOCKET_TIMEOUT.get().toMillis());
            socket.connect(addr, (int) props.TCP_CONNECTION_TIMEOUT.get().toMillis());

            return HealthCheck.Result.healthy("connected to: " + addr.toString());
        } catch (Exception err) {
            logger.warn(err.getMessage(), err);
            return HealthCheck.Result.unhealthy(err.getMessage());
        }
    }
    @Override
    public boolean isBootstrapOnly() {
        return bootstrapOnly;
    }
    @Override
    public void destroy() throws Exception {

    }
}
