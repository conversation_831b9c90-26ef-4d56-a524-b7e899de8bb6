package com.turbospaces.aspects;

import java.lang.reflect.Method;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import com.turbospaces.annotations.Retryable;

import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Aspect
@RequiredArgsConstructor
public class RetryAspect implements ApplicationContextAware {
    private final RetryRegistry retryRegistry;
    private ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = Objects.requireNonNull(applicationContext);
    }
    @Around("@within(com.turbospaces.annotations.Retryable)")
    public Object retryClass(ProceedingJoinPoint pjp) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        Class<?> declaringClass = method.getDeclaringClass();
        Retryable retry = declaringClass.getAnnotation(Retryable.class);

        return perform(pjp, retry, method);
    }

    @Around("execution (@com.turbospaces.annotations.Retryable * *.*(..))")
    public Object retryMethod(ProceedingJoinPoint pjp) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        Retryable retry = method.getAnnotation(Retryable.class);
        if (Objects.isNull(retry)) {
            method = pjp.getTarget().getClass().getMethod(method.getName(), method.getParameterTypes());
            retry = method.getAnnotation(Retryable.class);
        }

        return perform(pjp, retry, method);
    }

    private Object perform(ProceedingJoinPoint pjp, Retryable retryable, Method method) throws Throwable {
        String cfgName = StringUtils.isNotEmpty(retryable.config()) ? retryable.config() : "default";
        String retryName = method.getDeclaringClass().getSimpleName() + "_" + method.getName() + "_" + cfgName;
        Retry retry = null;
        try {
            if (StringUtils.isNotEmpty(retryable.config())) {
                retry = retryRegistry.retry(retryName, (RetryConfig) context.getBean(retryable.config()));
            } else {
                retry = retryRegistry.retry(retryName);
            }
        } catch (Exception e) {
            log.error("Error when resolving retry config {}, retry is not applied", retryable.config(), e);
        }
        if (retry == null) {
            return pjp.proceed();
        }

        return retry.executeCheckedSupplier(pjp::proceed);
    }

}
