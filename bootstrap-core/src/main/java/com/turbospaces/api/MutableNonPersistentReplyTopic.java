package com.turbospaces.api;

import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.cloud.util.UriInfoFactory;

import com.google.common.collect.Iterables;
import com.google.common.net.HostAndPort;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.PlainServiceInfo;

import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;

@Slf4j
public class MutableNonPersistentReplyTopic implements MutableReplyTopic {
    private final AsciiString name;
    private final ReplyTopic orig;

    public MutableNonPersistentReplyTopic(ApplicationProperties props, DynamicCloud cloud, ReplyTopic delegate) {
        //
        // ~ by default on tertiary port
        //
        orig = Objects.requireNonNull(delegate);
        name = AsciiString.cached(asHttpReplyTopic(props, cloud.toFQSN(props.CLOUD_APP_TERTIARY_PORT.get())));
    }
    @Override
    public AsciiString name() {
        return name;
    }
    @Override
    public AsciiString origin() {
        return orig.name();
    }
    @Override
    public void configure(ApplicationConfig cfg) {
        orig.configure(cfg);
    }
    @Override
    public String toString() {
        return name().toString();
    }
    public String asHttpReplyTopic(ApplicationProperties props, HostAndPort hostAndPort) {
        var uri = new HttpUrl.Builder();
        uri.scheme(PlainServiceInfo.HTTP_SCHEME);
        uri.host(hostAndPort.getHost());
        if (hostAndPort.getPort() > 0) {
            uri.port(hostAndPort.getPort());
        }
        uri.addPathSegment(StringUtils.substringAfterLast(HttpProto.V1, "/"));
        uri.addPathSegment(StringUtils.substringAfterLast(HttpProto.REQUEST_REPLY_PATH, "/"));
        uri.addQueryParameter(MutableReplyTopic.QUERY_PARAM_TOPIC, orig.name().toString());
        uri.addQueryParameter(MutableReplyTopic.QUERY_PARAM_SLOT, props.CLOUD_APP_INSTANCE_INDEX.get());
        return uri.build().url().toExternalForm();
    }
    public static String asRedisReplyTopic(String replyTo, UriInfoFactory uriFactory) {
        var uri = uriFactory.createUri(replyTo);
        if (Objects.nonNull(uri.getScheme())) {
            switch (uri.getScheme()) {
                case RedisServiceInfo.REDIS_SCHEME: {
                    return replyTo;
                }
                case PlainServiceInfo.HTTP_SCHEME:
                case PlainServiceInfo.HTTPS_SCHEME: {
                    var decoder = new QueryStringDecoder(uri.getUri());
                    var parameters = decoder.parameters();
                    var topic = Iterables.getOnlyElement(parameters.get(QUERY_PARAM_TOPIC));
                    var slot = Iterables.getOnlyElement(parameters.get(QUERY_PARAM_SLOT));

                    return uriFactory.createUri(
                            RedisServiceInfo.REDIS_SCHEME,
                            uri.getHost(),
                            uri.getPort(),
                            null, null,
                            topic + "/" + slot).getUriString();
                }
                default: {

                }
            }
        }

        return replyTo;
    }
    public static String asPersistentReplyTo(String replyTo, UriInfoFactory uriFactory) {
        var uri = uriFactory.createUri(replyTo);

        if (Objects.nonNull(uri.getScheme())) {
            switch (uri.getScheme()) {
                case KafkaServiceInfo.KAFKA_SCHEME: {
                    return uri.getHost();
                }
                case RedisServiceInfo.REDIS_SCHEME: {
                    return StringUtils.substringBeforeLast(uri.getPath(), "/");
                }
                case PlainServiceInfo.HTTP_SCHEME:
                case PlainServiceInfo.HTTPS_SCHEME: {
                    var decoder = new QueryStringDecoder(uri.getUri());
                    var parameters = decoder.parameters();
                    var list = parameters.get(QUERY_PARAM_TOPIC);
                    Optional<String> opt = Optional.empty();
                    if (Objects.nonNull(list)) {
                        opt = Optional.of(Iterables.getOnlyElement(list));
                    }
                    return opt.get();
                }
                default: {

                }
            }
        }

        return replyTo;
    }
}
