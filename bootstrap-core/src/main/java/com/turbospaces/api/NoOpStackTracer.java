package com.turbospaces.api;

import java.lang.StackWalker.StackFrame;
import java.util.Collections;
import java.util.Iterator;

import com.google.common.collect.ImmutableList;
import com.turbospaces.api.jpa.CompositeStackTracer;

public class NoOpStackTracer implements StackTracer, CompositeStackTracer {
    @Override
    public Iterator<StackTracer> iterator() {
        return Collections.emptyIterator();
    }
    @Override
    public ImmutableList<StackFrame> frames(Thread orig) {
        return ImmutableList.of();
    }
    @Override
    public void drainFrames(Thread orig) {

    }
}
