package com.turbospaces.cache;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheStats;
import com.google.common.collect.ImmutableMap;

import io.vavr.CheckedFunction0;
import io.vavr.Function0;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class BlockhoundCacheWrapper<K, V> implements Cache<K, V> {
    private final Cache<K, V> cache;
    private final ConcurrentMap<K, V> asMap;

    public BlockhoundCacheWrapper(Cache<K, V> cache) {
        this.cache = Objects.requireNonNull(cache);
        this.asMap = new BlockhoundMapWrapper<>(cache.asMap());
    }
    @Override
    public V get(K key, Callable<? extends V> loader) throws ExecutionException {
        try {
            return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<V>() {
                @Override
                public V apply() throws Throwable {
                    return cache.get(key, loader);
                }
            });
        } catch (Throwable err) {
            if (err instanceof ExecutionException) {
                throw (ExecutionException) err;
            }

            Throwable rootCause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(rootCause)) {
                rootCause = err;
            }

            throw new ExecutionException(rootCause);
        }
    }
    @Override
    public V getIfPresent(Object key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return cache.getIfPresent(key);
            }
        });
    }
    @Override
    public ImmutableMap<K, V> getAllPresent(Iterable<? extends Object> keys) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<ImmutableMap<K, V>>() {
            @Override
            public ImmutableMap<K, V> apply() {
                return cache.getAllPresent(keys);
            }
        });
    }
    @Override
    public void put(K key, V value) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.put(key, value);
            }
        });
    }
    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.putAll(m);
            }
        });
    }
    @Override
    public void invalidate(Object key) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.invalidate(key);
            }
        });
    }
    @Override
    public void invalidateAll(Iterable<? extends Object> keys) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.invalidateAll(keys);
            }
        });
    }
    @Override
    public void invalidateAll() {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.invalidateAll();
            }
        });
    }
    @Override
    public long size() {
        return cache.size();
    }
    @Override
    public CacheStats stats() {
        return cache.stats();
    }
    @Override
    public ConcurrentMap<K, V> asMap() {
        return asMap;
    }
    @Override
    public void cleanUp() {
        cache.cleanUp();
    }
}
