package com.turbospaces.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentMap;

import com.google.common.collect.ForwardingConcurrentMap;

import io.vavr.Function0;
import lombok.RequiredArgsConstructor;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@RequiredArgsConstructor
public class BlockhoundMapWrapper<K, V> extends ForwardingConcurrentMap<K, V> {
    private final ConcurrentMap<K, V> delegete;

    @Override
    public boolean containsKey(Object key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<Boolean>() {
            @Override
            public Boolean apply() {
                return delegate().containsKey(key);
            }
        });
    }
    @Override
    public boolean containsValue(Object value) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<Boolean>() {
            @Override
            public Boolean apply() {
                return delegate().containsValue(value);
            }
        });
    }
    @Override
    public V get(Object key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return delegate().get(key);
            }
        });
    }
    @Override
    public V put(K key, V value) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return delegate().put(key, value);
            }
        });
    }
    @Override
    public void putAll(Map<? extends K, ? extends V> map) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                delegate().putAll(map);
            }
        });
    }
    @Override
    public boolean remove(Object key, Object value) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<Boolean>() {
            @Override
            public Boolean apply() {
                return delegate().remove(key, value);
            }
        });
    }
    @Override
    public V remove(Object key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return delegate().remove(key);
            }
        });
    }
    @Override
    public V putIfAbsent(K key, V value) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return delegate().putIfAbsent(key, value);
            }
        });
    }
    @Override
    public V replace(K key, V value) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return delegate().replace(key, value);
            }
        });
    }
    @Override
    public boolean replace(K key, V oldValue, V newValue) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<Boolean>() {
            @Override
            public Boolean apply() {
                return delegate().replace(key, oldValue, newValue);
            }
        });
    }
    @Override
    protected ConcurrentMap<K, V> delegate() {
        return delegete;
    }
}
