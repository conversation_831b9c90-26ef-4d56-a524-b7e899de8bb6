package com.turbospaces.cache;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;

import io.vavr.CheckedFunction0;
import io.vavr.Function0;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class BlockhoundLoadingCacheWrapper<K, V> implements LoadingCache<K, V> {
    private final LoadingCache<K, V> cache;
    private final ConcurrentMap<K, V> asMap;

    public BlockhoundLoadingCacheWrapper(LoadingCache<K, V> cache) {
        this.cache = Objects.requireNonNull(cache);
        this.asMap = new BlockhoundMapWrapper<>(cache.asMap());
    }
    @Override
    public V get(K key) throws ExecutionException {
        try {
            return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<V>() {
                @Override
                public V apply() throws Throwable {
                    return cache.get(key);
                }
            });
        } catch (Throwable err) {
            if (err instanceof ExecutionException) {
                throw (ExecutionException) err;
            }

            Throwable rootCause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(rootCause)) {
                rootCause = err;
            }

            throw new ExecutionException(rootCause);
        }
    }
    @Override
    public V get(K key, Callable<? extends V> loader) throws ExecutionException {
        try {
            return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<V>() {
                @Override
                public V apply() throws Throwable {
                    return cache.get(key, loader);
                }
            });
        } catch (Throwable err) {
            if (err instanceof ExecutionException) {
                throw (ExecutionException) err;
            }

            Throwable rootCause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(rootCause)) {
                rootCause = err;
            }

            throw new ExecutionException(rootCause);
        }
    }
    @Override
    public ImmutableMap<K, V> getAll(Iterable<? extends K> keys) throws ExecutionException {
        try {
            return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<ImmutableMap<K, V>>() {
                @Override
                public ImmutableMap<K, V> apply() throws Throwable {
                    return cache.getAll(keys);
                }
            });
        } catch (Throwable err) {
            if (err instanceof ExecutionException) {
                throw (ExecutionException) err;
            }

            Throwable rootCause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(rootCause)) {
                rootCause = err;
            }

            throw new ExecutionException(rootCause);
        }
    }
    @Override
    public V getUnchecked(K key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return cache.getUnchecked(key);
            }
        });
    }
    @Override
    public V getIfPresent(Object key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            public V apply() {
                return cache.getIfPresent(key);
            }
        });
    }
    @Override
    public ImmutableMap<K, V> getAllPresent(Iterable<? extends Object> keys) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<ImmutableMap<K, V>>() {
            @Override
            public ImmutableMap<K, V> apply() {
                return cache.getAllPresent(keys);
            }
        });
    }
    @Override
    public void put(K key, V value) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.put(key, value);
            }
        });
    }
    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.putAll(m);
            }
        });
    }
    @Override
    public void invalidate(Object key) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.invalidate(key);
            }
        });
    }
    @Override
    public void invalidateAll(Iterable<? extends Object> keys) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.invalidateAll(keys);
            }
        });
    }
    @Override
    public void invalidateAll() {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.invalidateAll();
            }
        });
    }
    @Override
    public long size() {
        return cache.size();
    }
    @Override
    public CacheStats stats() {
        return cache.stats();
    }
    @Override
    public ConcurrentMap<K, V> asMap() {
        return asMap;
    }
    @Override
    public void cleanUp() {
        cache.cleanUp();
    }
    @Override
    public V apply(K key) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<V>() {
            @Override
            @SuppressWarnings("deprecation")
            public V apply() {
                return cache.apply(key);
            }
        });
    }
    @Override
    public void refresh(K key) {
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                cache.refresh(key);
            }
        });
    }
}
