package com.turbospaces.common;

import java.io.Closeable;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.OverlappingFileLockException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FileLocker implements Closeable {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final FileChannel channel;
    private java.nio.channels.FileLock flock = null;

    @SuppressWarnings("resource")
    public FileLocker(File file) throws FileNotFoundException, IOException {
        channel = new RandomAccessFile(file, "rw").getChannel();
    }
    public void lock() throws IOException {
        synchronized (this) {
            flock = channel.lock();
        }
    }
    public boolean tryLock() throws IOException {
        synchronized (this) {
            try {
                flock = channel.tryLock();
                return flock != null;
            } catch (OverlappingFileLockException err) {
                logger.debug(err.getMessage(), err);
                return false;
            }
        }
    }
    public void unlock() throws IOException {
        synchronized (this) {
            if (flock != null) {
                flock.release();
            }
        }
    }
    @Override
    public void close() throws IOException {
        synchronized (this) {
            unlock();
            channel.close();
        }
    }
}
