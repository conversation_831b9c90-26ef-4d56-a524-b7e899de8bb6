package com.turbospaces.common;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

import com.google.common.util.concurrent.ListeningScheduledExecutorService;

import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.internal.SemaphoreBasedRateLimiter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;

@Slf4j
public class TriggerOnceAfterNOccurrencesRateLimiter implements Disposable {
    private final AtomicInteger counter = new AtomicInteger(); // ~ minimum occurrences
    private final SemaphoreBasedRateLimiter rateLimiter;
    private final int N;

    public TriggerOnceAfterNOccurrencesRateLimiter(String key, int N, ListeningScheduledExecutorService scheduler) {
        this(key, N, scheduler, Duration.ofMinutes(1));
    }
    public TriggerOnceAfterNOccurrencesRateLimiter(String key, int N, ListeningScheduledExecutorService scheduler, Duration duration) {
        this.N = N;

        RateLimiterConfig.Builder config = RateLimiterConfig.custom();
        config.limitForPeriod(1);
        config.limitRefreshPeriod(duration);
        config.timeoutDuration(Duration.ZERO);

        rateLimiter = new SemaphoreBasedRateLimiter(key, config.build(), new ScheduledExecutorService() {
            @Override
            public void execute(Runnable command) {
                scheduler.execute(command);
            }
            @Override
            public <T> Future<T> submit(Runnable task, T result) {
                return scheduler.submit(task, result);
            }
            @Override
            public Future<?> submit(Runnable task) {
                return scheduler.submit(task);
            }
            @Override
            public <T> Future<T> submit(Callable<T> task) {
                return scheduler.submit(task);
            }
            @Override
            public List<Runnable> shutdownNow() {
                return scheduler.shutdownNow();
            }
            @Override
            public void shutdown() {
                scheduler.shutdown();
            }
            @Override
            public boolean isTerminated() {
                return scheduler.isTerminated();
            }
            @Override
            public boolean isShutdown() {
                return scheduler.isShutdown();
            }
            @Override
            public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                return scheduler.invokeAny(tasks, timeout, unit);
            }
            @Override
            public <T> T invokeAny(Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
                return scheduler.invokeAny(tasks);
            }
            @Override
            public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException {
                return scheduler.invokeAll(tasks, timeout, unit);
            }
            @Override
            public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
                return scheduler.invokeAll(tasks);
            }
            @Override
            public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
                return scheduler.awaitTermination(timeout, unit);
            }
            @Override
            public ScheduledFuture<?> scheduleWithFixedDelay(Runnable command, long initialDelay, long delay, TimeUnit unit) {
                return scheduler.scheduleWithFixedDelay(decorator(command), initialDelay, delay, unit);
            }
            @Override
            public ScheduledFuture<?> scheduleAtFixedRate(Runnable command, long initialDelay, long period, TimeUnit unit) {
                return scheduler.scheduleAtFixedRate(decorator(command), initialDelay, period, unit);
            }
            @Override
            public <V> ScheduledFuture<V> schedule(Callable<V> callable, long delay, TimeUnit unit) {
                return scheduler.schedule(callable, delay, unit);
            }
            @Override
            public ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit) {
                return scheduler.schedule(command, delay, unit);
            }
            private Runnable decorator(Runnable command) {
                return new Runnable() {
                    @Override
                    public void run() {
                        if (counter.get() > 0) {
                            log.debug("resetting counter: {} to zero, current: {}", key, counter.get());
                        }
                        counter.set(0);
                        command.run();
                    }
                };
            }
        });
    }
    public boolean acquirePermission() {
        //
        // ~ there must be N events before rate limiter to be activated, but we need to emit only single event
        //
        int current = counter.incrementAndGet();
        if (current >= N) {
            if (rateLimiter.acquirePermission()) {
                counter.set(0);
                return true;
            }
        }
        return false;
    }
    @Override
    public void dispose() {
        rateLimiter.shutdown();
    }
}
