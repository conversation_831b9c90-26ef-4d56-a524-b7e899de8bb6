package com.turbospaces.common;

import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicReference;

public class AtomicBigDecimal {
    private final AtomicReference<BigDecimal> holder = new AtomicReference<>();

    public AtomicBigDecimal() {
        holder.set(BigDecimal.ZERO);
    }
    public AtomicBigDecimal(BigDecimal value) {
        holder.set(value);
    }
    public BigDecimal get() {
        return holder.get();
    }
    public BigDecimal addAndGet(BigDecimal value) {
        while (true) {
            BigDecimal current = holder.get();
            BigDecimal next = current.add(value);
            if (holder.compareAndSet(current, next)) {
                return next;
            }
        }
    }
    public BigDecimal subtractAndGet(BigDecimal value) {
        while (true) {
            BigDecimal current = holder.get();
            BigDecimal next = current.subtract(value);
            if (holder.compareAndSet(current, next)) {
                return next;
            }
        }
    }
    public BigDecimal multiplyAndGet(BigDecimal value) {
        while (true) {
            BigDecimal current = holder.get();
            BigDecimal next = current.multiply(value);
            if (holder.compareAndSet(current, next)) {
                return next;
            }
        }
    }
    public BigDecimal divideAndGet(BigDecimal value) {
        while (true) {
            BigDecimal current = holder.get();
            BigDecimal next = current.divide(value);
            if (holder.compareAndSet(current, next)) {
                return next;
            }
        }
    }
}
