package com.turbospaces.common;

import java.io.Externalizable;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectOutputStream;
import java.lang.StackWalker.Option;
import java.lang.StackWalker.StackFrame;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.channels.DatagramChannel;
import java.nio.channels.ServerSocketChannel;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.ToDoubleFunction;
import java.util.jar.Attributes;
import java.util.jar.Manifest;
import java.util.stream.Stream;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.text.RandomStringGenerator;
import org.apache.commons.text.TextRandomProvider;

import com.fasterxml.uuid.Generators;
import com.fasterxml.uuid.impl.RandomBasedGenerator;
import com.fasterxml.uuid.impl.TimeBasedEpochGenerator;
import com.fasterxml.uuid.impl.TimeBasedEpochRandomGenerator;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.CloudOptions;

import api.v1.ApiFactory;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.vavr.Function0;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
@UtilityClass
public class PlatformUtil {
    public static final int RANDOM_MIN_PORT = 1024;
    public static final int RANDOM_MAX_PORT = 65535;

    public static final Random RANDOM = ApiFactory.RANDOM;

    public static final long UINT32_MAX_VALUE = Integer.toUnsignedLong(Integer.MAX_VALUE) * 2 + 1;
    public static final LocalDateTime UINT32_DATE_TIME_MAX_VALUE = PlatformUtil.toLocalUTCDateTime(new Date(TimeUnit.SECONDS.toMillis(UINT32_MAX_VALUE)));

    public static final RandomStringGenerator RANDOM_NUMERIC = newStringGenerator().withinRange(new char[][] { { '0', '9' } }).get();
    public static final RandomStringGenerator RANDOM_ALPHABETIC = newStringGenerator().withinRange(new char[][] { { 'A', 'Z' }, { 'a', 'z' } }).get();
    public static final RandomStringGenerator RANDOM_ALPHANUMERIC = newStringGenerator().withinRange(new char[][] { { '0', '9' }, { 'A', 'Z' }, { 'a', 'z' } })
            .get();

    public static final RandomBasedGenerator UUID_V4 = Generators.randomBasedGenerator(RANDOM);
    public static final TimeBasedEpochGenerator UUID_V6 = Generators.timeBasedEpochGenerator(RANDOM);
    public static final TimeBasedEpochRandomGenerator UUID_V7 = Generators.timeBasedEpochRandomGenerator(RANDOM);

    public static final List<String> LOCAL_HOST_ADDRESSES = ImmutableList.of("127.0.0.1", "0:0:0:0:0:0:0:1", "::1");
    public static final String REQUEST = "Request";
    public static final String RESPONSE = "Response";
    public static final String EVENT = "Event";

    public static RandomStringGenerator.Builder newStringGenerator() {
        return RandomStringGenerator.builder().usingRandom(new TextRandomProvider() {
            @Override
            public int nextInt(int max) {
                return RANDOM.nextInt(max);
            }
        });
    }
    public static Optional<StackFrame> currentOperation(Class<?> top) {
        StackWalker walker = StackWalker.getInstance(Option.RETAIN_CLASS_REFERENCE);
        return walker.walk(new Function<Stream<StackFrame>, Optional<StackFrame>>() {
            @Override
            public Optional<StackFrame> apply(Stream<StackFrame> stream) {
                return stream.dropWhile(new Predicate<StackFrame>() {
                    @Override
                    public boolean test(StackFrame t) {
                        if (t.getDeclaringClass().equals(PlatformUtil.class)) {
                            return true;
                        }
                        if (t.getDeclaringClass().isAssignableFrom(top)) {
                            return true;
                        }

                        return Boolean.FALSE.booleanValue();
                    }
                }).findFirst();
            }
        });
    }
    public static boolean isLocalHost(String host) {
        return LOCAL_HOST_ADDRESSES.contains(host);
    }
    public static InetSocketAddress address(ApplicationConfig cfg) {
        String ip = cfg.getString(CloudOptions.CLOUD_APP_HOST);
        int port = cfg.getInteger(CloudOptions.CLOUD_APP_PORT);
        return new InetSocketAddress(ip, port);
    }
    public static InetSocketAddress address(Properties props) {
        String ip = props.get(CloudOptions.CLOUD_APP_HOST).toString();
        String port = props.get(CloudOptions.CLOUD_APP_PORT).toString();
        return new InetSocketAddress(ip, Integer.parseInt(port));
    }
    public static List<String> readStaticConstants(Class<?> type) {
        Preconditions.checkArgument(type.isInterface());

        List<String> l = Lists.newLinkedList();
        Field[] fields = FieldUtils.getAllFields(type);

        for (Field f : fields) {
            if (Modifier.isPublic(f.getModifiers()) && BooleanUtils.isTrue(Modifier.isStatic(f.getModifiers()))) {
                try {
                    String key = (String) FieldUtils.readStaticField(f);
                    l.add(key);
                } catch (IllegalAccessException ex) {
                    ExceptionUtils.wrapAndThrow(ex);
                }
            }
        }

        return l;
    }
    public static Optional<String> jarVersion(Class<?> clazz) {
        try (InputStream stream = clazz.getClassLoader().getResourceAsStream("/META-INF/MANIFEST.MF")) {
            Manifest manifest = new Manifest(stream);
            Attributes attributes = manifest.getMainAttributes();
            String version = attributes.getValue("Implementation-Version");
            if (version != null) {
                return Optional.of(version);
            }
        } catch (Exception err) {
            log.trace(err.getMessage(), err);
        }

        String version = clazz.getPackage().getImplementationVersion();
        if (version != null) {
            return Optional.of(version);
        }

        try {
            URL location = clazz.getProtectionDomain().getCodeSource().getLocation();
            if (location.getFile() != null) {
                return jarVersion(new File(location.getFile()));
            }
        } catch (Exception err) {
            log.trace(err.getMessage(), err);
        }

        return Optional.empty();
    }
    public static Optional<String> jarVersion(File f) throws IOException {
        try (java.util.jar.JarFile jar = new java.util.jar.JarFile(f)) {
            java.util.jar.Manifest manifest = jar.getManifest();
            java.util.jar.Attributes attributes = manifest.getMainAttributes();

            String impVersion = attributes.getValue("Implementation-Version");
            if (impVersion != null) {
                return Optional.of(impVersion);
            }
        }
        return Optional.empty();
    }
    public static String version(Property<String> prop) {
        String appName = prop.get();
        if (StringUtils.isNotEmpty(appName)) {
            String[] split = appName.split(":");
            if (split.length == 3) { // maven coordinates
                String groupId = split[0];
                String artifactId = split[1];
                String version = split[2];
                if (StringUtils.isNotEmpty(groupId) && StringUtils.isNotEmpty(artifactId)) {
                    return version;
                }
            }
        }

        Optional<String> opt = PlatformUtil.jarVersion(PlatformUtil.class);
        if (opt.isPresent()) {
            return opt.get();
        }

        return "latest".toUpperCase().intern();
    }
    public static List<Runnable> shutdownExecutor(ExecutorService executor, Duration timeout) {
        List<Runnable> runnables = Collections.emptyList();
        executor.shutdown(); // Disable new tasks from being submitted.
        try {
            //
            // ~ Wait a while for existing tasks to terminate
            //
            boolean allCompleted = executor.awaitTermination(timeout.toSeconds(), TimeUnit.SECONDS);
            if (BooleanUtils.isFalse(allCompleted)) {
                runnables = executor.shutdownNow(); // Cancel currently executing tasks.
                log.debug("listing {} tasks that never commenced execution ...", runnables.size());
                for (Runnable runnable : runnables) {
                    log.debug(runnable.toString());
                }
            }
        } catch (InterruptedException ie) {
            log.debug(ie.getMessage(), ie);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        return runnables;
    }
    public static String detectIp() {
        try (DatagramChannel ch = DatagramChannel.open()) {
            try (DatagramChannel connect = ch.connect(new InetSocketAddress("*******", 1))) {
                return ((InetSocketAddress) connect.getLocalAddress()).getHostString();
            }
        } catch (Exception err) {
            log.debug(err.getMessage(), err);
            try {
                return InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException nested) {
                throw new RuntimeException(nested);
            }
        }
    }
    public static LocalDateTime limitUpperUint32DateTime(Date date) {
        return limitUpperUint32DateTime(PlatformUtil.toLocalUTCDateTime(date));
    }
    public static LocalDateTime limitUpperUint32DateTime(LocalDateTime candidate) {
        long epochSecond = candidate.toInstant(ZoneOffset.UTC).getEpochSecond();
        if (epochSecond >= UINT32_MAX_VALUE) {
            return UINT32_DATE_TIME_MAX_VALUE;
        }
        return candidate;
    }
    public static LocalDate toLocalUTCDate() {
        return toLocalUTCDate(new Date());
    }
    public static LocalDate toLocalUTCDate(Date date) {
        return date.toInstant().atOffset(ZoneOffset.UTC).toLocalDate();
    }
    public static LocalDateTime toLocalUTCDateTime(Date date) {
        return date.toInstant().atOffset(ZoneOffset.UTC).toLocalDateTime();
    }
    public static String toLowerUnderscore(String key) {
        StringBuffer buffer = new StringBuffer();
        String operation = key.trim();

        //
        // ~ remove protobuf name space separator
        //
        int idx = key.lastIndexOf('/');
        if (idx > 0) {
            operation = key.substring(idx + 1);
        }

        idx = operation.lastIndexOf('.');
        if (idx > 0) {
            operation = operation.substring(idx + 1);
        }

        //
        // ~ remove 'Request/Response/Event'
        //
        for (String it : Arrays.asList(REQUEST, RESPONSE, EVENT)) {
            if (operation.endsWith(it)) {
                operation = operation.substring(0, operation.length() - it.length());
            }
        }

        //
        // replace Upper case with Lower case and append '_'
        //
        String[] strings = StringUtils.splitByCharacterTypeCamelCase(operation);
        for (int i = 1; i <= strings.length; i++) {
            String original = strings[i - 1];
            String toLower = StringUtils.lowerCase(original);
            buffer.append(toLower);

            // ~ append magic underscore
            if (i != strings.length) {
                buffer.append("_");
            }
        }

        return buffer.toString();
    }
    public static Instant parseTimestamp(String timestamp) {
        OffsetDateTime now = Instant.now().atOffset(ZoneOffset.UTC);
        int offset = 1;
        Range<Integer> range = Range.closed(now.minusYears(offset).getYear(), now.plusYears(offset).getYear());
        return parseTimestamp(timestamp, range);
    }
    public static Instant parseTimestamp(String timestamp, Range<Integer> yearDiff) {
        long value = Long.parseLong(timestamp);
        OffsetDateTime dateTime = Instant.ofEpochSecond(value).atOffset(ZoneOffset.UTC);

        log.trace("checking year: {} in range: {}", dateTime.getYear(), yearDiff);

        if (yearDiff.contains(dateTime.getYear())) {
            return dateTime.toInstant();
        }

        return Instant.ofEpochMilli(value);
    }
    public static String randomAlphanumeric(int length) {
        return RANDOM_ALPHANUMERIC.generate(length);
    }
    public static String randomAlphabetic(int length) {
        return RANDOM_ALPHABETIC.generate(length);
    }
    public static String randomNumeric(int length) {
        return RANDOM_NUMERIC.generate(length);
    }
    public static UUID randomUUID() {
        return randomUUIDv4();
    }
    public static UUID randomUUIDv4() {
        //
        // ~ seems to be non-blocking in comparison to SecureRandom
        //
        return UUID_V4.generate();
    }
    public static UUID randomUUIDv6() {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public UUID apply() {
                return UUID_V6.generate();
            }
        });
    }
    public static UUID randomUUIDv7() {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public UUID apply() {
                return UUID_V7.generate();
            }
        });
    }
    @SuppressWarnings("unchecked")
    public static <T extends Comparable<T>> T lowerClosedEndpoint(Range<T> originalRange) {
        if (originalRange.lowerBoundType() == com.google.common.collect.BoundType.CLOSED) {
            return originalRange.lowerEndpoint();
        }

        if (originalRange.lowerEndpoint() instanceof Float) {
            Float lowerFloat = (Float) originalRange.lowerEndpoint();
            return (T) Float.valueOf(Math.nextUp(lowerFloat));
        } else if (originalRange.lowerEndpoint() instanceof Double) {
            Double lowerDouble = (Double) originalRange.lowerEndpoint();
            return (T) Double.valueOf(Math.nextUp(lowerDouble));
        } else if (originalRange.lowerEndpoint() instanceof Integer) {
            var lowerInt = (Integer) originalRange.lowerEndpoint();
            return (T) Integer.valueOf(BigInteger.valueOf(lowerInt).add(BigInteger.ONE).intValueExact());
        } else if (originalRange.lowerEndpoint() instanceof Long) {
            var lowerInt = (Long) originalRange.lowerEndpoint();
            return (T) Long.valueOf(BigInteger.valueOf(lowerInt).add(BigInteger.ONE).longValueExact());
        }

        throw new UnsupportedOperationException();
    }
    @SuppressWarnings("unchecked")
    public static <T extends Comparable<T>> T upperClosedEndpoint(Range<T> originalRange) {
        if (originalRange.upperBoundType() == com.google.common.collect.BoundType.CLOSED) {
            return originalRange.upperEndpoint();
        }

        if (originalRange.upperEndpoint() instanceof Float) {
            Float upperFloat = (Float) originalRange.upperEndpoint();
            return (T) Float.valueOf(Math.nextDown(upperFloat));
        } else if (originalRange.upperEndpoint() instanceof Double) {
            Double upperDouble = (Double) originalRange.upperEndpoint();
            return (T) Double.valueOf(Math.nextDown(upperDouble));
        } else if (originalRange.upperEndpoint() instanceof Integer) {
            Integer upperInt = (Integer) originalRange.upperEndpoint();
            return (T) Integer.valueOf(upperInt - 1);
        } else if (originalRange.upperEndpoint() instanceof Long) {
            Long upperInt = (Long) originalRange.upperEndpoint();
            return (T) Long.valueOf(upperInt - 1);
        }

        throw new UnsupportedOperationException();
    }
    public static byte[] serialize(Externalizable obj) throws IOException {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            try (ObjectOutputStream oos = new ObjectOutputStream(out)) {
                obj.writeExternal(oos);
                oos.flush();
                return out.toByteArray();
            }
        }
    }
    public static <T> Gauge registerGauge(MeterRegistry registry,
            String name,
            Iterable<Tag> tags,
            T obj,
            ToDoubleFunction<T> valueFunction) {
        Gauge existing = registry.find(name).tags(tags).gauge();
        return Objects.nonNull(existing) ? existing : Gauge.builder(name, obj, valueFunction).tags(tags).register(registry);
    }
    public static int findAvailableTcpPort() {
        return findAvailableTcpPortInternal();
    }
    public static boolean isPortAvailable(int port) {
        try (ServerSocketChannel channel = ServerSocketChannel.open()) {
            channel.socket().setReuseAddress(true);
            channel.socket().bind(new InetSocketAddress(port));
            return true;
        } catch (Exception err) {
            return false;
        }
    }
    private static int findAvailableTcpPortInternal() {
        for (;;) {
            var port = RANDOM_MIN_PORT + ApiFactory.RANDOM.nextInt(RANDOM_MAX_PORT - RANDOM_MIN_PORT + 1);
            if (isPortAvailable(port)) {
                return port;
            }
        }
    }
}
