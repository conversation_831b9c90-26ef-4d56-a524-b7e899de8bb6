package com.turbospaces.common;

import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.mutable.Mutable;
import org.apache.commons.lang3.mutable.MutableObject;

import com.google.common.util.concurrent.Uninterruptibles;

import reactor.core.Disposable;

public class DisposableCountdownLatch implements Disposable, Mutable<Object> {
    private final Disposable disposable;
    private final CountDownLatch latch;
    private final MutableObject<Object> value;

    public DisposableCountdownLatch(Disposable disposable, CountDownLatch latch, MutableObject<Object> value) {
        this.disposable = Objects.requireNonNull(disposable);
        this.latch = Objects.requireNonNull(latch);
        this.value = Objects.requireNonNull(value);
    }
    @Override
    public Object getValue() {
        return value.get();
    }
    @Override
    public void setValue(Object val) {
        value.setValue(val);
    }
    @Override
    public void dispose() {
        disposable.dispose();
    }
    @Override
    public boolean isDisposed() {
        return disposable.isDisposed();
    }
    public void await() throws InterruptedException {
        latch.await();
    }
    public boolean await(int timeout, TimeUnit unit) {
        return Uninterruptibles.awaitUninterruptibly(latch, timeout, unit);
    }
    public boolean awaitSec(int timeout) {
        return Uninterruptibles.awaitUninterruptibly(latch, timeout, TimeUnit.SECONDS);
    }
    public static DisposableCountdownLatch immediate() {
        return new DisposableCountdownLatch(new Disposable() {
            @Override
            public boolean isDisposed() {
                return false;
            }
            @Override
            public void dispose() {

            }
        }, new CountDownLatch(0), new MutableObject<>());
    }
}
