package com.turbospaces.common;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import io.micrometer.core.instrument.Clock;

public class EventTimeClock implements Clock {
    private final ThreadLocal<Clock> current = new ThreadLocal<>() {
        @Override
        protected Clock initialValue() {
            return Clock.SYSTEM;
        }
    };

    public void current(Clock value) {
        current.set(value);
    }
    public void current(Date now) {
        current(now.getTime());
    }
    public void current(long now) {
        current.set(new Clock() {
            @Override
            public long wallTime() {
                return now;
            }
            @Override
            public long monotonicTime() {
                return TimeUnit.MILLISECONDS.toNanos(now);
            }
        });
    }
    public void currentNano(long now) {
        current.set(new Clock() {
            @Override
            public long wallTime() {
                return TimeUnit.NANOSECONDS.toMillis(now);
            }
            @Override
            public long monotonicTime() {
                return now;
            }
        });
    }
    public void remove() {
        current.remove();
    }
    @Override
    public long wallTime() {
        return current.get().wallTime();
    }
    @Override
    public long monotonicTime() {
        return current.get().monotonicTime();
    }
}
