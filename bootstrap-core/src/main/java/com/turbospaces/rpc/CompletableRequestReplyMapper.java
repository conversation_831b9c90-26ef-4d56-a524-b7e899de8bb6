package com.turbospaces.rpc;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.support.AbstractApplicationContext;

import com.google.common.base.Suppliers;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.cache.BlockhoundCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.executor.PlatformThread;
import com.turbospaces.mdc.MdcUtil;

import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.Timer;
import io.netty.util.TimerTask;
import io.vavr.Function0;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public class CompletableRequestReplyMapper<K, V> implements InitializingBean, DisposableBean, BeanNameAware, ApplicationContextAware, RequestReplyMapper<K, V> {
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final ScheduledExecutorService timer;
    protected final PlatformExecutorService executor;
    protected Duration tickDuration = Duration.ofMillis(100);
    protected int ticksPerWheel = 512;
    protected Supplier<Boolean> reportToSentryOnTimeout = Suppliers.ofInstance(true);
    protected Duration timeout = Duration.ofMinutes(3);

    private ScheduledFuture<?> cleanUp;
    private BlockhoundCacheWrapper<K, SettableFuture<V>> corr;
    private Timer wheel;
    private AbstractApplicationContext applicationContext;

    public CompletableRequestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.timeout = props.BATCH_COMPLETION_TIMEOUT.get().plusMinutes(1);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);

        ThreadFactoryBuilder factory = new ThreadFactoryBuilder();
        factory.setDaemon(false);
        factory.setNameFormat("request-reply-timeout-%d");
        factory.setThreadFactory(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new PlatformThread(props, r);
            }
        });

        //
        // ~ just one thread (for cleanUp purpose)
        //
        this.timer = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                return t;
            }
        });
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (AbstractApplicationContext) applicationContext;
    }
    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();

        //
        // ~ thread factory
        //
        ThreadFactoryBuilder factory = new ThreadFactoryBuilder();
        factory.setDaemon(true);
        factory.setNameFormat("request-reply-hashed-wheel-%d");

        //
        // ~ hashed wheel (high performance timer for large number of scheduled tasks)
        //
        HashedWheelTimer origin = new HashedWheelTimer(
                factory.build(), // ~ daemon thread with corresponding name
                tickDuration.toMillis(), // ~ max discrepancy
                TimeUnit.MILLISECONDS,
                ticksPerWheel, // ~ number of entries
                false, // ~ we don't need leak detector, we have only 1 instance
                -1, // ~ no max limit of elements added
                new Executor() {
                    @Override
                    public void execute(Runnable command) {
                        //
                        // ~ check if we are actually running and active
                        //
                        boolean toApply = true;
                        if (Objects.nonNull(applicationContext)) {
                            toApply = applicationContext.isActive();
                        }

                        if (toApply) {
                            executor.execute(command);
                        }
                    }
                } // ~ which executor to use in case of timeout occurrence
        );
        origin.start();

        //
        // ~ block hound tolerant implementation
        //
        wheel = new Timer() {
            @Override
            public Set<Timeout> stop() {
                return origin.stop();
            }
            @Override
            public Timeout newTimeout(TimerTask task, long delay, TimeUnit unit) {
                //
                // ~ temporary allow blocking operation inside
                //
                return DefaultBlockHoundIntegration.allowBlocking(new Function0<Timeout>() {
                    @Override
                    public Timeout apply() {
                        return origin.newTimeout(task, delay, unit);
                    }
                });
            }
            @Override
            public String toString() {
                return origin.toString();
            }
        };

        //
        // ~ holder of all responses (god object)
        //
        corr = new BlockhoundCacheWrapper<>(CacheBuilder.newBuilder()
                .expireAfterWrite(timeout)
                .removalListener(new RemovalListener<K, SettableFuture<V>>() {
                    @Override
                    public void onRemoval(RemovalNotification<K, SettableFuture<V>> notification) {
                        String type = notification.getCause().name().toLowerCase().intern();
                        log.trace("onRemoval({}): {} {}", notification.getKey(), type, notification);
                    }
                }).build());

        //
        // ~ useful reporting in logs
        //
        Duration cleanupFreq = Duration.ofMinutes(1);
        cleanUp = timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                long size = corr.size();
                if (size > 0) {
                    log.debug("about to cleanUp correlation map of {} items ...", size);
                }

                corr.cleanUp();
            }
        }, cleanupFreq.toSeconds(), cleanupFreq.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(cleanUp)) {
            cleanUp.cancel(true);
        }
        try {
            executor.destroy();
        } finally {
            try {
                PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
            } finally {
                StopWatch stopWatch = StopWatch.createStarted();
                Set<Timeout> tasks = wheel.stop();
                stopWatch.stop();
                log.info("stopped wheel: {} with remaining tasks: {} in: {}", wheel, tasks.size(), stopWatch);
            }
        }
    }
    @Override
    public SettableFuture<V> acquire(K key, Duration duration) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC
        StopWatch stopWatch = StopWatch.createStarted();

        SettableFuture<V> toReturn = SettableFuture.create();
        SettableFuture<V> putIfAbsent = corr.asMap().putIfAbsent(key, toReturn);
        if (Objects.nonNull(putIfAbsent)) {
            toReturn.setException(new IllegalArgumentException("duplicate key violation for correlation id: " + key));
        } else {
            //
            // ~ timeout task - complete future with timeout exception just in case
            //
            Timeout timerTask = wheel.newTimeout(new TimerTask() {
                @Override
                public void run(Timeout t) throws Exception {
                    if (t.isExpired()) {
                        log.debug(t.toString());
                    }

                    //
                    // ~ remove entry and propagate timeout exception
                    //
                    SettableFuture<V> tmp = corr.asMap().remove(key);
                    if (Objects.nonNull(tmp)) {
                        //
                        // ~ only complete when necessary
                        //
                        if (BooleanUtils.isFalse(tmp.isDone())) {
                            MdcUtil.propagate(mdc);
                            try {
                                tmp.setException(new RequestReplyTimeout(duration, key, reportToSentryOnTimeout.get()));
                                log.info("request-reply(m={}) removed subj due to timeout", key);
                            } catch (Exception err) {
                                log.error(err.getMessage(), err);
                            } finally {
                                MDC.clear();
                            }
                        }
                    }
                }
                @Override
                public String toString() {
                    return key.toString();
                }
            }, duration.toSeconds(), TimeUnit.SECONDS);

            Futures.addCallback(toReturn, new FutureCallback<V>() {
                @Override
                public void onSuccess(V result) {
                    try {
                        //
                        // ~ cancelling the timer under the hood is blocking operation under some circumstances
                        //
                        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
                            @Override
                            public void run() {
                                timerTask.cancel();
                                stopWatch.stop();
                                log.debug("request-reply(m={}) completed in {}", key, stopWatch);
                            }
                        });
                    } finally {
                        corr.invalidate(key);
                    }
                }
                @Override
                public void onFailure(Throwable t) {
                    log.atTrace().setCause(t).log();
                }
            }, MoreExecutors.directExecutor());
        }

        return toReturn;
    }
    @Override
    public boolean contains(K corrId) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<Boolean>() {
            @Override
            public Boolean apply() {
                return Objects.nonNull(corr.getIfPresent(corrId));
            }
        });
    }
    @Override
    public void complete(K key, V value) {
        Objects.requireNonNull(key);
        SettableFuture<V> subj = corr.asMap().remove(key);
        if (Objects.nonNull(subj)) {
            setValue(subj, value);
        } else {
            log.trace("no such correlation on complete for key: {}", key);
        }
    }
    @Override
    public void completeExceptionally(K key, Throwable reason) {
        Objects.requireNonNull(key);
        SettableFuture<V> subj = corr.asMap().remove(key);
        if (Objects.nonNull(subj)) {
            setException(subj, reason);
        } else {
            log.trace("no such correlation on completeExceptionally for key: {}", key);
        }
    }
    @Override
    public void clear() {
        corr.invalidateAll();
    }
    @Override
    public String toString() {
        return wheel.toString();
    }
    @Override
    public int pendingCount() {
        return corr.asMap().size();
    }
    protected void setValue(SettableFuture<V> subj, V value) {
        subj.set(value);
    }
    protected void setException(SettableFuture<V> subj, Throwable reason) {
        subj.setException(reason);
    }
}
