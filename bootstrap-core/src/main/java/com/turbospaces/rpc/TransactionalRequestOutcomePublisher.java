package com.turbospaces.rpc;

import java.util.concurrent.CompletionStage;

import com.turbospaces.api.Topic;
import com.turbospaces.dispatch.TransactionalRequestOutcome;

public interface TransactionalRequestOutcomePublisher<C> extends NotifyQueuePostTemplate<C>, EventQueuePostTemplate<C> {
    CompletionStage<?> publishReply(TransactionalRequestOutcome outcome);
    CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome);
    CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome);
}
