package com.turbospaces.rpc;

import java.util.concurrent.CompletionStage;

import org.apache.commons.lang3.StringUtils;

import com.google.protobuf.Message;
import com.turbospaces.api.Topic;
import com.turbospaces.dispatch.EventQueuePostSpec;

import io.netty.util.AsciiString;

public interface EventQueuePostTemplate<C> extends HasApiFactory {
    CompletionStage<C> sendEvent(EventQueuePostSpec spec);
    default CompletionStage<C> sendEvent(Message payload) {
        return sendEvent(apiFactory().eventsTopic(), payload);
    }
    default CompletionStage<C> sendEvent(Topic destination, Message payload) {
        EventQueuePostSpec.EventBuilder spec = EventQueuePostSpec.newBuilder(payload);
        spec.setTopic(destination);
        return sendEvent(spec.build());
    }
    default CompletionStage<C> sendEvent(Message payload, AsciiString routingKey) {
        return sendEvent(apiFactory().eventsTopic(), payload, routingKey);
    }
    default CompletionStage<C> sendEvent(Topic destination, Message payload, AsciiString routingKey) {
        EventQueuePostSpec.EventBuilder spec = EventQueuePostSpec.newBuilder(payload);
        spec.setTopic(destination);
        if (StringUtils.isNotEmpty(routingKey)) {
            spec.setRoutingKey(routingKey);
        }
        return sendEvent(spec.build());
    }
}
