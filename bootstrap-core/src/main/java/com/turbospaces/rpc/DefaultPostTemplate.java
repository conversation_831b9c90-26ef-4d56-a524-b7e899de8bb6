package com.turbospaces.rpc;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.BiConsumer;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.event.Level;
import org.springframework.cloud.util.StandardUriInfoFactory;
import org.springframework.cloud.util.UriInfo;

import com.google.common.base.Preconditions;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.ups.KafkaServiceInfo;

import api.v1.ApiFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public final class DefaultPostTemplate<C> implements QueuePostTemplate<C> {
    private final StandardUriInfoFactory uriFactory = new StandardUriInfoFactory();
    private final ApplicationProperties props;
    private final ApiFactory apiFactory;
    private final QueuePostTemplate<C> persistent;
    private final DefaultNonPersistentPostTemplate<C> nonPersistent;

    @Override
    public ApiFactory apiFactory() {
        return apiFactory;
    }
    @Override
    public WrappedQueuePost sendReq(RequestQueuePostSpec spec) {
        return persistent.sendReq(spec);
    }
    @Override
    public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
        ResponseWrapperFacade respw = outcome.getReply();
        if (Objects.nonNull(respw)) {
            String replyTo = respw.headers().getReplyTo();
            if (StringUtils.isNotEmpty(replyTo)) {
                CompletableFuture<Object> toReturn = new CompletableFuture<>();
                nonPersistent.publishReply(outcome).whenComplete(new BiConsumer<Object, Throwable>() {
                    @Override
                    public void accept(Object outer, Throwable outerErr) {
                        if (Objects.isNull(outerErr)) {
                            toReturn.complete(outer);
                        } else {
                            try {
                                //
                                // ~ we keep it only for integration tests only if the schema explicitly passed
                                //
                                UriInfo uri = uriFactory.createUri(replyTo);
                                Preconditions.checkArgument(KafkaServiceInfo.KAFKA_SCHEME.equals(uri.getScheme()));

                                //
                                // ~ no reply topics and patterns otherwise
                                //
                                persistent.publishReply(outcome).whenComplete(new BiConsumer<Object, Throwable>() {
                                    @Override
                                    public void accept(Object inner, Throwable innerErr) {
                                        if (Objects.isNull(innerErr)) {
                                            toReturn.complete(inner);
                                        } else {
                                            log.atLevel(props.isDevMode() ? Level.ERROR : Level.WARN).setCause(innerErr).log();
                                            toReturn.completeExceptionally(innerErr);
                                        }
                                    }
                                });
                            } catch (Exception err) {
                                toReturn.completeExceptionally(outerErr);
                            }
                        }
                    }
                });
                return toReturn;
            }
        }
        return CompletableFuture.completedStage(new Object());
    }
    @Override
    @SuppressWarnings("unchecked")
    public CompletionStage<C> sendNotify(NotifyQueuePostSpec spec) {
        CompletableFuture<C> toReturn = new CompletableFuture<>();
        nonPersistent.sendNotify(spec).whenComplete(new BiConsumer<Object, Throwable>() {
            @Override
            public void accept(Object outer, Throwable err) {
                if (Objects.isNull(err)) {
                    toReturn.complete((C) outer);
                } else {
                    toReturn.completeExceptionally(err);
                }
            }
        });
        return toReturn;
    }
    @Override
    public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
        CompletableFuture<Object> toReturn = new CompletableFuture<>();
        nonPersistent.publishNotifications(topic, outcome).whenComplete(new BiConsumer<Object, Throwable>() {
            @Override
            public void accept(Object outer, Throwable err) {
                if (Objects.isNull(err)) {
                    toReturn.complete(outer);
                } else {
                    toReturn.completeExceptionally(err);
                }
            }
        });
        return toReturn;
    }
    @Override
    public CompletionStage<C> sendEvent(EventQueuePostSpec spec) {
        return persistent.sendEvent(spec);
    }
    @Override
    public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
        return persistent.publishEvents(topic, outcome);
    }
}
