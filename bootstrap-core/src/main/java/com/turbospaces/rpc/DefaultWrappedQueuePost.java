package com.turbospaces.rpc;

import java.util.Objects;
import java.util.concurrent.Executor;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import lombok.experimental.Delegate;

public class DefaultWrappedQueuePost implements WrappedQueuePost {
    @Delegate
    private final FluentFuture<ResponseWrapperFacade> subject;
    private final RequestWrapperFacade requestWrapper;

    public DefaultWrappedQueuePost(ListenableFuture<ResponseWrapperFacade> future, RequestWrapperFacade requestWrapper) {
        this.subject = FluentFuture.from(future);
        this.requestWrapper = Objects.requireNonNull(requestWrapper);
    }
    @Override
    public RequestWrapperFacade requestWrapper() {
        return requestWrapper;
    }
    @Override
    public void addCallback(FutureCallback<ResponseWrapperFacade> callback, Executor executor) {
        subject.addCallback(callback, executor);
    }
}
