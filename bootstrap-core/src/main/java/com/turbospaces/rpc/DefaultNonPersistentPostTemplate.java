package com.turbospaces.rpc;

import java.io.IOException;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;
import org.slf4j.event.Level;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.util.StandardUriInfoFactory;
import org.springframework.cloud.util.UriInfo;

import com.google.common.base.Preconditions;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListenableScheduledFuture;
import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cache.BlockhoundCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.ups.PlainServiceInfo;

import api.v1.ApiFactory;
import api.v1.CloudEventWithRoutingKey;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;

@Slf4j
public final class DefaultNonPersistentPostTemplate<C> implements InitializingBean, DisposableBean, TransactionalRequestOutcomePublisher<C> {
    public static final MediaType X_PROTOBUF = MediaType.get("application/x-protobuf");
    public static final Set<String> SCHEMAS = Set.of(PlainServiceInfo.HTTP_SCHEME, PlainServiceInfo.HTTPS_SCHEME);
    private final StandardUriInfoFactory uriFactory = new StandardUriInfoFactory();
    private final ApplicationProperties props;
    private final TransactionalRequestOutcomePublisher<C> delegate;
    private final ApiFactory apiFactory;
    private final ListeningScheduledExecutorService timer;
    private final BlockhoundCacheWrapper<String, TriggerOnceAfterNOccurrencesRateLimiter> triggers;
    private final OkHttpClient httpClient;
    private ListenableScheduledFuture<?> task;

    public DefaultNonPersistentPostTemplate(ApplicationProperties props, ApiFactory apiFactory, TransactionalRequestOutcomePublisher<C> delegate) {
        this.props = Objects.requireNonNull(props);
        this.delegate = Objects.requireNonNull(delegate);
        this.apiFactory = Objects.requireNonNull(apiFactory);

        //
        // ~ just one thread
        //
        this.timer = MoreExecutors.listeningDecorator(Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        }));

        int cacheMaxSize = props.CACHE_DEFAULT_MAX_SIZE.get();
        Duration cacheMaxIdle = props.APP_TIMER_INTERVAL.get();
        Duration cacheMaxTtl = props.CACHE_DEFAULT_MAX_IDLE.get();

        triggers = new BlockhoundCacheWrapper<>(
                CacheBuilder.newBuilder()
                        .maximumSize(cacheMaxSize)
                        .expireAfterAccess(cacheMaxIdle)
                        .expireAfterWrite(cacheMaxTtl).build());

        OkHttpClient.Builder ok = new OkHttpClient.Builder();
        ok.connectTimeout(props.TCP_CONNECTION_TIMEOUT.get().toSeconds(), TimeUnit.SECONDS);
        ok.readTimeout(props.TCP_SOCKET_TIMEOUT.get().toSeconds(), TimeUnit.SECONDS);

        if (props.isDevMode()) {
            HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(log::debug);
            loggingInterceptor.setLevel(okhttp3.logging.HttpLoggingInterceptor.Level.BODY);
            ok.addInterceptor(loggingInterceptor);
        } else {
            HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(log::trace);
            loggingInterceptor.setLevel(okhttp3.logging.HttpLoggingInterceptor.Level.HEADERS);
            ok.addInterceptor(loggingInterceptor);
        }

        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(props.HTTP_POOL_MAX_SIZE.get());
        dispatcher.setMaxRequestsPerHost(props.HTTP_POOL_MAX_PER_ROUTE.get());

        Duration keepAlive = Duration.ofSeconds(30);
        if (props.TCP_KEEP_ALIVE_TIMEOUT.get().toSeconds() > 0) {
            keepAlive = props.TCP_KEEP_ALIVE_TIMEOUT.get();
        }

        ok.connectionPool(new ConnectionPool(props.KAFKA_MAX_WORKERS.get(), keepAlive.toSeconds(), TimeUnit.SECONDS));
        ok.dispatcher(dispatcher);

        httpClient = ok.build();
    }
    @Override
    public ApiFactory apiFactory() {
        return apiFactory;
    }
    @Override
    public CompletionStage<C> sendNotify(NotifyQueuePostSpec spec) {
        return delegate.sendNotify(spec);
    }
    @Override
    public CompletionStage<C> sendEvent(EventQueuePostSpec spec) {
        return delegate.sendEvent(spec);
    }
    @Override
    public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
        return delegate.publishNotifications(topic, outcome);
    }
    @Override
    public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
        return delegate.publishEvents(topic, outcome);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        Duration duration = props.APP_TIMER_INTERVAL.get();
        task = timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                triggers.cleanUp();
            }
        }, 0, duration.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public void destroy() throws Exception {
        triggers.invalidateAll();
        if (Objects.nonNull(task)) {
            task.cancel(true);
        }
    }
    @Override
    public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
        Map<String, String> mdc = Maps.newHashMap();
        Optional.ofNullable(MDC.getCopyOfContextMap()).ifPresent(new Consumer<>() {
            @Override
            public void accept(Map<String, String> copy) {
                mdc.putAll(copy);
            }
        });

        ResponseWrapperFacade respw = outcome.getReply();
        String replyTo = respw.headers().getReplyTo();
        String typeUrl = respw.body().getTypeUrl();
        long now = System.currentTimeMillis();
        String messageId = respw.headers().getMessageId();
        String traceId = respw.headers().getTraceId();
        String status = StringUtils.lowerCase(respw.status().errorCode().toString()).intern();

        try {
            UriInfo uri = uriFactory.createUri(replyTo);
            if (StringUtils.isEmpty(uri.getScheme())) {
                return delegate.publishReply(outcome);
            }
            Preconditions.checkArgument(SCHEMAS.contains(uri.getScheme()));
        } catch (Exception err) {
            return delegate.publishReply(outcome);
        }

        mdc.putIfAbsent(MdcTags.MDC_MESSAGE_ID, messageId);
        mdc.putIfAbsent(MdcTags.MDC_ERROR_CODE, status);
        mdc.putIfAbsent(MdcTags.MDC_REPLY_TO, replyTo);
        if (StringUtils.isNotEmpty(traceId)) {
            mdc.putIfAbsent(MdcTags.MDC_TRACE_ID, traceId);
        }

        log.atLevel(props.isDevMode() ? Level.DEBUG : Level.TRACE).log("about to post {} to {} ...", typeUrl, replyTo);

        CloudEventWithRoutingKey payload = new CloudEventWithRoutingKey(now, outcome.getKey(), respw);

        try {
            CompletableFuture<Object> toReturn = new CompletableFuture<>();
            byte[] data = PlatformUtil.serialize(payload);

            Request.Builder reqb = new Request.Builder();
            reqb.url(replyTo);
            reqb.post(RequestBody.create(data, X_PROTOBUF));

            //
            // ~ we want to report to sentry accordingly and always for each and every error
            //
            var loggerCallback = new Consumer<Throwable>() {
                @Override
                public void accept(Throwable err) {
                    try {
                        //
                        // ~ might be wrapper by Execution Exception
                        //
                        Throwable cause = ExceptionUtils.getRootCause(err);
                        if (Objects.isNull(cause)) {
                            cause = err;
                        }

                        //
                        // ~ it might be so that we are facing temporal DNS resolution issue (restart deploy whatever)
                        //
                        if (cause instanceof UnknownHostException) {
                            log.warn(cause.getMessage(), cause);
                        } else if (cause instanceof IllegalArgumentException) {
                            log.warn(cause.getMessage(), cause);
                        } else {
                            var reporter = triggers.get(typeUrl, new Callable<>() {
                                @Override
                                public TriggerOnceAfterNOccurrencesRateLimiter call() throws Exception {
                                    return new TriggerOnceAfterNOccurrencesRateLimiter(typeUrl, 1, timer);
                                }
                            });

                            if (reporter.acquirePermission()) {
                                log.error(cause.getMessage(), cause);
                            }
                        }
                    } catch (ExecutionException ex) {
                        log.error(ex.getMessage(), ex.getCause());
                    }
                }
            };

            //
            // ~ delegate BiConsumer which will propagate error accordingly (and log)
            //
            var delegateCallback = new BiConsumer<Object, Throwable>() {
                @Override
                public void accept(Object result, Throwable err) {
                    if (Objects.nonNull(result)) {
                        toReturn.complete(result);
                    } else {
                        try {
                            loggerCallback.accept(err);
                        } finally {
                            toReturn.completeExceptionally(err);
                        }
                    }
                }
            };

            httpClient.newCall(reqb.build()).enqueue(new Callback() {
                @Override
                public void onResponse(Call call, Response response) {
                    MdcUtil.propagate(mdc);
                    try (ResponseBody body = response.body()) {
                        if (response.isSuccessful()) {
                            log.debug("OUT ::: ({}:{}:s-{}) has been accepted by http (topic: {}, took: {}, size: {})",
                                    typeUrl,
                                    messageId,
                                    status,
                                    replyTo,
                                    System.currentTimeMillis() - now,
                                    FileUtils.byteCountToDisplaySize(data.length));
                            toReturn.complete(response);
                        } else {
                            try {
                                var err = new IOException(
                                        "failed to complete reply: %s, messageId: %s, code: %s".formatted(typeUrl, messageId, response.code()));
                                loggerCallback.accept(err);
                            } finally {
                                delegate.publishReply(outcome).whenComplete(delegateCallback);
                            }
                        }
                    } finally {
                        MDC.clear();
                    }
                }
                @Override
                public void onFailure(Call call, IOException err) {
                    MdcUtil.propagate(mdc);

                    log.warn(err.getMessage(), err);

                    try {
                        delegate.publishReply(outcome).whenComplete(delegateCallback);
                    } finally {
                        MDC.clear();
                    }
                }
            });
            return toReturn;
        } catch (IOException err) {
            log.error(err.getMessage(), err);
            return CompletableFuture.failedStage(err);
        }
    }
}
