package com.turbospaces.rpc;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Suppliers;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.MDCToHeadersContexPropagator;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;

import api.v1.ApiFactory;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import io.cloudevents.core.builder.CloudEventBuilder;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

@Slf4j
public final class MockQueuePostTemplate implements QueuePostTemplate<CloudEvent> {
    private final Supplier<Duration> defaultTimeout = Suppliers.ofInstance(Duration.ofSeconds(30));

    private final ApplicationProperties props;
    private final ApiFactory apiFactory;

    public final Sinks.Many<CloudEvent> requests = Sinks.many().replay().latest();
    public final Sinks.Many<CloudEvent> replies = Sinks.many().replay().latest();
    public final Sinks.Many<CloudEvent> events = Sinks.many().replay().latest();
    public final Sinks.Many<CloudEvent> notifies = Sinks.many().replay().latest();

    public MockQueuePostTemplate(ApplicationProperties props, ApiFactory apiFactory) {
        this.props = Objects.requireNonNull(props);
        this.apiFactory = Objects.requireNonNull(apiFactory);
    }
    @Override
    public ApiFactory apiFactory() {
        return apiFactory;
    }
    @Override
    public WrappedQueuePost sendReq(RequestQueuePostSpec spec) {
        var reqw = apiFactory.requestMapper().pack(spec, new MDCToHeadersContexPropagator(), defaultTimeout.get());
        var emitResult = requests.tryEmitNext(reqw);
        if (emitResult.isSuccess()) {
            return new DefaultWrappedQueuePost(SettableFuture.create(), reqw);
        }

        return new DefaultWrappedQueuePost(Futures.immediateFailedFuture(new IllegalStateException("unable to emit event: " + emitResult.name())), reqw);
    }
    @Override
    public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
        ResponseWrapperFacade respw = outcome.getReply();
        if (Objects.nonNull(respw)) {
            String replyTo = respw.headers().getReplyTo();
            if (StringUtils.isNotEmpty(replyTo)) {
                var emitResult = replies.tryEmitNext(respw);
                if (emitResult.isSuccess()) {
                    return CompletableFuture.completedFuture(respw);
                }

                return CompletableFuture.failedFuture(new IllegalStateException("unable to emit event: " + emitResult.name()));
            }
        }
        return CompletableFuture.completedStage(new Object());
    }
    @Override
    public CompletionStage<CloudEvent> sendNotify(NotifyQueuePostSpec spec) {
        var now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var typeUrl = spec.body().getTypeUrl();

        var emitResult = notifies.tryEmitNext(spec);
        if (emitResult.isSuccess()) {
            log.debug("OUT ::: ({}) has been accepted (topic={},took={})",
                    typeUrl,
                    qualifier,
                    System.currentTimeMillis() - now);
            return CompletableFuture.completedFuture(spec);
        }

        return CompletableFuture.failedFuture(new IllegalStateException("unable to emit event: " + emitResult.name()));
    }
    @Override
    public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
        var notifications = outcome.getNotifications();
        var list = new CompletableFuture<?>[notifications.size()];

        for (int i = 0; i < notifications.size(); i++) {
            var notify = notifications.get(i);
            var typeUrl = notify.body().getTypeUrl();
            var now = System.currentTimeMillis();
            var emitResult = notifies.tryEmitNext(notify);
            if (emitResult.isSuccess()) {
                log.debug("OUT ::: ({}) has been accepted (topic={},took={})",
                        typeUrl,
                        topic.name().toString(),
                        System.currentTimeMillis() - now);
                list[i] = CompletableFuture.completedFuture(notify);
            } else {
                list[i] = CompletableFuture.failedFuture(new IllegalStateException("unable to emit event: " + emitResult.name()));
            }
        }

        return CompletableFuture.allOf(list);
    }
    @Override
    public CompletionStage<CloudEvent> sendEvent(EventQueuePostSpec spec) {
        var any = spec.pack(); // ~ not wrapping to request (Any)
        var now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var typeUrl = any.getTypeUrl();
        var eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
        var event = eventTemplate.newBuilder()
                .withId(PlatformUtil.randomUUID().toString())
                .withData(new CloudEventData() {
                    @Override
                    public byte[] toBytes() {
                        return any.toByteArray();
                    }
                })
                .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                .withType(typeUrl).build();

        var emitResult = events.tryEmitNext(event);
        if (emitResult.isSuccess()) {
            log.debug("OUT ::: ({}) has been accepted (topic={},took={})",
                    typeUrl,
                    qualifier,
                    System.currentTimeMillis() - now);
            return CompletableFuture.completedFuture(event);
        }

        return CompletableFuture.failedFuture(new IllegalStateException("unable to emit event: " + emitResult.name()));
    }
    @Override
    public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
        var eventStream = outcome.getEventStream();
        var list = new CompletableFuture<?>[eventStream.size()];

        for (int i = 0; i < eventStream.size(); i++) {
            var wrapper = eventStream.get(i);
            var typeUrl = wrapper.getTypeUrl();
            var now = System.currentTimeMillis();
            var eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
            var event = eventTemplate.newBuilder()
                    .withId(PlatformUtil.randomUUID().toString())
                    .withType(typeUrl)
                    .withData(new CloudEventData() {
                        @Override
                        public byte[] toBytes() {
                            return wrapper.toByteArray();
                        }
                    }).build();

            var emitResult = events.tryEmitNext(event);
            if (emitResult.isSuccess()) {
                log.debug("OUT ::: ({}) has been accepted (topic={},took={})",
                        wrapper.getTypeUrl(),
                        topic.name().toString(),
                        System.currentTimeMillis() - now);
                list[i] = CompletableFuture.completedFuture(event);
            } else {
                list[i] = CompletableFuture.failedFuture(new IllegalStateException("unable to emit event: " + emitResult.name()));
            }
        }

        return CompletableFuture.allOf(list);
    }
}
