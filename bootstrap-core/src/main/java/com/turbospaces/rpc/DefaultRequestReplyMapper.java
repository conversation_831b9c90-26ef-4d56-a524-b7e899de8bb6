package com.turbospaces.rpc;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;

import com.google.common.collect.ImmutableList;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcTags;

import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class DefaultRequestReplyMapper extends CompletableRequestReplyMapper<UUID, ResponseWrapperFacade> {
    public DefaultRequestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
        super(props, meterRegistry);
        this.reportToSentryOnTimeout = props.SENTRY_REQUEST_REPLY_TIMEOUT_REPORT;
    }
    @Override
    protected void setValue(SettableFuture<ResponseWrapperFacade> subj, ResponseWrapperFacade value) {
        ImmutableList.Builder<Tag> tags = ImmutableList.builder();
        addMetricFromMdcIfPresent(MdcTags.MDC_OPERATION, tags);
        addMetricFromMdcIfPresent(MdcTags.MDC_TOPIC, tags);
        tags.add(new ImmutableTag(MdcTags.MDC_ERROR_CLASS, "none"));

        StopWatch stopWatch = StopWatch.createStarted();
        subj.set(value);
        stopWatch.stop();

        report(stopWatch, tags);
    }
    @Override
    protected void setException(SettableFuture<ResponseWrapperFacade> subj, Throwable reason) {
        ImmutableList.Builder<Tag> tags = ImmutableList.builder();
        addMetricFromMdcIfPresent(MdcTags.MDC_OPERATION, tags);
        addMetricFromMdcIfPresent(MdcTags.MDC_TOPIC, tags);
        tags.add(new ImmutableTag(MdcTags.MDC_ERROR_CLASS, reason.getClass().getSimpleName()));

        StopWatch stopWatch = StopWatch.createStarted();
        subj.setException(reason);
        stopWatch.stop();

        report(stopWatch, tags);
    }
    private void report(StopWatch stopWatch, ImmutableList.Builder<Tag> tags) {
        //
        // ~ blocking call under the hood (temporary allow it)
        //
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                var meter = meterRegistry.timer("reply.complete.latency", tags.build());
                TimeUnit timeUnit = TimeUnit.NANOSECONDS;
                long nano = stopWatch.getTime(timeUnit);
                meter.record(nano, timeUnit);
            }
        });
    }
    private static void addMetricFromMdcIfPresent(String key, ImmutableList.Builder<Tag> tags) {
        String mdc = MDC.get(key);
        if (StringUtils.isNotEmpty(mdc)) {
            tags.add(new ImmutableTag(key, mdc));
        }
    }
}
