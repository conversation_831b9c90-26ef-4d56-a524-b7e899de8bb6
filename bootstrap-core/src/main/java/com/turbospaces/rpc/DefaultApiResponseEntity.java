package com.turbospaces.rpc;

import java.io.IOException;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import api.v1.CacheControl;
import api.v1.Headers;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultApiResponseEntity<T extends Message> implements ApiResponseEntity<T> {
    private final ResponseWrapperFacade wrapper;
    private final Class<T> type;

    @Override
    public Headers headers() {
        return wrapper.headers();
    }
    @Override
    public ResponseStatusFacade status() {
        return wrapper.status();
    }
    @Override
    public Any any() {
        return wrapper.body();
    }
    @Override
    public CacheControl cacheControl() {
        return wrapper.cacheControl();
    }
    @Override
    public T unpack() throws IOException {
        return any().unpack(type);
    }
}
