package com.turbospaces.rpc;

import java.time.Duration;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;

import com.google.protobuf.Message;
import com.turbospaces.api.MutableReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.dispatch.RequestQueuePostSpec;

import io.netty.util.AsciiString;

public interface RequestQueuePostTemplate {
    WrappedQueuePost sendReq(RequestQueuePostSpec spec);
    default WrappedQueuePost sendReq(Topic destination, MutableReplyTopic replyTo, Message payload) {
        RequestQueuePostSpec.RequestBuilder spec = RequestQueuePostSpec.newBuilder(payload);
        spec.setTopic(destination);
        spec.setReplyTo(replyTo);
        return sendReq(spec.build());
    }
    default WrappedQueuePost sendReq(Topic destination, Topic replyTo, Message payload, AsciiString routingKey) {
        return sendReq(destination, MutableReplyTopic.class.cast(replyTo), payload, routingKey);
    }
    default WrappedQueuePost sendReq(Topic destination, MutableReplyTopic replyTo, Message payload, AsciiString routingKey) {
        RequestQueuePostSpec.RequestBuilder spec = RequestQueuePostSpec.newBuilder(payload);
        spec.setTopic(destination);
        spec.setReplyTo(replyTo);
        if (StringUtils.isNotEmpty(routingKey)) {
            spec.setRoutingKey(routingKey);
        }
        return sendReq(spec.build());
    }
    default WrappedQueuePost sendReq(Topic destination, Topic replyTo, Message payload, AsciiString routingKey, Supplier<Duration> timeout) {
        return sendReq(destination, MutableReplyTopic.class.cast(replyTo), payload, routingKey, timeout);
    }
    default WrappedQueuePost sendReq(Topic destination, MutableReplyTopic replyTo, Message payload, AsciiString routingKey, Supplier<Duration> timeout) {
        RequestQueuePostSpec.RequestBuilder spec = RequestQueuePostSpec.newBuilder(payload, timeout.get());
        spec.setTopic(destination);
        spec.setReplyTo(replyTo);
        if (StringUtils.isNotEmpty(routingKey)) {
            spec.setRoutingKey(routingKey);
        }
        return sendReq(spec.build());
    }
}
