package com.turbospaces.rpc;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeoutException;

import lombok.Getter;

@Getter
public class RequestReplyTimeout extends TimeoutException {
    private final Object key;
    private final Duration duration;
    private final boolean reportToSentryOnTimeout;

    public RequestReplyTimeout(Duration duration, Object key, boolean reportToSentryOnTimeout) {
        super(String.format("no response in %d sec(s) for %s", duration.toSeconds(), key));
        this.duration = Objects.requireNonNull(duration);
        this.key = Objects.requireNonNull(key);
        this.reportToSentryOnTimeout = reportToSentryOnTimeout;
    }
}
