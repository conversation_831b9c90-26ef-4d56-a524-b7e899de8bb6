package com.turbospaces.rpc;

import java.util.Arrays;
import java.util.Objects;

import com.google.common.util.concurrent.FutureCallback;
import com.turbospaces.cfg.ApplicationProperties;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FutureCallbackWrapper<T> implements FutureCallback<T> {
    private final FutureCallback<T> callback;
    private final ApplicationProperties props;
    private final Thread origin;
    private final StackTraceElement[] traces;

    public FutureCallbackWrapper(ApplicationProperties props, FutureCallback<T> callback, Thread origin) {
        this.props = props;
        this.callback = Objects.requireNonNull(callback);
        this.origin = Objects.requireNonNull(origin);
        this.traces = Thread.currentThread().getStackTrace();
    }
    @Override
    public void onSuccess(T result) {
        runGuard();
        callback.onSuccess(result);
    }
    @Override
    public void onFailure(Throwable t) {
        runGuard();
        callback.onFailure(t);
    }
    private void runGuard() {
        Thread currentThread = Thread.currentThread();

        //
        // ~ we should not execute callback in same thread as it was submitted
        //
        if (origin.equals(currentThread)) {
            if (props.isDevMode()) {
                String reason = "detected callback execution abuse in: " + callback.toString();
                IllegalStateException err = new IllegalStateException(reason);

                throw err;
            }

            log.error("detected callback execution of {} in origin: {} \n {}", callback, origin, Arrays.toString(traces));
        }
    }
}
