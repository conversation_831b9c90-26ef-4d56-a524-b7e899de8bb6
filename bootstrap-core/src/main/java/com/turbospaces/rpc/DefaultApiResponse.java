package com.turbospaces.rpc;

import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import api.v1.ReplyUtil;
import io.vavr.CheckedConsumer;

public class DefaultApiResponse<RESP extends Message> extends AbstractApiResponse<RESP> {
    private final Thread origin;
    private final ApplicationProperties props;
    private final FluentFuture<ResponseWrapperFacade> subject;
    private final Class<RESP> type;

    public DefaultApiResponse(ApplicationProperties props, WrappedQueuePost post, ApiFactory apiFactory, Class<RESP> type) {
        super(apiFactory, post.requestWrapper());
        this.props = Objects.requireNonNull(props);
        this.subject = FluentFuture.from(post);
        this.type = Objects.requireNonNull(type);
        this.origin = Thread.currentThread();
    }
    public DefaultApiResponse(
            ApplicationProperties props,
            ListenableFuture<ResponseWrapperFacade> future,
            ApiFactory apiFactory,
            Class<RESP> type,
            RequestWrapperFacade sreqw) {
        this(props, future, apiFactory, type, sreqw, Thread.currentThread());
    }
    private DefaultApiResponse(
            ApplicationProperties props,
            ListenableFuture<ResponseWrapperFacade> future,
            ApiFactory apiFactory,
            Class<RESP> type,
            RequestWrapperFacade sreqw,
            Thread origin) {
        super(apiFactory, sreqw);
        this.props = Objects.requireNonNull(props);
        this.subject = FluentFuture.from(future);
        this.type = Objects.requireNonNull(type);
        this.origin = Objects.requireNonNull(origin);
    }
    @Override
    public ApiResponseEntity<RESP> get() throws InterruptedException, ExecutionException {
        ResponseWrapperFacade respw = subject.get();
        return new DefaultApiResponseEntity<>(respw, type);
    }
    @Override
    public ApiResponseEntity<RESP> get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        ResponseWrapperFacade respw = subject.get(timeout, unit);
        return new DefaultApiResponseEntity<>(respw, type);
    }
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return subject.cancel(mayInterruptIfRunning);
    }
    @Override
    public boolean isCancelled() {
        return subject.isCancelled();
    }
    @Override
    public boolean isDone() {
        return subject.isDone();
    }
    @Override
    public void addListener(Runnable listener, Executor executor) {
        subject.addListener(listener, executor);
    }
    @Override
    public void addCallback(FutureCallback<ApiResponseEntity<RESP>> callback, Executor executor) {
        var target = new FutureCallback<ResponseWrapperFacade>() {
            @Override
            public void onSuccess(ResponseWrapperFacade result) {
                callback.onSuccess(new DefaultApiResponseEntity<>(result, type));
            }
            @Override
            public void onFailure(Throwable t) {
                callback.onFailure(t);
            }
        };
        var wrapper = new FutureCallbackWrapper<>(props, target, origin);
        subject.addCallback(isDone() ? target : wrapper, executor);
    }
    @Override
    public ApiResponse<RESP> thenVerifyOkAndAccept(CheckedConsumer<RESP> callback, Executor executor) {
        SettableFuture<ResponseWrapperFacade> toReturn = SettableFuture.create();
        subject.addCallback(new FutureCallback<ResponseWrapperFacade>() {
            @Override
            public void onSuccess(ResponseWrapperFacade result) {
                boolean isOK = true;

                try {
                    ReplyUtil.verifyOk(result);
                } catch (Exception err) {
                    toReturn.setException(err);
                    isOK = false;
                }

                if (isOK) {
                    try {
                        RESP body = result.unpack(type);
                        callback.accept(body);
                        toReturn.set(result);
                    } catch (Throwable err) {
                        toReturn.setException(err);
                    }
                }
            }
            @Override
            public void onFailure(Throwable t) {
                toReturn.setException(t);
            }
        }, executor);

        //
        // ~ new API response by contract
        //
        return new DefaultApiResponse<>(props, toReturn, apiFactory, type, requestWrapper, origin);
    }
}
