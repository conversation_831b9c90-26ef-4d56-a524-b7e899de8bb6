package com.turbospaces.rpc;

import java.util.concurrent.CompletionStage;

import org.apache.commons.lang3.StringUtils;

import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.dispatch.NotifyQueuePostSpec;

import io.netty.util.AsciiString;

public interface NotifyQueuePostTemplate<C> extends HasApiFactory {
    CompletionStage<C> sendNotify(NotifyQueuePostSpec spec);
    default CompletionStage<C> sendNotify(NotificationWrapperFacade payload) {
        return sendNotify(apiFactory().notifyTopic(), payload);
    }
    default CompletionStage<C> sendNotify(Topic destination, NotificationWrapperFacade payload) {
        NotifyQueuePostSpec.NotificationBuilder spec = NotifyQueuePostSpec.newBuilder(payload);
        spec.setTopic(destination);
        return sendNotify(spec.build());
    }
    default CompletionStage<C> sendNotify(NotificationWrapperFacade payload, AsciiString routingKey) {
        return sendNotify(apiFactory().notifyTopic(), payload, routingKey);
    }
    default CompletionStage<C> sendNotify(Topic destination, NotificationWrapperFacade payload, AsciiString routingKey) {
        NotifyQueuePostSpec.NotificationBuilder spec = NotifyQueuePostSpec.newBuilder(payload);
        spec.setTopic(destination);
        if (StringUtils.isNotEmpty(routingKey)) {
            spec.setRoutingKey(routingKey);
        }
        return sendNotify(spec.build());
    }
}
