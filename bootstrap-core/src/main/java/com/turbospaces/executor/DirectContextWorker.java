package com.turbospaces.executor;

import java.util.concurrent.Executor;

import com.google.common.util.concurrent.MoreExecutors;

public class DirectContextWorker<T extends WorkUnit> extends AbstractContextWorker implements ContextWorkerFactory<T> {
    private final Executor directExecutor;

    public DirectContextWorker() {
        directExecutor = MoreExecutors.directExecutor();
    }
    @Override
    public void execute(Runnable command) {
        directExecutor.execute(command);
    }
    @Override
    public ContextWorker forKey(WorkUnit unit) {
        return this;
    }
    @Override
    public ContextWorker worker(T partition) {
        return this;
    }
}
