package com.turbospaces.executor;

import java.lang.Thread.Builder.OfVirtual;
import java.util.concurrent.Executors;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Supplier;

import com.google.common.annotations.Beta;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Beta
@Slf4j
public class DefaultVirtualPlatformExecutorService extends AbstractPlatformExecutorService {
    private final OfVirtual ofVirtual = Thread.ofVirtual().inheritInheritableThreadLocals(true);
    private final AtomicLong successCount = new AtomicLong();
    private final AtomicLong failureCount = new AtomicLong();
    private final AtomicReference<Semaphore> holder = new AtomicReference<>();

    public DefaultVirtualPlatformExecutorService(ApplicationProperties props, MeterRegistry meterRegistry) {
        this(props, meterRegistry, props.APP_PLATFORM_MAX_SIZE.get() * Runtime.getRuntime().availableProcessors());
    }
    public DefaultVirtualPlatformExecutorService(ApplicationProperties props, MeterRegistry meterRegistry, int permits) {
        super(props, meterRegistry);
        holder.set(new Semaphore(permits));

        executor = Executors.newThreadPerTaskExecutor(ofVirtual.factory());
    }
    @Override
    public void setBeanName(String name) {
        super.setBeanName(name);

        executor = Executors.newThreadPerTaskExecutor(ofVirtual.name(beanName + "-", 0).factory());

        setMaxPoolSize(props.cfg().factory().get(beanName + "." + "platform.max-size", int.class).orElse(holder.get().availablePermits()));
    }
    public void setMaxPoolSize(Supplier<Integer> maxPoolSize) {
        holder.set(new Semaphore(maxPoolSize.get()));
        if (maxPoolSize instanceof Property<Integer> cast) {
            cast.subscribe(new Consumer<Integer>() {
                @Override
                public void accept(Integer newValue) {
                    log.info("accepting new maxPool value: {}, executor: {}", newValue, beanName);
                    holder.set(new Semaphore(newValue));
                }
            });
        }
    }
    @Override
    public void afterPropertiesSet() {
        log.info("{} has been created max: {}", beanName, holder.get().availablePermits());
    }
    @Override
    public void execute(Runnable command) {
        Semaphore semaphore = holder.get();
        boolean tryAcquire = semaphore.tryAcquire();
        if (tryAcquire) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    String toRevert = Thread.currentThread().getName();
                    try {
                        if (props.APP_BLOCKHOUND_ENABLED.get()) {
                            Thread.currentThread().setName(DefaultBlockHoundIntegration.PREFIX + toRevert);
                        }
                        command.run();
                        successCount.incrementAndGet();
                    } catch (Throwable err) {
                        failureCount.incrementAndGet();
                        throw err;
                    } finally {
                        Thread.currentThread().setName(toRevert);
                        semaphore.release();
                    }
                }
            });
        } else {
            var msg = "task rejected from virtual pool: %s, available permits: %d, success: %s, failures: %d";
            throw new RejectedExecutionException(msg.formatted(beanName, semaphore.availablePermits(), successCount, failureCount));
        }
    }
    public static final DefaultVirtualPlatformExecutorService unbounded(ApplicationProperties props, MeterRegistry meterRegistry) {
        return new DefaultVirtualPlatformExecutorService(props, meterRegistry, Integer.MAX_VALUE);
    }
}
