package com.turbospaces.executor;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;

import org.slf4j.event.Level;

import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;
import reactor.core.Disposable;

@Slf4j
@RequiredArgsConstructor
public class LogQueueFullCallerRunsPolicy extends CallerRunsPolicy implements Disposable {
    private final ThreadLocal<Boolean> flag = DefaultBlockHoundIntegration.FLAG;
    private final TriggerOnceAfterNOccurrencesRateLimiter limiter;

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        log.atLevel(limiter.acquirePermission() ? Level.ERROR : Level.WARN).log("task: {} will be executed in current thread because {} has no resources to run it", r, e);

        //
        // ~ if should not happen normally, but if this occurs we don't want to bombard sentry 2 times
        //
        boolean toReset = flag.get();
        try {
            flag.set(false);
            super.rejectedExecution(r, e);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public void dispose() {
        limiter.dispose();
    }
}
