package com.turbospaces.executor;

import java.time.Duration;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalCause;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.turbospaces.cache.BlockhoundLoadingCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ThreadPoolContextWorker extends AbstractContextWorker {
    private static EnumSet<RemovalCause> REASONS = EnumSet.of(RemovalCause.EXPIRED, RemovalCause.SIZE);

    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final Executor executor;
    protected final ScheduledExecutorService timer;
    protected final boolean autoClose;
    protected LoadingCache<AsciiString, SerialContextWorker> executors;

    public ThreadPoolContextWorker(ApplicationProperties props, MeterRegistry meterRegistry, PlatformExecutorService executor) {
        this(props, meterRegistry, executor, false);
    }
    public ThreadPoolContextWorker(ApplicationProperties props, MeterRegistry meterRegistry, Executor executor, boolean autoClose) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.executor = Objects.requireNonNull(executor);
        this.autoClose = autoClose;

        //
        // ~ just one thread
        //
        this.timer = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                return t;
            }
        });
    }
    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();

        Duration timeout = props.BATCH_COMPLETION_TIMEOUT.get().plusMinutes(1);
        Duration cleanupInterval = props.APP_TIMER_INTERVAL.get();

        //
        // ~ we should try to free occupied memory but keep for reasonable time
        //
        executors = new BlockhoundLoadingCacheWrapper<>(CacheBuilder.newBuilder()
                .expireAfterAccess(timeout)
                .removalListener(new RemovalListener<>() {
                    @Override
                    public void onRemoval(RemovalNotification<Object, Object> notification) {
                        if (Objects.nonNull(notification.getCause())) {
                            if (REASONS.contains(notification.getCause())) {
                                String type = notification.getCause().name().toLowerCase().intern();
                                log.trace("onRemoval({}): {}", type, notification.getKey());
                            }
                        }
                    }
                })
                .build(new CacheLoader<>() {
                    @Override
                    public SerialContextWorker load(AsciiString key) throws Exception {
                        return new SerialContextWorker(key, executor, meterRegistry);
                    }
                }));

        //
        // ~ perform corresponding maintenance (crucial for memory cleanUp)
        //
        timer.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                long size = executors.size();
                if (size > 0) {
                    log.debug("about to cleanUp executors cache of {} items ...", size);
                }
                executors.cleanUp();
            }
        }, cleanupInterval.toSeconds(), cleanupInterval.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public ContextWorker forKey(WorkUnit unit) {
        return executors.getUnchecked(new AsciiString(unit.key()));
    }
    @Override
    public void execute(Runnable command) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        executor.execute(wrapRunnable(command, mdc));
    }
    @Override
    public void destroy() {
        super.destroy();

        PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());

        //
        // ~ try to shutdown rejection handler if possible
        //
        if (autoClose) {
            if (executor instanceof ExecutorService service) {
                StopWatch stopWatch = StopWatch.createStarted();
                Duration timeout = props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get();
                List<Runnable> never = PlatformUtil.shutdownExecutor(service, timeout);
                stopWatch.stop();
                log.debug("stopped worker executor in {}. {} task(s) were never executed ...", stopWatch, never.size());

                if (service instanceof ThreadPoolExecutor tpe) {
                    var rejection = tpe.getRejectedExecutionHandler();
                    if (rejection instanceof LogQueueFullCallerRunsPolicy lqf) {
                        lqf.dispose();
                    }
                }
            }
        }
    }
}
