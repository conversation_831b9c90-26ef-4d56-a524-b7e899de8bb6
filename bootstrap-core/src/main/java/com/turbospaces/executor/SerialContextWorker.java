package com.turbospaces.executor;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import io.vavr.CheckedRunnable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class SerialContextWorker extends AbstractContextWorker {
    private final AtomicInteger counter = new AtomicInteger();
    private final AsciiString key;
    private final Executor executor;
    private final MeterRegistry meterRegistry;

    public SerialContextWorker(AsciiString key, Executor executor, MeterRegistry meterRegistry) {
        this.key = Objects.requireNonNull(key);
        this.executor = MoreExecutors.newSequentialExecutor(executor);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
    }
    @Override
    public ContextWorker forKey(WorkUnit unit) {
        throw new UnsupportedOperationException();
    }
    @Override
    public void execute(Runnable command) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        executor.execute(wrapRunnable(command, mdc));
    }
    @Override
    public ListenableFuture<?> submit(CheckedRunnable command) {
        long now = System.currentTimeMillis();
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC
        SettableFuture<Object> settable = SettableFuture.create();

        try {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    Thread currentThread = Thread.currentThread();
                    String oldName = currentThread.getName();
                    String newName = oldName + "|" + key;
                    currentThread.setName(newName);

                    MdcUtil.propagate(mdc);

                    try {
                        long delta = System.currentTimeMillis() - now;
                        if (delta > 0) {
                            reportMetrics(delta);
                            log.debug("task({}) was queued for {} ms before exec for key: {}", counter.incrementAndGet(), delta, key);
                        }

                        command.run();
                        settable.set(new Object());
                    } catch (Throwable err) {
                        settable.setException(err);
                    } finally {
                        currentThread.setName(oldName);
                        MDC.clear();
                    }
                }
            });
        } catch (RejectedExecutionException err) {
            settable.setException(err);
        }

        return settable;
    }
    @Override
    public <T> ListenableFuture<T> submit(CheckedFunction0<T> command) {
        long now = System.currentTimeMillis();
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC
        SettableFuture<T> settable = SettableFuture.create();

        try {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    Thread currentThread = Thread.currentThread();
                    String oldName = currentThread.getName();
                    String newName = oldName + "|" + key;
                    currentThread.setName(newName);

                    MdcUtil.propagate(mdc);

                    try {
                        long delta = System.currentTimeMillis() - now;
                        if (delta > 0) {
                            log.debug("task({}) was queued for {} ms before exec for key: {}", counter.incrementAndGet(), delta, key);
                        }

                        settable.set(command.apply());
                    } catch (Throwable err) {
                        settable.setException(err);
                    } finally {
                        currentThread.setName(oldName);

                        MDC.clear();
                    }
                }
            });
        } catch (RejectedExecutionException err) {
            settable.setException(err);
        }

        return settable;
    }
    private void reportMetrics(long delta) {
        var tags = Lists.<Tag> newArrayList();

        var topic = MDC.get(MdcTags.MDC_TOPIC);
        var partition = MDC.get(MdcTags.MDC_PARITION);
        var operation = MDC.get(MdcTags.MDC_OPERATION);

        if (StringUtils.isNotEmpty(topic)) {
            tags.add(Tag.of(MdcTags.MDC_TOPIC, topic));
        }
        if (StringUtils.isNotEmpty(partition)) {
            tags.add(Tag.of(MdcTags.MDC_PARITION, partition));
        }
        if (StringUtils.isNotEmpty(operation)) {
            tags.add(Tag.of(MdcTags.MDC_OPERATION, operation));
        }

        meterRegistry.timer("worker.queue.delay", tags).record(delta, TimeUnit.MILLISECONDS);
    }
}
