package com.turbospaces.executor;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.BiConsumer;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;

import io.github.resilience4j.retry.Retry;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class AsyncSerialContextWorker<T> implements AsyncContextWorker<T> {
    private final Object mutex = new Object();
    private final SerialContextWorker delegate;
    private volatile ListenableFuture<T> lastTask = Futures.immediateFuture(null);

    public AsyncSerialContextWorker(SerialContextWorker delegate) {
        this.delegate = Objects.requireNonNull(delegate);
    }
    public AsyncSerialContextWorker(AsciiString key, PlatformExecutorService executor, MeterRegistry meterRegistry) {
        this.delegate = new SerialContextWorker(key, executor, meterRegistry);
    }

    @Override
    public ListenableFuture<T> submit(
            Retry retry,
            ScheduledExecutorService scheduler,
            Supplier<ListenableFuture<T>> taskSupplier) {
        return submit(retry, scheduler, Futures.immediateFuture(null), taskSupplier);
    }
    @Override
    public ListenableFuture<List<T>> submitAll(
            Retry retry,
            ScheduledExecutorService scheduler,
            List<Supplier<ListenableFuture<T>>> taskSuppliers) {
        SettableFuture<List<T>> toReturn = SettableFuture.create();

        if (CollectionUtils.isEmpty(taskSuppliers)) {
            return Futures.immediateFuture(List.of());
        }

        synchronized (mutex) {
            List<T> results = Lists.newArrayListWithCapacity(taskSuppliers.size());
            ListenableFuture<T> current = lastTask;

            for (Supplier<ListenableFuture<T>> taskSupplier : taskSuppliers) {
                current = submit(retry, scheduler, current, taskSupplier);
                Futures.addCallback(current, new FutureCallback<T>() {
                    @Override
                    public void onSuccess(T result) {
                        synchronized (results) {
                            results.add(result);
                            if (results.size() == taskSuppliers.size()) {
                                toReturn.set(List.copyOf(results));
                            }
                        }
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        log.warn(t.getMessage(), t);
                        if (!toReturn.isDone()) {
                            toReturn.setException(t);
                        }
                    }
                }, MoreExecutors.directExecutor());
            }

            lastTask = current;
        }

        return toReturn;
    }

    @Override
    public CompletionStage<T> submitStage(Retry retry, ScheduledExecutorService scheduler, Supplier<CompletionStage<T>> taskSupplier) {
        ListenableFuture<T> future = submit(retry, scheduler, toListenableFutureSupplier(taskSupplier));
        return toCompletionStage(future);
    }

    @Override
    public CompletionStage<List<T>> submitStageAll(Retry retry, ScheduledExecutorService scheduler, List<Supplier<CompletionStage<T>>> taskSuppliers) {
        if (CollectionUtils.isEmpty(taskSuppliers)) {
            return CompletableFuture.completedFuture(List.of());
        }
        List<Supplier<ListenableFuture<T>>> listenableSuppliers = taskSuppliers.stream()
                .map(this::toListenableFutureSupplier)
                .toList();
        ListenableFuture<List<T>> future = submitAll(retry, scheduler, listenableSuppliers);
        return toCompletionStage(future);
    }

    private ListenableFuture<T> submit(
            Retry retry,
            ScheduledExecutorService scheduler,
            ListenableFuture<T> dependsOn,
            Supplier<ListenableFuture<T>> taskSupplier) {
        SettableFuture<T> toReturn = SettableFuture.create();

        synchronized (mutex) {
            var runnable = new Runnable() {
                @Override
                public void run() {
                    retry.executeCompletionStage(scheduler, () -> {
                        CompletableFuture<T> completable = new CompletableFuture<>();

                        Futures.addCallback(taskSupplier.get(), new FutureCallback<T>() {
                            @Override
                            public void onSuccess(T value) {
                                completable.complete(value);
                            }
                            @Override
                            public void onFailure(Throwable t) {
                                completable.completeExceptionally(t);
                            }
                        }, MoreExecutors.directExecutor());

                        return completable;
                    }).whenComplete(new BiConsumer<T, Throwable>() {
                        @Override
                        public void accept(T result, Throwable exception) {
                            if (exception != null) {
                                toReturn.setException(exception);
                            } else {
                                toReturn.set(result);
                            }
                        }
                    });
                }
            };

            Futures.addCallback(
                    Futures.successfulAsList(lastTask, dependsOn),
                    new FutureCallback<>() {
                        @Override
                        public void onSuccess(List<T> result) {
                            delegate.execute(runnable);
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            toReturn.setException(t);
                        }
                    }, MoreExecutors.directExecutor());

            lastTask = toReturn;
        }

        return toReturn;
    }

    private Supplier<ListenableFuture<T>> toListenableFutureSupplier(Supplier<CompletionStage<T>> supplier) {
        return () -> {
            SettableFuture<T> listenableFuture = SettableFuture.create();
            supplier.get().whenComplete((result, exception) -> {
                if (exception != null) {
                    listenableFuture.setException(exception);
                } else {
                    listenableFuture.set(result);
                }
            });
            return listenableFuture;
        };
    }

    private static <V> CompletionStage<V> toCompletionStage(ListenableFuture<V> listenableFuture) {
        CompletableFuture<V> completableFuture = new CompletableFuture<>();
        Futures.addCallback(listenableFuture, new FutureCallback<>() {
                    @Override
                    public void onSuccess(V result) {
                        completableFuture.complete(result);
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        completableFuture.completeExceptionally(t);
                    }
                },
                MoreExecutors.directExecutor()
        );
        return completableFuture;
    }

}
