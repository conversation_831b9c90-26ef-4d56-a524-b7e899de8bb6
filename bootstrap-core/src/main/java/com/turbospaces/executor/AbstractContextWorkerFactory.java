package com.turbospaces.executor;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import javax.annotation.OverridingMethodsMustInvokeSuper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.InitializingBean;

import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableScheduledFuture;
import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.micrometer.core.instrument.MeterRegistry;

public abstract class AbstractContextWorkerFactory<T, U extends WorkUnit> implements ContextWorkerFactory<U>, InitializingBean, BeanNameAware {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final ListeningScheduledExecutorService timer;
    protected LoadingCache<T, ContextWorker> workers;
    protected String beanName;

    private ListenableScheduledFuture<?> task;

    protected AbstractContextWorkerFactory(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);

        //
        // ~ just one thread
        //
        this.timer = MoreExecutors.listeningDecorator(Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        }));
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = Objects.requireNonNull(name);
    }
    @Override
    @OverridingMethodsMustInvokeSuper
    public void destroy() throws Exception {
        if (Objects.nonNull(task)) {
            task.cancel(true);
        }
        PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        if (Objects.nonNull(workers)) {
            Duration duration = props.APP_TIMER_INTERVAL.get();
            Integer maxSize = props.CACHE_DEFAULT_MAX_SIZE.get();
            task = timer.scheduleWithFixedDelay(new Runnable() {
                @Override
                public void run() {
                    workers.cleanUp();
                    //
                    // ~ we want to raise sentry alert pro-actively and not to wait for OOM
                    //
                    if (workers.size() > maxSize) {
                        logger.error("cache : ({}) exceeded reasonable size: {}, current: {}", beanName, maxSize, workers.size());
                    }
                }
            }, 0, duration.toSeconds(), TimeUnit.SECONDS);
        }
    }
}
