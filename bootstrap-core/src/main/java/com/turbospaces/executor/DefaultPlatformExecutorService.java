package com.turbospaces.executor;

import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.AbortPolicy;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import org.slf4j.MDC;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.AsFlux;
import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.Many;

@Slf4j
public class DefaultPlatformExecutorService extends AbstractPlatformExecutorService implements ExecutorService, AsFlux<ThreadPoolExecutor> {
    private final ThreadFactoryBuilder factory = new ThreadFactoryBuilder();
    private final Many<ThreadPoolExecutor> observer;

    public DefaultPlatformExecutorService(ApplicationProperties props, MeterRegistry meterRegistry) {
        this(props, meterRegistry, new Function<>() {
            @Override
            public ThreadPoolExecutor apply(ThreadFactoryBuilder factory) {
                return new ThreadPoolExecutor(
                        props.APP_PLATFORM_MIN_SIZE.get(),
                        props.APP_PLATFORM_MAX_SIZE.get(),
                        props.APP_PLATFORM_MAX_IDLE.get().toSeconds(),
                        TimeUnit.SECONDS,
                        new SynchronousQueue<>(),
                        factory.build(),
                        new AbortPolicy());
            }
        });
    }
    public DefaultPlatformExecutorService(ApplicationProperties props, MeterRegistry meterRegistry, Function<ThreadFactoryBuilder, ThreadPoolExecutor> mapper) {
        super(props, meterRegistry);

        //
        // ~ thread factory
        //
        factory.setDaemon(false);
        factory.setThreadFactory(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new PlatformThread(props, r);
            }
        });

        executor = mapper.apply(factory);
        observer = Sinks.many().replay().latestOrDefault((ThreadPoolExecutor) executor);
    }
    @Override
    public Flux<ThreadPoolExecutor> asFlux() {
        return observer.asFlux();
    }
    @Override
    public void setBeanName(String name) {
        super.setBeanName(name);

        factory.setNameFormat(beanName + "-%d");
        ((ThreadPoolExecutor) executor).setThreadFactory(factory.build());

        setMinPoolSize(props.cfg().factory().get(beanName + "." + "platform.min-size", int.class).orElse(props.APP_PLATFORM_MIN_SIZE.get()));
        setMaxPoolSize(props.cfg().factory().get(beanName + "." + "platform.max-size", int.class).orElse(props.APP_PLATFORM_MAX_SIZE.get()));
        setKeepAlive(props.cfg().factory().get(beanName + "." + "platform.max-idle", Duration.class).orElse(props.APP_PLATFORM_MAX_IDLE.get()));
    }
    public void setMinPoolSize(Supplier<Integer> minPoolSize) {
        ((ThreadPoolExecutor) executor).setCorePoolSize(minPoolSize.get());
        if (minPoolSize instanceof Property<Integer> cast) {
            cast.subscribe(new Consumer<Integer>() {
                @Override
                public void accept(Integer newValue) {
                    log.info("accepting new corePool value: {}, executor: {}", newValue, beanName);
                    ((ThreadPoolExecutor) executor).setCorePoolSize(newValue);
                    if (observer.tryEmitNext((ThreadPoolExecutor) executor).isSuccess()) {

                    }
                }
            });
        }
    }
    public void setMaxPoolSize(Supplier<Integer> maxPoolSize) {
        ((ThreadPoolExecutor) executor).setMaximumPoolSize(maxPoolSize.get());
        if (maxPoolSize instanceof Property<Integer> cast) {
            cast.subscribe(new Consumer<Integer>() {
                @Override
                public void accept(Integer newValue) {
                    log.info("accepting new maxPool value: {}, executor: {}", newValue, beanName);
                    ((ThreadPoolExecutor) executor).setMaximumPoolSize(newValue);
                    if (observer.tryEmitNext((ThreadPoolExecutor) executor).isSuccess()) {

                    }
                }
            });
        }
    }
    public void setKeepAlive(Supplier<Duration> keepAlive) {
        ((ThreadPoolExecutor) executor).setKeepAliveTime(keepAlive.get().toSeconds(), TimeUnit.SECONDS);
        if (keepAlive instanceof Property<Duration> cast) {
            cast.subscribe(new Consumer<Duration>() {
                @Override
                public void accept(Duration newValue) {
                    log.info("accepting new keepAlive value: {}, executor: {}", newValue, beanName);
                    ((ThreadPoolExecutor) executor).setKeepAliveTime(newValue.toSeconds(), TimeUnit.SECONDS);
                    if (observer.tryEmitNext((ThreadPoolExecutor) executor).isSuccess()) {

                    }
                }
            });
        }
    }
    @Override
    public void afterPropertiesSet() {
        int min = ((ThreadPoolExecutor) executor).getCorePoolSize();
        int max = ((ThreadPoolExecutor) executor).getMaximumPoolSize();
        long keepAlive = ((ThreadPoolExecutor) executor).getKeepAliveTime(TimeUnit.SECONDS);

        log.info("{} has been created min: {}, max: {}, keepAlive: {}", beanName, min, max, keepAlive);

        //
        // ~ bind metrics
        //
        ExecutorServiceMetrics metrics = new ExecutorServiceMetrics(executor, beanName, Collections.emptyList());
        metrics.bindTo(meterRegistry);
    }
    @Override
    public void execute(Runnable command) {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        executor.execute(wrapRunnable(command, mdcContextMap));
    }
    @Override
    public Future<?> submit(Runnable task) {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).submit(wrapRunnable(task, mdcContextMap));
    }
    @Override
    public <T> Future<T> submit(Callable<T> task) {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).submit(wrapCallable(task, mdcContextMap));
    }
    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).submit(wrapRunnable(task, mdcContextMap), result);
    }
    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return ((ThreadPoolExecutor) executor).awaitTermination(timeout, unit);
    }
    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).invokeAll(tasks.stream().map(c -> wrapCallable(c, mdcContextMap)).toList());
    }
    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).invokeAll(tasks.stream().map(c -> wrapCallable(c, mdcContextMap)).toList(), timeout, unit);
    }
    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).invokeAny(tasks.stream().map(c -> wrapCallable(c, mdcContextMap)).toList());
    }
    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
            throws InterruptedException, ExecutionException, TimeoutException {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();
        return ((ThreadPoolExecutor) executor).invokeAny(tasks.stream().map(c -> wrapCallable(c, mdcContextMap)).toList(), timeout, unit);
    }
    @Override
    public boolean isShutdown() {
        return ((ThreadPoolExecutor) executor).isShutdown();
    }
    @Override
    public void shutdown() {
        ((ThreadPoolExecutor) executor).shutdown();
    }
    @Override
    public List<Runnable> shutdownNow() {
        return ((ThreadPoolExecutor) executor).shutdownNow();
    }
    @Override
    public boolean isTerminated() {
        return ((ThreadPoolExecutor) executor).isTerminated();
    }
}
