package com.turbospaces.executor;

import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.Supplier;

import com.google.common.util.concurrent.ListenableFuture;

import io.github.resilience4j.retry.Retry;

public interface AsyncContextWorker<T> {
    ListenableFuture<T> submit(Retry retry, ScheduledExecutorService scheduler, Supplier<ListenableFuture<T>> task);
    ListenableFuture<List<T>> submitAll(Retry retry, ScheduledExecutorService scheduler, List<Supplier<ListenableFuture<T>>> tasks);
    CompletionStage<T> submitStage(Retry retry, ScheduledExecutorService scheduler, Supplier<CompletionStage<T>> task);
    CompletionStage<List<T>> submitStageAll(Retry retry, ScheduledExecutorService scheduler, List<Supplier<CompletionStage<T>>> taskSuppliers);
}
