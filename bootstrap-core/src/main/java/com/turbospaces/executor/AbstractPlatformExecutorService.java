package com.turbospaces.executor;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;

import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.util.concurrent.ListenableFuture;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.CheckedFunction0;
import io.vavr.CheckedFunction1;
import io.vavr.CheckedFunction2;
import io.vavr.CheckedRunnable;
import lombok.experimental.Delegate;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public abstract class AbstractPlatformExecutorService implements PlatformExecutorService {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ThreadLocal<Boolean> flag = DefaultBlockHoundIntegration.FLAG;
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected String beanName;
    @Delegate(types = { Executor.class })
    protected ExecutorService executor;

    protected AbstractPlatformExecutorService(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = Objects.requireNonNull(name);
    }
    @Override
    public void destroy() {
        if (Objects.nonNull(executor)) {
            Duration timeout = props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get();
            StopWatch stopWatch = StopWatch.createStarted();
            PlatformUtil.shutdownExecutor(executor, timeout);
            stopWatch.stop();
            log.debug("stopped executor-service: {} in {}", toString(), stopWatch);
        }
    }
    @Override
    public ListenableFuture<?> submit(CheckedRunnable action) {
        //
        // ~ we can afford it, we know that even if we reach max threads, any new task will be rejected
        // ~ so we can safely assume that this is actually not blocking
        //
        boolean toReset = flag.get();
        try {
            flag.set(false);
            return PlatformExecutorService.super.submit(action);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public <T> ListenableFuture<T> submit(CheckedFunction0<T> action) {
        //
        // ~ we can afford it, we know that even if we reach max threads, any new task will be rejected
        // ~ so we can safely assume that this is actually not blocking
        //
        boolean toReset = flag.get();
        try {
            flag.set(false);
            return PlatformExecutorService.super.submit(action);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public <T, R> ListenableFuture<R> submit(CheckedFunction1<T, R> action, T arg) {
        //
        // ~ we can afford it, we know that even if we reach max threads, any new task will be rejected
        // ~ so we can safely assume that this is actually not blocking
        //
        boolean toReset = flag.get();
        try {
            flag.set(false);
            return PlatformExecutorService.super.submit(action, arg);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public <T1, T2, R> ListenableFuture<R> submit(CheckedFunction2<T1, T2, R> action, T1 arg1, T2 arg2) {
        //
        // ~ we can afford it, we know that even if we reach max threads, any new task will be rejected
        // ~ so we can safely assume that this is actually not blocking
        //
        boolean toReset = flag.get();
        try {
            flag.set(false);
            return PlatformExecutorService.super.submit(action, arg1, arg2);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public String toString() {
        return String.format("%s(%s)", beanName, executor);
    }
}
