package com.turbospaces.boot;

import java.time.Duration;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;

import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import io.github.resilience4j.retry.internal.InMemoryRetryRegistry;

public class CustomRetryRegistry implements RetryRegistry {
    private final ApplicationProperties props;
    private final InMemoryRetryRegistry inMemoryRetryRegistry;

    public CustomRetryRegistry(ApplicationProperties props) {
        this.props = props;
        Duration backoffInitialInterval = props.APP_BACKOFF_RETRY_FIRST.get();
        Duration backoffMaxInterval = props.APP_BACKOFF_RETRY_MAX.get();
        int backoffMaxRetries = props.APP_BACKOFF_RETRY_NUM.get();
        IntervalFunction backoff = IntervalFunction.ofExponentialBackoff(backoffInitialInterval, IntervalFunction.DEFAULT_MULTIPLIER, backoffMaxInterval);
        RetryConfig defaultConfig = RetryConfig.custom().maxAttempts(backoffMaxRetries).intervalFunction(backoff).build();
        this.inMemoryRetryRegistry = new InMemoryRetryRegistry(defaultConfig);
    }

    private RetryConfig retryConfigByNameOrDefault(String name) {
        Duration backoffInitialInterval = props.cfg().get(
                Duration.class,
                name + "." + props.APP_BACKOFF_RETRY_FIRST.getKey(),
                props.APP_BACKOFF_RETRY_FIRST.get());
        Duration backoffMaxInterval = props.cfg().get(
                Duration.class,
                name + "." + props.APP_BACKOFF_RETRY_MAX.getKey(),
                props.APP_BACKOFF_RETRY_MAX.get());
        Integer backoffMaxRetries = props.cfg().get(
                Integer.class,
                name + "." + props.APP_BACKOFF_RETRY_NUM.getKey(),
                props.APP_BACKOFF_RETRY_NUM.get());
        IntervalFunction backoff = IntervalFunction.ofExponentialBackoff(backoffInitialInterval, IntervalFunction.DEFAULT_MULTIPLIER, backoffMaxInterval);
        return RetryConfig.custom().maxAttempts(backoffMaxRetries).intervalFunction(backoff).build();
    }
    @Override
    public java.util.Map<String, String> getTags() {
        return inMemoryRetryRegistry.getTags();
    }
    @Override
    public Retry retry(String name) {
        return inMemoryRetryRegistry.retry(name, retryConfigByNameOrDefault(name));
    }
    @Override
    public Retry retry(String name, java.util.Map<String, String> tags) {
        return inMemoryRetryRegistry.retry(name, retryConfigByNameOrDefault(name), tags);
    }
    @Override
    public Retry retry(String name, RetryConfig config, java.util.Map<String, String> tags) {
        return inMemoryRetryRegistry.retry(name, config, tags);
    }
    @Override
    public Retry retry(String name, String configName, java.util.Map<String, String> tags) {
        return inMemoryRetryRegistry.retry(name, configName, tags);
    }
    @Override
    public Retry retry(String name, Supplier<RetryConfig> retryConfigSupplier, java.util.Map<String, String> tags) {
        return inMemoryRetryRegistry.retry(name, retryConfigSupplier, tags);
    }
    @Override
    public Retry retry(String name, RetryConfig config) {
        return inMemoryRetryRegistry.retry(name, config);
    }
    @Override
    public Retry retry(String name, Supplier<RetryConfig> retryConfigSupplier) {
        return inMemoryRetryRegistry.retry(name, retryConfigSupplier);
    }
    @Override
    public Retry retry(String name, String configName) {
        return inMemoryRetryRegistry.retry(name, configName);
    }
    @Override
    public void addConfiguration(String configName, RetryConfig configuration) {
        inMemoryRetryRegistry.addConfiguration(configName, configuration);
    }
    @Override
    public Optional<Retry> find(String name) {
        return inMemoryRetryRegistry.find(name);
    }
    @Override
    public RetryConfig removeConfiguration(String configName) {
        return inMemoryRetryRegistry.removeConfiguration(configName);
    }
    @Override
    public Optional<Retry> remove(String name) {
        return inMemoryRetryRegistry.remove(name);
    }
    @Override
    public Optional<Retry> replace(String name, Retry newEntry) {
        return inMemoryRetryRegistry.replace(name, newEntry);
    }
    @Override
    public Optional<RetryConfig> getConfiguration(String configName) {
        return inMemoryRetryRegistry.getConfiguration(configName);
    }
    @Override
    public RetryConfig getDefaultConfig() {
        return inMemoryRetryRegistry.getDefaultConfig();
    }
    @Override
    public EventPublisher<Retry> getEventPublisher() {
        return inMemoryRetryRegistry.getEventPublisher();
    }
    @Override
    public Set<Retry> getAllRetries() {
        return inMemoryRetryRegistry.getAllRetries();
    }
}
