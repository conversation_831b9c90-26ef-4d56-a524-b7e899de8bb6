package com.turbospaces.boot;

import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.net.URL;
import java.security.KeyStore;
import java.security.Security;
import java.time.Duration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.bridge.SLF4JBridgeHandler;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.BootstrapContextClosedEvent;
import org.springframework.boot.BootstrapRegistry;
import org.springframework.boot.BootstrapRegistryInitializer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.ConfigurableCloudFactory;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.SmartCloudConnector;
import org.springframework.cloud.app.ApplicationInstanceInfo;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.UriBasedServiceInfo;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.core.ReactiveAdapterRegistry.SpringCoreBlockHoundIntegration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertySource;
import org.springframework.util.ResourceUtils;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.health.HealthCheck;
import com.codahale.metrics.health.HealthCheck.Result;
import com.codahale.metrics.health.HealthCheckFilter;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.codahale.metrics.health.HealthCheckRegistryListener;
import com.codahale.metrics.jvm.ThreadDump;
import com.google.common.base.Suppliers;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.Uninterruptibles;
import com.netflix.archaius.api.Config;
import com.netflix.archaius.config.DefaultConfigListener;
import com.netflix.archaius.config.PollingDynamicConfig;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.CloudOptions;
import com.turbospaces.cfg.DynamicCompositeConfig;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.logging.AlertLoggingFilter;
import com.turbospaces.logging.Logback;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.core.EventConsumer;
import io.github.resilience4j.core.registry.EntryAddedEvent;
import io.github.resilience4j.micrometer.tagged.TaggedCircuitBreakerMetrics;
import io.github.resilience4j.micrometer.tagged.TaggedRateLimiterMetrics;
import io.github.resilience4j.micrometer.tagged.TaggedRetryMetrics;
import io.github.resilience4j.micrometer.tagged.TaggedTimeLimiterMetrics;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import io.github.resilience4j.timelimiter.TimeLimiter;
import io.github.resilience4j.timelimiter.TimeLimiterRegistry;
import io.jaegertracing.Configuration;
import io.jaegertracing.Configuration.SamplerConfiguration;
import io.jaegertracing.internal.Constants;
import io.jaegertracing.internal.JaegerTracer;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.MeterBinder;
import io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmCompilationMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmHeapPressureMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmInfoMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics;
import io.micrometer.core.instrument.binder.logging.LogbackMetrics;
import io.micrometer.core.instrument.binder.system.FileDescriptorMetrics;
import io.micrometer.core.instrument.binder.system.ProcessorMetrics;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.micrometer.core.instrument.dropwizard.DropwizardConfig;
import io.micrometer.core.instrument.dropwizard.DropwizardMeterRegistry;
import io.micrometer.core.instrument.util.HierarchicalNameMapper;
import io.micrometer.core.ipc.http.OkHttpSender;
import io.micrometer.elastic.ElasticConfig;
import io.micrometer.elastic.ElasticMeterRegistry;
import io.micrometer.influx.InfluxConfig;
import io.micrometer.influx.InfluxMeterRegistry;
import io.netty.util.internal.NotHidden;
import io.opentracing.Tracer;
import io.sentry.SentryClient;
import io.sentry.SentryClientFactory;
import io.sentry.connection.EventSendCallback;
import io.sentry.event.Event;
import io.sentry.event.helper.ShouldSendEventCallback;
import okhttp3.OkHttpClient;
import reactor.blockhound.BlockHound;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;
import reactor.blockhound.integration.ReactorIntegration;
import reactor.blockhound.integration.StandardOutputIntegration;
import reactor.core.scheduler.ReactorBlockHoundIntegration;

public abstract class AbstractBootstrap<PROPS extends ApplicationProperties> extends SpringApplication implements Bootstrap {
    static {
        //
        // ~ install BC provider globally (it is actually huge library). we should keep an eye on not adding 'conscrypt' or any other
        //
        Security.addProvider(new BouncyCastleProvider());

        //
        // ~ install corresponding bridge
        //
        if (BooleanUtils.isFalse(SLF4JBridgeHandler.isInstalled())) {
            SLF4JBridgeHandler.removeHandlersForRootLogger();
            SLF4JBridgeHandler.install();
        }

        //
        // ~ configure block hound in a specific order adding custom handler last
        //
        BlockHound.builder()
                .with(NotHidden.nettyBlockhound())
                .with(new StandardOutputIntegration())
                .with(new SpringCoreBlockHoundIntegration())
                .with(new ReactorIntegration())
                .with(new ReactorBlockHoundIntegration())
                .with(new DefaultBlockHoundIntegration())
                .install();
    }

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final MetricRegistry metricRegistry = new MetricRegistry();
    private final CompositeMeterRegistry meterRegistry = new CompositeMeterRegistry();

    private final HealthCheckRegistry healthCheckRegistry;
    private final Thread shutdownHook;
    private final String release;

    private final ConfigurableCloudFactory cloudFactory;
    private final DynamicCloud cloud;
    private final PROPS props;
    private final JaegerTracer tracer;
    private final KeyStore keyStore;

    private final RetryRegistry retryRegistry;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final TimeLimiterRegistry timeLimiterRegistry;
    private final CircuitBreakerRegistry circuitBreakerRegistry;

    private InputStream logFile;
    private GenericApplicationContext applicationContext;
    private SentryClient sentry;

    protected AbstractBootstrap(PROPS props, Class<?>... mainClasses) throws Throwable {
        this(props, new ConfigurableCloudConnector(), mainClasses);
    }
    protected AbstractBootstrap(PROPS props, SmartCloudConnector connector, Class<?>... mainClasses) throws Throwable {
        super(mainClasses);

        this.props = Objects.requireNonNull(props);
        this.keyStore = KeyStore.getInstance(KeyStore.getDefaultType());

        //
        // ~ retry with default configuration
        //
        retryRegistry = new CustomRetryRegistry(props);
        retryRegistry.getEventPublisher().onEntryAdded(new EventConsumer<EntryAddedEvent<Retry>>() {
            @Override
            public void consumeEvent(EntryAddedEvent<Retry> event) {
                Retry retry = event.getAddedEntry();
                logger.debug("added new retry: {} with config: {}", retry.getName(), retry.getRetryConfig());
                retry.getEventPublisher().onRetry(retryEvent -> logger.warn("{} ", retryEvent, retryEvent.getLastThrowable()));
                retry.getEventPublisher().onIgnoredError(retryEvent -> logger.info("{} ", retryEvent, retryEvent.getLastThrowable()));
            }
        });

        //
        // ~ rate limiter with default configuration
        //
        rateLimiterRegistry = RateLimiterRegistry.ofDefaults();
        rateLimiterRegistry.getEventPublisher().onEntryAdded(new EventConsumer<EntryAddedEvent<RateLimiter>>() {
            @Override
            public void consumeEvent(EntryAddedEvent<RateLimiter> event) {
                RateLimiter rateLimiter = event.getAddedEntry();
                logger.debug("added new rate limiter: {} with config: {}", rateLimiter.getName(), rateLimiter.getRateLimiterConfig());
            }
        });

        //
        // ~ timer limiter with default configuration
        //
        timeLimiterRegistry = TimeLimiterRegistry.ofDefaults();
        timeLimiterRegistry.getEventPublisher().onEntryAdded(new EventConsumer<EntryAddedEvent<TimeLimiter>>() {
            @Override
            public void consumeEvent(EntryAddedEvent<TimeLimiter> event) {
                TimeLimiter timeLimiter = event.getAddedEntry();
                logger.debug("added new time limiter: {} with config: {}", timeLimiter.getName(), timeLimiter.getTimeLimiterConfig());
            }
        });

        // ~ circuit breaker registry with defaults
        circuitBreakerRegistry = CircuitBreakerRegistry.ofDefaults();
        circuitBreakerRegistry.getEventPublisher().onEntryAdded(eventConsumer -> {
            var circuitBreaker = eventConsumer.getAddedEntry();
            logger.debug("added new circuit breaker: {}, with config: {}",
                    circuitBreaker.getName(),
                    eventConsumer.getAddedEntry().getCircuitBreakerConfig());
            var ep = circuitBreaker.getEventPublisher();
            ep.onFailureRateExceeded(event -> logger.warn("CB TYPE: {}; RATE: {}", event.getEventType(), event.getFailureRate()));
            ep.onCallNotPermitted(event -> logger.warn("CB CallNotPermitted: {}", event.getCircuitBreakerName()));
        });

        //
        // ~ override spring boot's default properties
        //
        setWebApplicationType(WebApplicationType.NONE);
        setLogStartupInfo(false);
        setRegisterShutdownHook(false);

        //
        // ~ load UPS(s) and optionally subscribe to dynamic re-load
        //
        connector.load(props, keyStore(), retryRegistry());

        ApplicationInstanceInfo info = connector.getApplicationInstanceInfo();
        Map<String, Object> cloudProps = info.getProperties();

        String space = cloudProps.get(CloudOptions.CLOUD_APP_SPACE_NAME).toString();
        String host = cloudProps.get(CloudOptions.CLOUD_APP_HOST).toString();
        String slot = cloudProps.get(CloudOptions.CLOUD_APP_INSTANCE_INDEX).toString();
        String service = info.getAppId();

        // ~ determine application version
        release = PlatformUtil.version(props.CLOUD_APP_NAME);

        //
        // ~ meter registry
        //
        ImmutableList.Builder<Tag> meterTags = ImmutableList.builder();
        meterTags.add(Tag.of("env", space));
        meterTags.add(Tag.of("release", release));
        meterTags.add(Tag.of("service", service));
        meterTags.add(Tag.of("host", host));

        meterRegistry.config().commonTags(meterTags.build());
        meterRegistry.add(new DropwizardMeterRegistry(new DropwizardConfig() {
            @Override
            public String prefix() {
                return "boot";
            }
            @Override
            public String get(String key) {
                return props.cfg().getString(key, null);
            }
        }, metricRegistry, HierarchicalNameMapper.DEFAULT, Clock.SYSTEM) {
            @Override
            protected Double nullGaugeValue() {
                return Double.NaN;
            }
        });

        //
        // ~ sentry (find services directly from connector at this moment)
        //
        connector.getServiceInfos().forEach(new Consumer<ServiceInfo>() {
            @Override
            public void accept(ServiceInfo t) {
                if (t.getId().equals(UPSs.SENTRY)) {
                    UriBasedServiceInfo serviceInfo = (UriBasedServiceInfo) t;

                    logger.info("about to init sentry from UPS: {}", serviceInfo.getId());

                    sentry = SentryClientFactory.sentryClient(serviceInfo.getUri());
                    sentry.setEnvironment(space);
                    sentry.setRelease(release);
                    sentry.setServerName(service + "/" + slot);
                    sentry.addTag("service", service);
                    sentry.addShouldSendEventCallback(new ShouldSendEventCallback() {
                        @Override
                        public boolean shouldSend(Event event) {
                            return props.APP_SENTRY_ENABLED.get();
                        }
                    });
                    sentry.addEventSendCallback(new EventSendCallback() {
                        @Override
                        public void onSuccess(Event event) {
                            Counter counter = metricRegistry.counter(MetricRegistry.name("sentry", "success"));
                            counter.inc();
                        }
                        @Override
                        public void onFailure(Event event, Exception exception) {
                            Counter counter = metricRegistry.counter(MetricRegistry.name("sentry", "failure"));
                            counter.inc();
                        }
                    });
                }
            }
        });

        //
        // ~ prepare logger context
        //
        LoggerContext loggerFactory = (LoggerContext) LoggerFactory.getILoggerFactory();
        loggerFactory.setPackagingDataEnabled(isDevMode());

        //
        // ~ INFO (patch annoying logs)
        //
        String[] infoByDefault = { "org.eclipse.jgit" };
        for (String it : infoByDefault) {
            ch.qos.logback.classic.Logger log = loggerFactory.getLogger(it);
            log.setLevel(Level.INFO);
        }

        //
        // ~ cloud (load cloud instance with application info and UPSs)
        //
        cloudFactory = new ConfigurableCloudFactory(
                connector,
                props,
                retryRegistry(),
                new Consumer<String>() {
                    @Override
                    public void accept(String content) {
                        try {
                            logFile = new ByteArrayInputStream(content.getBytes());
                            configureLogging(connector);
                        } catch (Exception err) {
                            logger.error(err.getMessage(), err);
                        }
                    }
                });
        cloud = cloudFactory.getCloud();
        logger.debug("remoteIp: {}", props.externalIp());

        //
        // ~ maybe reset logging
        //
        if (Objects.isNull(logFile)) {
            configureLogging(connector);
        }

        //
        // ~ tracer
        //
        ImmutableMap.Builder<String, String> tracingTags = ImmutableMap.builder();
        tracingTags.put(Constants.TRACER_IP_TAG_KEY, PlatformUtil.detectIp());
        tracingTags.put(Constants.TRACER_HOSTNAME_TAG_KEY, host);
        tracingTags.put(CloudOptions.CLOUD_APP_INSTANCE_INDEX, slot);

        Configuration tracerCfg = Configuration.fromEnv(service).withTracerTags(tracingTags.build()).withSampler(SamplerConfiguration.fromEnv());
        JaegerTracer.Builder tracerBuilder = tracerCfg.getTracerBuilder();
        tracerBuilder.withManualShutdown();
        tracer = tracerBuilder.build();

        //
        // ~ enable influxDB reporter and (ELK or any other optionally)
        //
        if (BooleanUtils.isFalse(props.APP_METRICS_DRY_RUN.get())) {
            Optional<PlainServiceInfo> optelk = UPSs.findServiceInfoByName(cloud, UPSs.ELASTIC_SEARCH);
            Optional<PlainServiceInfo> optinf = UPSs.findServiceInfoByName(cloud, UPSs.INFLUX);

            com.google.common.base.Supplier<OkHttpSender> http = Suppliers.memoize(new com.google.common.base.Supplier<>() {
                @Override
                public OkHttpSender get() {
                    OkHttpClient.Builder ok = new OkHttpClient.Builder();
                    ok.connectTimeout(props.TCP_CONNECTION_TIMEOUT.get());
                    ok.readTimeout(props.TCP_SOCKET_TIMEOUT.get());
                    ok.writeTimeout(props.TCP_SOCKET_TIMEOUT.get());
                    ok.retryOnConnectionFailure(true);

                    return new OkHttpSender(ok.build());
                }
            });

            if (optelk.isPresent()) {
                PlainServiceInfo si = optelk.get();

                if (props.APP_METRICS_ELK_REPORTER_ENABLED.get()) {
                    meterRegistry.add(ElasticMeterRegistry.builder(new ElasticConfig() {
                        @Override
                        public int batchSize() {
                            return props.APP_METRICS_BULK_SIZE.get();
                        }
                        @Override
                        public String host() {
                            return String.format("%s://%s:%d", si.getScheme(), si.getHost(), si.getPort());
                        }
                        @Override
                        public String userName() {
                            return si.getUserName();
                        }
                        @Override
                        public String password() {
                            return si.getPassword();
                        }
                        @Override
                        public Duration connectTimeout() {
                            return props.TCP_CONNECTION_TIMEOUT.get();
                        }
                        @Override
                        public Duration readTimeout() {
                            return props.TCP_SOCKET_TIMEOUT.get();
                        }
                        @Override
                        public String get(String k) {
                            return props.cfg().getString(k, null);
                        }
                    }).httpClient(http.get()).build());
                }
            }

            if (optinf.isPresent()) {
                PlainServiceInfo si = optinf.get();

                if (props.APP_METRICS_INFLUX_REPORTER_ENABLED.get()) {
                    meterRegistry.add(InfluxMeterRegistry.builder(new InfluxConfig() {
                        @Override
                        public Duration step() {
                            return props.APP_METRICS_REPORT_INTERVAL.get();
                        }
                        @Override
                        public int batchSize() {
                            return props.APP_METRICS_BULK_SIZE.get();
                        }
                        @Override
                        public String uri() {
                            return String.format("%s://%s:%d", si.getScheme(), si.getHost(), si.getPort());
                        }
                        @Override
                        public String userName() {
                            return si.getUserName();
                        }
                        @Override
                        public String password() {
                            return si.getPassword();
                        }
                        @Override
                        public String db() {
                            return si.getPath();
                        }
                        @Override
                        public String get(String k) {
                            return props.cfg().getString(k, null);
                        }
                        @Override
                        public boolean autoCreateDb() {
                            return props.isDevMode();
                        }
                    }).httpClient(http.get()).build());
                }
            }
        }

        //
        // ~ out of the box reasonable metrics
        //
        List<MeterBinder> binders = Lists.newLinkedList();
        binders.add(new FileDescriptorMetrics());
        binders.add(new ClassLoaderMetrics());
        binders.add(new JvmGcMetrics());
        binders.add(new ProcessorMetrics());
        binders.add(new JvmThreadMetrics());
        binders.add(new JvmHeapPressureMetrics());
        binders.add(new JvmCompilationMetrics());
        binders.add(new JvmInfoMetrics());
        binders.add(new JvmMemoryMetrics());
        binders.add(new LogbackMetrics());

        for (MeterBinder binder : binders) {
            binder.bindTo(meterRegistry);
        }

        //
        // ~ expose retry/rate/time limiter(s) metrics
        //
        TaggedRetryMetrics.ofRetryRegistry(retryRegistry).bindTo(meterRegistry);
        TaggedRateLimiterMetrics.ofRateLimiterRegistry(rateLimiterRegistry).bindTo(meterRegistry);
        TaggedTimeLimiterMetrics.ofTimeLimiterRegistry(timeLimiterRegistry).bindTo(meterRegistry);
        TaggedCircuitBreakerMetrics.ofCircuitBreakerRegistry(circuitBreakerRegistry).bindTo(meterRegistry);

        //
        // ~ shutdown hook (use own which wraps spring's boot)
        //
        shutdownHook = new Thread(new Runnable() {
            @Override
            public void run() {
                logger.info("running shutdown hook now ...");
                try {
                    shutdown();
                } catch (Throwable err) {
                    logger.error(err.getMessage(), err);
                } finally {
                    //
                    // ~ in worth case scenario just halt
                    //
                    if (isProdMode()) {
                        Runtime.getRuntime().halt(0);
                    }
                }
            }
        });

        //
        // ~ health check registry
        //
        healthCheckRegistry = new HealthCheckRegistry();
        healthCheckRegistry.addListener(new HealthCheckRegistryListener() {
            @Override
            public void onHealthCheckAdded(String name, HealthCheck healthCheck) {

            }
            @Override
            public void onHealthCheckRemoved(String name, HealthCheck healthCheck) {
                if (healthCheck instanceof AbstractHealtchCheck preDestoy) {
                    try {
                        preDestoy.destroy();
                    } catch (Exception err) {
                        logger.error(err.getMessage(), err);
                    }
                }
            }
        });
    }
    @Override
    public ConfigurableApplicationContext run(String... args) {
        Lock rwLock = lock.writeLock();
        for (;;) {
            boolean isHealthy = true;
            Set<String> unhealthy = new HashSet<>();
            rwLock.lock();
            try {
                if (props.APP_WAIT_FOR_HEALTHCHECKS_ENABLED.get()) {
                    logger.debug("about to run health-checks now ...");

                    int it = 0;
                    long now = System.currentTimeMillis();
                    long timeout = props.APP_WAIT_FOR_HEALTHCHECKS_TIMEOUT.get().toMillis();
                    isHealthy = false;

                    while (System.currentTimeMillis() - now <= timeout) {
                        boolean tmp = true;
                        it++;
                        unhealthy.clear();

                        // ~ we want to run regular health-checks or just those which are meant for boot only
                        for (Entry<String, Result> entry : healthCheckRegistry().runHealthChecks(new HealthCheckFilter() {
                            @Override
                            public boolean matches(String name, HealthCheck healthCheck) {
                                if (healthCheck instanceof AbstractHealtchCheck check) {
                                    return check.isBootstrapOnly();
                                }
                                return true;
                            }
                        }).entrySet()) {
                            logger.debug("iteration({}) ::: healthcheck({}) - isHealthy({})", it, entry.getKey(), entry.getValue().isHealthy());

                            tmp &= entry.getValue().isHealthy();
                            if (BooleanUtils.isFalse(entry.getValue().isHealthy())) {
                                unhealthy.add(entry.getKey());
                            }
                        }
                        if (tmp) {
                            isHealthy = true;
                            break;
                        }

                        int waitSec = (int) props.APP_WAIT_FOR_HEALTHCHECKS_INTERVAL.get().toSeconds();
                        logger.debug("about to wait {} sec before next health_check attempt ...", waitSec);
                        Uninterruptibles.sleepUninterruptibly(waitSec, TimeUnit.SECONDS);
                    }
                }

                if (isHealthy) {
                    logger.info("about to perform actual application start ...");

                    StopWatch now = StopWatch.createStarted();
                    doStart(args);
                    now.stop();

                    logger.info("application started in: {}", now);

                    //
                    // ~ register shutdown hook
                    //
                    if (props.APP_SHUTDOWN_HOOK_ENABLED.get()) {
                        logger.info("shutdown hook has been registered ...");
                        Runtime.getRuntime().addShutdownHook(shutdownHook);
                    }

                    return applicationContext;
                }

                logger.info("app will not start due to failed health checks ...");
                healthCheckRegistry.shutdown();

                throw new Throwable("unhealthy" + " = " + unhealthy.toString());
            } catch (Throwable err) {
                //
                // ~ log only if healthy
                //
                if (props.isProdMode()) {
                    err.printStackTrace();
                    logger.error(err.getMessage(), err);
                }

                //
                // ~ remove hook (important)
                //
                boolean removed = Runtime.getRuntime().removeShutdownHook(shutdownHook);
                if (removed) {
                    logger.info("removed shutdown hook ...");
                }

                //
                // ~ try to perform clean shutdown
                //
                if (isHealthy) {
                    logger.error("application failed to start, stopping lifecycle thread ...");
                    try {
                        shutdown();
                    } catch (Throwable t) {
                        logger.warn(t.getMessage(), t);
                    }
                }

                //
                // ~ we need to dump all threads for better troubleshooting
                //
                logger.info("dumping all threads now before termination ... ");
                ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
                ThreadDump dump = new ThreadDump(threadMXBean);
                dump.dump(System.err);

                //
                // ~ re-throw error which will cause JVM exit with non-zero code docker(restart_policy=on-failure)
                // ~ just wait a little before exit (we want to give some time for sentry/ELK appenders to complete and report)
                //
                try {
                    Uninterruptibles.sleepUninterruptibly(props.APP_SYSTEM_EXIT_DELAY.get());
                } finally {
                    ExceptionUtils.wrapAndThrow(err);
                }
            } finally {
                rwLock.unlock();
            }
        }
    }
    @Override
    public void shutdown() throws Exception {
        Lock rwLock = lock.writeLock();
        rwLock.lock();
        try {
            doStop();
        } finally {
            rwLock.unlock();
        }
    }
    @Override
    public void squashLogging() {
        if (StringUtils.isNotEmpty(props.APP_LOGGING_RESET_TO.get())) {
            Logback.resetToLevel(Level.valueOf(props.APP_LOGGING_RESET_TO.get()));
        }
    }
    @Override
    public CompositeMeterRegistry meterRegistry() {
        return meterRegistry;
    }
    @Override
    public HealthCheckRegistry healthCheckRegistry() {
        return healthCheckRegistry;
    }
    @Override
    public DynamicCloud cloud() {
        return cloud;
    }
    @Override
    public KeyStore keyStore() {
        return keyStore;
    }
    @Override
    public String release() {
        return release;
    }
    @Override
    public boolean isDevMode() {
        return props.isDevMode();
    }
    @Override
    public int port() {
        return props.CLOUD_APP_PORT.get();
    }
    @Override
    public int secondaryPort() {
        return props.CLOUD_APP_SECONDARY_PORT.get();
    }
    @Override
    public int tertiaryPort() {
        return props.CLOUD_APP_TERTIARY_PORT.get();
    }
    @Override
    public String spaceName() {
        return props.CLOUD_APP_SPACE_NAME.get();
    }
    @Override
    public String appId() {
        return props.CLOUD_APP_ID.get();
    }
    @Override
    public Tracer tracer() {
        return tracer;
    }
    @Override
    public RetryRegistry retryRegistry() {
        return retryRegistry;
    }
    @Override
    public RateLimiterRegistry rateLimiterRegistry() {
        return rateLimiterRegistry;
    }
    @Override
    public TimeLimiterRegistry timeLimiterRegistry() {
        return timeLimiterRegistry;
    }
    @Override
    public CircuitBreakerRegistry circuitBreakerRegistry() {
        return circuitBreakerRegistry;
    }
    @Override
    public void registerHealthCheck(String name, HealthCheck check) {
        healthCheckRegistry.register(name, check);
    }
    @Override
    protected void configurePropertySources(ConfigurableEnvironment environment, String[] args) {
        super.configurePropertySources(environment, args);

        //
        // ~ with highest priority
        //
        environment.getPropertySources().addFirst(new PropertySource<>("archaius") {
            @Override
            public Object getProperty(String key) {
                return props.cfg().getRawProperty(key);
            }
        });
    }
    @Override
    protected void postProcessApplicationContext(ConfigurableApplicationContext context) {
        super.postProcessApplicationContext(context);
    }
    protected void doStart(String... args) throws Throwable {
        //
        // ~ register common beans since we create them by hand (may change in future ie move them to shared configuration)
        //
        addBootstrapRegistryInitializer(new BootstrapRegistryInitializer() {
            @Override
            public void initialize(BootstrapRegistry registry) {
                registry.addCloseListener(new ApplicationListener<BootstrapContextClosedEvent>() {
                    @Override
                    public void onApplicationEvent(BootstrapContextClosedEvent event) {
                        var beanFactory = event.getApplicationContext().getBeanFactory();

                        beanFactory.registerSingleton("cloud", cloud());
                        beanFactory.registerSingleton("props", props);
                        props.fragments().forEach(f -> beanFactory.registerSingleton(f.getClass().getSimpleName(), f));
                        beanFactory.registerSingleton("cfg", props.cfg());
                        beanFactory.registerSingleton("tracer", tracer());
                        beanFactory.registerSingleton("key-store", keyStore());
                        beanFactory.registerSingleton("meter-registry", meterRegistry());
                        beanFactory.registerSingleton("health-check-registry", healthCheckRegistry());
                        beanFactory.registerSingleton("rate-limiter-registry", rateLimiterRegistry());
                        beanFactory.registerSingleton("time-limiter-registry", timeLimiterRegistry());
                        beanFactory.registerSingleton("circuit-breaker-registry", circuitBreakerRegistry());
                        beanFactory.registerSingleton("retry-registry", retryRegistry());
                    }
                });
            }
        });

        //
        // ~ finally call super
        //
        applicationContext = (GenericApplicationContext) super.run(args);
    }
    @Override
    protected void afterRefresh(ConfigurableApplicationContext context, ApplicationArguments args) {
        applicationContext = (GenericApplicationContext) context;
    }
    protected void doStop() throws Exception {
        healthCheckRegistry.shutdown();

        for (String name : healthCheckRegistry.getNames()) {
            logger.info("removing health-check {} ...", name);
            healthCheckRegistry.unregister(name);
        }

        try {
            if (Objects.nonNull(applicationContext)) {
                logger.info("closing application now ...");
                applicationContext.close();
            }
        } finally {
            for (Retry retry : retryRegistry.getAllRetries()) {
                retryRegistry.remove(retry.getName());
            }
            for (RateLimiter rateLimiter : rateLimiterRegistry.getAllRateLimiters()) {
                rateLimiterRegistry.remove(rateLimiter.getName());
            }
            for (TimeLimiter timeLimiter : timeLimiterRegistry.getAllTimeLimiters()) {
                timeLimiterRegistry.remove(timeLimiter.getName());
            }
            for (CircuitBreaker circuitBreaker : circuitBreakerRegistry.getAllCircuitBreakers()) {
                circuitBreakerRegistry.remove(circuitBreaker.getName());
            }

            if (Objects.nonNull(sentry)) {
                logger.info("closing sentry now ...");
                sentry.closeConnection();
            }

            meterRegistry.close();
            meterRegistry.clear();

            tracer.close();

            cloud.dispose();
            cloudFactory.dispose();

            //
            // ~ cleanup action on configuration
            //
            if (props.APP_CLEAR_CONFIG_AT_SHUTDOWN_ENABLED.get()) {
                logger.debug("disposing CFG ...");

                ApplicationConfig cfg = props.cfg();
                for (String next : cfg.getConfigNames()) {
                    Config removed = cfg.removeConfig(next);
                    if (removed instanceof PollingDynamicConfig pdc) {
                        pdc.shutdown();
                    }
                }
            }
        }
    }
    private void configureLogging(SmartCloudConnector connector) throws Exception {
        ApplicationInstanceInfo info = connector.getApplicationInstanceInfo();
        Map<String, Object> cloudProps = info.getProperties();

        String space = cloudProps.get(CloudOptions.CLOUD_APP_SPACE_NAME).toString();
        String host = cloudProps.get(CloudOptions.CLOUD_APP_HOST).toString();
        String slot = cloudProps.get(CloudOptions.CLOUD_APP_INSTANCE_INDEX).toString();
        String service = info.getAppId();

        LoggerContext loggerFactory = (LoggerContext) LoggerFactory.getILoggerFactory();
        loggerFactory.setPackagingDataEnabled(isDevMode());

        boolean toConfigure = true;

        Map<String, String> options = new HashMap<>();
        options.put(Logback.SPACE, space);
        options.put(Logback.HOST, host);
        options.put(Logback.SERVICE, service);
        options.put(Logback.SLOT, slot);
        options.put(Logback.RELEASE, release());

        //
        // ~ logging/alerts/report to sentry (populate with initial values)
        //
        AtomicBoolean logging = new AtomicBoolean(props.APP_LOGGING_DRY_RUN.get());
        AtomicBoolean alerts = new AtomicBoolean(props.APP_ALERTS_DRY_RUN.get());
        AtomicBoolean reportToSentry = new AtomicBoolean(props.APP_LOGGING_REPORT_TO_SENTRY.get());

        //
        // ~ watch GIT specifically (very important for production) so that in worth case we can disable logging to ELK
        //
        Config git = props.cfg().getConfig(DynamicCompositeConfig.GIT_CFG_NAME);
        git.addListener(new DefaultConfigListener() {
            @Override
            public void onConfigUpdated(Config config) {
                String klog = props.APP_LOGGING_DRY_RUN.getKey();
                String kalert = props.APP_ALERTS_DRY_RUN.getKey();
                String kreport = props.APP_LOGGING_REPORT_TO_SENTRY.getKey();

                if (config.containsKey(klog)) {
                    boolean updated = config.getBoolean(klog);
                    logging.set(updated);
                }
                if (config.containsKey(kalert)) {
                    boolean updated = config.getBoolean(kalert);
                    alerts.set(updated);
                }
                if (config.containsKey(kreport)) {
                    boolean updated = config.getBoolean(kreport);
                    reportToSentry.set(updated);
                }
            }
        });

        Supplier<Boolean> loggingDryRun = new Supplier<>() {
            @Override
            public Boolean get() {
                return logging.get();
            }
        };
        Supplier<Boolean> alertsDryRun = new Supplier<>() {
            @Override
            public Boolean get() {
                return alerts.get();
            }
        };
        Supplier<Boolean> loggingReportToSentry = new Supplier<>() {
            @Override
            public Boolean get() {
                return reportToSentry.get();
            }
        };

        //
        // ~ re-load classical conventional file in DEV mode
        //
        if (isDevMode()) {
            try {
                URL logbackTest = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "logback-test.xml");
                try (InputStream io = logbackTest.openStream()) {
                    toConfigure = false;
                }
            } catch (FileNotFoundException err) {
                logger.trace(err.getMessage(), err);
            }
        }

        if (toConfigure) {
            AlertLoggingFilter alertFilter = new AlertLoggingFilter(props.SENTRY_ALERTS_LOG_MESSAGES_TO_IGNORE, props.SENTRY_ALERTS_LOGGERS_TO_IGNORE);

            AtomicReference<UriBasedServiceInfo> sentryUps = new AtomicReference<>();
            AtomicReference<UriBasedServiceInfo> elasticUps = new AtomicReference<>();
            connector.getServiceInfos().forEach(si -> {
                switch (si.getId()) {
                    case Logback.UPS_SENTRY: {
                        sentryUps.set((UriBasedServiceInfo) si);
                        break;
                    }
                    case Logback.UPS_ELASTIC_SEARCH: {
                        elasticUps.set((UriBasedServiceInfo) si);
                        break;
                    }
                    default:
                        break;
                }
            });

            //
            // ~ add additional substitute properties only from some configurations
            //
            Map<String, String> substitute = Maps.newHashMap();
            for (String name : new String[] {
                    DynamicCompositeConfig.GIT_CFG_NAME,
                    DynamicCompositeConfig.CMD_LINE_CFG_NAME,
                    DynamicCompositeConfig.CONFIG_CAT_CFG_NAME }) {
                Config config = props.cfg().getConfig(name);
                if (Objects.nonNull(config)) {
                    config.keys().forEach(new Consumer<>() {
                        @Override
                        public void accept(String key) {
                            Object raw = config.getRawProperty(key);
                            if (Objects.nonNull(raw)) {
                                substitute.put(key, raw.toString());
                            }
                        }
                    });
                }
            }

            //
            // ~ load from GIT (logging.xml file editable in real-time)
            //
            if (Objects.nonNull(logFile)) {
                try (InputStream io = logFile) {
                    Logback.configureFrom(
                            loggerFactory,
                            alertFilter,
                            io,
                            options,
                            sentry,
                            rateLimiterRegistry,
                            meterRegistry,
                            loggingDryRun,
                            alertsDryRun,
                            loggingReportToSentry,
                            sentryUps.get(),
                            elasticUps.get(),
                            substitute);
                }
            } else {
                try {
                    //
                    // ~ regularly, we take it from class path directly (GIT override should be treated as hot fix only)
                    //
                    URL loggingXml = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "logging.xml");
                    try (InputStream io = loggingXml.openStream()) {
                        Logback.configureFrom(
                                loggerFactory,
                                alertFilter,
                                io,
                                options,
                                sentry,
                                rateLimiterRegistry,
                                meterRegistry,
                                loggingDryRun,
                                alertsDryRun,
                                loggingReportToSentry,
                                sentryUps.get(),
                                elasticUps.get(),
                                substitute);
                    }
                } catch (FileNotFoundException err) {
                    logger.atTrace().setCause(err).log();
                }
            }
        }
    }
}
