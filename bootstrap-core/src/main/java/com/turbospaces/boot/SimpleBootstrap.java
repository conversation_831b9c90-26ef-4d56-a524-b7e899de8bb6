package com.turbospaces.boot;

import org.springframework.cloud.SmartCloudConnector;

import com.google.common.annotations.VisibleForTesting;
import com.turbospaces.cfg.ApplicationProperties;

public class SimpleBootstrap extends AbstractBootstrap<ApplicationProperties> {
    public SimpleBootstrap(ApplicationProperties props, Class<?>... mainClass) throws Throwable {
        super(props, mainClass);
    }
    public SimpleBootstrap(ApplicationProperties props, SmartCloudConnector connector) throws Throwable {
        super(props, connector, Object.class);
    }
    public SimpleBootstrap(ApplicationProperties props, SmartCloudConnector connector, Class<?>... mainClasses) throws Throwable {
        super(props, connector, mainClasses);
    }
    @VisibleForTesting
    public SimpleBootstrap(ApplicationProperties props) throws Throwable {
        super(props, Object.class);
        props.cfg().setDefaultProperty(props.APP_SHUTDOWN_HOOK_ENABLED.getKey(), false);
    }
}
