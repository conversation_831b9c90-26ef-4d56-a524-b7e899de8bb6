package com.turbospaces.json;

import java.util.Collection;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock.ReadLock;
import java.util.concurrent.locks.ReentrantReadWriteLock.WriteLock;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.cfg.MapperConfig;
import com.fasterxml.jackson.databind.introspect.AnnotatedClass;
import com.fasterxml.jackson.databind.introspect.AnnotatedMember;
import com.fasterxml.jackson.databind.jsontype.NamedType;
import com.fasterxml.jackson.databind.jsontype.SubtypeResolver;
import com.fasterxml.jackson.databind.jsontype.impl.StdSubtypeResolver;

public final class ThreadSafeStdSubtypeResolver extends StdSubtypeResolver {
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();

    public ThreadSafeStdSubtypeResolver() {
        super();
    }
    public ThreadSafeStdSubtypeResolver(StdSubtypeResolver src) {
        super(src);
    }
    @Override
    public SubtypeResolver copy() {
        return new ThreadSafeStdSubtypeResolver(this);
    }
    @Override
    public void registerSubtypes(NamedType... types) {
        WriteLock lock = rwLock.writeLock();
        lock.lock();
        try {
            super.registerSubtypes(types);
        } finally {
            lock.unlock();
        }
    }
    @Override
    public Collection<NamedType> collectAndResolveSubtypesByClass(MapperConfig<?> config, AnnotatedMember property, JavaType baseType) {
        ReadLock lock = rwLock.readLock();
        lock.lock();
        try {
            return super.collectAndResolveSubtypesByClass(config, property, baseType);
        } finally {
            lock.unlock();
        }
    }
    @Override
    public Collection<NamedType> collectAndResolveSubtypesByClass(MapperConfig<?> config, AnnotatedClass type) {
        ReadLock lock = rwLock.readLock();
        lock.lock();
        try {
            return super.collectAndResolveSubtypesByClass(config, type);
        } finally {
            lock.unlock();
        }
    }
    @Override
    public Collection<NamedType> collectAndResolveSubtypesByTypeId(MapperConfig<?> config, AnnotatedMember property, JavaType baseType) {
        ReadLock lock = rwLock.readLock();
        lock.lock();
        try {
            return super.collectAndResolveSubtypesByTypeId(config, property, baseType);
        } finally {
            lock.unlock();
        }
    }
    @Override
    public Collection<NamedType> collectAndResolveSubtypesByTypeId(MapperConfig<?> config, AnnotatedClass baseType) {
        ReadLock lock = rwLock.readLock();
        lock.lock();
        try {
            return super.collectAndResolveSubtypesByTypeId(config, baseType);
        } finally {
            lock.unlock();
        }
    }
}
