package com.turbospaces.json;

import java.util.concurrent.TimeUnit;

import com.codahale.metrics.MetricFilter;
import com.codahale.metrics.json.HealthCheckModule;
import com.codahale.metrics.json.MetricsModule;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsonorg.JsonOrgModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.robtimus.obfuscation.jackson.databind.ObfuscationModule;

public class CommonObjectMapper extends BasicObjectMapper {
    public CommonObjectMapper(JsonMapper jsonMapper) {
        super(jsonMapper);
    }
    public CommonObjectMapper() {
        super();

        setSubtypeResolver(new ThreadSafeStdSubtypeResolver());

        registerModule(new Jdk8Module());
        registerModule(new JsonOrgModule());
        registerModule(new JavaTimeModule());
        registerModule(new GuavaModule());

        //
        // ~ obfuscation
        //
        registerModule(ObfuscationModule.defaultModule());

        //
        // ~ metrics
        //
        registerModule(new HealthCheckModule());
        registerModule(new MetricsModule(TimeUnit.SECONDS, TimeUnit.MILLISECONDS, true, MetricFilter.ALL));

        //
        // ~ date format
        //
        setDateFormat(new StdDateFormat().withColonInTimeZone(true));
    }
    @Override
    public CommonObjectMapper copy() {
        this._checkInvalidCopy(CommonObjectMapper.class);
        return new CommonObjectMapper(this);
    }
}
