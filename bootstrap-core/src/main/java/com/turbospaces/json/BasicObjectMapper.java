package com.turbospaces.json;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;

public class BasicObjectMapper extends JsonMapper {
    public BasicObjectMapper(JsonMapper jsonMapper) {
        super(jsonMapper);
    }
    public BasicObjectMapper() {
        super();

        setSerializationInclusion(Include.NON_NULL);

        setDateFormat(new StdDateFormat());
        disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        new JsonMapper.Builder(this).enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS);
    }
    @Override
    public BasicObjectMapper copy() {
        this._checkInvalidCopy(BasicObjectMapper.class);
        return new BasicObjectMapper();
    }
}
