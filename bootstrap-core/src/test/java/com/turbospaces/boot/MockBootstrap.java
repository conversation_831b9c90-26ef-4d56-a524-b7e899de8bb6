package com.turbospaces.boot;

import java.io.IOException;
import java.net.URI;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.bootstrap.HttpServer;
import org.apache.http.impl.bootstrap.ServerBootstrap;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HttpContext;
import org.apache.http.protocol.HttpRequestHandler;
import org.apache.http.protocol.HttpRequestHandlerMapper;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.boot.BootstrapContextClosedEvent;
import org.springframework.boot.BootstrapRegistry;
import org.springframework.boot.BootstrapRegistry.InstanceSupplier;
import org.springframework.boot.BootstrapRegistryInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.SmartLifecycle;

import com.turbospaces.cfg.ApplicationProperties;

import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

public class MockBootstrap extends SimpleBootstrap {
    public MockBootstrap(ApplicationProperties props, Class<?>... mainClass) throws Throwable {
        super(props, mainClass);
    }
    @Override
    protected void doStart(String... args) throws Throwable {
        addBootstrapRegistryInitializer(new HttpClientPlugin());
        super.doStart();
    }

    public static class HttpClientPlugin implements BootstrapRegistryInitializer {
        @Override
        public void initialize(BootstrapRegistry registry) {
            registry.register(HttpClientBuilder.class, InstanceSupplier.of(HttpClients.custom()));
            registry.addCloseListener(new ApplicationListener<BootstrapContextClosedEvent>() {
                @Override
                public void onApplicationEvent(BootstrapContextClosedEvent event) {
                    HttpClientBuilder builder = registry.getRegisteredInstanceSupplier(HttpClientBuilder.class).get(event.getBootstrapContext());

                    ConfigurableApplicationContext ctx = event.getApplicationContext();
                    ctx.getBeanFactory().registerSingleton("http-client-factory", builder);
                }
            });
        }
    }

    @Slf4j
    public static class InboundChannel implements SmartLifecycle {
        private final ApplicationProperties props;
        private final URI uri;
        private final ServerBootstrap http;
        private HttpServer channel;
        private boolean running;

        @Inject
        private CloseableHttpClient httpClient;

        public InboundChannel(ApplicationProperties props, URI uri) {
            this.props = Objects.requireNonNull(props);
            this.uri = Objects.requireNonNull(uri);
            http = ServerBootstrap.bootstrap();
        }
        @Override
        public void start() {
            try {
                int port = props.CLOUD_APP_PORT.get();
                http.setListenerPort(port);
                http.setHandlerMapper(new HttpRequestHandlerMapper() {
                    @Override
                    public HttpRequestHandler lookup(HttpRequest request) {
                        return new HttpRequestHandler() {
                            @Override
                            public void handle(HttpRequest req, HttpResponse resp, HttpContext context) throws HttpException, IOException {
                                String reqUri = req.getRequestLine().getUri();
                                if (reqUri.equals(uri.getPath())) {
                                    resp.setStatusCode(HttpStatus.SC_OK);
                                    resp.setEntity(new StringEntity(req.toString(), ContentType.APPLICATION_JSON));
                                } else {
                                    resp.setStatusCode(HttpStatus.SC_NOT_FOUND);
                                }
                            }
                        };
                    }
                });

                log.debug("Starting embedded Http server: {}", port);
                channel = http.create();
                channel.start();

                this.running = true;

                HttpGet req = new HttpGet();
                req.setURI(uri);

                CountDownLatch latch = new CountDownLatch(1);
                try (CloseableHttpResponse resp = httpClient.execute(req)) {
                    String text = EntityUtils.toString(resp.getEntity());
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            log.debug(text);
                            latch.countDown();
                        }
                    }).start();
                }
                Assertions.assertTrue(latch.await(30, TimeUnit.SECONDS));
            } catch (Exception err) {
                throw new BeanInitializationException(err.getMessage(), err);
            }
        }
        @Override
        public void stop(Runnable callback) {
            if (channel != null) {
                stop();
                callback.run();
            }
        }
        @Override
        public void stop() {
            log.debug("Stoppping embedded Http server");
            channel.stop();
            this.running = false;
        }
        @Override
        public boolean isRunning() {
            return this.running;
        }
    }
}
