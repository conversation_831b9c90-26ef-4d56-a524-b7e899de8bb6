package com.turbospaces.boot;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.Scheduled;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.boot.MockBootstrap.InboundChannel;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.CloudOptions;
import com.turbospaces.support.TimedConfiguration;

import io.micrometer.core.annotation.Timed;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@Import({ TimedConfiguration.class })
public class MockConfig {
    @Bean
    public HttpFactory factory(HttpClientBuilder client) {
        return new HttpFactory(client);
    }
    @Bean
    public Foo foo(HttpFactory factory) throws Exception {
        return new Foo(factory.getObject());
    }
    @Scheduled(fixedRate = 1000)
    public void mock() {
        log.info("The time is now {}", new SimpleDateFormat("HH:mm:ss").format(new Date()));
    }
    @Bean
    public InboundChannel inboundChannel(ApplicationProperties props) throws Exception {
        URIBuilder uri = new URIBuilder().setScheme("http").setHost("localhost").setPath("/v1/status").setPort(props.CLOUD_APP_PORT.get());
        return new InboundChannel(props, uri.build());
    }

    public static class Foo implements InitializingBean, DisposableBean {
        @Value(CloudOptions.CLOUD_APP_ID)
        String appId;

        private final CloseableHttpClient client;

        @Inject
        public Foo(CloseableHttpClient client) {
            this.client = Objects.requireNonNull(client);
        }
        @Override
        public void afterPropertiesSet() throws Exception {
            log.debug("Starting foo bean now ...");
        }
        @Override
        public void destroy() throws Exception {
            log.debug("Stopping foo bean now ...");
        }
        @Timed
        public void print() throws InterruptedException {
            CountDownLatch l = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    log.debug(toString());
                    l.countDown();
                }
            }).start();

            Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));
        }
        @Override
        public String toString() {
            ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
            return toString.append("appId", appId).append("client", client).build();
        }
    }

    public static class HttpFactory extends AbstractFactoryBean<CloseableHttpClient> {
        private final HttpClientBuilder builder;

        public HttpFactory(HttpClientBuilder builder) {
            this.builder = Objects.requireNonNull(builder);
        }
        @Override
        public Class<?> getObjectType() {
            return CloseableHttpClient.class;
        }
        @Override
        protected CloseableHttpClient createInstance() throws Exception {
            return builder.build();
        }
        @Override
        protected void destroyInstance(CloseableHttpClient instance) throws Exception {
            instance.close();
        }
    }
}
