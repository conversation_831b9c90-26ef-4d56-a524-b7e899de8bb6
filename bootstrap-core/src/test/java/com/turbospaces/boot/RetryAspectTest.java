package com.turbospaces.boot;

import java.net.SocketTimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import com.netflix.archaius.api.annotations.Configuration;
import com.turbospaces.annotations.Retryable;
import com.turbospaces.aspects.RetryAspect;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;

class RetryAspectTest {
    @Test
    void testRetriedOnce() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();

        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            ServiceApi serviceApi = applicationContext.getBean(ServiceApi.class);
            serviceApi.failOnce();
            Assertions.assertEquals(2, ServiceApi.ATTEMPTED_COUNT.getAndSet(0));
            Assertions.assertEquals(1, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failOnce_default", "kind", "successful_with_retry").functionCounter().count());
            Assertions.assertEquals(0, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failOnce_default", "kind", "failed_with_retry").functionCounter().count());
            Assertions.assertEquals(0, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failOnce_default", "kind", "successful_without_retry").functionCounter().count());
            Assertions.assertEquals(0, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failOnce_default", "kind", "failed_without_retry").functionCounter().count());
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void testNoRetryCauseCustomConfig() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();

        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            ServiceApi serviceApi = applicationContext.getBean(ServiceApi.class);
            Assertions.assertThrows(RuntimeException.class, serviceApi::failInstantly);
            Assertions.assertEquals(1, ServiceApi.ATTEMPTED_COUNT.getAndSet(0));
            Assertions.assertEquals(0, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failInstantly_custom", "kind", "successful_with_retry").functionCounter().count());
            Assertions.assertEquals(0, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failInstantly_custom", "kind", "failed_with_retry").functionCounter().count());
            Assertions.assertEquals(0, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failInstantly_custom", "kind", "successful_without_retry").functionCounter().count());
            Assertions.assertEquals(1, bootstrap.meterRegistry().get("resilience4j.retry.calls")
                    .tags("name", "ServiceApi_failInstantly_custom", "kind", "failed_without_retry").functionCounter().count());
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void testUnknown() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();

        SimpleBootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            ServiceApi serviceApi = applicationContext.getBean(ServiceApi.class);
            Assertions.assertThrows(RuntimeException.class, serviceApi::doNotRetry);
            Assertions.assertEquals(1, ServiceApi.ATTEMPTED_COUNT.getAndSet(0));
            Assertions.assertTrue(bootstrap.meterRegistry().find("resilience4j.retry.calls").functionCounters().isEmpty());
        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    @EnableAspectJAutoProxy(proxyTargetClass = true)
    public static class AppConfig {
        @Bean("custom")
        public RetryConfig customConfig() {
            return RetryConfig.custom().maxAttempts(3).retryExceptions(SocketTimeoutException.class).build();
        }

        @Bean
        public RetryAspect aspect(RetryRegistry retryRegistry) {
            return new RetryAspect(retryRegistry);
        }

        @Bean
        public ServiceApi serviceApi() {
            return new ServiceApi();
        }
    }

    public static class ServiceApi {
        public static AtomicInteger ATTEMPTED_COUNT = new AtomicInteger(0);

        @Retryable
        public void failOnce() {
            if (ATTEMPTED_COUNT.getAndIncrement() == 0) {
                throw new RuntimeException("Temporary error");
            }
        }

        @Retryable(config = "custom")
        public void failInstantly() {
            if (ATTEMPTED_COUNT.getAndIncrement() == 0) {
                throw new RuntimeException("Temporary error");
            }
        }

        @Retryable(config = "unknown")
        public void doNotRetry() {
            if (ATTEMPTED_COUNT.getAndIncrement() == 0) {
                throw new RuntimeException("Temporary error");
            }
        }
    }

}