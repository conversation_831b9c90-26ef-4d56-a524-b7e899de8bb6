package com.turbospaces.boot;

import org.apache.commons.rng.UniformRandomProvider;
import org.apache.commons.rng.simple.RandomSource;
import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.codahale.metrics.health.HealthCheck;
import com.turbospaces.boot.MockConfig.Foo;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.healthchecks.HttpHealthCheck;
import com.turbospaces.healthchecks.SocketHealthCheck;
import com.turbospaces.ups.PlainServiceInfo;

import io.jaegertracing.client.Version;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ExtendWith(SpringExtension.class)
@BootstrapWith(MockSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = { MockConfig.class })
public class BootstrapTest {
    @Autowired
    private Foo foo;

    @Autowired
    private ApplicationProperties props;

    @Test
    public void works() throws Throwable {
        URIBuilder uri = new URIBuilder().setScheme("http").setHost("localhost").setPath("/v1/status").setPort(props.CLOUD_APP_PORT.get());

        HealthCheck check1 = new HttpHealthCheck(props, uri.build());
        Assertions.assertTrue(check1.execute().isHealthy());

        UniformRandomProvider rng = RandomSource.XO_RO_SHI_RO_128_PP.create();

        SocketHealthCheck check2 = new SocketHealthCheck(props, new PlainServiceInfo(rng.nextBoolean() + "-" + Version.get(), uri.toString()));
        Assertions.assertTrue(check2.execute().isHealthy());

        log.debug(foo.toString());
    }
}
