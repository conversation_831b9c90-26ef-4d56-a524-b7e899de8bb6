package com.turbospaces.boot;

import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.cloud.service.BaseServiceInfo;
import org.springframework.cloud.service.ServiceInfo;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.ServiceInfoSubscription;

import uk.org.webcompere.systemstubs.jupiter.SystemStubsExtension;

@ExtendWith(SystemStubsExtension.class)
public class ServiceInfoSubscriptionTest {

    @Test
    void subscribeForScopeAndName_addNameBeforeScope() throws Throwable {
        String scope = Long.toString(System.currentTimeMillis());
        String ups = PlatformUtil.randomUUID().toString();
        UUID uuid = PlatformUtil.randomUUID();

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);

        try {
            AtomicInteger invocationCounter = new AtomicInteger(0);
            ServiceInfoSubscription<ServiceInfo> serviceInfoSubscription = ServiceInfoSubscription.of(
                    bootstrap.cloud(),
                    scope,
                    ups,
                    new Consumer<ServiceInfo>() {
                        @Override
                        public void accept(ServiceInfo serviceInfo) {
                            invocationCounter.incrementAndGet();
                        }
                    });

            CountDownLatch executedAddName = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.addUps(new BaseServiceInfo(ups));
                    executedAddName.countDown();
                }
            }).start();

            Assertions.assertTrue(executedAddName.await(20, TimeUnit.SECONDS));
            Assertions.assertEquals(1, invocationCounter.get());
            Assertions.assertEquals(ups, serviceInfoSubscription.get().getId());
            Assertions.assertNotNull(bootstrap.cloud().getServiceInfo(ups));

            CountDownLatch executedAddScope = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.addUps(new BaseServiceInfo(uuid + "-" + ups));
                    bootstrap.addUps(new BaseServiceInfo(scope + "-" + ups));
                    executedAddScope.countDown();
                }
            }).start();

            Assertions.assertTrue(executedAddScope.await(20, TimeUnit.SECONDS));
            Assertions.assertEquals(2, invocationCounter.get());
            Assertions.assertEquals(scope + "-" + ups, serviceInfoSubscription.get().getId());
            Assertions.assertNotNull(bootstrap.cloud().getServiceInfo(scope + "-" + ups));

            CountDownLatch executedRemove = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.removeUps(ups);
                    bootstrap.removeUps(scope + "-" + ups);
                    bootstrap.removeUps(uuid + "-" + ups);
                    executedRemove.countDown();
                }
            }).start();

            Assertions.assertTrue(executedRemove.await(20, TimeUnit.SECONDS));
            Assertions.assertEquals(2, invocationCounter.get());
            Assertions.assertTrue(bootstrap.cloud().getServiceInfos().isEmpty());
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void subscribeForScopeAndName_addScopeBeforeName() throws Throwable {
        String scope = Long.toString(System.currentTimeMillis());
        String ups = PlatformUtil.randomUUID().toString();
        UUID uuid = PlatformUtil.randomUUID();

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);

        try {
            AtomicInteger invocationCounter = new AtomicInteger(0);
            ServiceInfoSubscription<ServiceInfo> serviceInfoSubscription = ServiceInfoSubscription.of(
                    bootstrap.cloud(),
                    scope,
                    ups,
                    new Consumer<ServiceInfo>() {
                        @Override
                        public void accept(ServiceInfo serviceInfo) {
                            invocationCounter.incrementAndGet();
                        }
                    });

            CountDownLatch executedAddScope = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.addUps(new BaseServiceInfo(uuid + "-" + ups));
                    bootstrap.addUps(new BaseServiceInfo(scope + "-" + ups));
                    executedAddScope.countDown();
                }
            }).start();

            Assertions.assertTrue(executedAddScope.await(20, TimeUnit.SECONDS));
            Assertions.assertEquals(1, invocationCounter.get());
            Assertions.assertEquals(scope + "-" + ups, serviceInfoSubscription.get().getId());
            Assertions.assertNotNull(bootstrap.cloud().getServiceInfo(scope + "-" + ups));

            CountDownLatch executedAddName = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.addUps(new BaseServiceInfo(ups));
                    executedAddName.countDown();
                }
            }).start();

            Assertions.assertTrue(executedAddName.await(20, TimeUnit.SECONDS));
            Assertions.assertEquals(1, invocationCounter.get());
            Assertions.assertNotEquals(ups, serviceInfoSubscription.get().getId());
            Assertions.assertEquals(scope + "-" + ups, serviceInfoSubscription.get().getId());
            Assertions.assertNotNull(bootstrap.cloud().getServiceInfo(ups));

            CountDownLatch executedRemove = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.removeUps(ups);
                    bootstrap.removeUps(scope + "-" + ups);
                    bootstrap.removeUps(uuid + "-" + ups);
                    executedRemove.countDown();
                }
            }).start();

            Assertions.assertTrue(executedRemove.await(20, TimeUnit.SECONDS));
            Assertions.assertEquals(1, invocationCounter.get());
            Assertions.assertTrue(bootstrap.cloud().getServiceInfos().isEmpty());
        } finally {
            bootstrap.shutdown();
        }
    }
}
