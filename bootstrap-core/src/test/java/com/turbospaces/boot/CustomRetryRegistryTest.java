package com.turbospaces.boot;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.core.IntervalBiFunction;
import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;

public class CustomRetryRegistryTest {
    @Test
    void testRetryDefaultConfig() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        CustomRetryRegistry retryRegistry = new CustomRetryRegistry(props);

        Retry retryWithDefaultConfig = retryRegistry.retry("retry-without-special-config");
        Integer maxAttempts = props.APP_BACKOFF_RETRY_NUM.get();
        Duration initialDelay = props.APP_BACKOFF_RETRY_FIRST.get();
        Duration maxDelay = props.APP_BACKOFF_RETRY_MAX.get();

        RetryConfig config = retryWithDefaultConfig.getRetryConfig();
        IntervalBiFunction<Object> intervalBiFunction = config.getIntervalBiFunction();
        Assertions.assertEquals(maxAttempts, config.getMaxAttempts());

        checkIntervalFunction(config.getMaxAttempts(), intervalBiFunction, initialDelay.toMillis(), maxDelay.toMillis());
    }

    @Test
    void testRetryDynamicConfig() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        String testRetryName = "test-retry";
        var initialDelay = Duration.ofMillis(2);
        var maxDelay = Duration.ofMillis(3);
        var maxAttempts = 3;

        cfg.setLocalProperty(testRetryName + "." + props.APP_BACKOFF_RETRY_FIRST.getKey(), initialDelay);
        cfg.setLocalProperty(testRetryName + "." + props.APP_BACKOFF_RETRY_MAX.getKey(), maxDelay);
        cfg.setLocalProperty(testRetryName + "." + props.APP_BACKOFF_RETRY_NUM.getKey(), maxAttempts);

        CustomRetryRegistry retryRegistry = new CustomRetryRegistry(props);

        Retry retryWithDefaultConfig = retryRegistry.retry(testRetryName);

        RetryConfig config = retryWithDefaultConfig.getRetryConfig();

        IntervalBiFunction<Object> intervalBiFunction = config.getIntervalBiFunction();
        Assertions.assertEquals(maxAttempts, config.getMaxAttempts());

        checkIntervalFunction(config.getMaxAttempts(), intervalBiFunction, initialDelay.toMillis(), maxDelay.toMillis());
    }

    @Test
    void testDynamicRetryWithInitialDelayOverridden() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        String testRetryName = "test-retry-with-custom-initial-delay";
        var initialDelay = Duration.ofMillis(100);
        var maxDelay = props.APP_BACKOFF_RETRY_MAX.get();
        var maxAttempts = props.APP_BACKOFF_RETRY_NUM.get();

        cfg.setLocalProperty(testRetryName + "." + props.APP_BACKOFF_RETRY_FIRST.getKey(), initialDelay);

        CustomRetryRegistry retryRegistry = new CustomRetryRegistry(props);

        Retry retryWithDefaultConfig = retryRegistry.retry(testRetryName);

        RetryConfig config = retryWithDefaultConfig.getRetryConfig();

        IntervalBiFunction<Object> intervalBiFunction = config.getIntervalBiFunction();
        Assertions.assertEquals(maxAttempts, config.getMaxAttempts());

        checkIntervalFunction(config.getMaxAttempts(), intervalBiFunction, initialDelay.toMillis(), maxDelay.toMillis());

    }

    private static void checkIntervalFunction(int maxAttempts, IntervalBiFunction<Object> intervalBiFunction, long expectedInitialDelay, long maxDelay) {
        long expectedValue = expectedInitialDelay;
        int i = 1;
        do {
            Assertions.assertEquals(expectedValue, intervalBiFunction.apply(i, null));
            expectedValue = (long) (expectedValue * IntervalFunction.DEFAULT_MULTIPLIER);
            expectedValue = Math.min(expectedValue, maxDelay);
            i++;
        } while (i <= maxAttempts);
    }
}
