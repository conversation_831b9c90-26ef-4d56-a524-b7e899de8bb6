package com.turbospaces.boot;

import java.util.List;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.SpringBootContextLoader;
import org.springframework.boot.test.context.SpringBootTestContextBootstrapper;
import org.springframework.test.context.ContextConfigurationAttributes;
import org.springframework.test.context.ContextLoader;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

public class MockSpringBootTestContextBootstrapper extends SpringBootTestContextBootstrapper {
    @Override
    protected ContextLoader resolveContextLoader(Class<?> testClass, List<ContextConfigurationAttributes> configAttributesList) {
        return new SpringBootContextLoader() {
            @Override
            protected SpringApplication getSpringApplication() {
                try {
                    int port = PlatformUtil.findAvailableTcpPort();
                    ApplicationConfig cfg = ApplicationConfig.mock(port);
                    ApplicationProperties props = new ApplicationProperties(cfg.factory());
                    return new MockBootstrap(props);
                } catch (Throwable err) {
                    return super.getSpringApplication();
                }
            }
        };
    }
}
