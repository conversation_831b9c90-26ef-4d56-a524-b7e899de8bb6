package com.turbospaces.boot;

import java.io.IOException;
import java.lang.reflect.UndeclaredThrowableException;
import java.time.Duration;
import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.function.Function;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.service.BaseServiceInfo;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;

import com.google.common.base.Joiner;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.EnvUtil;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;

import reactor.core.publisher.Flux;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import uk.org.webcompere.systemstubs.environment.EnvironmentVariables;
import uk.org.webcompere.systemstubs.jupiter.SystemStub;
import uk.org.webcompere.systemstubs.jupiter.SystemStubsExtension;

@ExtendWith(SystemStubsExtension.class)
class UPSsTest {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @SystemStub
    private EnvironmentVariables envVariables;

    @Test
    void hierarchicalScoped() throws Throwable {
        String scope1 = "scope1";
        String scope2 = "scope2";
        String scope3 = "scope3";
        String upsName = PlatformUtil.randomUUID().toString();

        var ups = new BaseServiceInfo(upsName);
        var ups1 = new BaseServiceInfo(scope1 + "-" + upsName);
        var ups1_2 = new BaseServiceInfo(Joiner.on("-").join(scope1, scope2) + "-" + upsName);
        var ups1_2_3 = new BaseServiceInfo(Joiner.on("-").join(scope1, scope2, scope3) + "-" + upsName);

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);
        bootstrap.run();

        try {
            CountDownLatch executed = new CountDownLatch(1);
            new Thread(() -> {
                bootstrap.addUps(ups);
                bootstrap.addUps(ups1);
                bootstrap.addUps(ups1_2);
                bootstrap.addUps(ups1_2_3);
                executed.countDown();
            }).start();
            Assertions.assertTrue(executed.await(30, TimeUnit.SECONDS));

            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, scope2, scope3), ups1_2_3);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, scope2, scope3, "invalidScope"), ups1_2_3);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, scope2, scope3, null), ups1_2_3);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, scope2), ups1_2);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1.toUpperCase(), scope2, "invalidScope"), ups1_2);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, scope2, "invalidScope"), ups1_2);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, scope2, null), ups1_2);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1), ups1);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1.toUpperCase()), ups1);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1, "invalidScope"), ups1);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName), ups);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, null, null), ups);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, "invalidScope"), ups);

            Assertions.assertFalse(UPSs.findScopedServiceInfoByName(bootstrap.cloud(), "invalidUpsName").isPresent());
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void testEnvScopedSecret() throws Throwable {
        String env1 = "qa";
        String env2 = "qa1";
        String env3 = "qa2";
        String scope1 = "scope1";
        String scope2 = "scope2";
        String upsName = PlatformUtil.randomUUID().toString();

        var ups = new BaseServiceInfo(upsName);
        var ups1 = new BaseServiceInfo(env1 + "-" + upsName);
        var ups2 = new BaseServiceInfo(env2 + "-" + upsName);
        var ups3 = new BaseServiceInfo(env3 + "-" + upsName);
        var ups3s1 = new BaseServiceInfo(env3 + "-" + scope1 + "-" + upsName);
        var upss2 = new BaseServiceInfo(scope2 + "-" + upsName);

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);
        bootstrap.run();

        try {
            CountDownLatch executed = new CountDownLatch(1);
            new Thread(() -> {
                bootstrap.addUps(ups);
                bootstrap.addUps(ups1);
                bootstrap.addUps(ups2);
                bootstrap.addUps(ups3);
                bootstrap.addUps(ups3s1);
                bootstrap.addUps(upss2);
                executed.countDown();
            }).start();
            Assertions.assertTrue(executed.await(30, TimeUnit.SECONDS));

            Assertions.assertEquals(UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), upsName), ups);

            envVariables.set(EnvUtil.ENV_STAGE, env1);
            Assertions.assertEquals(UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), upsName), ups1);

            envVariables.set(EnvUtil.ENV_STAGE, env2);
            Assertions.assertEquals(UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), upsName), ups2);

            envVariables.set(EnvUtil.ENV_STAGE, env3);
            Assertions.assertEquals(UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), upsName), ups3);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope1), ups3s1);
            Assertions.assertEquals(UPSs.findRequiredScopedServiceInfoByName(bootstrap.cloud(), upsName, scope2), upss2);
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void works() throws Throwable {
        String ups = PlatformUtil.randomUUID().toString();

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);
        bootstrap.run();

        try {
            try {
                bootstrap.serviceInfoByName(ups).blockFirst(Duration.ofMillis(1));
                Assertions.fail();
            } catch (RuntimeException err) {

            }

            Scheduler scheduler = Schedulers.parallel();
            CountDownLatch latch = new CountDownLatch(1);
            bootstrap.serviceInfoByName(ups).subscribeOn(scheduler).subscribe(new Consumer<ServiceInfo>() {
                @Override
                public void accept(ServiceInfo t) {
                    latch.countDown();
                }
            });

            CountDownLatch executed = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.addUps(new PlainServiceInfo(ups, "dns:///api.google.com"));
                    executed.countDown();
                }
            }).start();
            Assertions.assertTrue(executed.await(30, TimeUnit.SECONDS));

            Assertions.assertTrue(latch.await(1, TimeUnit.MINUTES));

            PlainServiceInfo si = (PlainServiceInfo) bootstrap.cloud().getServiceInfo(ups);
            Assertions.assertNotNull(si);
            Assertions.assertEquals("dns", si.getScheme());
            Assertions.assertEquals("api.google.com", si.getPath());
            Assertions.assertEquals("dns:///api.google.com", si.getUri());

            bootstrap.removeUps(ups);
            Assertions.assertTrue(bootstrap.cloud().getServiceInfos().isEmpty());

            try {
                bootstrap.serviceInfoByName(ups).timeout(Duration.ofMillis(1)).hasElements().block();
                Assertions.fail();
            } catch (Exception err) {
                Assertions.assertTrue(ExceptionUtils.getRootCause(err).getClass().equals(TimeoutException.class));
            }
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void scoped() throws Throwable {
        String scope = Long.toString(System.currentTimeMillis());
        String ups = PlatformUtil.randomUUID().toString();
        UUID uuid = PlatformUtil.randomUUID();

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);
        bootstrap.run();

        try {
            try {
                bootstrap.scopedServiceInfoByName(scope, ups).blockFirst(Duration.ofMillis(1));
                Assertions.fail();
            } catch (RuntimeException err) {

            }

            Scheduler scheduler = Schedulers.parallel();
            CountDownLatch latch = new CountDownLatch(2);
            bootstrap.scopedServiceInfoByName(scope, ups).subscribeOn(scheduler).subscribe(new Consumer<ServiceInfo>() {
                @Override
                public void accept(ServiceInfo t) {
                    latch.countDown();
                }
            });

            CountDownLatch executed = new CountDownLatch(1);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    bootstrap.addUps(new BaseServiceInfo(ups));
                    bootstrap.addUps(new BaseServiceInfo(uuid + "-" + ups));
                    bootstrap.addUps(new BaseServiceInfo(scope + "-" + ups));
                    executed.countDown();
                }
            }).start();
            Assertions.assertTrue(executed.await(30, TimeUnit.SECONDS));

            Assertions.assertTrue(latch.await(1, TimeUnit.MINUTES));
            Assertions.assertNotNull(bootstrap.cloud().getServiceInfo(ups));
            Assertions.assertNotNull(bootstrap.cloud().getServiceInfo(scope + "-" + ups));

            bootstrap.removeUps(ups);
            bootstrap.removeUps(scope + "-" + ups);
            bootstrap.removeUps(uuid + "-" + ups);
            Assertions.assertTrue(bootstrap.cloud().getServiceInfos().isEmpty());

            try {
                bootstrap.serviceInfoByName(ups).timeout(Duration.ofMillis(1)).hasElements().block();
                Assertions.fail();
            } catch (Exception err) {
                Assertions.assertTrue(ExceptionUtils.getRootCause(err).getClass().equals(TimeoutException.class));
            }
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void plainServiceInfo() {
        PlainServiceInfo si1 = new PlainServiceInfo("key", "https://a.b/c?d=e");
        PlainServiceInfo si2 = new PlainServiceInfo("key", "https://a.b/c?d=e1&d=e2");
        PlainServiceInfo si3 = new PlainServiceInfo("key", "https://a.b?d=e");

        Flux<PlainServiceInfo> flux = Flux
                .fromIterable(Arrays.asList(si1, si1, si1, si2, si3))
                .log()
                .distinctUntilChanged(Function.identity(), new BiPredicate<PlainServiceInfo, PlainServiceInfo>() {
                    @Override
                    public boolean test(PlainServiceInfo t, PlainServiceInfo u) {
                        return UPSs.isEquals(t, u);
                    }
                })
                .distinctUntilChanged(Function.identity(), new BiPredicate<PlainServiceInfo, PlainServiceInfo>() {
                    @Override
                    public boolean test(PlainServiceInfo s1, PlainServiceInfo s2) {
                        try {
                            return UPSs.hashCode(s1) == UPSs.hashCode(s2);
                        } catch (IOException err) {
                            throw new UndeclaredThrowableException(err);
                        }
                    }
                });

        AtomicLong l = new AtomicLong();
        flux.subscribe(new Consumer<PlainServiceInfo>() {
            @Override
            public void accept(PlainServiceInfo t) {
                logger.info("accepting UPS: {}", t);
                l.incrementAndGet();
            }
        });

        flux.subscribe();
        Assertions.assertEquals(3, l.get());
    }

    @Test
    void rawServiceInfo() {
        RawServiceInfo si1 = new RawServiceInfo("key", "a".getBytes());
        RawServiceInfo si2 = new RawServiceInfo("key", "b".getBytes());
        RawServiceInfo si3 = new RawServiceInfo("key", "c".getBytes());

        Flux<RawServiceInfo> flux = Flux
                .fromIterable(Arrays.asList(si1, si1, si1, si2, si2, si3))
                .log()
                .distinctUntilChanged(Function.identity(), new BiPredicate<RawServiceInfo, RawServiceInfo>() {
                    @Override
                    public boolean test(RawServiceInfo t, RawServiceInfo u) {
                        return UPSs.isEquals(t, u);
                    }
                })
                .distinctUntilChanged(Function.identity(), new BiPredicate<RawServiceInfo, RawServiceInfo>() {
                    @Override
                    public boolean test(RawServiceInfo s1, RawServiceInfo s2) {
                        try {
                            return UPSs.hashCode(s1) == UPSs.hashCode(s2);
                        } catch (IOException err) {
                            throw new UndeclaredThrowableException(err);
                        }
                    }
                });

        AtomicLong l = new AtomicLong();
        flux.subscribe(new Consumer<RawServiceInfo>() {
            @Override
            public void accept(RawServiceInfo t) {
                logger.info("accepting UPS: {}", t.getId());
                l.incrementAndGet();
            }
        });

        flux.subscribe();
        Assertions.assertEquals(3, l.get());
    }

    @Test
    void pg() {
        PostgresqlServiceInfo si1 = new PostgresqlServiceInfo("key", "postgres://app_owner:app_owner@127.0.0.1:5432/defaultdb1");
        PostgresqlServiceInfo si2 = new PostgresqlServiceInfo("key", "postgres://app_owner:app_owner2@127.0.0.1:5432/defaultdb");
        PostgresqlServiceInfo si3 = new PostgresqlServiceInfo("key", "postgres://app_owner:app_owner@*********:5432/defaultdb");

        Flux<PostgresqlServiceInfo> flux = Flux
                .fromIterable(Arrays.asList(si1, si1, si1, si2, si2, si3))
                .log()
                .distinctUntilChanged(Function.identity(), (t, u) -> {
                    return UPSs.isEquals(t, u);
                })
                .distinctUntilChanged(Function.identity(), new BiPredicate<PostgresqlServiceInfo, PostgresqlServiceInfo>() {
                    @Override
                    public boolean test(PostgresqlServiceInfo s1, PostgresqlServiceInfo s2) {
                        try {
                            return UPSs.hashCode(s1) == UPSs.hashCode(s2);
                        } catch (IOException err) {
                            throw new UndeclaredThrowableException(err);
                        }
                    }
                });

        AtomicLong l = new AtomicLong();
        flux.subscribe(new Consumer<PostgresqlServiceInfo>() {
            @Override
            public void accept(PostgresqlServiceInfo t) {
                logger.info("accepting UPS: {}", t.getId());
                l.incrementAndGet();
            }
        });

        flux.subscribe();
        Assertions.assertEquals(3, l.get());
    }
}
