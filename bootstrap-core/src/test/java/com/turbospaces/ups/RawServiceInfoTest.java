package com.turbospaces.ups;

import java.io.IOException;
import java.util.Arrays;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import api.v1.ApiFactory;

class RawServiceInfoTest {
    @Test
    void chars() throws IOException {
        RawServiceInfo si = new RawServiceInfo(UPSs.MAXMIND, ("""
            a\
            b\
            c""").getBytes());
        Assertions.assertEquals("abc", si.read());
        Assertions.assertEquals("abc", IOUtils.toString(si.openBufferedStream()));
    }
    @Test
    void bytes() throws IOException {
        byte[] array = new byte[16];
        ApiFactory.RANDOM.nextBytes(array);

        RawServiceInfo si = new RawServiceInfo(UPSs.MAXMIND, array);
        Assertions.assertTrue(Arrays.equals(array, si.toByteSource().read()));
    }
}
