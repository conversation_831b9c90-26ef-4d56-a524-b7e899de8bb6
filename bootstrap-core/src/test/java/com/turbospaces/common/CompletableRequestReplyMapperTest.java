package com.turbospaces.common;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.CompletableRequestReplyMapper;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.rpc.RequestReplyTimeout;

import io.micrometer.core.instrument.search.RequiredSearch;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

public class CompletableRequestReplyMapperTest {
    Logger logger = LoggerFactory.getLogger(getClass());
    CompletableRequestReplyMapper<UUID, ResponseWrapperFacade> mapper;

    @AfterEach
    public void after() throws Exception {
        mapper.destroy();
    }

    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();

        mapper = new DefaultRequestReplyMapper(props, meterRegistry);
        mapper.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
        mapper.afterPropertiesSet();

        UUID key = PlatformUtil.randomUUID();
        CountDownLatch l = new CountDownLatch(1);
        ListenableFuture<ResponseWrapperFacade> f = mapper.acquire(key, Duration.ofSeconds(15));
        f.addListener(new Runnable() {
            @Override
            public void run() {
                l.countDown();
            }
        }, MoreExecutors.directExecutor());
        Assertions.assertEquals(1, mapper.pendingCount());
        MDC.put(MdcTags.MDC_OPERATION, "test");
        mapper.complete(key, Mockito.mock(ResponseWrapperFacade.class));
        Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));
        Assertions.assertEquals(0, mapper.pendingCount());

        RequiredSearch search1 = meterRegistry.get("reply.complete.latency").tag(MdcTags.MDC_OPERATION, "test");

        Assertions.assertNotNull(search1.timer());
    }

    @Test
    public void timeoutRaw() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();

        mapper = new CompletableRequestReplyMapper<>(props, meterRegistry);
        mapper.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
        mapper.afterPropertiesSet();

        UUID key = PlatformUtil.randomUUID();
        CountDownLatch l = new CountDownLatch(1);
        ListenableFuture<ResponseWrapperFacade> f = mapper.acquire(key, Duration.ofMillis(1));
        f.addListener(new Runnable() {
            @Override
            public void run() {
                try {
                    f.get();
                } catch (Exception err) {
                    RequestReplyTimeout timeout = (RequestReplyTimeout) err.getCause();
                    logger.error(timeout.getMessage(), timeout);
                    l.countDown();
                }
            }
        }, MoreExecutors.directExecutor());
        Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));

        Awaitility
                .waitAtMost(Duration.ofSeconds(1))
                .until(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        return mapper.pendingCount() == 0;
                    }
                });
    }

    @Test
    public void timeoutFluent() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();

        mapper = new DefaultRequestReplyMapper(props, meterRegistry);
        mapper.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
        mapper.afterPropertiesSet();

        ExecutorService executor = Executors.newSingleThreadExecutor();

        UUID key = PlatformUtil.randomUUID();
        CountDownLatch l = new CountDownLatch(1);
        FluentFuture<ResponseWrapperFacade> f = FluentFuture.from(mapper.acquire(key, Duration.ofMillis(1)));
        f.addCallback(new FutureCallback<ResponseWrapperFacade>() {
            @Override
            public void onSuccess(ResponseWrapperFacade result) {

            }
            @Override
            public void onFailure(Throwable t) {
                RequestReplyTimeout timeout = (RequestReplyTimeout) ExceptionUtils.getRootCause(t);
                logger.error(timeout.getMessage(), timeout);
                l.countDown();
            }
        }, executor);
        Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));

        Awaitility
                .waitAtMost(Duration.ofSeconds(1))
                .until(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        return mapper.pendingCount() == 0;
                    }
                });

        executor.shutdown();
    }

    @Test
    public void exceptionally() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();

        mapper = new DefaultRequestReplyMapper(props, meterRegistry);
        mapper.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
        mapper.afterPropertiesSet();

        ExecutorService executor = Executors.newSingleThreadExecutor();

        UUID key = PlatformUtil.randomUUID();
        CountDownLatch l = new CountDownLatch(1);
        FluentFuture<ResponseWrapperFacade> f = FluentFuture.from(mapper.acquire(key, Duration.ofMinutes(1)));
        f.addCallback(new FutureCallback<ResponseWrapperFacade>() {
            @Override
            public void onSuccess(ResponseWrapperFacade result) {

            }
            @Override
            public void onFailure(Throwable t) {
                l.countDown();
            }
        }, executor);

        mapper.completeExceptionally(key, new IllegalStateException());
        Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));

        Awaitility
                .waitAtMost(Duration.ofSeconds(1))
                .until(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        return mapper.pendingCount() == 0;
                    }
                });

        executor.shutdown();
    }
}
