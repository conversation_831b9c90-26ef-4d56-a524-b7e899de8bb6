package com.turbospaces.common;

import java.time.Instant;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Maps;
import com.neovisionaries.i18n.CountryCode;

@SuppressWarnings("deprecation")
class PlatformUtilTest {
    @Test
    void iso2() {
        Map<String, String> ISO2_CODES = Maps.newHashMap();

        String[] countries = Locale.getISOCountries();
        for (String country : countries) {
            Locale locale = new Locale(StringUtils.EMPTY, country);
            ISO2_CODES.put(country, locale.getISO3Country().toUpperCase());
        }

        for (Entry<String, String> entry : ISO2_CODES.entrySet()) {
            CountryCode country = CountryCode.getByAlpha2Code(entry.getKey());
            Assertions.assertEquals(entry.getValue(), country.getAlpha3());
        }
    }
    @Test
    void iso3() {
        Map<String, String> ISO3_CODES = Maps.newHashMap();

        String[] countries = Locale.getISOCountries();
        for (String country : countries) {
            Locale locale = new Locale(StringUtils.EMPTY, country);
            ISO3_CODES.put(locale.getISO3Country().toUpperCase(), locale.getCountry().toUpperCase());
        }

        for (Entry<String, String> entry : ISO3_CODES.entrySet()) {
            CountryCode country = CountryCode.getByAlpha3Code(entry.getKey());
            Assertions.assertEquals(entry.getValue(), country.getAlpha2());
        }
    }
    @Test
    public void parseMillis() {
        long now = System.currentTimeMillis();
        Instant dateTime = PlatformUtil.parseTimestamp(Long.toString(now));
        Assertions.assertEquals(now, dateTime.toEpochMilli());
    }
    @Test
    public void parseUnitTime() {
        long now = System.currentTimeMillis() / 1000;
        Instant dateTime = PlatformUtil.parseTimestamp(Long.toString(now));
        Assertions.assertEquals(now * 1000, dateTime.toEpochMilli());
    }
}
