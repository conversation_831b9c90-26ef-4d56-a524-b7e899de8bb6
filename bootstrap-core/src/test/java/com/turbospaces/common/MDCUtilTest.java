package com.turbospaces.common;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.executor.SerialContextWorker;
import com.turbospaces.mdc.MdcUtil;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;

class MDCUtilTest {
    @Test
    void empty() {
        try {
            Map<String, String> mdc = MDC.getCopyOfContextMap();
            ImmutableMap<String, String> toCleanUp = MdcUtil.propagate(mdc);
            MdcUtil.cleanUp(toCleanUp);
        } finally {
            MDC.clear();
        }
    }
    @Test
    void works() throws InterruptedException {
        try {
            MDC.put("k1", "v1");

            ExecutorService threadPool = Executors.newFixedThreadPool(1);
            ImmutableMap<String, String> toCleanUp = MdcUtil.propagate(MDC.getCopyOfContextMap());

            SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();
            SerialContextWorker worker = new SerialContextWorker(AsciiString.cached("k1"), MoreExecutors.listeningDecorator(threadPool), meterRegistry);
            CountDownLatch l1 = new CountDownLatch(1);
            worker.submit(new CheckedRunnable() {
                @Override
                public void run() throws Throwable {
                    Assertions.assertEquals("v1", MDC.get("k1"));
                    l1.countDown();
                    // self schedule with delay guarantee
                    worker.submit(new CheckedRunnable() {
                        @Override
                        public void run() throws Throwable {

                        }
                    });
                    Thread.sleep(1);
                }
            });
            Assertions.assertTrue(l1.await(30, TimeUnit.SECONDS));

            MdcUtil.cleanUp(toCleanUp);

            CountDownLatch l2 = new CountDownLatch(1);
            worker.submit(new CheckedRunnable() {
                @Override
                public void run() throws Throwable {
                    Map<String, String> contextMap = MDC.getCopyOfContextMap();
                    System.out.println(contextMap);
                    Assertions.assertTrue(CollectionUtils.isEmpty(contextMap));
                    l2.countDown();
                }
            });
            Assertions.assertTrue(l2.await(30, TimeUnit.SECONDS));
            Assertions.assertTrue(meterRegistry.timer("worker.queue.delay").measure().iterator().next().getValue() > 0);

        } finally {
            MDC.clear();
        }
    }
    @Test
    void changed() throws InterruptedException {
        try {
            MDC.put("k1", "v1");
            MDC.put("k2", "v2");

            ExecutorService threadPool = Executors.newFixedThreadPool(1);
            SerialContextWorker worker = new SerialContextWorker(AsciiString.cached("k1"), MoreExecutors.listeningDecorator(threadPool), new SimpleMeterRegistry());
            CountDownLatch l = new CountDownLatch(1);
            worker.submit(new CheckedRunnable() {
                @Override
                public void run() throws Throwable {
                    MDC.put("k2", "m2");
                    MDC.put("k3", "m3");

                    Assertions.assertEquals("m2", MDC.get("k2"));
                    Assertions.assertEquals("m3", MDC.get("k3"));
                    l.countDown();
                }
            });

            Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));
        } finally {
            MDC.clear();
        }
    }
}
