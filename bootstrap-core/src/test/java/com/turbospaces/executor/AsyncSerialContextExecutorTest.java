package com.turbospaces.executor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AsyncSerialContextExecutorTest {
    private ScheduledExecutorService scheduler;
    private PlatformExecutorService platform;
    private AsyncSerialContextWorker<String> executor;
    private MeterRegistry meterRegistry;
    private Retry retry;

    @BeforeEach
    public void beforeEach() throws Exception {
        RetryConfig config = RetryConfig.custom().maxAttempts(3).waitDuration(Duration.ofMillis(1)).build();
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        retry = Retry.of(getClass().getSimpleName(), config);
        meterRegistry = new SimpleMeterRegistry();

        platform = new DefaultPlatformExecutorService(props, meterRegistry);
        platform.setBeanName(PlatformUtil.randomUUID().toString());
        platform.afterPropertiesSet();

        executor = new AsyncSerialContextWorker<>(AsciiString.of(PlatformUtil.randomUUID().toString()), platform, meterRegistry);
        scheduler = Executors.newSingleThreadScheduledExecutor();
    }
    @AfterEach
    public void afterEach() throws Exception {
        scheduler.shutdown();
        platform.destroy();
    }

    @Test
    void testRetryInMiddleOfChain() throws Exception {
        AtomicInteger attempts = new AtomicInteger();
        Supplier<ListenableFuture<String>> failingTask = new Supplier<>() {
            @Override
            public ListenableFuture<String> get() {
                int attempt = attempts.incrementAndGet();
                log.info("Creating task for attempt: {}", attempt);
                if (attempt <= 2) {
                    var error = new RuntimeException("Deliberate failure on attempt " + attempt);
                    log.info("Failing with error on attempt: {}", attempt);
                    return Futures.immediateFailedFuture(error);
                }
                log.info("Succeeding on attempt: {}", attempt);
                return Futures.immediateFuture("Task 2 succeeded on attempt " + attempt);
            }
        };

        var taskSuppliers = Lists.newArrayList(() -> Futures.immediateFuture("Task 1 result"), failingTask, () -> Futures.immediateFuture("Task 3 result"));
        var result = executor.submitAll(retry, scheduler, taskSuppliers);
        var results = result.get(30, TimeUnit.SECONDS);

        assertEquals(3, results.size(), "Should have 3 results");
        assertEquals("Task 1 result", results.get(0), "First task result incorrect");
        assertEquals("Task 2 succeeded on attempt 3", results.get(1), "Second task result incorrect");
        assertEquals("Task 3 result", results.get(2), "Third task result incorrect");

        assertEquals(3, attempts.get(), "Should have attempted the failing task exactly 3 times");
    }

    @Test
    void testRetryInMiddleOfChainForSubmitStageAll() throws Exception {
        AtomicInteger attempts = new AtomicInteger();
        Supplier<CompletionStage<String>> failingTask = new Supplier<>() {
            @Override
            public CompletionStage<String> get() {
                int attempt = attempts.incrementAndGet();
                log.info("Creating task for attempt: {}", attempt);
                if (attempt <= 2) {
                    var error = new RuntimeException("Deliberate failure on attempt " + attempt);
                    log.info("Failing with error on attempt: {}", attempt);
                    return CompletableFuture.failedStage(error);
                }
                log.info("Succeeding on attempt: {}", attempt);
                return CompletableFuture.completedStage("Task 2 succeeded on attempt " + attempt);
            }
        };

        var taskSuppliers = Lists.newArrayList(() -> CompletableFuture.completedStage("Task 1 result"), failingTask, () -> CompletableFuture.completedStage("Task 3 result"));
        var result = executor.submitStageAll(retry, scheduler, taskSuppliers);
        var results = result.toCompletableFuture().get(30, TimeUnit.SECONDS);

        assertEquals(3, results.size(), "Should have 3 results");
        assertEquals("Task 1 result", results.get(0), "First task result incorrect");
        assertEquals("Task 2 succeeded on attempt 3", results.get(1), "Second task result incorrect");
        assertEquals("Task 3 result", results.get(2), "Third task result incorrect");

        assertEquals(3, attempts.get(), "Should have attempted the failing task exactly 3 times");
    }


    @Test
    void testRetryExceedsMaxAttempts() throws Exception {
        AtomicInteger attempts = new AtomicInteger();
        Supplier<ListenableFuture<String>> alwaysFailingTask = new Supplier<>() {
            @Override
            public ListenableFuture<String> get() {
                int attempt = attempts.incrementAndGet();
                log.info("Creating failing task for attempt: {}", attempt);
                return Futures.immediateFailedFuture(new RuntimeException("Always failing task at attempt " + attempt));
            }
        };

        var result = executor.submit(retry, scheduler, alwaysFailingTask);

        try {
            result.get(30, TimeUnit.SECONDS);
            fail("Expected ExecutionException");
        } catch (ExecutionException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertTrue(e.getCause().getMessage().contains("Always failing task"));
        }

        assertEquals(3, attempts.get(), "Should have attempted the task exactly 3 times");
    }

    @Test
    void testRetryExceedsMaxAttemptsForSubmitStage() throws Exception {
        AtomicInteger attempts = new AtomicInteger();
        Supplier<CompletionStage<String>> alwaysFailingTask = new Supplier<>() {
            @Override
            public CompletionStage<String> get() {
                int attempt = attempts.incrementAndGet();
                log.info("Creating failing task for attempt: {}", attempt);
                return CompletableFuture.failedStage(new RuntimeException("Always failing task at attempt " + attempt));
            }
        };

        var result = executor.submitStage(retry, scheduler, alwaysFailingTask);

        try {
            result.toCompletableFuture().get(30, TimeUnit.SECONDS);
            fail("Expected ExecutionException");
        } catch (ExecutionException e) {
            assertInstanceOf(RuntimeException.class, e.getCause());
            assertTrue(e.getCause().getMessage().contains("Always failing task"));
        }

        assertEquals(3, attempts.get(), "Should have attempted the task exactly 3 times");
    }

    @Test
    void testSubmitAllWithEmptyList() throws Exception {
        ListenableFuture<List<String>> result = executor.submitAll(retry, scheduler, List.of());
        List<String> results = result.get(1, TimeUnit.SECONDS);
        assertTrue(results.isEmpty(), "Result should be an empty list");
    }

    @Test
    void testSubmitAllWithEmptyListForSubmitStageAll() throws Exception {
        CompletionStage<List<String>> result = executor.submitStageAll(retry, scheduler, List.of());
        List<String> results = result.toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertTrue(results.isEmpty(), "Result should be an empty list");
    }

    @Test
    void testTasksExecuteInOrder() throws Exception {
        CountDownLatch task1Latch = new CountDownLatch(1);
        CountDownLatch task2Latch = new CountDownLatch(1);
        AtomicInteger executionOrder = new AtomicInteger();

        Supplier<ListenableFuture<String>> task1Supplier = () -> Futures.transformAsync(
                Futures.immediateFuture(null),
                input -> {
                    log.info("Executing task 1");
                    task1Latch.await(5, TimeUnit.SECONDS);
                    executionOrder.compareAndSet(0, 1);
                    return Futures.immediateFuture("Task 1 complete");
                },
                MoreExecutors.directExecutor());

        Supplier<ListenableFuture<String>> task2Supplier = () -> Futures.transformAsync(
                Futures.immediateFuture(null),
                input -> {
                    log.info("Executing task 2");
                    executionOrder.compareAndSet(1, 2);
                    task2Latch.countDown();
                    return Futures.immediateFuture("Task 2 complete");
                },
                MoreExecutors.directExecutor());

        ListenableFuture<List<String>> result = executor.submitAll(retry, scheduler, List.of(task1Supplier, task2Supplier));

        task1Latch.countDown();
        assertTrue(task2Latch.await(30, TimeUnit.SECONDS), "Task 2 should complete");

        List<String> results = result.get(5, TimeUnit.SECONDS);
        assertEquals(2, executionOrder.get(), "Tasks should execute in sequence");
        assertEquals(2, results.size(), "Should have 2 results");
        assertEquals("Task 1 complete", results.get(0), "First result incorrect");
        assertEquals("Task 2 complete", results.get(1), "Second result incorrect");
    }

    @Test
    void testTasksExecuteInOrderForSubmitStageAll() throws Exception {
        CountDownLatch task1Latch = new CountDownLatch(1);
        CountDownLatch task2Latch = new CountDownLatch(1);
        AtomicInteger executionOrder = new AtomicInteger();

        Supplier<CompletionStage<String>> task1Supplier = () -> CompletableFuture.supplyAsync(() -> {
            log.info("Executing stage task 1");
            try {
                task1Latch.await(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            executionOrder.compareAndSet(0, 1);
            return "Task 1 complete";
        });

        Supplier<CompletionStage<String>> task2Supplier = () -> CompletableFuture.supplyAsync(() -> {
            log.info("Executing stage task 2");
            executionOrder.compareAndSet(1, 2);
            task2Latch.countDown();
            return "Task 2 complete";
        });

        CompletionStage<List<String>> result = executor.submitStageAll(retry, scheduler, List.of(task1Supplier, task2Supplier));

        task1Latch.countDown();
        assertTrue(task2Latch.await(30, TimeUnit.SECONDS), "Task 2 should complete");

        List<String> results = result.toCompletableFuture().get(5, TimeUnit.SECONDS);
        assertEquals(2, executionOrder.get(), "Tasks should execute in sequence");
        assertEquals(2, results.size(), "Should have 2 results");
        assertEquals("Task 1 complete", results.get(0), "First result incorrect");
        assertEquals("Task 2 complete", results.get(1), "Second result incorrect");
    }
}
