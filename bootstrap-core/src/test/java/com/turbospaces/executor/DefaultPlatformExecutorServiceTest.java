package com.turbospaces.executor;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Uninterruptibles;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.vavr.CheckedRunnable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class DefaultPlatformExecutorServiceTest {
    @Test
    void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        MeterRegistry meterRegistry = new SimpleMeterRegistry();

        int tasks = 1024;
        CountDownLatch latch = new CountDownLatch(tasks);

        DefaultPlatformExecutorService executor = new DefaultPlatformExecutorService(props, meterRegistry);
        executor.setBeanName(PlatformUtil.randomUUID().toString());
        executor.afterPropertiesSet();

        ThreadPoolContextWorker worker = new ThreadPoolContextWorker(props, meterRegistry, executor);
        worker.afterPropertiesSet();

        AtomicInteger success = new AtomicInteger();
        AtomicInteger failure = new AtomicInteger();
        try {
            for (int i = 0; i < tasks; i++) {
                int temp = i;
                FluentFuture.from(worker.submit(new CheckedRunnable() {
                    @Override
                    public void run() throws Throwable {
                        Uninterruptibles.sleepUninterruptibly(Duration.ofMillis(1));
                        if (temp % 2 == 0) {
                            throw new IllegalArgumentException();
                        }
                    }
                })).addCallback(new FutureCallback<Object>() {
                    @Override
                    public void onSuccess(Object result) {
                        success.incrementAndGet();
                        latch.countDown();
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        failure.incrementAndGet();
                        Assertions.assertInstanceOf(IllegalArgumentException.class, t);
                        latch.countDown();
                    }
                }, MoreExecutors.directExecutor());
            }

            Assertions.assertTrue(latch.await(1, TimeUnit.MINUTES));
            Assertions.assertEquals(success.get(), failure.get());
        } finally {
            executor.destroy();
        }
    }
    @Test
    void rejected() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        MeterRegistry meterRegistry = new SimpleMeterRegistry();

        int tasks = 1024;
        CountDownLatch latch = new CountDownLatch(tasks);
        String beanName = PlatformUtil.randomUUID().toString();

        cfg.setLocalProperty(beanName + "." + "platform.min-size", 0);
        cfg.setLocalProperty(beanName + "." + "platform.max-size", 4);


        try(DefaultPlatformExecutorService executor = new DefaultPlatformExecutorService(props, meterRegistry)) {
            executor.setBeanName(beanName);
            executor.afterPropertiesSet();

            AtomicInteger failure = new AtomicInteger();

            for (int i = 0; i < tasks; i++) {
                FluentFuture.from(executor.submit(new CheckedRunnable() {
                    @Override
                    public void run() throws Throwable {
                        Uninterruptibles.sleepUninterruptibly(Duration.ofMillis(1));
                    }
                })).addCallback(new FutureCallback<Object>() {
                    @Override
                    public void onSuccess(Object result) {
                        latch.countDown();
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        failure.incrementAndGet();
                        Assertions.assertInstanceOf(RejectedExecutionException.class, t);
                        latch.countDown();
                    }
                }, MoreExecutors.directExecutor());
            }

            log.info("failed tasks count: {}", failure.get());
            Assertions.assertTrue(latch.await(1, TimeUnit.MINUTES));
            Assertions.assertTrue(failure.get() > tasks / 2);
        } 
    }
}
