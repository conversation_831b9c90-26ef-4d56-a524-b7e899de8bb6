package com.turbospaces.executor;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.vavr.CheckedRunnable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConfigurablePlatformTest {
    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();
        cfg.setLocalProperty(props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.getKey(), Duration.ofSeconds(1));

        String beanName = PlatformUtil.randomUUID().toString();

        cfg.setLocalProperty(beanName + "." + "platform.min-size", 1);
        cfg.setLocalProperty(beanName + "." + "platform.max-size", 2);
        cfg.setLocalProperty(beanName + "." + "platform.max-idle", Duration.ofMillis(1));

        try (DefaultPlatformExecutorService platform = new DefaultPlatformExecutorService(props, meterRegistry)) {
            platform.setBeanName(beanName);
            platform.afterPropertiesSet();

            CountDownLatch semaphore = new CountDownLatch(1);
            CountDownLatch l = new CountDownLatch(1);
            CountDownLatch interrupted = new CountDownLatch(1);
            platform.submit(new CheckedRunnable() {
                @Override
                public void run() throws Exception {
                    System.out.println(Thread.currentThread().getName() + " ::: work");
                    Assertions.assertTrue(semaphore.await(30, TimeUnit.SECONDS));
                    l.countDown();
                }
            });
            platform.execute(new Runnable() {
                @Override
                public void run() {
                    System.out.println(Thread.currentThread().getName() + " ::: execute");
                    semaphore.countDown();
                }
            });
            Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));
            CountDownLatch started = new CountDownLatch(1);
            platform.submit(new CheckedRunnable() {
                @Override
                public void run() {
                    synchronized (this) {
                        try {
                            log.debug("about to hang forever ...");
                            Thread.sleep(100);
                            log.debug("count down to mark started ...");
                            started.countDown();
                            wait();
                        } catch (InterruptedException err) {
                            interrupted.countDown();
                        }
                    }
                }
            });
            Assertions.assertTrue(started.await(30, TimeUnit.SECONDS));
            platform.destroy();
            Assertions.assertTrue(interrupted.await(30, TimeUnit.SECONDS));
        }
    }
}
