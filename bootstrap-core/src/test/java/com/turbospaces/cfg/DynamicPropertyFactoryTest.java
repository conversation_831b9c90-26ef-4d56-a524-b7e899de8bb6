package com.turbospaces.cfg;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;

import com.netflix.archaius.api.Property;

public class DynamicPropertyFactoryTest {
    private static final String PROPERTY_NAME = "soft-kyc-required-before-purchase.percentage.users-applied";
    private static final int DEFAULT_VALUE = 1;
    private static final String WRONG_VALUE = "50akDFs83$#%#";
    private static final int CORRECT_VALUE = 50;

    @Test
    public void testOrElseGetCorrectValue() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, CORRECT_VALUE);

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.orElse(DEFAULT_VALUE).get();

        assertEquals(CORRECT_VALUE, integerValue);
    }

    @Test
    public void testOrElseGetWrongValueDefault() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, WRONG_VALUE);

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.orElse(DEFAULT_VALUE).get();

        assertEquals(DEFAULT_VALUE, integerValue);
    }

    @Test
    public void testOrElseGetNullValueDefault() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, null);

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.orElse(DEFAULT_VALUE).get();

        assertEquals(DEFAULT_VALUE, integerValue);
    }

    @Test
    public void testOrElseGetEmptyValueDefault() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, "");

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.orElse(DEFAULT_VALUE).get();

        assertEquals(DEFAULT_VALUE, integerValue);
    }

    @Test
    public void testGetCorrectValue() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, CORRECT_VALUE);

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.orElse(DEFAULT_VALUE).get();

        assertEquals(CORRECT_VALUE, integerValue);
    }

    @Test
    public void testWrongValueDefault() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, WRONG_VALUE);

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.orElse(DEFAULT_VALUE).get();

        assertEquals(DEFAULT_VALUE, integerValue);
    }

    @Test
    public void testGetNullValueNull() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, null);

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.get();

        assertNull(integerValue);
    }

    @Test
    public void testGetEmptyValueNull() {
        DynamicPropertyFactory factory = createDynamicPropertyFactory(PROPERTY_NAME, "");

        Property<Integer> integerProperty = factory.get(PROPERTY_NAME, Integer.class);
        Integer integerValue = integerProperty.get();

        assertNull(integerValue);
    }

    private static DynamicPropertyFactory createDynamicPropertyFactory(String name, String value) {
        ApplicationConfig cfg = ApplicationConfig.mock();
        cfg.setLocalProperty(name, value);
        return cfg.factory();
    }


    private static DynamicPropertyFactory createDynamicPropertyFactory(String name, int value) {
        ApplicationConfig cfg = ApplicationConfig.mock();
        cfg.setLocalProperty(name, String.valueOf(value));
        return cfg.factory();
    }
}