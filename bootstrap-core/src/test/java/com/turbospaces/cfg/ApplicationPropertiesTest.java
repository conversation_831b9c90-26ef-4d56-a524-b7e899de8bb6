package com.turbospaces.cfg;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.turbospaces.boot.SimpleBootstrap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.netflix.archaius.api.Property;

public class ApplicationPropertiesTest {
    @Test
    public void consistentNaming() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        Set<String> whitelist = Arrays.asList(FieldUtils.getAllFields(CloudOptions.class)).stream().map(Field::getName).collect(Collectors.toSet());

        Field[] fields = FieldUtils.getAllFields(ApplicationProperties.class);
        for (Field f : fields) {
            if (Modifier.isPublic(f.getModifiers()) && BooleanUtils.isFalse(Modifier.isStatic(f.getModifiers()))) {
                Property<?> prop = (Property<?>) FieldUtils.readField(f, props);
                prop.get();
                String fieldName = f.getName();
                String originalKey = prop.getKey();
                originalKey = originalKey.replace(ScopedProperty.SCOPE_PLACEHOLDER + ".", "");
                originalKey = originalKey.replace("." + ScopedProperty.SCOPE_PLACEHOLDER, "");
                originalKey = originalKey.replace(ScopedProperty.SCOPE_PLACEHOLDER, "");
                String propKey = originalKey.toUpperCase().replaceAll("\\.", "_").replaceAll("-", "_");

                if (BooleanUtils.isFalse(fieldName.equals(propKey))) {
                    if (BooleanUtils.isFalse(whitelist.contains(fieldName))) {
                        Assertions.assertEquals(fieldName, propKey);
                    }
                }
            }
        }
    }

    @Test
    public void listOfInts() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();

        Property<List<Integer>> p = cfg.factory().listOfInts("key").orElse(Lists.newArrayList(1, 2));

        Iterator<Integer> it = p.get().iterator();
        int it1 = it.next();
        int it2 = it.next();
        Assertions.assertEquals(1, it1);
        Assertions.assertEquals(2, it2);

        cfg.setLocalProperty(p.getKey(), "1, 3, 5");
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        int it3 = it.next();
        Assertions.assertEquals(1, it1);
        Assertions.assertEquals(3, it2);
        Assertions.assertEquals(5, it3);

        cfg.setLocalProperty(p.getKey(), "1,5,9");
        it = cfg.getList(p.getKey(), Integer.class).iterator();
        it1 = it.next();
        it2 = it.next();
        it3 = it.next();
        Assertions.assertEquals(1, it1);
        Assertions.assertEquals(5, it2);
        Assertions.assertEquals(9, it3);
    }

    @Test
    public void listOfLong() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();

        Property<List<Long>> p = cfg.factory().listOfLongs("key").orElse(Lists.newArrayList(1L, Long.MAX_VALUE));

        Iterator<Long> it = p.get().iterator();
        long it1 = it.next();
        long it2 = it.next();
        Assertions.assertEquals(1, it1);
        Assertions.assertEquals(Long.MAX_VALUE, it2);

        cfg.setLocalProperty(p.getKey(), "1, 3, 9223372036854775807");
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        long it3 = it.next();
        Assertions.assertEquals(1, it1);
        Assertions.assertEquals(3, it2);
        Assertions.assertEquals(Long.MAX_VALUE, it3);

        cfg.setLocalProperty(p.getKey(), "1,5,9223372036854775807");
        it = cfg.getList(p.getKey(), Long.class).iterator();
        it1 = it.next();
        it2 = it.next();
        it3 = it.next();
        Assertions.assertEquals(1, it1);
        Assertions.assertEquals(5, it2);
        Assertions.assertEquals(Long.MAX_VALUE, it3);
    }

    @Test
    public void listOfString() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();

        Property<List<String>> p = cfg.factory().listOfStrings("key").orElse(Lists.newArrayList("k1", "k2"));

        Iterator<String> it = p.get().iterator();
        String it1 = it.next();
        String it2 = it.next();
        Assertions.assertEquals("k1", it1);
        Assertions.assertEquals("k2", it2);

        cfg.setLocalProperty(p.getKey(), "k1, k2, k3");
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        String it3 = it.next();
        Assertions.assertEquals("k1", it1);
        Assertions.assertEquals("k2", it2);
        Assertions.assertEquals("k3", it3);

        cfg.setLocalProperty(p.getKey(), "k1,k2,k5");
        it = cfg.getList(p.getKey(), String.class).iterator();
        it1 = it.next();
        it2 = it.next();
        it3 = it.next();
        Assertions.assertEquals("k1", it1);
        Assertions.assertEquals("k2", it2);
        Assertions.assertEquals("k5", it3);
    }

    @Test
    public void listOfDoubles() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();

        Property<List<Double>> p = cfg.factory().listOfDoubles("key").orElse(Lists.newArrayList(1D, 2D));

        Iterator<Double> it = p.get().iterator();
        Double it1 = it.next();
        Double it2 = it.next();
        Assertions.assertEquals(1D, it1);
        Assertions.assertEquals(2D, it2);

        cfg.setLocalProperty(p.getKey(), "1, 2, 3");
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        Double it3 = it.next();
        Assertions.assertEquals(1D, it1);
        Assertions.assertEquals(2D, it2);
        Assertions.assertEquals(3D, it3);

        cfg.setLocalProperty(p.getKey(), "1,2,5");
        it = cfg.getList(p.getKey(), Double.class).iterator();
        it1 = it.next();
        it2 = it.next();
        it3 = it.next();
        Assertions.assertEquals(1D, it1);
        Assertions.assertEquals(2D, it2);
        Assertions.assertEquals(5D, it3);
    }

    @Test
    public void listOfBigDecimal() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();

        Property<List<BigDecimal>> p = cfg.factory().listOfBigDecimals("key").orElse(Lists.newArrayList(BigDecimal.ONE, BigDecimal.ZERO));

        Iterator<BigDecimal> it = p.get().iterator();
        BigDecimal it1 = it.next();
        BigDecimal it2 = it.next();
        Assertions.assertEquals(BigDecimal.ONE, it1);
        Assertions.assertEquals(BigDecimal.ZERO, it2);

        cfg.setLocalProperty(p.getKey(), "1, 2, 3");
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        BigDecimal it3 = it.next();
        Assertions.assertEquals(BigDecimal.valueOf(1), it1);
        Assertions.assertEquals(BigDecimal.valueOf(2), it2);
        Assertions.assertEquals(BigDecimal.valueOf(3), it3);

        cfg.setLocalProperty(p.getKey(), "1,2,5");
        it = cfg.getList(p.getKey(), BigDecimal.class).iterator();
        it1 = it.next();
        it2 = it.next();
        it3 = it.next();
        Assertions.assertEquals(BigDecimal.valueOf(1), it1);
        Assertions.assertEquals(BigDecimal.valueOf(2), it2);
        Assertions.assertEquals(BigDecimal.valueOf(5), it3);
    }

    @Test
    public void listOfLocalDates() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();

        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        Property<List<LocalDate>> p = props.factory().listOfLocalDates("key").orElse(Lists.newArrayList(LocalDate.now(ZoneOffset.UTC), LocalDate.now(ZoneOffset.UTC).plusDays(1)));

        Iterator<LocalDate> it = p.get().iterator();
        LocalDate it1 = it.next();
        LocalDate it2 = it.next();
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC), it1);
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC).plusDays(1), it2);

        cfg.setLocalProperty(p.getKey(), Joiner.on(',').join(LocalDate.now(ZoneOffset.UTC).toString(), LocalDate.now(ZoneOffset.UTC).plusDays(1), LocalDate.now(ZoneOffset.UTC).plusDays(2)));
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        LocalDate it3 = it.next();
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC), it1);
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC).plusDays(1), it2);
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC).plusDays(2), it3);

        cfg.setLocalProperty(p.getKey(), Joiner.on(',').join(LocalDate.now(ZoneOffset.UTC).toString(), LocalDate.now(ZoneOffset.UTC).plusDays(1), LocalDate.now(ZoneOffset.UTC).plusDays(5)));
        it = cfg.getList(p.getKey(), LocalDate.class).iterator();
        it1 = it.next();
        it2 = it.next();
        it3 = it.next();
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC), it1);
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC).plusDays(1), it2);
        Assertions.assertEquals(LocalDate.now(ZoneOffset.UTC).plusDays(5), it3);
    }

    @Test
    public void listOfPatterns() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        Property<List<Pattern>> p = props.factory().listOfPatterns("key").orElse(Lists.newArrayList(Pattern.compile("[a-zA-Z]{1}"), Pattern.compile("[a-zA-Z]{2}")));

        Iterator<Pattern> it = p.get().iterator();
        Pattern it1 = it.next();
        Pattern it2 = it.next();
        Assertions.assertEquals("[a-zA-Z]{1}", it1.pattern());
        Assertions.assertEquals("[a-zA-Z]{2}", it2.pattern());

        cfg.setLocalProperty(p.getKey(), Joiner.on(',').join("[a-zA-Z]{1}", "[a-zA-Z]{3}", "[a-zA-Z]{5}"));
        it = p.get().iterator();
        it1 = it.next();
        it2 = it.next();
        Pattern it3 = it.next();
        Assertions.assertEquals("[a-zA-Z]{1}", it1.pattern());
        Assertions.assertEquals("[a-zA-Z]{3}", it2.pattern());
        Assertions.assertEquals("[a-zA-Z]{5}", it3.pattern());

        CountDownLatch l = new CountDownLatch(1);
        p.subscribe(new Consumer<List<Pattern>>() {
            @Override
            public void accept(List<Pattern> t) {
                Iterator<Pattern> i = p.get().iterator();

                Pattern i1 = i.next();
                Pattern i2 = i.next();
                Pattern i3 = i.next();

                Assertions.assertEquals("[a-zA-Z]{1}", i1.pattern());
                Assertions.assertEquals("[a-zA-Z]{5}", i2.pattern());
                Assertions.assertEquals("[a-zA-Z]{9}", i3.pattern());

                l.countDown();
            }
        });

        cfg.setLocalProperty(p.getKey(), Joiner.on(',').join("[a-zA-Z]{1}", "[a-zA-Z]{5}", "[a-zA-Z]{9}"));
        Assertions.assertTrue(l.await(1, TimeUnit.MINUTES));
    }

    @Test
    public void testGetValueWithDefaultsValue() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        final String defaultValue = "default";
        final String correctValue = "correct";
        String propertyName = "key";
        cfg.setLocalProperty(propertyName, correctValue);

        final String value = cfg.getValueWithDefault(String.class, propertyName, defaultValue);

        assertEquals(correctValue, value);
    }

    @Test
    public void testGetValueWithDefaultsAbsentPropertyDefault() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        final String defaultValue = "default";

        String value = cfg.getValueWithDefault(String.class, "key", defaultValue);

        assertEquals(defaultValue, value);
    }

    @Test
    public void testGetValueWithDefaultsWrongDefault() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        final String defaultValue = "default";
        final String wrongValue = "50kj$%DF";
        String propertyName = "key";
        cfg.setLocalProperty(propertyName, wrongValue);

        final String value = cfg.getValueWithDefault(int.class, propertyName, defaultValue);

        assertEquals(defaultValue, value);
    }

    @Test
    public void testFragmentProperties() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        var props = new ServiceProperties(cfg.factory());
        var boot = new SimpleBootstrap(props);
        boot.run();

        // check that fragment properties are taken into account
        Assertions.assertEquals("property",  props.readFieldAsMap().get("some.property"));

        // check that fragment properties are updated
        Assertions.assertEquals("property", props.fragment.SOME_PROPERTY.get());
        cfg.setLocalProperty(props.fragment.SOME_PROPERTY.getKey(), "newProperty");
        Assertions.assertEquals("newProperty", props.fragment.SOME_PROPERTY.get());
    }

    public static class ServiceFragment implements FragmentProperties {
        public final Property<String> SOME_PROPERTY;

        public ServiceFragment(DynamicPropertyFactory pf) {
            SOME_PROPERTY = pf.get("some.property", String.class).orElse("property");
        }
    }

    public static class ServiceProperties extends ApplicationProperties {
        public final ServiceFragment fragment;

        public ServiceProperties(DynamicPropertyFactory pf) {
            super(pf);
            this.fragment = new ServiceFragment(pf);
        }
    }
}
