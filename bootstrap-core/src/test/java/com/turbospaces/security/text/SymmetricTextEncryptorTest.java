package com.turbospaces.security.text;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.RepeatedTest;

import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;

class SymmetricTextEncryptorTest {
    @RepeatedTest(100)
    void works() throws Exception {
        String nonce = PlatformUtil.randomAlphanumeric(16);
        String jwt = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxMzgzOTk5OTMzNSIsImJyZCI6InB1bHN6IiwibXQiOiJtYW51YWwiLCJyayI6ImJjMjIzODg2IiwiaXNzIjoiY3JtLXNlcnZlciIsImlhdCI6MTcwMTA2NTU2MSwiZXhwIjoxNzAzNzQzOTYxfQ.SsRdP3a-AG_C2MTlSmG6LAVczoiriEkhv1Sd1eF-dwhkJG3NfTJ4JXtdL4xNelVifKMAsbpBMRfrMBN7QsFplA";

        Hasher hasher = Hashing.sha512().newHasher();
        hasher.putString("bluedream", StandardCharsets.UTF_8);
        hasher.putString(jwt, StandardCharsets.UTF_8);
        hasher.putLong(new Date().getTime());
        String secret = hasher.hash().toString();

        String encrypted = SymmetricTextEncryptor.encrypt(jwt, secret, nonce.getBytes());
        String decrypt = SymmetricTextEncryptor.decrypt(encrypted, secret, nonce.getBytes());

        Assertions.assertEquals(jwt, decrypt);
    }
}
