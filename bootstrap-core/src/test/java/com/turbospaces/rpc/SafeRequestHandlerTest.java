package com.turbospaces.rpc;

import java.net.SocketTimeoutException;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.google.common.io.ByteSource;
import com.google.common.util.concurrent.Futures;
import com.google.protobuf.Any;
import com.google.protobuf.Timestamp;
import com.turbospaces.api.NoOpStackTracer;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EmbeddedTransactionalRequest;
import com.turbospaces.dispatch.SafeRequestHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.dispatch.TransactionalRequestHandler;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.dispatch.WorkerCompletableTask;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.json.CommonObjectMapper;

import api.v1.ApplicationException;
import api.v1.MockApiFactory;
import api.v1.MockResponseStatusFacade.Code;
import io.micrometer.core.instrument.Measurement;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.search.RequiredSearch;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.opentracing.noop.NoopSpan;
import io.vavr.CheckedRunnable;
import lombok.extern.slf4j.Slf4j;
import nl.altindag.log.LogCaptor;
import reactor.core.publisher.Sinks;

@Slf4j
class SafeRequestHandlerTest {
    CommonObjectMapper mapper = new CommonObjectMapper();
    String key = PlatformUtil.randomAlphanumeric(4);
    WorkUnit unit = new WorkUnit() {
        @Override
        public String topic() {
            return "junit";
        }
        @Override
        public long timestamp() {
            return System.currentTimeMillis();
        }
        @Override
        public byte[] key() {
            return key.getBytes();
        }
        @Override
        public ByteSource value() {
            return ByteSource.empty();
        }
    };

    @Test
    void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap boot = new SimpleBootstrap(props);
        boot.run();

        try {
            var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
            var apiFactory = new MockApiFactory(props, mapper);
            var reqw = new DefaultRequestWrapperFacade(
                    apiFactory.eventTemplate(),
                    api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                    Any.pack(req));

            Semaphore semaphore = new Semaphore(0);

            CheckedRunnable task1 = Mockito.mock(CheckedRunnable.class);
            CheckedRunnable task2 = Mockito.mock(CheckedRunnable.class);

            try (DefaultPlatformExecutorService executorService = new DefaultPlatformExecutorService(props, new SimpleMeterRegistry())) {
                executorService.setBeanName(PlatformUtil.randomUUID().toString());
                executorService.afterPropertiesSet();

                var handler = new SafeRequestHandler<>(
                        boot.meterRegistry(),
                        boot.tracer(),
                        reqw,
                        apiFactory,
                        new NoOpStackTracer(),
                        NoopSpan.INSTANCE,
                        unit,
                        new EmbeddedTransactionalRequest<>(Timestamp.class, Timestamp.newBuilder(), unit, reqw, Sinks.one()),
                        new TransactionalRequestHandler<>() {
                            @Override
                            public boolean actorIsRequired() {
                                return false;
                            }

                            @Override
                            public boolean isImmediateAcknowledge() {
                                return false;
                            }

                            @Override
                            public void apply(TransactionalRequest<Timestamp, Timestamp.Builder> cmd) throws Exception {
                                cmd.addMetricTag("provider", Long.toString(System.currentTimeMillis()));

                                cmd.replyWhen(executorService.submit((CheckedRunnable) () -> {
                                    semaphore.acquire();
                                    task1.run();
                                }));

                                cmd.replyWhen(executorService.submit((CheckedRunnable) () -> {
                                    semaphore.acquire();
                                    task2.run();
                                }));
                            }
                        });

                WorkerCompletableTask task = handler.get();
                handler.run();

                Mockito.verify(task1, Mockito.never()).run();
                Mockito.verify(task2, Mockito.never()).run();

                semaphore.release(2);

                TransactionalRequestOutcome operationOutcome = task.get();

                Mockito.verify(task1).run();
                Mockito.verify(task2).run();

                RequiredSearch search = boot.meterRegistry().get(SafeRequestHandler.METRIC_NAME);
                Timer timer = search.timer();
                Measurement next = timer.measure().iterator().next();
                log.info(next.toString());

                Assertions.assertTrue(operationOutcome.getNotifications().isEmpty());
                Assertions.assertTrue(operationOutcome.getEventStream().isEmpty());
            }
        } finally {
            boot.shutdown();
        }
    }
    @Test
    void applicationExceptionInMain() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap boot = new SimpleBootstrap(props);
        boot.run();

        try {
            var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
            var apiFactory = new MockApiFactory(props, mapper);

            var reqw = new DefaultRequestWrapperFacade(
                    apiFactory.eventTemplate(),
                    api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                    Any.pack(req));

            var handler = new SafeRequestHandler<>(
                    boot.meterRegistry(),
                    boot.tracer(),
                    reqw,
                    apiFactory,
                    new NoOpStackTracer(),
                    NoopSpan.INSTANCE,
                    unit,
                    new EmbeddedTransactionalRequest<>(Timestamp.class, Timestamp.newBuilder(), unit, reqw, Sinks.one()),
                    new TransactionalRequestHandler<Timestamp, Timestamp.Builder>() {
                        @Override
                        public boolean actorIsRequired() {
                            return false;
                        }
                        @Override
                        public boolean isImmediateAcknowledge() {
                            return false;
                        }
                        @Override
                        public void apply(TransactionalRequest<Timestamp, Timestamp.Builder> cmd) throws Exception {
                            throw ApplicationException.of("unable to find", Code.ERR_NOT_FOUND);
                        }
                    });

            WorkerCompletableTask task = handler.get();
            handler.run();
            TransactionalRequestOutcome operationOutcome = task.get();

            RequiredSearch search = boot.meterRegistry().get(SafeRequestHandler.METRIC_NAME);
            Timer timer = search.timer();
            Measurement next = timer.measure().iterator().next();
            log.info(next.toString());

            Assertions.assertTrue(operationOutcome.getReply().status().isNotFound());
        } finally {
            boot.shutdown();
        }
    }

    @Test
    void exceptionInMainWarnAlarm() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap boot = new SimpleBootstrap(props);
        boot.run();
        try {
            var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
            var apiFactory = new MockApiFactory(props, mapper);
            var reqw = new DefaultRequestWrapperFacade(
                    apiFactory.eventTemplate(),
                    api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                    Any.pack(req));
            var handler = new SafeRequestHandler<>(
                    boot.meterRegistry(),
                    boot.tracer(),
                    reqw,
                    apiFactory,
                    new NoOpStackTracer(),
                    NoopSpan.INSTANCE,
                    unit,
                    new EmbeddedTransactionalRequest<>(Timestamp.class, Timestamp.newBuilder(), unit, reqw, Sinks.one()),
                    new TransactionalRequestHandler<Timestamp, Timestamp.Builder>() {
                        @Override
                        public boolean actorIsRequired() {
                            return false;
                        }
                        @Override
                        public boolean isImmediateAcknowledge() {
                            return false;
                        }
                        @Override
                        public void apply(TransactionalRequest<Timestamp, Timestamp.Builder> cmd) throws Exception {
                            throw ApplicationException.rethrowing("Network error", new SocketTimeoutException("Shark bite cable"), Code.ERR_OK);

                        }
                    });
            try (var logCaptor = LogCaptor.forClass(SafeRequestHandler.class)) {
                handler.run();
                Assertions.assertTrue(logCaptor.getErrorLogs().isEmpty());
                // null message, check count
                Assertions.assertEquals(1, logCaptor.getWarnLogs().size());
            }
        } finally {
            boot.shutdown();
        }
    }

    @Test
    void exceptionInReplyWhen() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap boot = new SimpleBootstrap(props);
        boot.run();

        try {
            var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
            var apiFactory = new MockApiFactory(props, mapper);
            var reqw = new DefaultRequestWrapperFacade(
                    apiFactory.eventTemplate(),
                    api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                    Any.pack(req));

            var handler = new SafeRequestHandler<>(
                    boot.meterRegistry(),
                    boot.tracer(),
                    reqw,
                    apiFactory,
                    new NoOpStackTracer(),
                    NoopSpan.INSTANCE,
                    unit,
                    new EmbeddedTransactionalRequest<>(Timestamp.class, Timestamp.newBuilder(), unit, reqw, Sinks.one()),
                    new TransactionalRequestHandler<Timestamp, Timestamp.Builder>() {
                        @Override
                        public boolean actorIsRequired() {
                            return true;
                        }
                        @Override
                        public boolean isImmediateAcknowledge() {
                            return false;
                        }
                        @Override
                        public void apply(TransactionalRequest<Timestamp, Timestamp.Builder> cmd) throws Exception {
                            cmd.replyWhen(Futures.immediateFailedFuture(new RuntimeException("unable to complete")));
                        }
                    });

            WorkerCompletableTask task = handler.get();
            handler.run();
            TransactionalRequestOutcome operationOutcome = task.get();

            Assertions.assertTrue(operationOutcome.getNotifications().isEmpty());
            Assertions.assertTrue(operationOutcome.getReply().status().isSystem());
        } finally {
            boot.shutdown();
        }
    }
}
