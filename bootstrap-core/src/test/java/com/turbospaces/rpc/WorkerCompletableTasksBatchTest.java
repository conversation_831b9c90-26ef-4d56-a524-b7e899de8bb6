package com.turbospaces.rpc;

import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.base.Suppliers;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Timestamp;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.WorkerCompletableTasksBatch;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WorkerCompletableTasksBatchTest {
    @Test
    public void empty() throws InterruptedException {
        WorkerCompletableTasksBatch batch = new WorkerCompletableTasksBatch(ImmutableMap.builder());
        Assertions.assertTrue(batch.awaitAndJustLogIncomplete(log, Suppliers.ofInstance(1), TimeUnit.NANOSECONDS));
    }
    @Test
    public void one() throws InterruptedException {
        var f = SettableFuture.create();
        var map = ImmutableMap.<String, ListenableFuture<?>> builder().put(PlatformUtil.randomUUID().toString(), f).build();
        var batch = new WorkerCompletableTasksBatch(map);

        f.set(Timestamp.newBuilder().setNanos((int) System.nanoTime()).build());

        Assertions.assertTrue(batch.awaitAndJustLogIncomplete(log, Suppliers.ofInstance(1), TimeUnit.NANOSECONDS));
    }
    @Test
    public void exceptional() throws InterruptedException {
        var f = SettableFuture.create();
        var map = ImmutableMap.<String, ListenableFuture<?>> builder().put(PlatformUtil.randomUUID().toString(), f).build();
        var batch = new WorkerCompletableTasksBatch(map);

        Assertions.assertFalse(batch.awaitAndJustLogIncomplete(log, Suppliers.ofInstance(1), TimeUnit.NANOSECONDS));
    }
}
