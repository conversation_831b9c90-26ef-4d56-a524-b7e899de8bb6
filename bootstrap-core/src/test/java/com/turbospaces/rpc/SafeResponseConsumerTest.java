package com.turbospaces.rpc;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Any;
import com.google.protobuf.Timestamp;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.api.facade.MockResponseWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import com.turbospaces.json.CommonObjectMapper;

import api.v1.ApiFactory;
import api.v1.MockApiFactory;
import api.v1.MockResponseWrapper;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class SafeResponseConsumerTest {
    @Test
    void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ExecutorService executor = Executors.newSingleThreadExecutor();
        SettableFuture<ResponseWrapperFacade> future = SettableFuture.create();

        var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
        var headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();
        var apiFactory = new MockApiFactory(props, new CommonObjectMapper());
        var reqw = new DefaultRequestWrapperFacade(
                apiFactory.eventTemplate(),
                api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                Any.pack(req));

        executor.execute(new Runnable() {
            @Override
            public void run() {
                future.set(new MockResponseWrapperFacade(apiFactory.eventTemplate(), new MockResponseWrapper(headers, Any.pack(req))));
            }
        });

        var apiResponse = new DefaultApiResponse<>(props, future, apiFactory, Timestamp.class, reqw);
        CountDownLatch latch = new CountDownLatch(1);
        AbstractSafeResponseConsumer consumer = new AbstractSafeResponseConsumer(apiResponse, Mockito.mock(ApiFactory.class)) {
            @Override
            public void accept(ResponseWrapperFacade t) throws Throwable {
                latch.countDown();
            }
        };

        consumer.run();
        latch.await();
        executor.shutdown();
    }
    @Test
    void exception() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        ExecutorService executor = Executors.newSingleThreadExecutor();
        SettableFuture<ResponseWrapperFacade> future = SettableFuture.create();

        var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
        var headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();
        var apiFactory = new MockApiFactory(props, new CommonObjectMapper());
        var reqw = new DefaultRequestWrapperFacade(
                apiFactory.eventTemplate(),
                api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                Any.pack(req));

        executor.execute(new Runnable() {
            @Override
            public void run() {
                future.set(new MockResponseWrapperFacade(apiFactory.eventTemplate(), new MockResponseWrapper(headers, Any.pack(req))));
            }
        });

        var apiResponse = new DefaultApiResponse<>(props, future, apiFactory, Timestamp.class, reqw);
        CountDownLatch latch = new CountDownLatch(1);

        AbstractSafeResponseConsumer consumer = new AbstractSafeResponseConsumer(apiResponse, Mockito.mock(ApiFactory.class)) {
            @Override
            public void onFailure(Throwable t) {
                super.onFailure(t);
                latch.countDown();
            }
            @Override
            public void accept(ResponseWrapperFacade t) throws Throwable {
                throw new IllegalArgumentException();
            }
        };

        consumer.run();
        latch.await();
        executor.shutdown();
    }
    @Test
    void timeout() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        ListenableFuture<ResponseWrapperFacade> future = SettableFuture.create();
        ListenableFuture<ResponseWrapperFacade> fluent = FluentFuture.from(future).withTimeout(Duration.ofMillis(1), executor);
        var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
        var apiFactory = new MockApiFactory(props, new CommonObjectMapper());
        var reqw = new DefaultRequestWrapperFacade(
                apiFactory.eventTemplate(),
                api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                Any.pack(req));

        var apiResponse = new DefaultApiResponse<>(props, fluent, apiFactory, Timestamp.class, reqw);
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Boolean> code = new AtomicReference<>();
        AbstractSafeResponseConsumer consumer = new AbstractSafeResponseConsumer(apiResponse, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade t) throws Throwable {
                code.set(t.status().isTimeout());
                log.debug(t.status().errorText());
                latch.countDown();
            }
        };

        consumer.run();
        latch.await();
        executor.shutdown();

        Assertions.assertTrue(code.get());
    }
    @Test
    void timeoutWithMapper() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        CompletableRequestReplyMapper<String, ResponseWrapperFacade> mapper = new CompletableRequestReplyMapper<>(props, new SimpleMeterRegistry());
        mapper.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
        mapper.afterPropertiesSet();

        ListenableFuture<ResponseWrapperFacade> future = mapper.acquire(PlatformUtil.randomUUID().toString(), Duration.ofMillis(1));

        var req = Timestamp.newBuilder().setSeconds(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis())).build();
        var apiFactory = new MockApiFactory(props, new CommonObjectMapper());
        var reqw = new DefaultRequestWrapperFacade(
                apiFactory.eventTemplate(),
                api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build(),
                Any.pack(req));

        var apiResponse = new DefaultApiResponse<>(props, future, apiFactory, Timestamp.class, reqw);
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Boolean> code = new AtomicReference<>();
        AbstractSafeResponseConsumer consumer = new AbstractSafeResponseConsumer(apiResponse, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade t) throws Throwable {
                code.set(t.status().isTimeout());
                log.debug(t.status().errorText());
                latch.countDown();
            }
        };

        consumer.run();
        latch.await();
        mapper.destroy();

        Assertions.assertTrue(code.get());
    }
}
