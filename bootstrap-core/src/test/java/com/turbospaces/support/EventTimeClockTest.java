package com.turbospaces.support;

import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.turbospaces.common.EventTimeClock;

import io.micrometer.core.instrument.distribution.TimeWindowMax;

class EventTimeClockTest {
    @Test
    public void timeWindowMaxWithBackdatedEvents() {
        EventTimeClock clock = new EventTimeClock();

        TimeWindowMax window = new TimeWindowMax(
                clock,
                TimeUnit.HOURS.toMillis(1),
                3);

        long baseTime = System.currentTimeMillis();

        clock.current(baseTime - TimeUnit.HOURS.toMillis(2));
        try {
            window.record(50.0);
        } finally {
            clock.remove();
        }

        clock.current(baseTime - TimeUnit.MINUTES.toMillis(30));
        try {
            window.record(75.0);
        } finally {
            clock.remove();
        }

        clock.current(baseTime - TimeUnit.HOURS.toMillis(2) - TimeUnit.MINUTES.toMillis(30));
        try {
            window.record(100.0);
        } finally {
            clock.remove();
        }

        clock.current(baseTime);
        try {
            window.record(25.0);
        } finally {
            clock.remove();
        }

        clock.current(baseTime);
        try {
            window.record(85);
        } finally {
            clock.remove();
        }

        Assertions.assertEquals(100.0D, window.poll());
    }
}
