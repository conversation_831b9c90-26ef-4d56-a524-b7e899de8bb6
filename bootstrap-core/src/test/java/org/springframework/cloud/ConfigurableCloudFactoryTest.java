package org.springframework.cloud;

import com.netflix.archaius.api.Property;
import com.netflix.archaius.config.DefaultSettableConfig;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.ScopedProperty;
import nl.altindag.log.LogCaptor;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

class ConfigurableCloudFactoryTest {
    @Test
    void testCheckWithKnownDynamicProperties() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        props.cfg().setLocalProperty(props.DYNAMIC_PROPERTY_KEYS.getKey(), "dynamic.property");
        DefaultSettableConfig testConfig = new DefaultSettableConfig();
        testConfig.setProperty("dynamic.property", "value");
        Map<String, Pair<Property<?>, Object>> map = new HashMap<>();

        try (LogCaptor captor = LogCaptor.forClass(ConfigurableCloudFactory.class)) {
            ConfigurableCloudFactory.check(props, "test-config", testConfig, map);
            Assertions.assertTrue(
                    captor.getDebugLogs().stream()
                            .anyMatch(l -> l.contains("marking dynamic property: dynamic.property as known"))
            );
        }
    }

    @Test
    void testCheckWithUnknownProperty() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DefaultSettableConfig testConfig = new DefaultSettableConfig();
        testConfig.setProperty("unknown.property", "value");
        Map<String, Pair<Property<?>, Object>> map = new HashMap<>();
        try (LogCaptor captor = LogCaptor.forClass(ConfigurableCloudFactory.class)) {
            ConfigurableCloudFactory.check(props, "test-config", testConfig, map);
            Assertions.assertTrue(
                    captor.getWarnLogs().stream().anyMatch(l -> l.contains("unknown properties: [unknown.property] (loaded from 'test-config')"))
            );
        }
    }

    @Test
    void testCheckWithSameValueProperty() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DefaultSettableConfig testConfig = new DefaultSettableConfig();
        Property<Integer> sameValue = new Property<>() {
            @Override
            public Integer get() {
                return 10;
            }

            @Override
            public String getKey() {
                return "same.property";
            }
        };
        testConfig.setProperty("same.property", "10");
        Map<String, Pair<Property<?>, Object>> map = new HashMap<>();
        map.put("same.property", Pair.of(sameValue, 10));
        try (LogCaptor captor = LogCaptor.forClass(ConfigurableCloudFactory.class)) {
            ConfigurableCloudFactory.check(props, "test-config", testConfig, map);
            Assertions.assertTrue(
                    captor.getWarnLogs().stream().anyMatch(l -> l.contains("properties have the same values: [same.property] (loaded from 'test-config')"))
            );
        }

        // different default value
        map.put("same.property", Pair.of(sameValue, 9));
        try (LogCaptor captor = LogCaptor.forClass(ConfigurableCloudFactory.class)) {
            ConfigurableCloudFactory.check(props, "test-config", testConfig, map);
            Assertions.assertFalse(
                    captor.getWarnLogs().stream().anyMatch(l -> l.contains("properties have the same values: [same.property] (loaded from 'test-config')"))
            );
        }

        // default value null
        map.put("same.property", Pair.of(sameValue, null));
        try (LogCaptor captor = LogCaptor.forClass(ConfigurableCloudFactory.class)) {
            ConfigurableCloudFactory.check(props, "test-config", testConfig, map);
            Assertions.assertFalse(
                    captor.getWarnLogs().stream().anyMatch(l -> l.contains("properties have the same values: [same.property] (loaded from 'test-config')"))
            );
        }
    }

    @Test
    void testScopedPropertyKeySkipsUnknown() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DefaultSettableConfig testConfig = new DefaultSettableConfig();
        testConfig.setProperty("one.two.namespace.three", "value");
        testConfig.setProperty("namespace.end", "value");
        testConfig.setProperty("begin.namespace", "value");
        testConfig.setProperty("hellomillions.big-wins.enabled", "value");
        Map<String, Pair<Property<?>, Object>> map = new HashMap<>();
        map.put("one.two.${scope}.three", Pair.of(Mockito.mock(ScopedProperty.class), "value"));
        map.put("${scope}.end", Pair.of(Mockito.mock(ScopedProperty.class), "value)"));
        map.put("begin.${scope}", Pair.of(Mockito.mock(ScopedProperty.class), "value)"));
        map.put("big-wins.enabled", Pair.of(Mockito.mock(ScopedProperty.class), "value"));
        try (LogCaptor captor = LogCaptor.forClass(ConfigurableCloudFactory.class)) {
            ConfigurableCloudFactory.check(props, "test-config", testConfig, map);
            Assertions.assertTrue(
                    captor.getLogs().stream().noneMatch(l -> l.contains("unknown properties")),
                    "Should not log unknown properties if a scoped property is present"
            );
        }
    }
}
