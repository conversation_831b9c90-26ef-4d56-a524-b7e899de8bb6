package reactor.blockhound.integration;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.mutable.MutableBoolean;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class DefaultBlockHoundIntegrationTest {
    @Test
    public void latch() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        cfg.setLocalProperty(props.APP_BLOCKHOUND_ENABLED.getKey(), true);

        MutableBoolean failed = new MutableBoolean(false);
        CountDownLatch executed = new CountDownLatch(1);
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);

        try (DefaultPlatformExecutorService executor = new DefaultPlatformExecutorService(props, new SimpleMeterRegistry())) {
            executor.setBeanName(bootstrap.toString());
            executor.afterPropertiesSet();

            try {
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        CountDownLatch latch = new CountDownLatch(1);
                        DefaultBlockHoundIntegration.FLAG.set(true);

                        try {
                            log.info("about to await in blocking manner ...");
                            latch.await(1, TimeUnit.SECONDS);
                        } catch (InterruptedException err) {
                            Thread.currentThread().interrupt();
                        } catch (Throwable err) {
                            log.atError().setCause(err).log();
                            failed.setTrue();
                        } finally {
                            DefaultBlockHoundIntegration.FLAG.remove();
                            executed.countDown();
                        }
                    }
                });

                executed.await();
                Assertions.assertTrue(failed.get());
            } finally {
                executor.destroy();
                bootstrap.shutdown();
            }
        }
    }
    @Test
    public void http() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        cfg.setLocalProperty(props.APP_BLOCKHOUND_ENABLED.getKey(), true);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        MutableBoolean failed = new MutableBoolean(false);
        CountDownLatch executed = new CountDownLatch(1);
        SimpleBootstrap bootstrap = new SimpleBootstrap(props);

        try (DefaultPlatformExecutorService executor = new DefaultPlatformExecutorService(props, new SimpleMeterRegistry())) {
            executor.setBeanName(bootstrap.toString());
            executor.afterPropertiesSet();

            try {
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        DefaultBlockHoundIntegration.FLAG.set(true);

                        try {
                            log.info("about to make blocking http call ...");
                            HttpGet req = new HttpGet("https://google.com?q=DDOS");
                            try (CloseableHttpResponse resp = httpClient.execute(req)) {

                            }
                        } catch (Throwable err) {
                            log.atError().setCause(err).log();
                            failed.setTrue();
                        } finally {
                            DefaultBlockHoundIntegration.FLAG.remove();
                            executed.countDown();
                        }
                    }
                });

                executed.await();
                Assertions.assertTrue(failed.get());
            } finally {
                httpClient.close();
                executor.destroy();
                bootstrap.shutdown();
            }
        }
    }
}
