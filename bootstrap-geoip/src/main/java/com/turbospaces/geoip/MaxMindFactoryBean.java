package com.turbospaces.geoip;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URL;
import java.nio.channels.Channels;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.mutable.MutableObject;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.params.HttpParams;
import org.apache.http.protocol.HttpContext;
import org.slf4j.event.Level;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.http.ContentDisposition;

import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpRequest;
import com.google.api.client.http.HttpRequestFactory;
import com.google.api.client.http.HttpResponse;
import com.google.api.client.http.HttpResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.apache.v2.ApacheHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.gax.paging.Page;
import com.google.auth.http.HttpTransportFactory;
import com.google.cloud.http.HttpTransportOptions;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.HttpStorageOptions;
import com.google.cloud.storage.Storage;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.net.MediaType;
import com.maxmind.geoip2.DatabaseProvider;
import com.maxmind.geoip2.DatabaseReader;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.ServiceInfoSubscription;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.vavr.CheckedConsumer;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MaxMindFactoryBean extends AbstractFactoryBean<DatabaseProvider> {
    public static final String GEO_LITE2 = "GeoLite2";
    public static final String GEO_IP2 = "GeoIP2";

    public static final String BUCKET_PARAM = "bucket";
    public static final String DATE_FORMAT = "yyyyMMdd";
    public static final String TAR_GZ = "tar.gz";
    public static final String MMDB = "mmdb";

    private final ApplicationProperties props;
    private final ServiceInfoSubscription<PlainServiceInfo> si;
    private final GeoDatabaseType type;
    private final ReloadableDatabaseProvider proxy;
    private final CheckedConsumer<CheckedConsumer<Storage>> acceptor;
    private final Timer timer;
    private TimerTask maxmindPull, bucketPull;

    public MaxMindFactoryBean(ApplicationProperties props, DynamicCloud cloud, GeoDatabaseType type) {
        this(props, cloud, new HttpTransportFactory() {
            @Override
            public HttpTransport create() {
                return new NetHttpTransport();
            }
        }, type);
    }
    public MaxMindFactoryBean(ApplicationProperties props, DynamicCloud cloud, CloseableHttpClient httpClient, GeoDatabaseType type) {
        // @formatter:off
        this(props, cloud, new HttpTransportFactory() {
            @Override
            @SuppressWarnings("deprecation")
            public HttpTransport create() {
                return new ApacheHttpTransport(new HttpClient() {
                    @Override
                    public HttpParams getParams() {
                        return httpClient.getParams();
                    }
                    @Override
                    public ClientConnectionManager getConnectionManager() {
                        return httpClient.getConnectionManager();
                    }
                    @Override
                    public org.apache.http.HttpResponse execute(HttpUriRequest request) throws IOException, ClientProtocolException {
                        return httpClient.execute(request);
                    }
                    @Override
                    public org.apache.http.HttpResponse execute(HttpUriRequest request, HttpContext context) throws IOException, ClientProtocolException {
                        return httpClient.execute(request, context);
                    }
                    @Override
                    public org.apache.http.HttpResponse execute(HttpHost target, org.apache.http.HttpRequest request) throws IOException, ClientProtocolException {
                        return httpClient.execute(target, request);
                    }
                    @Override
                    public org.apache.http.HttpResponse execute(HttpHost target, org.apache.http.HttpRequest request, HttpContext context) throws IOException, ClientProtocolException {
                        return httpClient.execute(target, request, context);
                    }
                    @Override
                    public <T> T execute(HttpUriRequest request, ResponseHandler<? extends T> responseHandler) throws IOException, ClientProtocolException {
                        return httpClient.execute(request, responseHandler);
                    }
                    @Override
                    public <T> T execute(HttpUriRequest request, ResponseHandler<? extends T> responseHandler, HttpContext context) throws IOException, ClientProtocolException {
                        return httpClient.execute(request, responseHandler, context);
                    }
                    @Override
                    public <T> T execute(HttpHost target, org.apache.http.HttpRequest request, ResponseHandler<? extends T> responseHandler) throws IOException, ClientProtocolException {
                        return httpClient.execute(target, request, responseHandler);
                    }
                    @Override
                    public <T> T execute(HttpHost target, org.apache.http.HttpRequest request, ResponseHandler<? extends T> responseHandler, HttpContext context) throws IOException, ClientProtocolException {
                        return httpClient.execute(target, request, responseHandler, context);
                    }
                });
            }
        }, type);
        // @formatter:on
    }
    public MaxMindFactoryBean(ApplicationProperties props, DynamicCloud cloud, HttpTransportFactory httpFactory, GeoDatabaseType type) {
        this.type = type;
        this.props = Objects.requireNonNull(props);
        this.timer = new Timer(true);
        this.proxy = new ReloadableDatabaseProvider();
        this.si = ServiceInfoSubscription.of(cloud, UPSs.MAXMIND);
        this.acceptor = new CheckedConsumer<>() {
            @Override
            public void accept(CheckedConsumer<Storage> call) throws Throwable {
                HttpTransportOptions http = HttpTransportOptions.newBuilder().setHttpTransportFactory(httpFactory).build();
                try (Storage storage = HttpStorageOptions.newBuilder().setTransportOptions(http).build().getService()) {
                    call.accept(storage);
                }
            }
        };

        switch (type) {
            case ASN:
                break;
            case CITY: {
                maxmindPull = new TimerTask() {
                    @Override
                    public void run() {
                        try {
                            si.ifPresent(new CheckedConsumer<>() {
                                @Override
                                public void accept(PlainServiceInfo psi) throws Throwable {
                                    URL url = new URI(psi.getUri()).toURL();
                                    File tmp = File.createTempFile("maxmind", "db");

                                    //
                                    // ~ download file into temporary storage first and preserve exact file name
                                    //
                                    HttpTransport transport = httpFactory.create();
                                    HttpRequestFactory requestFactory = transport.createRequestFactory();
                                    HttpRequest req = requestFactory.buildGetRequest(new GenericUrl(url));
                                    req.setCurlLoggingEnabled(false);
                                    req.setThrowExceptionOnExecuteError(false);
                                    req.setFollowRedirects(true);
                                    req.setConnectTimeout((int) props.TCP_CONNECTION_TIMEOUT.get().toMillis());
                                    req.setReadTimeout((int) props.TCP_SOCKET_TIMEOUT.get().toMillis());

                                    HttpResponse resp = req.execute();
                                    MutableObject<String> name = new MutableObject<>();
                                    try {
                                        if (resp.isSuccessStatusCode()) {
                                            String disposition = resp.getHeaders().getFirstHeaderStringValue(HttpHeaderNames.CONTENT_DISPOSITION.toString());
                                            if (StringUtils.isNotEmpty(disposition)) {
                                                ContentDisposition contentDisposition = ContentDisposition.parse(disposition.toString());
                                                name.setValue(contentDisposition.getFilename());
                                            }
                                            try (OutputStream out = new FileOutputStream(tmp)) {
                                                log.info("copying new db to location: {}, content-disposition: {}", tmp.toURI().toURL(), disposition);
                                                resp.download(out);
                                            }
                                        } else if (resp.getStatusCode() == HttpStatus.SC_TOO_MANY_REQUESTS) {
                                            HttpResponseException exception = new HttpResponseException(resp);
                                            log.warn(exception.getMessage(), exception);
                                        } else {
                                            HttpResponseException exception = new HttpResponseException(resp);
                                            log.error(exception.getMessage(), exception);
                                        }
                                    } finally {
                                        resp.disconnect();
                                    }

                                    try {
                                        //
                                        // ~ only if we have Content Disposition header available then we can upload the file into bucket with preserved name
                                        //
                                        if (Objects.nonNull(name.get())) {
                                            QueryStringDecoder decoder = new QueryStringDecoder(psi.getUri());
                                            Map<String, List<String>> parameters = decoder.parameters();
                                            if (Objects.nonNull(parameters)) {
                                                if (parameters.containsKey(BUCKET_PARAM)) {
                                                    String bucket = Iterables.getOnlyElement(parameters.get(BUCKET_PARAM));
                                                    if (StringUtils.isNotEmpty(bucket)) {
                                                        acceptor.accept(new CheckedConsumer<Storage>() {
                                                            @Override
                                                            public void accept(Storage storage) throws Throwable {
                                                                StopWatch stopWatch = StopWatch.createStarted();
                                                                BlobId blobId = BlobId.of(bucket, name.get());

                                                                //
                                                                // ~ only if the file is missing
                                                                //
                                                                if (Objects.isNull(storage.get(blobId))) {
                                                                    BlobInfo.Builder blobInfo = BlobInfo.newBuilder(blobId);
                                                                    blobInfo.setContentType(MediaType.GZIP.toString());
                                                                    blobInfo.setContentDisposition(name.get());
                                                                    Blob blob = storage.createFrom(blobInfo.build(), tmp.toPath());

                                                                    stopWatch.stop();
                                                                    log.info("uploaded content file content from: {} to bucket ({}:{}) of size: {} in: {}",
                                                                            tmp.getAbsolutePath(),
                                                                            blob.getBucket(),
                                                                            blob.getName(),
                                                                            FileUtils.byteCountToDisplaySize(blob.getSize()),
                                                                            stopWatch);

                                                                }
                                                            }
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    } finally {
                                        try (InputStream io = tmp.toURI().toURL().openStream()) {
                                            DatabaseProvider toReplace = create(io);
                                            log.info("about to replace current maxmind city database ...");
                                            proxy.replace(toReplace);
                                        } finally {
                                            tmp.delete();
                                        }
                                    }
                                }
                            });
                        } catch (Throwable err) {
                            log.error(err.getMessage(), err);
                        }
                    }
                };
                break;
            }
            default:
                break;
        }
    }
    @Override
    public Class<?> getObjectType() {
        return DatabaseProvider.class;
    }
    @Override
    public void destroy() throws Exception {
        try {
            if (Objects.nonNull(maxmindPull)) {
                maxmindPull.cancel();
            }
            if (Objects.nonNull(bucketPull)) {
                bucketPull.cancel();
            }
            if (Objects.nonNull(timer)) {
                timer.cancel();
            }
            if (Objects.nonNull(si)) {
                si.dispose();
            }
        } finally {
            super.destroy();
        }
    }
    @Override
    protected void destroyInstance(DatabaseProvider instance) throws Exception {
        if (Objects.nonNull(instance)) {
            ((ReloadableDatabaseProvider) instance).close();
        }
    }
    @Override
    protected DatabaseProvider createInstance() throws Exception {
        //
        // ~ try to download the file from bucket
        //
        bucketPull = new TimerTask() {
            @Override
            public void run() {
                si.ifPresent(new CheckedConsumer<>() {
                    @Override
                    public void accept(PlainServiceInfo psi) {
                        try {
                            String prefix = "%s-%s".formatted(GEO_IP2, type.code);
                            QueryStringDecoder decoder = new QueryStringDecoder(psi.getUri());
                            Map<String, List<String>> parameters = decoder.parameters();
                            if (Objects.nonNull(parameters)) {
                                if (parameters.containsKey(BUCKET_PARAM)) {
                                    String bucket = Iterables.getOnlyElement(parameters.get(BUCKET_PARAM));
                                    SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);

                                    if (StringUtils.isNotEmpty(bucket)) {
                                        acceptor.accept(new CheckedConsumer<Storage>() {
                                            @Override
                                            public void accept(Storage storage) throws Throwable {
                                                log.trace("about to list files in bucket: {} by prefix: {}", bucket, prefix);
                                                Page<Blob> page = storage.list(bucket, Storage.BlobListOption.prefix(prefix));

                                                Tuple2<Date, Blob> latest = null;
                                                List<String> names = Lists.newLinkedList();
                                                for (Blob blob : page.iterateAll()) {
                                                    String name = blob.getName();
                                                    names.add(name);
                                                    if (name.endsWith(TAR_GZ)) {
                                                        name = name.substring(prefix.length() + 1);
                                                        name = name.substring(0, DATE_FORMAT.length());
                                                        Date date = dateFormat.parse(name);
                                                        if (Objects.isNull(latest)) {
                                                            latest = Tuple.of(date, blob);
                                                        } else {
                                                            if (date.after(latest._1())) {
                                                                latest = Tuple.of(date, blob);
                                                            }
                                                        }
                                                    }
                                                }
                                                if (Objects.nonNull(latest)) {
                                                    log.info("{} has been detected as latest version among: {}", latest._2().getName(), names);

                                                    Date date = latest._1();
                                                    Blob blob = latest._2();
                                                    StopWatch stopWatch = StopWatch.createStarted();
                                                    try (InputStream io = Channels.newInputStream(blob.reader())) {
                                                        DatabaseProvider provider = create(io);
                                                        proxy.replace(provider);
                                                        stopWatch.stop();
                                                        log.info("copied blob content from ({}:{}), size: {}, asOf: {} in: {}",
                                                                blob.getBucket(),
                                                                blob.getName(),
                                                                FileUtils.byteCountToDisplaySize(blob.getSize()),
                                                                dateFormat.format(date),
                                                                stopWatch);
                                                    }
                                                }
                                            }
                                        });
                                    }
                                }
                            }
                        } catch (Throwable err) {
                            log.error(err.getMessage(), err);
                        }
                    }
                });
            }
        };
        //
        // ~ run it in current thread w/o any scheduling
        //
        bucketPull.run();

        //
        // ~ fallback to default file resolution logic (subject for removal soon)
        //
        if (proxy.isAbsent()) {
            String prefix = "%s-%s".formatted(GEO_LITE2, type.code);
            String ant = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX + "%s_%s.%s".formatted(prefix, "?".repeat(DATE_FORMAT.length()), TAR_GZ);
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources(ant);
            Level level = props.isDevMode() ? Level.DEBUG : Level.ERROR;
            log.atLevel(level).log("about to load maxmind from classpath: located {} resource(s) by pattern: {}", resources.length, ant);
            if (ArrayUtils.isNotEmpty(resources)) {
                Resource resource = Iterables.getOnlyElement(Arrays.asList(resources));
                try (InputStream io = resource.getInputStream()) {
                    DatabaseProvider provider = create(io);
                    proxy.replace(provider);
                }
            } else {
                //
                // ~ well it is critical we can't continue w/o having database in class path
                //
                throw new BeanCreationException("unable to locate classpath geo IP database by ant pattern: " + ant);
            }
        }

        scheduleMaxmindPull(true);
        scheduleBucketPull();

        return proxy;
    }
    private void scheduleMaxmindPull(boolean first) {
        //
        // ~ somehow distribute IP loading randomly so that there are no spike and we are not facing Rate limiter
        //
        long runAfter = TimeUnit.HOURS.toMillis(1 + ApiFactory.RANDOM.nextInt((int) TimeUnit.DAYS.toHours(1))); // ~ [1..24] h
        runAfter += TimeUnit.MINUTES.toMillis(1 + ApiFactory.RANDOM.nextInt((int) TimeUnit.HOURS.toMinutes(1))); // ~ [1..60] m

        //
        // ~ well in production mode we want to start downloading new fresh database as soon as possible
        //
        if (first) {
            if (props.isProdMode()) {
                //
                // ~ well maybe it makes sense initially to pro actively download fresh one?
                // ~ on the other hand it causes rate limiter to trigger and block which we don't want to
                // ~ uncomment runAfter = 0 and change condition to isDevMode() if you want to play locally
                //

                // runAfter = 0;
            }
        }

        if (Objects.nonNull(maxmindPull)) {
            log.info("pull task from maxmind will start in: {}", DurationFormatUtils.formatDurationWords(runAfter, true, true));
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    try {
                        maxmindPull.run();
                    } finally {
                        scheduleMaxmindPull(false);
                    }
                }
            }, runAfter);
        }
    }
    private void scheduleBucketPull() {
        if (Objects.nonNull(bucketPull)) {
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    try {
                        bucketPull.run();
                    } finally {
                        scheduleBucketPull();
                    }
                }
            }, TimeUnit.HOURS.toMillis(1));
        }
    }
    private static DatabaseProvider create(InputStream io) throws IOException {
        try (GzipCompressorInputStream gzipIn = new GzipCompressorInputStream(io);TarArchiveInputStream tarIn = new TarArchiveInputStream(gzipIn)) {
            TarArchiveEntry entry;
            while ((entry = tarIn.getNextEntry()) != null) {
                if (entry.getName().endsWith(MMDB)) {
                    return new DatabaseReader.Builder(tarIn).build();
                }
            }
        }
        throw new IllegalStateException("unable to find matching GeoIP database entry matching '" + MMDB);
    }
}
