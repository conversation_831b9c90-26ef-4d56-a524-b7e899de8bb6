package com.turbospaces.geoip;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import com.google.common.net.InetAddresses;
import com.maxmind.geoip2.DatabaseProvider;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MaxmindGeoLocationService implements IpLocationService {
    private final DatabaseProvider db;

    public MaxmindGeoLocationService(DatabaseProvider geodb) {
        this.db = Objects.requireNonNull(geodb);
    }

    public Optional<CityResponse> city(String ip) throws IOException, GeoIp2Exception {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            if (StringUtils.isNotEmpty(ip)) {
                Optional<CityResponse> opt = db.tryCity(InetAddresses.forString(ip));
                if (opt.isPresent()) {
                    var resp = opt.get();
                    var country = resp.getCountry().getIsoCode();
                    var city = resp.getCity();
                    var location = resp.getLocation();
                    log.debug("{} country={}, city={}, location={}", ip, country, city, location);

                    return opt;
                }
            }

            log.warn("unable to resolve location by {}", ip);
            return Optional.empty();
        } finally {
            stopWatch.stop();
            log.debug("{} : {}", "resolve-city", stopWatch);
        }
    }
}
