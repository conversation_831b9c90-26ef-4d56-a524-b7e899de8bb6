package com.turbospaces.geoip;

import java.io.Closeable;
import java.io.IOException;
import java.net.InetAddress;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import com.maxmind.geoip2.DatabaseProvider;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.AnonymousIpResponse;
import com.maxmind.geoip2.model.AnonymousPlusResponse;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.model.ConnectionTypeResponse;
import com.maxmind.geoip2.model.CountryResponse;
import com.maxmind.geoip2.model.DomainResponse;
import com.maxmind.geoip2.model.EnterpriseResponse;
import com.maxmind.geoip2.model.IspResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReloadableDatabaseProvider implements DatabaseProvider, Closeable {
    private final AtomicReference<DatabaseProvider> updater = new AtomicReference<>();

    public void replace(DatabaseProvider provider) throws IOException {
        DatabaseProvider old = updater.getAndSet(provider);
        if (Objects.nonNull(old)) {
            //
            // ~ well if this is memory direct we simply allow GC to cleanUp as soon as it runs, but not immediately
            //
            log.trace("disposing old database ... ");
            ((DatabaseReader) old).close();
        }
    }
    public boolean isPresent() {
        return Objects.nonNull(updater.get());
    }
    public boolean isAbsent() {
        return Objects.isNull(updater.get());
    }
    @Override
    public void close() throws IOException {
        DatabaseProvider current = updater.get();
        if (Objects.nonNull(current)) {
            ((DatabaseReader) current).close();
        }
    }
    @Override
    public Optional<CountryResponse> tryCountry(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryCountry(ipAddress);
    }
    @Override
    public CountryResponse country(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().country(ipAddress);
    }
    @Override
    public CityResponse city(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().city(ipAddress);
    }
    @Override
    public Optional<CityResponse> tryCity(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryCity(ipAddress);
    }
    @Override
    public AnonymousIpResponse anonymousIp(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().anonymousIp(ipAddress);
    }
    @Override
    public Optional<AnonymousIpResponse> tryAnonymousIp(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryAnonymousIp(ipAddress);
    }
    @Override
    public AsnResponse asn(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().asn(ipAddress);
    }
    @Override
    public Optional<AsnResponse> tryAsn(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryAsn(ipAddress);
    }
    @Override
    public ConnectionTypeResponse connectionType(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().connectionType(ipAddress);
    }
    @Override
    public Optional<ConnectionTypeResponse> tryConnectionType(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryConnectionType(ipAddress);
    }
    @Override
    public DomainResponse domain(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().domain(ipAddress);
    }
    @Override
    public Optional<DomainResponse> tryDomain(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryDomain(ipAddress);
    }
    @Override
    public EnterpriseResponse enterprise(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().enterprise(ipAddress);
    }
    @Override
    public Optional<EnterpriseResponse> tryEnterprise(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryEnterprise(ipAddress);
    }
    @Override
    public IspResponse isp(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().isp(ipAddress);
    }
    @Override
    public Optional<IspResponse> tryIsp(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryIsp(ipAddress);
    }
    @Override
    @SuppressWarnings("deprecation")
    public com.maxmind.geoip2.model.IpRiskResponse ipRisk(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().ipRisk(ipAddress);
    }
    @Override
    @SuppressWarnings("deprecation")
    public Optional<com.maxmind.geoip2.model.IpRiskResponse> tryIpRisk(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryIpRisk(ipAddress);
    }
    @Override
    public AnonymousPlusResponse anonymousPlus(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().anonymousPlus(ipAddress);
    }
    @Override
    public Optional<AnonymousPlusResponse> tryAnonymousPlus(InetAddress ipAddress) throws IOException, GeoIp2Exception {
        return updater.get().tryAnonymousPlus(ipAddress);
    }
}
