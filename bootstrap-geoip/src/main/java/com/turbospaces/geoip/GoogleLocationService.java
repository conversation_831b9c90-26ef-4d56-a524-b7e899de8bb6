package com.turbospaces.geoip;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.AddressComponent;
import com.google.maps.model.AddressComponentType;
import com.google.maps.model.AddressType;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.ServiceInfoSubscription;
import com.turbospaces.ups.UPSs;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GoogleLocationService implements CoordinatesLocationService, InitializingBean, DisposableBean {
    private final DynamicCloud cloud;
    private ServiceInfoSubscription<PlainServiceInfo> si;
    private final AtomicReference<GeoApiContext> apiReference = new AtomicReference<>();
    private final ApplicationProperties props;

    public GoogleLocationService(DynamicCloud cloud, ApplicationProperties props) {
        this.cloud = Objects.requireNonNull(cloud);
        this.props = Objects.requireNonNull(props);
    }

    @Override
    public void afterPropertiesSet() {
        this.si = ServiceInfoSubscription.of(cloud, UPSs.GOOGLE_MAPS, this::updateApiContext);
    }

    private void updateApiContext(PlainServiceInfo serviceInfo) {
        var api = serviceInfo != null ? createApiContext(serviceInfo) : null;

        this.apiReference.set(api);
    }

    private GeoApiContext createApiContext(PlainServiceInfo serviceInfo) {
        return new GeoApiContext.Builder()
                .apiKey(serviceInfo.getPassword())
                .connectTimeout(props.GEO_LOCATION_TIMEOUT.get().toMillis(), TimeUnit.MILLISECONDS)
                .readTimeout(props.GEO_LOCATION_TIMEOUT.get().toMillis(), TimeUnit.MILLISECONDS)
                .build();
    }

    @Override
    public Optional<LocationInfo> location(double latitude, double longitude) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            GeoApiContext api = apiReference.get();
            if (api != null) {
                LatLng coordinates = new LatLng(latitude, longitude);
                GeocodingResult[] response = GeocodingApi.reverseGeocode(api, coordinates)
                        .resultType(AddressType.COUNTRY, AddressType.ADMINISTRATIVE_AREA_LEVEL_1, AddressType.LOCALITY)
                        .await();

                String country = "";
                String stateIso = "";
                String stateName = "";
                String city = "";

                for (var address : response[0].addressComponents) {
                    if (StringUtils.isEmpty(country) && isCountry(address)) {
                        country = address.shortName;
                    }
                    if (StringUtils.isEmpty(stateIso) && isState(address)) {
                        stateIso = address.shortName;
                        stateName = address.longName;
                    }
                    if (StringUtils.isEmpty(city) && isCity(address)) {
                        city = address.shortName;
                    }
                }

                log.debug("lat:{} lon:{} country={}, state={}, city={}", latitude, longitude, country, stateIso, city);

                return Optional.of(new LocationInfo(country, stateIso, stateName, city));
            }
        } catch (Throwable err) {
            log.warn("unable to resolve location by lat:{} lon:{}", latitude, longitude, err);
        } finally {
            stopWatch.stop();
            log.debug("{} : {}", "resolve-location", stopWatch);
        }
        return Optional.empty();
    }

    private static boolean isCountry(AddressComponent comp) {
        return Arrays.asList(comp.types).contains(AddressComponentType.COUNTRY);
    }

    private static boolean isState(AddressComponent comp) {
        return Arrays.asList(comp.types).contains(AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_1);
    }

    private static boolean isCity(AddressComponent comp) {
        return Arrays.asList(comp.types).contains(AddressComponentType.LOCALITY);
    }

    @Override
    public void destroy() throws Exception {
        si.dispose();
    }
}
