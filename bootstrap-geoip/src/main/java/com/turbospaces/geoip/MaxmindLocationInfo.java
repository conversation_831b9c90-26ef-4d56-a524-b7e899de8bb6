package com.turbospaces.geoip;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.Subdivision;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class MaxmindLocationInfo implements LocationSpec<CityResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MaxmindLocationInfo.class);

    private String remoteIp;
    private String city;
    private String stateName, stateIso;
    private String country;
    @JsonIgnore
    private CityResponse lookup;

    protected MaxmindLocationInfo(String remoteIp) {
        this.remoteIp = Objects.requireNonNull(remoteIp);
    }
    protected MaxmindLocationInfo(String remoteIp, String country) {
        this.remoteIp = Objects.requireNonNull(remoteIp);
        this.country = Objects.requireNonNull(country);
    }
    protected MaxmindLocationInfo(String remoteIp, String country, String state) {
        this.remoteIp = Objects.requireNonNull(remoteIp);
        this.country = Objects.requireNonNull(country);
        this.stateIso = Objects.requireNonNull(state);
    }
    protected MaxmindLocationInfo(String remoteIp, String country, String stateIso, String stateName, String city) {
        this.remoteIp = Objects.requireNonNull(remoteIp);
        this.country = Objects.requireNonNull(country);
        this.stateIso = Objects.requireNonNull(stateIso);
        this.stateName = Objects.requireNonNull(stateName);
        this.city = Objects.requireNonNull(city);
    }
    protected MaxmindLocationInfo(String remoteIp, CityResponse lookup, String country) {
        this.lookup = lookup;
        this.remoteIp = Objects.requireNonNull(remoteIp);

        if (lookup.getCountry() != null) {
            String code = lookup.getCountry().getIsoCode();
            if (StringUtils.isNotEmpty(code)) {
                this.country = code;
            }
        }
        if (lookup.getCity() != null) {
            String name = lookup.getCity().getName();
            if (StringUtils.isNotEmpty(name)) {
                this.city = name;
            }
        }
        if (lookup.getMostSpecificSubdivision() != null) {
            Subdivision subdivision = lookup.getMostSpecificSubdivision();
            String name = subdivision.getName();
            String isoCode = subdivision.getIsoCode();
            if (StringUtils.isNotEmpty(name)) {
                this.stateName = name;
            }
            if (StringUtils.isNotEmpty(isoCode)) {
                this.stateIso = isoCode;
            }
        }

        if (StringUtils.isEmpty(this.country)) {
            this.country = country;
        } else {
            if (StringUtils.isNotEmpty(country)) {
                if (Strings.CS.equals(this.country, country)) {

                } else {
                    LOGGER.warn("country missmatch found external: {}, db: {}", country, this.country);

                    // ~ we trust CloudFlare or any other external balancer
                    this.country = country;
                    this.city = null;
                    this.stateName = null;
                    this.stateIso = null;
                }
            }
        }
    }
    @Override
    public CityResponse raw() {
        return lookup;
    }
    @Override
    public boolean hasCountry() {
        return StringUtils.isNotEmpty(country);
    }
    @Override
    public boolean hasCity() {
        return StringUtils.isNotEmpty(city);
    }
    @Override
    public boolean hasState() {
        return StringUtils.isNotEmpty(stateIso);
    }
    @Override
    public String remoteIp() {
        return remoteIp;
    }
    @Override
    public String country() {
        return country;
    }
    @Override
    public String stateIso() {
        return stateIso;
    }
    @Override
    public String stateName() {
        return stateName;
    }
    @Override
    public String city() {
        return city;
    }
    @Override
    public String toString() {
        return Joiner.on(", ").skipNulls().join(country, stateName, city);
    }
}
