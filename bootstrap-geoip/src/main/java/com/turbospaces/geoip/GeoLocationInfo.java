package com.turbospaces.geoip;

import lombok.Getter;

@Getter
public class GeoLocationInfo extends LocationInfo {

    double latitude;
    double longitude;

    public GeoLocationInfo(double latitude, double longitude, LocationInfo locationInfo) {
        super(locationInfo.getCountry(), locationInfo.getStateIso(), locationInfo.getStateName(), locationInfo.getCity());
        this.latitude = latitude;
        this.longitude = longitude;
    }
}
