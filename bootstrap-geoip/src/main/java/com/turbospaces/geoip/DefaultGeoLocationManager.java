package com.turbospaces.geoip;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.maxmind.geoip2.model.CityResponse;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;

import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.CheckedFunction1;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultGeoLocationManager implements GeoLocationManager, InitializingBean, DisposableBean, BeanNameAware {
    private final IpLocationService ipLocationService;
    private final CoordinatesLocationService coordinatesLocationService;
    private final PlatformExecutorService executor;

    public DefaultGeoLocationManager(
            IpLocationService ipLocationService,
            CoordinatesLocationService coordinatesLocationService,
            ApplicationProperties props,
            MeterRegistry meterRegistry) {
        this.ipLocationService = Objects.requireNonNull(ipLocationService);
        this.coordinatesLocationService = Objects.requireNonNull(coordinatesLocationService);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }
    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        executor.destroy();
    }
    @Override
    public Optional<CityResponse> city(String remoteIp) throws Exception {
        return ipLocationService.city(remoteIp);
    }
    @Override
    public ListenableFuture<CompositeLocationInfo> locationAsync(String remoteIp, String ipCountry, double latitude, double longitude) {
        var toReturn = new CompositeLocationInfo();
        try {
            var cityOpt = city(remoteIp);
            var locationInfo = cityOpt.map(cityResponse -> toLocation(remoteIp, cityResponse, ipCountry)).orElseGet(() -> toLocation(ipCountry));
            toReturn.setIpLocationInfo(new IpLocationInfo(remoteIp, locationInfo));
        } catch (Exception err) {
            return Futures.immediateFailedFuture(err);
        }

        //
        // ~ well we should submit this into thread pool because it is IO blocking operation but only if the coordinates being provided not always
        //
        if (latitude != 0 && longitude != 0) {
            return executor.submit(new CheckedFunction1<>() {
                @Override
                public CompositeLocationInfo apply(CompositeLocationInfo arg) throws Throwable {
                    coordinatesLocationService.location(latitude, longitude).ifPresent(new Consumer<>() {
                        @Override
                        public void accept(LocationInfo info) {
                            arg.setGeoLocationInfo(new GeoLocationInfo(latitude, longitude, info));
                        }
                    });
                    return arg;
                }
            }, toReturn);
        }
        return Futures.immediateFuture(toReturn);
    }
    private static LocationInfo toLocation(String remoteIp, CityResponse lookup, String country) {
        var maxmindLocation = new MaxmindLocationInfo(remoteIp, lookup, country);
        var location = new LocationInfo();
        location.setCountry(maxmindLocation.country());
        location.setStateIso(maxmindLocation.stateIso());
        location.setStateName(maxmindLocation.stateName());
        location.setCity(maxmindLocation.city());
        return location;
    }
    private static LocationInfo toLocation(String country) {
        var location = new LocationInfo();
        location.setCountry(country);
        return location;
    }
}
