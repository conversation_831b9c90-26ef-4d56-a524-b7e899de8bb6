package com.turbospaces.clickhouse;

import java.util.List;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.netflix.archaius.api.Property;
import com.turbospaces.kafka.sink.AbstractSink;

import io.github.resilience4j.core.functions.CheckedSupplier;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;

public abstract class AbstractClickhouseSink extends AbstractSink {
    private final RetryRegistry retryRegistry;

    protected AbstractClickhouseSink(KafkaListenerEndpointRegistry registry, RetryRegistry retryRegistry, Property<Boolean> enabled) {
        super(registry, enabled);
        this.retryRegistry = retryRegistry;
    }
    @Override
    public final void accept(List<ConsumerRecord<byte[], byte[]>> data, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        try {
            Retry retry = retryRegistry.retry(beanName);
            retry.executeCheckedSupplier(new CheckedSupplier<>() {
                @Override
                public Object get() throws Throwable {
                    try {
                        acceptBatch(data, acknowledgment, consumer);
                        return new Object();
                    } catch (Throwable err) {
                        logger.warn(err.getMessage(), err);
                        throw err;
                    }
                }
            });
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }
    protected abstract void acceptBatch(List<ConsumerRecord<byte[], byte[]>> data, Acknowledgment acknowledgment, Consumer<?, ?> consumer) throws Throwable;
}
