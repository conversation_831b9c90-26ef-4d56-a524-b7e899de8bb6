package com.turbospaces.clickhouse;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.apache.hc.core5.net.URIBuilder;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.clickhouse.client.api.Client;
import com.clickhouse.client.api.command.CommandResponse;
import com.clickhouse.client.api.insert.InsertResponse;
import com.clickhouse.client.api.metadata.TableSchema;
import com.clickhouse.client.api.metrics.ServerMetrics;
import com.clickhouse.client.api.query.GenericRecord;
import com.clickhouse.client.api.query.QueryResponse;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.ClickhouseServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.core.functions.CheckedRunnable;
import io.github.resilience4j.retry.RetryRegistry;
import io.netty.handler.codec.http.QueryStringDecoder;
import jakarta.persistence.Table;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClickhouseClient implements InitializingBean, DisposableBean, Supplier<Client> {
    public static final String QUEURY_PARAM_SSL = "ssl";
    public static final String OPTIMIZE_TABLE_QUERY = "OPTIMIZE TABLE %s final SETTINGS optimize_throw_if_noop = %d, alter_sync = %d, mutations_sync = %d ";
    public static final String OPTIMIZE_PARTITION_QUERY = "OPTIMIZE TABLE %s partition '%s' final SETTINGS optimize_throw_if_noop = %d, alter_sync = %d, mutations_sync = %d";
    public static final String PARTITION_EXISTS_QUERY = "SELECT count() as count FROM %s where toYYYYMM(at) = %s";
    public static final DateTimeFormatter PARTITION_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");

    protected final ApplicationProperties props;
    protected final RetryRegistry retryRegistry;
    protected final DynamicCloud cloud;
    protected final Iterable<Class<?>> entities;
    private final AtomicReference<Client> holder = new AtomicReference<>();

    public ClickhouseClient(ApplicationProperties props, DynamicCloud cloud, RetryRegistry retryRegistry, Iterable<Class<?>> entities) {
        this.props = Objects.requireNonNull(props);
        this.cloud = Objects.requireNonNull(cloud);
        this.retryRegistry = Objects.requireNonNull(retryRegistry);
        this.entities = Objects.requireNonNull(entities);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        cloud.serviceInfoByName(UPSs.CLICKHOUSE).subscribe(si -> {
            try {
                Client old = holder.getAndSet(createClient(props, (ClickhouseServiceInfo) si, entities));
                if (Objects.nonNull(old)) {
                    old.close();
                }
            } catch (Exception err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        });
    }
    @Override
    public void destroy() throws Exception {
        Optional.ofNullable(get()).ifPresent(new Consumer<>() {
            @Override
            public void accept(Client client) {
                client.close();
            }
        });
    }
    @Override
    public Client get() {
        return holder.get();
    }
    public CompletableFuture<InsertResponse> insertAsync(List<?> data) {
        String tbl = data.getFirst().getClass().getAnnotation(Table.class).name();
        Client client = get();
        return client.insert(tbl, data).whenComplete(new BiConsumer<>() {
            @Override
            public void accept(InsertResponse response, Throwable err) {
                if (Objects.isNull(err)) {
                    var serverTime = response.getMetrics().getMetric(ServerMetrics.ELAPSED_TIME);
                    log.info("bulk insert on ({}) query: {}, rows: {}, size: {}, took: {}",
                            data.getFirst().getClass().getSimpleName(),
                            response.getQueryId(),
                            response.getWrittenRows(),
                            FileUtils.byteCountToDisplaySize(response.getWrittenBytes()),
                            Objects.isNull(serverTime) ? "N/A" : DurationFormatUtils.formatDurationHMS(Duration.ofNanos(serverTime.getLong()).toMillis()));
                } else {
                    log.warn(err.getMessage(), err);
                }
            }
        });
    }
    public void insert(List<?> rows) throws InterruptedException, ExecutionException {
        if (CollectionUtils.isNotEmpty(rows)) {
            try (InsertResponse response = insertAsync(rows).get()) {

            } finally {
                rows.clear();
            }
        }
    }
    public <T> List<T> queryAll(Class<T> clazz) {
        return queryAll(clazz, false);
    }
    public <T> List<T> queryAll(Class<T> clazz, boolean isFinal) {
        TableSchema tableSchema = get().getTableSchema(clazz.getAnnotation(Table.class).name());
        if (isFinal) {
            return queryAll("select * from %s.%s final".formatted(tableSchema.getDatabaseName(), tableSchema.getTableName()), clazz);
        }
        return queryAll("select * from %s.%s".formatted(tableSchema.getDatabaseName(), tableSchema.getTableName()), clazz);
    }
    public <T> List<T> queryAll(String sql, Class<T> clazz) {
        Client client = get();
        return client.queryAll(sql, clazz, client.getTableSchema(clazz.getAnnotation(Table.class).name()));
    }
    public <T> CompletableFuture<QueryResponse> queryAsync(String sql, Map<String, Object> params) {
        Client client = get();
        return client.query(sql, params).whenComplete(new BiConsumer<>() {
            @Override
            public void accept(QueryResponse response, Throwable err) {
                if (Objects.isNull(err)) {
                    var serverTime = response.getMetrics().getMetric(ServerMetrics.ELAPSED_TIME);
                    log.info("bulk select query: ({} -> {}) params: {}, rows: {}, size: {}, took: {}",
                            response.getQueryId(),
                            sql,
                            params,
                            response.getResultRows(),
                            FileUtils.byteCountToDisplaySize(response.getReadBytes()),
                            Objects.isNull(serverTime) ? "N/A" : DurationFormatUtils.formatDurationHMS(Duration.ofNanos(serverTime.getLong()).toMillis()));
                } else {
                    log.warn(err.getMessage(), err);
                }
            }
        });
    }
    public <T> QueryResponse query(String sql, Map<String, Object> params) throws InterruptedException, ExecutionException {
        try {
            return queryAsync(sql, params).get();
        } catch (ExecutionException err) {
            log.warn(err.getMessage(), err);
            throw err;
        } finally {

        }
    }
    public CompletableFuture<CommandResponse> truncateAsync(Class<?> clazz) {
        Client client = get();
        TableSchema tableSchema = client.getTableSchema(clazz.getAnnotation(Table.class).name());
        return client.execute("truncate table %s.%s".formatted(tableSchema.getDatabaseName(), tableSchema.getTableName()));
    }
    public CommandResponse truncate(Class<?> entity) throws Throwable {
        try {
            return truncateAsync(entity).get();
        } catch (ExecutionException err) {
            log.warn(err.getMessage(), err);
            throw err.getCause();
        } finally {

        }
    }
    public void optimize(Class<?> entity, LocalDate from, LocalDate to) throws Throwable {
        LocalDate current = from.withDayOfMonth(1);
        LocalDate end = to.withDayOfMonth(1);

        while (!current.isAfter(end)) {
            optimize(entity, current);
            current = current.plusMonths(1);
        }
    }
    public void optimize(Class<?> entity) throws Throwable {
        String tableName = entity.getAnnotation(Table.class).name();

        retryRegistry.retry(tableName).decorateCheckedRunnable(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                String query = OPTIMIZE_TABLE_QUERY.formatted(
                        tableName,
                        props.CLICKHOUSE_OPTIMIZE_THROW_IF_NOOP.get(),
                        props.CLICKHOUSE_OPTIMIZE_ALTER_SYNC.get(),
                        props.CLICKHOUSE_OPTIMIZE_MUTATION_SYNC.get());
                try (CommandResponse response = get().execute(query).get()) {
                    var reply = response.getMetrics();
                    var serverTime = reply.getMetric(ServerMetrics.ELAPSED_TIME);

                    log.info("{} query: ({}), took: {}",
                            query,
                            reply.getQueryId(),
                            Objects.isNull(serverTime) ? "N/A" : DurationFormatUtils.formatDurationHMS(Duration.ofNanos(serverTime.getLong()).toMillis()));
                }
            }
        }).run();
    }
    public void optimize(Class<?> entity, LocalDate at) throws Throwable {
        String tableName = entity.getAnnotation(Table.class).name();
        String partition = at.format(PARTITION_DATE_FORMAT);

        retryRegistry.retry(tableName + ":" + partition).decorateCheckedRunnable(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                if (isPartitionNotEmpty(tableName, partition)) {
                    String query = OPTIMIZE_PARTITION_QUERY.formatted(
                            tableName,
                            partition,
                            props.CLICKHOUSE_OPTIMIZE_THROW_IF_NOOP.get(),
                            props.CLICKHOUSE_OPTIMIZE_ALTER_SYNC.get(),
                            props.CLICKHOUSE_OPTIMIZE_MUTATION_SYNC.get());
                    try (CommandResponse response = get().execute(query).get()) {
                        var reply = response.getMetrics();
                        var serverTime = reply.getMetric(ServerMetrics.ELAPSED_TIME);

                        log.info("{} query: ({}), took: {}",
                                query,
                                reply.getQueryId(),
                                Objects.isNull(serverTime) ? "N/A" : DurationFormatUtils.formatDurationHMS(Duration.ofNanos(serverTime.getLong()).toMillis()));
                    }
                }
            }
        }).run();
    }
    public boolean isPartitionNotEmpty(String tableName, String partition) {
        String query = String.format(PARTITION_EXISTS_QUERY, tableName, partition);
        List<GenericRecord> list = get().queryAll(query);
        long count = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            GenericRecord last = Iterables.getLast(list);
            count = last.getLong("count");
        }
        return count > 0;
    }
    @VisibleForTesting
    public static Client createClient(
            ApplicationProperties props,
            boolean secure,
            String host,
            int port,
            String username,
            String password,
            String path,
            Map<String, String> params,
            Iterable<Class<?>> entities) throws Exception {

        var uri = new URIBuilder().setScheme(secure ? "https" : "http").setHost(host).setPort(port);
        var builder = new Client.Builder()
                .addEndpoint(uri.build().toURL().toExternalForm())
                .setUsername(username)
                .setPassword(password)
                .compressServerResponse(true)
                .compressClientRequest(true)
                .useHttpCompression(false)
                .setClientName(props.CLOUD_APP_ID.get())
                .setMaxConnections(props.HTTP_POOL_MAX_PER_ROUTE.get())
                .setConnectTimeout(props.TCP_CONNECTION_TIMEOUT.get().toMillis())
                .setSocketKeepAlive(props.TCP_KEEP_ALIVE.get())
                .setSocketTcpNodelay(props.TCP_NO_DELAY.get())
                .setSocketTimeout(props.CLICKHOUSE_SOCKET_TIMEOUT.get().toMinutes(), ChronoUnit.MINUTES)
                .setDefaultDatabase(path);

        Duration keepAlive = Duration.ofSeconds(30);
        if (props.TCP_KEEP_ALIVE_TIMEOUT.get().toSeconds() > 0) {
            keepAlive = props.TCP_KEEP_ALIVE_TIMEOUT.get();
            builder.setKeepAliveTimeout(keepAlive.toSeconds(), ChronoUnit.SECONDS);
        }

        //
        // ~ put (and maybe override) configuration options
        //
        if (Objects.nonNull(params)) {
            params.entrySet().forEach(new Consumer<>() {
                @Override
                public void accept(Entry<String, String> entry) {
                    builder.setOption(entry.getKey(), entry.getValue());
                }
            });
        }

        var client = builder.build();
        entities.forEach(e -> {
            if (e.isAnnotationPresent(Table.class)) {
                client.register(e, client.getTableSchema(e.getAnnotation(Table.class).name()));
            }
        });
        return client;
    }
    @VisibleForTesting
    public static Client createClient(ApplicationProperties props, ClickhouseServiceInfo si, Iterable<Class<?>> entities) throws Exception {
        Map<String, String> params = Maps.newHashMap();

        QueryStringDecoder decoder = new QueryStringDecoder(si.getUri());
        decoder.parameters().forEach(new BiConsumer<>() {
            @Override
            public void accept(String key, List<String> values) {
                params.put(key, Iterables.getOnlyElement(values));
            }
        });

        boolean secure = false;
        if (params.containsKey(QUEURY_PARAM_SSL)) {
            secure = Boolean.parseBoolean(params.get(QUEURY_PARAM_SSL));
        }

        //
        // ~ un-neccessary anymore
        //
        params.remove(QUEURY_PARAM_SSL);

        return createClient(
                props,
                secure,
                si.getHost(),
                si.getPort(),
                si.getUserName(),
                si.getPassword(),
                si.getPath(),
                params,
                entities);
    }
}
