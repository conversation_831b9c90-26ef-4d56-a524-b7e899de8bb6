package com.turbospaces.clickhouse;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.flywaydb.core.api.output.MigrateResult;
import org.springframework.cloud.DynamicCloud;

import com.clickhouse.jdbc.DataSourceV1;
import com.google.common.annotations.VisibleForTesting;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.FlywayUberRunner;
import com.turbospaces.ups.ClickhouseServiceInfo;
import com.turbospaces.ups.UPSs;

import io.ebean.DatabaseFactory;
import io.ebean.config.DatabaseConfig;
import io.ebean.config.EncryptKey;
import io.ebean.config.EncryptKeyManager;
import io.ebeaninternal.api.SpiEbeanServer;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@VisibleForTesting
public class ClickhouseFlywayUberClient extends ClickhouseClient {
    public ClickhouseFlywayUberClient(ApplicationProperties props, DynamicCloud cloud, RetryRegistry retryRegistry, Iterable<Class<?>> entities) {
        super(props, cloud, retryRegistry, entities);
    }
    @Override
    @SuppressWarnings("deprecation")
    public void afterPropertiesSet() throws Exception {
        //
        // ~ keep using old V1 client just for simplification in integration testing and flyway compatibility
        //
        ClickhouseServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.CLICKHOUSE);
        DatabaseConfig sc = new DatabaseConfig();
        sc.setDataSource(new DataSourceV1(si.getJdbcUrl()));
        sc.loadModuleInfo(false);
        sc.setEncryptKeyManager(new EncryptKeyManager() {
            @Override
            public EncryptKey getEncryptKey(String tableName, String columnName) {
                return new EncryptKey() {
                    @Override
                    public String getStringValue() {
                        throw new UnsupportedOperationException();
                    }
                };
            }
        });

        entities.forEach(sc::addClass);

        SpiEbeanServer ebean = (SpiEbeanServer) DatabaseFactory.create(sc);

        //
        // ~ run UBER migration by re-using the code
        //
        try {
            MigrateResult migrateResult = FlywayUberRunner.run(ebean);
            log.info(ReflectionToStringBuilder.toString(migrateResult, ToStringStyle.SHORT_PREFIX_STYLE));
        } finally {
            ebean.shutdown();
        }

        super.afterPropertiesSet();
    }
}
