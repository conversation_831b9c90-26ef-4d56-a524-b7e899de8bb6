package com.turbospaces.plugins;

import java.lang.reflect.UndeclaredThrowableException;
import java.sql.SQLException;
import java.util.Collection;
import java.util.function.Supplier;

import javax.sql.DataSource;

import org.springframework.cloud.service.ServiceInfo;

import com.clickhouse.jdbc.DataSourceV1;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.ClickhouseServiceInfo;

public class ClickhouseFlywayBootstrapInitializer extends FlywayBootstrapInitializer {
    public ClickhouseFlywayBootstrapInitializer(ApplicationProperties props, Supplier<String> location, ServiceInfo ownerInfo, Collection<Class<?>> entities, Collection<String> schemas) {
        super(props, location, ownerInfo, entities, schemas);
    }
    public ClickhouseFlywayBootstrapInitializer(ApplicationProperties props, Supplier<String> location, ServiceInfo ownerInfo, Collection<Class<?>> entities, String... schemas) {
        super(props, location, ownerInfo, entities, schemas);
    }
    public ClickhouseFlywayBootstrapInitializer(ApplicationProperties props, Supplier<String> location, ServiceInfo ownerInfo, Collection<Class<?>> entities) {
        super(props, location, ownerInfo, entities);
    }
    @Override
    @SuppressWarnings("deprecation")
    protected DataSource createDataSource(ServiceInfo si) {
        //
        // ~ we can to keep v1 version usage (deprecated but w/o that flyway will not work)
        //
        try {
            return new DataSourceV1(ClickhouseServiceInfo.class.cast(si).getJdbcUrl());
        } catch (SQLException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
}
