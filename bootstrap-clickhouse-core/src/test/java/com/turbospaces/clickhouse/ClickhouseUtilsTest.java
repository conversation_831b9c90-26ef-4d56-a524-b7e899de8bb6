package com.turbospaces.clickhouse;

import java.time.LocalDateTime;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.turbospaces.common.PlatformUtil;

class ClickhouseUtilsTest {
    @Test
    void works() {
        Date now = new Date();
        LocalDateTime time = PlatformUtil.toLocalUTCDateTime(now);
        Assertions.assertEquals(time, PlatformUtil.limitUpperUint32DateTime(time));

        now = DateUtils.addYears(now, 10);
        time = PlatformUtil.toLocalUTCDateTime(now);
        Assertions.assertEquals(time, PlatformUtil.limitUpperUint32DateTime(time));

        now = DateUtils.addYears(now, 150);
        time = PlatformUtil.toLocalUTCDateTime(now);
        Assertions.assertEquals(PlatformUtil.UINT32_DATE_TIME_MAX_VALUE, PlatformUtil.limitUpperUint32DateTime(time));
    }
}
