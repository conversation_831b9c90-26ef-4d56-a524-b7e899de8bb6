package com.turbospaces.mdc;

public interface MdcTags {
    String MDC_OPERATION = "operation";
    String MDC_ROUTING_KEY = "routingKey";
    String MDC_TRACE_ID = "traceId";
    String MDC_BROWSER = "browser";
    String MDC_MESSAGE_ID = "messageId";
    String MDC_BATCH_ID = "batchId";
    String MDC_BATCH_TOOK = "batchTook";
    String MDC_TOPIC = "topic";
    String MDC_PARITION = "partition";
    String MDC_OFFSET = "offset";
    String MDC_TOOK = "took";
    String MDC_STATUS = "status";
    String MDC_ASN = "asn";
    String MDC_ASN_ORG = "asnOrg";
    String MDC_REMOTE_IP = "remoteIp";
    String MDC_ERROR_CLASS = "error";
    String MDC_ERROR_CODE = "errorCode";
    String MDC_PATH = "path";
    String MDC_METHOD = "method";
    String MDC_STATUS_CODE = "statusCode";
    String MDC_BRAND_NAME = "brandName";
    String MDC_OPERATOR = "operator";
    String MDC_REPLY_TO = "replyTo";
    String MDC_TIMEOUT = "timeout";
    String MDC_COUNTERPARTY = "counterparty";
    String MDC_TRANSACTION_ID = "transactionId";
    String MDC_QUERY_LABEL = "queryLabel";
}
