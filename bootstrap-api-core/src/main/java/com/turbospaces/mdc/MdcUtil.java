package com.turbospaces.mdc;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import com.google.common.collect.ImmutableMap;
import com.turbospaces.executor.WorkUnit;

import io.netty.util.AsciiString;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class MdcUtil {
    public static void setMdc(WorkUnit record, String operation, String traceId) {
        MDC.put(MdcTags.MDC_OPERATION, operation);
        if (StringUtils.isNotEmpty(traceId)) {
            MDC.put(MdcTags.MDC_TRACE_ID, traceId);
        }
        if (Objects.nonNull(record.topic())) {
            MDC.put(MdcTags.MDC_TOPIC, record.topic());
        }
        if (Objects.nonNull(record.key())) {
            AsciiString partitionKey = new AsciiString(record.key());
            MDC.put(MdcTags.MDC_ROUTING_KEY, partitionKey.toString());
        }

        if (log.isTraceEnabled()) {
            log.trace("MDC before: {}", MDC.getCopyOfContextMap());
        }
    }

    public static void clearMdc(WorkUnit record) {
        MDC.remove(MdcTags.MDC_OPERATION);
        MDC.remove(MdcTags.MDC_MESSAGE_ID);
        MDC.remove(MdcTags.MDC_TOPIC);

        if (Objects.nonNull(record.key())) {
            MDC.remove(MdcTags.MDC_TRACE_ID);
        }
        if (Objects.nonNull(record.key())) {
            MDC.remove(MdcTags.MDC_ROUTING_KEY);
        }

        if (log.isTraceEnabled()) {
            log.trace("MDC after: {}", MDC.getCopyOfContextMap());
        }
    }
    public static ImmutableMap<String, String> propagate(Map<String, String> mdc) {
        if (Objects.nonNull(mdc)) {
            ImmutableMap.Builder<String, String> toReturn = ImmutableMap.builder();

            for (Entry<String, String> entry : mdc.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                String current = MDC.get(key);
                if (Objects.isNull(current)) {
                    MDC.put(key, value);
                    toReturn.put(key, value);
                } else {
                    //
                    // ~ corner case (if-not-modified)
                    //
                    if (Objects.equals(current, value)) {
                        MDC.put(key, value);
                        toReturn.put(key, value);
                    }
                }
            }

            return toReturn.build();
        }

        return ImmutableMap.of();
    }
    public static void cleanUp(ImmutableMap<String, String> map) {
        if (Objects.nonNull(map)) {
            for (Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String current = MDC.get(key);
                if (Objects.nonNull(current)) {
                    //
                    // ~ corner case (if-not-modified)
                    //
                    if (Objects.equals(current, value)) {
                        MDC.remove(key);
                    }
                } else {
                    MDC.remove(key);
                }
            }
        }
    }
}
