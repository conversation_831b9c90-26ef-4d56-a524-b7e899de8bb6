package com.turbospaces.boot;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;

import com.codahale.metrics.health.HealthCheck;

public abstract class AbstractHealtchCheck extends HealthCheck implements DisposableBean {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    // ~ only on bootstrap
    public abstract boolean isBootstrapOnly();
    public boolean isPermanent() {
        return BooleanUtils.negate(isBootstrapOnly());
    }
}
