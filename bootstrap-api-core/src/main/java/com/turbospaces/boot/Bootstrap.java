package com.turbospaces.boot;

import java.security.KeyStore;

import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.SmartCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;

import com.codahale.metrics.health.HealthCheck;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.retry.RetryRegistry;
import io.github.resilience4j.timelimiter.TimeLimiterRegistry;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.opentracing.Tracer;
import reactor.core.publisher.Flux;

public interface Bootstrap extends SmartCloud, UPSs, DevMode {

    //
    // life-cycle
    //
    ConfigurableApplicationContext run(String... args);
    void shutdown() throws Exception; // ~ graceful shutdown

    String spaceName();
    String appId();
    String release();

    //
    // logging
    //
    void squashLogging() throws Exception;

    //
    // ~ port
    //
    int port();
    int secondaryPort();
    int tertiaryPort();

    //
    // health-checks and metrics support
    //
    void registerHealthCheck(String name, HealthCheck check);

    DynamicCloud cloud();
    KeyStore keyStore();
    Tracer tracer();
    CompositeMeterRegistry meterRegistry();
    HealthCheckRegistry healthCheckRegistry();
    RetryRegistry retryRegistry();
    RateLimiterRegistry rateLimiterRegistry();
    TimeLimiterRegistry timeLimiterRegistry();
	CircuitBreakerRegistry circuitBreakerRegistry();

    @Override
    default void addUps(ServiceInfo info) {
        cloud().addUps(info);
    }
    @Override
    default boolean removeUps(String id) {
        return cloud().removeUps(id);
    }
    @Override
    default boolean removeUps(ServiceInfo si) {
        return cloud().removeUps(si);
    }
    @Override
    default <T extends ServiceInfo> Flux<ServiceInfo> serviceInfoByName(String ups) {
        return cloud().serviceInfoByName(ups);
    }
    @Override
    default <T extends ServiceInfo> Flux<ServiceInfo> scopedServiceInfoByName(String scope, String name) {
        return cloud().scopedServiceInfoByName(scope, name);
    }
}
