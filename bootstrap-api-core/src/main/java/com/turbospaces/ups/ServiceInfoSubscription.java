package com.turbospaces.ups;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;

import io.vavr.CheckedConsumer;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;

@Slf4j
@SuppressWarnings("unchecked")
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ServiceInfoSubscription<T extends ServiceInfo> implements Disposable, Supplier<T> {
    private final AtomicReference<T> reference;
    private final Disposable subscription;

    @Override
    public T get() {
        return Objects.requireNonNull(reference.get());
    }
    public boolean isPresent() {
        return Objects.nonNull(reference.get());
    }
    public boolean isEmpty() {
        return Objects.isNull(reference.get());
    }
    @Override
    public void dispose() {
        if (Objects.nonNull(subscription)) {
            subscription.dispose();
        }
    }
    public void ifPresent(CheckedConsumer<? super T> action) {
        ifPresentOrElse(action, () -> {});
    }
    public void ifPresentOrElse(CheckedConsumer<? super T> action, Runnable emptyAction) {
        Optional.ofNullable(reference.get()).ifPresentOrElse(t -> {
            try {
                action.accept(t);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }, emptyAction);
    }
    //
    // ~ subscribe and monitor for changes (can be updated in real-time w/o restart)
    // ~ first value will be emitted immediately in subscribe call (no need to get initial value outside)
    //
    public static <T extends ServiceInfo> ServiceInfoSubscription<T> of(DynamicCloud cloud, String name) {
        return of(cloud, name, (si) -> {});
    }

    //
    // ~ subscribe and monitor for changes (can be updated in real-time w/o restart)
    // ~ first value will be emitted immediately in subscribe call (no need to get initial value outside)
    //
    public static <T extends ServiceInfo> ServiceInfoSubscription<T> of(DynamicCloud cloud, String name, CheckedConsumer<T> siConsumer) {
        AtomicReference<T> reference = new AtomicReference<>();
        var subscription = cloud.serviceInfoByName(name).subscribe(t -> {
            log.debug("accepting UPS: {}", t.getId());
            reference.set((T) t);
            try {
                siConsumer.accept((T) t);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        });

        return new ServiceInfoSubscription<>(reference, subscription);
    }

    //
    // ~ subscribe and monitor for changes (can be updated in real-time w/o restart)
    // ~ first value will be emitted immediately in subscribe call (no need to get initial value outside)
    //
    public static <T extends ServiceInfo> ServiceInfoSubscription<T> of(DynamicCloud cloud, String scope, String name, Consumer<T> siConsumer) {
        AtomicReference<T> reference = new AtomicReference<>();
        var subscription = cloud.scopedServiceInfoByName(scope, name).subscribe(t -> {
            log.debug("accepting UPS: {}", t.getId());
            reference.set((T) t);
            siConsumer.accept((T) t);
        });

        return new ServiceInfoSubscription<>(reference, subscription);
    }
}
