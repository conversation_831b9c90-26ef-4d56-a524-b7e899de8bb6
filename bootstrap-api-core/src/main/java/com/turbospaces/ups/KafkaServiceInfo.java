package com.turbospaces.ups;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.service.BaseServiceInfo;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.net.HostAndPort;
import com.turbospaces.ups.UPSs.UnhiddenUriInfo;

public class KafkaServiceInfo extends BaseServiceInfo {
    public static final String KAFKA_SCHEME = "kafka";
    private final UnhiddenUriInfo delegate;
    private final List<HostAndPort> urls = Lists.newArrayList();

    public KafkaServiceInfo(String id, String uri) {
        super(id);
        delegate = new UnhiddenUriInfo(id, uri);
        parseUrls(uri);
    }
    public KafkaServiceInfo(String uri) {
        super(UPSs.KAFKA);
        delegate = new UnhiddenUriInfo(UPSs.KAFKA, uri);
        parseUrls(uri);
    }
    public KafkaServiceInfo(String host, int port) {
        super(UPSs.KAFKA);
        delegate = new UnhiddenUriInfo(UPSs.KAFKA, KAFKA_SCHEME, host, port, null, null, null, Map.of());
        urls.add(HostAndPort.fromParts(host, port));
    }
    public KafkaServiceInfo(HostAndPort hostAndPort) {
        super(UPSs.KAFKA);
        delegate = new UnhiddenUriInfo(UPSs.KAFKA, KAFKA_SCHEME, hostAndPort.getHost(), hostAndPort.getPort(), null, null, null, Map.of());
        urls.add(hostAndPort);
    }
    public String getUri() {
        return delegate.getUri();
    }
    public String getUserName() {
        return delegate.getUserName();
    }
    public String getPassword() {
        return delegate.getPassword();
    }
    public String getHost() {
        return Iterables.getOnlyElement(urls).getHost();
    }
    public int getPort() {
        return Iterables.getOnlyElement(urls).getPort();
    }
    public String getBootstrapServers() {
        return Joiner.on(',').join(urls.stream().map(HostAndPort::toString).iterator());
    }
    private void parseUrls(String url) {
        String host = delegate.getUriInfo().getHost();
        int port = delegate.getUriInfo().getPort();
        if (StringUtils.isNotEmpty(host) && port > 0) {
            urls.add(HostAndPort.fromParts(host, port));
        } else {
            String authority = delegate.getUriInfo().getUri().getAuthority();
            Iterable<String> iterable = Splitter.on(',').omitEmptyStrings().split(authority);
            iterable.forEach(new Consumer<>() {
                @Override
                public void accept(String str) {
                    List<String> parts = Arrays.asList(str.split(":"));
                    Iterator<String> it = parts.iterator();
                    String hostPart = it.next();
                    int portPart = Integer.parseInt(it.next());
                    if (BooleanUtils.isFalse(it.hasNext())) {
                        urls.add(HostAndPort.fromParts(hostPart, portPart));
                    }
                }
            });
        }
    }
}
