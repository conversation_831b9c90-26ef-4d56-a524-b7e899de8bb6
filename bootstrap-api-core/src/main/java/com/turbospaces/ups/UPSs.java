package com.turbospaces.ups;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.springframework.cloud.Cloud;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.BaseServiceInfo;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.UriBasedServiceInfo;
import org.springframework.cloud.util.UriInfo;

import com.google.common.base.Joiner;
import com.google.common.collect.Iterables;
import com.turbospaces.common.EnvUtil;

import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.handler.codec.http.QueryStringEncoder;
import reactor.core.publisher.Flux;

public interface UPSs {
    String CFG = "cfg";
    String SENTRY = "sentry";
    String ELASTIC_SEARCH = "elastic-search";
    String INFLUX = "influx";
    String MAXMIND = "maxmind";
    String ZOOKEEPER = "zookeeper";
    String KAFKA = "kafka";
    String CDC_KAFKA = "cdc-kafka";
    String TEMPORAL = "temporal";

    String[] INFRA_CORE = {
            CFG,
            SENTRY,
            ELASTIC_SEARCH,
            INFLUX,
            MAXMIND,
            ZOOKEEPER,
            KAFKA,
            CDC_KAFKA,
            TEMPORAL
    };

    String HMAC = "hmac";
    String REDIS = "redis";
    String MEMCACHED = "memcached";
    String RECAPTCHA = "recaptcha";
    String INVISIBLE_RECAPTCHA = "invisible-recaptcha";
    String TURNSTILE = "turnstile";
    String DOCUMENTATION = "documentation";
    String GOOGLE_MAPS = "google-maps";



    String[] INFRA_FRONTEND = {
            HMAC,
            REDIS,
            MEMCACHED,
            RECAPTCHA,
            INVISIBLE_RECAPTCHA,
            TURNSTILE,
            DOCUMENTATION,
            GOOGLE_MAPS
    };

    String[] INFRA_GATEWAY = {
            HMAC,
            REDIS,
            MEMCACHED,
            RECAPTCHA,
            INVISIBLE_RECAPTCHA,
            TURNSTILE
    };

    //
    // ~ PG
    //
    String POSTGRES_OWNER = "postgres-owner";
    String POSTGRES_APP = "postgres-app";
    String QUARTZ_APP = "quartz-app";

    String[] INFRA_SERVER = {
            POSTGRES_APP,
            POSTGRES_OWNER,
            QUARTZ_APP
    };

    String JAEGER = "jaeger";
    String LOCUST = "locust";
    String NATS = "nats";
    String CLICKHOUSE = "clickhouse";




    // TODO remove this if possible
    String PUBSUB = "pubsub";
    String UPS_GCV = "google-application-credentials";

    //
    // ~ testing
    //
    String H2_OWNER = "h2-owner";
    String H2_APP = "h2-app";

    <T extends ServiceInfo> Flux<ServiceInfo> serviceInfoByName(String ups);
    <T extends ServiceInfo> Flux<ServiceInfo> scopedServiceInfoByName(String scope, String name);

    static <T extends ServiceInfo> Optional<T> findServiceInfoByName(DynamicCloud cloud, String name) {
        var env = EnvUtil.envStage();

        if (env.isPresent()) {
            var scoped = String.format("%s-%s", env.get(), name);
            return UPSs.<T> findServiceInfo(cloud, scoped).or(() -> findServiceInfo(cloud, name));
        }

        return findServiceInfo(cloud, name);
    }
    static <T extends ServiceInfo> Optional<T> findScopedServiceInfoByName(DynamicCloud cloud, String name, String... scopes) {
        // ~ skip nulls in the end
        if (scopes.length > 0 && StringUtils.isEmpty(scopes[scopes.length - 1])) {
            return findScopedServiceInfoByName(cloud, name, Arrays.copyOfRange(scopes, 0, scopes.length - 1));
        }
        for (int i = scopes.length; i > 0; i--) {
            String joined = Joiner.on("-").join(Arrays.copyOfRange(scopes, 0, i));
            Optional<T> serviceMaybe = findServiceInfoByName(cloud, String.format("%s-%s", joined.toLowerCase(), name));
            if (serviceMaybe.isPresent()) {
                return serviceMaybe;
            }
        }
        return findServiceInfoByName(cloud, name);
    }
    @SuppressWarnings("unchecked")
    static <T extends ServiceInfo> T findRequiredScopedServiceInfoByName(DynamicCloud cloud, String name, String... scopes) {
        return (T) findScopedServiceInfoByName(cloud, name, scopes).orElseThrow(new Supplier<NoSuchElementException>() {
            @Override
            public NoSuchElementException get() {
                return new NoSuchElementException(
                        "No value present for scoped secret: %s with scopes: %s, please add it to SecretManager".formatted(name, Arrays.toString(scopes)));
            }
        });
    }
    @SuppressWarnings("unchecked")
    static <T extends ServiceInfo> T findRequiredServiceInfoByName(DynamicCloud cloud, String name) {
        return (T) findServiceInfoByName(cloud, name).orElseThrow(new Supplier<NoSuchElementException>() {
            @Override
            public NoSuchElementException get() {
                return new NoSuchElementException("No value present for secret: %s, please add it to SecretManager".formatted(name));
            }
        });
    }
    static <T extends ServiceInfo> Optional<T> findScopedServiceInfoByName(String scope, DynamicCloud cloud, String name) {
        Optional<T> opt = findServiceInfoByName(cloud, String.format("%s-%s", scope, name));
        if (opt.isPresent()) {
            return opt;
        }
        return findServiceInfoByName(cloud, name);
    }
    @SuppressWarnings("unchecked")
    static <T extends ServiceInfo> T findScopedRequiredServiceInfoByName(String scope, DynamicCloud cloud, String name) {
        Optional<ServiceInfo> opt = findServiceInfoByName(cloud, String.format("%s-%s", scope, name));
        if (opt.isPresent()) {
            return (T) opt.get();
        }
        opt = findServiceInfoByName(cloud, name);
        return (T) opt.orElseThrow(new Supplier<NoSuchElementException>() {
            @Override
            public NoSuchElementException get() {
                return new NoSuchElementException("No value present for secret: %s with scope: %s, please add it to SecretManager".formatted(name, scope));
            }
        });
    }
    @SuppressWarnings("unchecked")
    private static <T extends ServiceInfo> Optional<T> findServiceInfo(Cloud cloud, String name) {
        List<ServiceInfo> serviceInfos = cloud.getServiceInfos();
        return (Optional<T>) serviceInfos.stream().filter(input -> input.getId().equals(name)).findAny();
    }
    static boolean isEquals(ServiceInfo t, ServiceInfo u) {
        if (t.getClass().equals(u.getClass())) {
            if (t instanceof UriBasedServiceInfo turi && u instanceof UriBasedServiceInfo uuri) {
                return new EqualsBuilder().append(turi.getId(), uuri.getId()).append(turi.getUri(), uuri.getUri()).isEquals();
            }
            if (t instanceof RawServiceInfo turi && u instanceof RawServiceInfo uuri) {
                return new EqualsBuilder().append(turi.getId(), uuri.getId()).append(turi.read(), uuri.read()).isEquals();
            }
            if (t instanceof BaseServiceInfo turi && u instanceof BaseServiceInfo uuri) {
                return Objects.equals(turi.getId(), uuri.getId());
            }

            throw new UnsupportedOperationException("don't know how to compare " + t.getClass().getSimpleName());
        }
        return false;
    }
    static int hashCode(ServiceInfo si) throws IOException {
        if (si instanceof UriBasedServiceInfo usi) {
            return Objects.hash(usi.getId(), usi.getUri());
        }
        if (si instanceof RawServiceInfo rsi) {
            return Objects.hash(rsi.getId(), Arrays.hashCode(rsi.toByteSource().read()));
        }
        if (si instanceof BaseServiceInfo bsi) {
            return Objects.hash(bsi.getId());
        }
        throw new UnsupportedOperationException("don't know how to calculate hashcode " + si.getClass().getSimpleName());
    }
    static Optional<String> getQueryParam(String key, PlainServiceInfo info) {
        if (StringUtils.isNotEmpty(info.getQuery())) {
            QueryStringDecoder decoder = new QueryStringDecoder(info.getQuery(), false);
            for (Entry<String, List<String>> entry : decoder.parameters().entrySet()) {
                if (entry.getKey().equals(key)) {
                    List<String> list = entry.getValue();
                    String toReturn = Iterables.getOnlyElement(list);
                    return Optional.of(toReturn);
                }
            }
        }
        return Optional.empty();
    }
    static String getRequiredQueryParam(String key, PlainServiceInfo info) throws Exception {
        return getQueryParam(key, info).orElseThrow(new Supplier<IllegalArgumentException>() {
            @Override
            public IllegalArgumentException get() {
                return new IllegalArgumentException("param '%s' is not found or miss-configured".formatted(key));
            }
        });
    }
    static UriInfo toUriInfo(String id, String scheme, String host, int port, String username, String password, String path) {
        UnhiddenUriInfo si = new UnhiddenUriInfo(id, scheme, host, port, username, password, path);
        return si.getUriInfo();
    }
    static UriInfo toUriInfo(String id, String scheme, String host, int port, String username, String password, String path, Map<String, String> query) {
        UnhiddenUriInfo si = new UnhiddenUriInfo(id, scheme, host, port, username, password, path, query);
        return si.getUriInfo();
    }

    static class UnhiddenUriInfo extends UriBasedServiceInfo {
        public UnhiddenUriInfo(
                String id,
                String scheme,
                String host,
                int port,
                String username,
                String password,
                String path) {
            super(id, scheme, host, port, username, password, path);
        }
        public UnhiddenUriInfo(
                String id,
                String scheme,
                String host,
                int port,
                String username,
                String password,
                String path,
                Map<String, String> query) {
            super(id, new UriInfo(scheme, host, port, username, password, path, query(query)).getUriString());
        }
        public UnhiddenUriInfo(String id, String uriString) {
            super(id, uriString);
        }
        @Override
        public UriInfo getUriInfo() {
            return super.getUriInfo();
        }
        private static String query(Map<String, String> query) {
            if (query.isEmpty()) {
                return StringUtils.EMPTY;
            }

            QueryStringEncoder encoder = new QueryStringEncoder(StringUtils.EMPTY);
            query.forEach(new BiConsumer<>() {
                @Override
                public void accept(String k, String v) {
                    encoder.addParam(k, v);
                }
            });

            return StringUtils.substringAfter(encoder.toString(), "?");
        }
    }
}
