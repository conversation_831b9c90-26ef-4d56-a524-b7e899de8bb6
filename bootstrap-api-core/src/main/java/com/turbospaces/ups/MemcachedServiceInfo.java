package com.turbospaces.ups;

import org.springframework.cloud.service.ServiceInfo.ServiceLabel;
import org.springframework.cloud.service.UriBasedServiceInfo;

@ServiceLabel("memcached")
public class MemcachedServiceInfo extends UriBasedServiceInfo {
    public static final String MEMCACHED_SCHEME = "memcached";

    public MemcachedServiceInfo(String id, String host, int port) {
        super(id, MEMCACHED_SCHEME, host, port, null, null, null);
    }
    public MemcachedServiceInfo(String id, String uri) {
        super(id, uri);
    }
}