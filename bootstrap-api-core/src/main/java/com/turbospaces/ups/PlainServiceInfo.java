package com.turbospaces.ups;

import org.springframework.cloud.service.UriBasedServiceInfo;

import com.google.common.net.HostAndPort;

public class PlainServiceInfo extends UriBasedServiceInfo {
    public static final String HTTP_SCHEME = "http";
    public static final String HTTPS_SCHEME = "https";
    public static final String DNS_SCHEME = "dns";

    public PlainServiceInfo(String id, String uri) {
        super(id, uri);
    }
    public PlainServiceInfo(String id, String scheme, String host, int port, String username, String password, String path) {
        super(id, scheme, host, port, username, password, path);
    }
    public PlainServiceInfo(String id, String scheme, HostAndPort hostAndPort, String username, String password, String path) {
        super(id, scheme, hostAndPort.getHost(), hostAndPort.getPort(), username, password, path);
    }
    public PlainServiceInfo(String id, String scheme, HostAndPort hostAndPort) {
        super(id, scheme, hostAndPort.getHost(), hostAndPort.getPort(), null, null, null);
    }
}
