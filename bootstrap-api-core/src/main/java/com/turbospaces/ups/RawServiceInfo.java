package com.turbospaces.ups;

import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.UndeclaredThrowableException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;

import org.springframework.cloud.service.ServiceInfo;

import com.google.common.collect.ImmutableList;
import com.google.common.io.ByteSource;

public class RawServiceInfo implements ServiceInfo {
    private final String id;
    private final byte[] payload;

    public RawServiceInfo(String id, byte[] payload) {
        this.id = Objects.requireNonNull(id);
        this.payload = Arrays.copyOf(payload, payload.length);
    }
    @Override
    public String getId() {
        return id;
    }
    public String read() {
        try {
            return toByteSource().asCharSource(StandardCharsets.UTF_8).read();
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public ImmutableList<String> readLines() {
        try {
            return toByteSource().asCharSource(StandardCharsets.UTF_8).readLines();
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public BufferedReader openBufferedStream() {
        try {
            return toByteSource().asCharSource(StandardCharsets.UTF_8).openBufferedStream();
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public ByteSource toByteSource() {
        return ByteSource.wrap(payload);
    }
}
