package com.turbospaces.common;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EnvUtil {
    public static final String ENV_UPS_PREFIX = "UPS_";
    public static final String ENV_CACERT_PREFIX = "CACERT_";

    public static final String ENV_HOSTNAME = "HOSTNAME";
    public static final String ENV_SPACE_NAME = "SPACE_NAME";
    public static final String ENV_STAGE = "STAGE";
    public static final String INDEX = "INDEX";

    public static Optional<String> envStage() {
        return Optional.ofNullable(System.getenv(ENV_STAGE));
    }

    public static String env() {
        return envStage().orElse(StringUtils.EMPTY);
    }
}
