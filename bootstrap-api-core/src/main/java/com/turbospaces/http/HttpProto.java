package com.turbospaces.http;

public interface HttpProto {
    String PARAM_PROXY_REF = "_pref";

    //
    // ~ pretty print HTTP wire
    //
    String CONTEXT_REQ_PRINTER = "req-printer";
    String CONTEXT_RESPONSE_PRINTER = "response-printer";
    String CONTEXT_RESPONSE_DO_NOT_PRINT_BODY = "response-do-not-print-body";
    String CONTEXT_MATCHED = "matched";
    String CONTEXT_STARTED_AT = "started-at";
    String CONTEXT_URI = "uri";
    String CONTEXT_METRICS_TAGS = "metrics-tags";

    String V1 = "/v1";
    String V2 = "/v2";
    String V3 = "/v3";

    //
    // ~ system preserved path(s)
    //
    String VERSION_PATH = "/version";
    String IP_PATH = "/ip";
    String HEALTHCHECK_PATH = "/health_check";
    String THREAD_DUMP_PATH = "/thread_dump";
    String METRICS_PATH = "/metrics";
    String REQUEST_REPLY_PATH = "/request-reply";

    //
    // ~ VERCEL preserved header(s)
    //
    String HEADER_X_VERCEL_IP_COUNTRY = "X-Vercel-Ip-Country";
    String HEADER_X_VERCEL_CONNECTING_IP = "X-Vercel-Connecting-Ip";
    String HEADER_X_USER_AGENT = "X-User-Agent";
    String HEADER_X_REAL_HOST = "X-Real-Host";

    //
    // ~ CloudFlare preserved header(s)
    //
    String HEADER_CF_IP_COUNTRY = "CF-IPCountry";
    String HEADER_CF_CONNECTING_IP = "CF-Connecting-IP";
    String HEADER_CF_CLIENT_CERT_SHA256 = "CF-Client-Cert-SHA256";

    //
    // ~ common application headers
    //
    String HEADER_X_CLIENT_REAL_IP = "X-Client-Real-IP";
    String HEADER_X_CLIENT_COORDINATES = "X-Client-Coordinates";
    String HEADER_X_TRACE_ID = "X-Trace-Id";
    String HEADER_X_ASN = "X-Asn";
    String HEADER_X_ASN_ORG = "X-Asn-Organization";
    String HEADER_X_PLATFORM = "X-Platform";
    String HEADER_X_TIMESTAMP = "X-Timestamp";
    String HEADER_X_CHK = "X-Chk";
    String HEADER_X_SITE_VERIFY = "X-Site-Verify";
    String HEADER_X_SITE_INVISIBLE_VERIFY = "X-Site-Invisible-Verify";
    String HEADER_X_SITE_ONE_TIME_VERIFY = "X-Site-OT-Verify";
    String HEADER_X_SITE_INVISIBLE_ONE_TIME_VERIFY = "X-Site-Invisible-OT-Verify";
    String HEADER_X_REASON = "X-Reason";
    String HEADER_X_MESSAGE_ID = "X-Message-Id";
    String HEADER_X_TOOK = "X-Took";
    String HEADER_X_STATUS = "X-Status";
    String HEADER_X_STATUS_TEXT = "X-Status-Text";
    String HEADER_X_CSRF = "X-CSRF";
    String HEADER_X_TAGS = "X-Tags";
    String HEADER_X_NONCE = "X-Nonce";
    String HEADER_X_CF_CHK = "X-CF-Chk";
    String HEADER_X_ENCRYPT = "X-Payload-Var"; // ~ such naming was done on purpose not to reveal header meaning
    String HEADER_X_ALGORITHM = "X-Payload-AG";


    //
    // ~ common application cookies
    //
    String COOKIE_CF_TRACE = "_cftrace";

    //
    // ~ common application query parameters
    //
    String QUERY_PARAM_HMAC_ALG = "hmacAlg";
    String QUERY_PARAM_HMAC_METHOD = "hmacMethod";
    String QUERY_PARAM_HMAC_KEY = "hmacKey";
    String QUERY_PARAM_NOTIFICATION_CHANNEL = "notificationChannel";

    static String toCtxName(String prop) {
        return String.format("%s.%s", HttpProto.class.getSimpleName(), prop);
    }
}
