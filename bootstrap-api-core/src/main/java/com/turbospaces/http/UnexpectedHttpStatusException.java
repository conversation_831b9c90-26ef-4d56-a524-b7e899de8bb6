package com.turbospaces.http;

import java.io.IOException;
import java.util.Collection;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

@Getter
public abstract class UnexpectedHttpStatusException extends IOException {
    private final int code;
    private final String entity;

    protected UnexpectedHttpStatusException(String statusLine, String entity, int code) {
        super(statusLine + (StringUtils.isNotEmpty(entity) ? " : " + entity : StringUtils.EMPTY));
        this.entity = entity;
        this.code = code;
    }
    public abstract Collection<String> getHeaderValues(String header);
    public abstract Optional<String> getFirstHeaderValue(String header);
}
