package com.turbospaces.executor;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;

import io.vavr.CheckedFunction0;
import io.vavr.CheckedRunnable;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DirectPlatformExecutorService implements PlatformExecutorService {
    public static final DirectPlatformExecutorService INSTANCE = new DirectPlatformExecutorService();

    @Override
    public void execute(Runnable command) {
        MoreExecutors.directExecutor().execute(command);
    }
    @Override
    public ListenableFuture<?> submit(CheckedRunnable action) {
        SettableFuture<Object> settable = SettableFuture.create();

        try {
            MoreExecutors.directExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        action.run();
                        settable.set(new Object());
                    } catch (Throwable err) {
                        settable.setException(err);
                    }
                }
            });
        } catch (Throwable err) {
            settable.setException(err);
        }

        return settable;
    }
    @Override
    public <T> ListenableFuture<T> submit(CheckedFunction0<T> action) {
        SettableFuture<T> settable = SettableFuture.create();

        try {
            MoreExecutors.directExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        settable.set(action.apply());
                    } catch (Throwable err) {
                        settable.setException(err);
                    }
                }
            });
        } catch (Throwable err) {
            settable.setException(err);
        }

        return settable;
    }
    @Override
    public void setBeanName(String name) {

    }
    @Override
    public void afterPropertiesSet() throws Exception {

    }
    @Override
    public void destroy() throws Exception {

    }
}
