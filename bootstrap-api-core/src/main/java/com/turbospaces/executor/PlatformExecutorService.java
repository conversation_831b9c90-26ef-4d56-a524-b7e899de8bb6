package com.turbospaces.executor;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;

import org.slf4j.MDC;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.mdc.MdcUtil;

import io.vavr.CheckedFunction0;
import io.vavr.CheckedFunction1;
import io.vavr.CheckedFunction2;
import io.vavr.CheckedRunnable;

public interface PlatformExecutorService extends Executor, BeanNameAware, InitializingBean, DisposableBean {
    default ListenableFuture<?> submit(CheckedRunnable action) {
        SettableFuture<Object> settable = SettableFuture.create();

        try {
            execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        action.run();
                        settable.set(new Object());
                    } catch (Throwable err) {
                        settable.setException(err);
                    }
                }
            });
        } catch (RejectedExecutionException err) {
            settable.setException(err);
        }

        return settable;
    }
    default <T> ListenableFuture<T> submit(CheckedFunction0<T> action) {
        SettableFuture<T> settable = SettableFuture.create();

        try {
            execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        settable.set(action.apply());
                    } catch (Throwable err) {
                        settable.setException(err);
                    }
                }
            });
        } catch (RejectedExecutionException err) {
            settable.setException(err);
        }

        return settable;
    }
    default <T, R> ListenableFuture<R> submit(CheckedFunction1<T, R> action, T arg) {
        SettableFuture<R> settable = SettableFuture.create();

        try {
            execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        settable.set(action.apply(arg));
                    } catch (Throwable err) {
                        settable.setException(err);
                    }
                }
            });
        } catch (RejectedExecutionException err) {
            settable.setException(err);
        }

        return settable;
    }
    default <T1, T2, R> ListenableFuture<R> submit(CheckedFunction2<T1, T2, R> action, T1 arg1, T2 arg2) {
        SettableFuture<R> settable = SettableFuture.create();

        try {
            execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        settable.set(action.apply(arg1, arg2));
                    } catch (Throwable err) {
                        settable.setException(err);
                    }
                }
            });
        } catch (RejectedExecutionException err) {
            settable.setException(err);
        }

        return settable;
    }
    default Runnable wrapRunnable(Runnable task, Map<String, String> mdcContextMap) {
        return new Runnable() {
            @Override
            public void run() {
                MdcUtil.propagate(mdcContextMap);
                try {
                    task.run();
                } finally {
                    MDC.clear();
                }
            }
        };
    }
    default <V> Callable<V> wrapCallable(Callable<V> task, Map<String, String> mdcContextMap) {
        return new Callable<>() {
            @Override
            public V call() throws Exception {
                MdcUtil.propagate(mdcContextMap);
                try {
                    return task.call();
                } finally {
                    MDC.clear();
                }
            }
        };
    }
}
