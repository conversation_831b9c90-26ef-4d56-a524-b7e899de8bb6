package com.turbospaces.executor;

import java.util.Objects;

public interface ContextWorker extends PlatformExecutorService {
    ContextWorker forKey(WorkUnit unit);

    default ContextWorker ifPresent(WorkUnit unit) {
        if (Objects.nonNull(unit.key())) {
            return actor(unit);
        }
        return this;
    }
    default ContextWorker actor(WorkUnit unit) {
        return forKey(unit);
    }
    default ContextWorker actor(WorkUnit unit, boolean actorIsRequired) {
        if (actorIsRequired) {
            return actor(unit);
        }
        return ifPresent(unit);
    }
}
