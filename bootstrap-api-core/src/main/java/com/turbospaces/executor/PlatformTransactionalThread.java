package com.turbospaces.executor;

import java.util.Optional;

import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.cfg.ApplicationProperties;

public class PlatformTransactionalThread extends PlatformThread implements CurrentTransactionProvider {
    private final CurrentTransactionProvider currentTx;

    public PlatformTransactionalThread(ApplicationProperties props, CurrentTransactionProvider currentTx, Runnable target) {
        super(props, target);
        this.currentTx = currentTx;
    }
    @Override
    public Optional<Object> current() {
        return currentTx.current();
    }
}
