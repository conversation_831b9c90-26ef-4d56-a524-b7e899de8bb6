package com.turbospaces.executor;

import java.util.Objects;

import com.turbospaces.cfg.ApplicationProperties;

public class PlatformThread extends Thread {
    private final ApplicationProperties props;

    public PlatformThread(ApplicationProperties props, Runnable target) {
        super(target);
        this.props = Objects.requireNonNull(props);
    }
    public boolean blockhoundEnabled() {
        return props.APP_BLOCKHOUND_ENABLED.get();
    }
}
