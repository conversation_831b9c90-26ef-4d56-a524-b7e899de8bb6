package com.turbospaces.executor;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import com.google.common.io.ByteSource;

import io.netty.util.AsciiString;

public interface WorkUnit {
    long timestamp();
    String topic();
    byte[] key();
    ByteSource value();
    default Optional<String> lastHeader(String key) {
        return Optional.empty();
    }
    default WorkUnit ifKeyPresent(Consumer<AsciiString> consumer) {
        if (Objects.nonNull(key())) {
            consumer.accept(new AsciiString(key()));
        }
        return this;
    }
}
