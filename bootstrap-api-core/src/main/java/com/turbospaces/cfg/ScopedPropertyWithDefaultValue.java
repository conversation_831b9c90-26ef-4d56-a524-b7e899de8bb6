package com.turbospaces.cfg;

import java.lang.reflect.Type;
import java.util.Objects;
import java.util.function.Function;

import com.netflix.archaius.api.Config;
import com.netflix.archaius.api.Property;

import io.vavr.Function0;
import lombok.Getter;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

final class ScopedPropertyWithDefaultValue<T> extends AbstractScopedProperty<T> {
    private final DynamicPropertyFactory factory;
    private final Config config;
    @Getter
    private final T defaultValue;
    private Class<T> type;
    private Type paramType;

    public ScopedPropertyWithDefaultValue(String key, DynamicPropertyFactory factory, Class<T> type, Config config, T defaultValue) {
        super(key, config);
        this.factory = Objects.requireNonNull(factory);
        this.type = Objects.requireNonNull(type);
        this.config = Objects.requireNonNull(config);
        this.defaultValue = defaultValue;
    }
    public ScopedPropertyWithDefaultValue(String key, DynamicPropertyFactory factory, Type type, Config config, T defaultValue) {
        super(key, config);
        this.factory = Objects.requireNonNull(factory);
        this.paramType = Objects.requireNonNull(type);
        this.config = Objects.requireNonNull(config);
        this.defaultValue = defaultValue;
    }
    @Override
    public T get() {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public T apply() {
                var l = lock.readLock();
                l.lock();
                try {
                    var prop = properties.computeIfAbsent(getKey(), new Function<String, Property<T>>() {
                        @Override
                        public Property<T> apply(String t) {
                            return getProperty(t).orElse(defaultValue);
                        }
                    });
                    return prop.get();
                } finally {
                    l.unlock();
                }
            }
        });
    }
    @Override
    public T get(String scope) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public T apply() {
                var l = lock.readLock();
                l.lock();
                try {
                    var k = getKey();
                    Property<T> prop;
                    if (k.contains(SCOPE_PLACEHOLDER)) {
                        prop = getScopedProperty(getKey().replace(SCOPE_PLACEHOLDER, scope));
                    } else {
                        prop = getScopedProperty(scope + "." + getKey());
                    }
                    var scoped = prop.get();
                    return isNullOrEmpty(scoped) ? ScopedPropertyWithDefaultValue.this.get() : scoped;
                } finally {
                    l.unlock();
                }
            }
        });
    }

    @Override
    public T get(String scope, T fallback) {
        var val = get();
        if (val == null) {
            return fallback;
        }
        return val;
    }

    private Property<T> getScopedProperty(String namespace) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public Property<T> apply() {
                var l = lock.readLock();
                l.lock();
                try {
                    return properties.computeIfAbsent(namespace, new Function<String, Property<T>>() {
                        @Override
                        public Property<T> apply(String t) {
                            if (config.containsKey(t)) {
                                return getProperty(t);
                            }
                            if (config.containsKey(getKey())) {
                                return getProperty(getKey());
                            }
                            return getProperty(t).orElse(defaultValue);
                        }
                    });
                } finally {
                    l.unlock();
                }
            }
        });
    }
    private Property<T> getProperty(String namespace) {
        if (paramType != null) {
            return factory.get(namespace, paramType);
        }
        return factory.get(namespace, type);
    }
}
