package com.turbospaces.cfg;

import java.io.IOException;
import java.net.URL;
import java.util.Properties;

import com.netflix.archaius.api.config.CompositeConfig;

public interface DynamicCompositeConfig extends CompositeConfig {
    //
    // source names
    //
    String RUNTIME_CFG_NAME = "core.RUNTIME_CFG";
    String GIT_CFG_NAME = "core.GIT_CFG";
    String SYSTEM_PROPS_CFG_NAME = "core.SYSTEM_PROPS_CFG";
    String SYSTEM_ENV_CFG_NAME = "core.ENV_PROPS_CFG";
    String CMD_LINE_CFG_NAME = "core.CMD_LINE_CFG";
    String LOCAL_CFG_NAME = "core.LOCAL_APPLICATION_CFG";
    String CLOUD_CFG_NAME = "core.CLOUD_CFG";
    String LEGACY_CFG_NAME = "core.LEGACY_CFG";
    String DEFAULT_APP_CFG_NAME = "core.DEFAULT_APP_CFG";
    String BUILD_META_CFG_NAME = "core.BUILD_META_CFG";
    String CONFIG_CAT_CFG_NAME = "core.CONFIG_CAT_CFG";

    // dynamic factory
    DynamicPropertyFactory factory();

    //
    // ~ local properties
    //
    void setLocalProperty(String key, Object value);
    void setLocalProperties(Properties props);
    void clearLocalProperty(String key);

    //
    // ~ CMD line properties
    //
    void setCmdLineParams(String key, Object value);
    void setCmdLineParams(Properties props);
    void clearCmdLineParams(String key);

    //
    // ~ default properties
    //
    void setDefaultProperty(String key, Object value);
    void setDefaultProperties(Properties props);
    void clearDefaultProperty(String key);
    void clearAllDefaultProperties() throws Exception;

    //
    // utility methods to load local development properties
    //
    DynamicCompositeConfig loadDefaultPropsFromResource(URL url) throws IOException;
    DynamicCompositeConfig loadLocalDevProperties(String username) throws Exception;
    DynamicCompositeConfig loadLocalDevProperties() throws Exception;
}
