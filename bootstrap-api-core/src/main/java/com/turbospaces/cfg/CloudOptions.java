package com.turbospaces.cfg;

public interface CloudOptions {
    String CLOUD_APP_NAMESPACE = "cloud.application.";
    String CLOUD_SERVICES_NAMESPACE = "cloud.services.";

    String CLOUD_APP_SPACE_NAME = CLOUD_APP_NAMESPACE + "space_name";
    String CLOUD_APP_NAME = CLOUD_APP_NAMESPACE + "name";
    String CLOUD_APP_ID = CLOUD_APP_NAMESPACE + "app-id";
    String CLOUD_APP_INSTANCE_INDEX = CLOUD_APP_NAMESPACE + "instance_index";
    String CLOUD_APP_HOST = CLOUD_APP_NAMESPACE + "host";

    String CLOUD_APP_PORT = CLOUD_APP_NAMESPACE + "port";
    String CLOUD_APP_SECONDARY_PORT = CLOUD_APP_NAMESPACE + "secondary-port";
    String CLOUD_APP_TERTIARY_PORT = CLOUD_APP_NAMESPACE + "tertiary-port";
}
