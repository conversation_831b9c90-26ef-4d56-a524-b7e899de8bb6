package com.turbospaces.cfg;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;

import com.google.common.collect.Range;
import com.google.common.reflect.TypeToken;
import com.netflix.archaius.DefaultPropertyFactory;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.config.CompositeConfig;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DynamicPropertyFactory extends DefaultPropertyFactory {
    private final CompositeConfig config;

    private DynamicPropertyFactory(CompositeConfig config) {
        super(config);
        this.config = Objects.requireNonNull(config);
    }
    public static DynamicPropertyFactory from(CompositeConfig config) {
        return new DynamicPropertyFactory(config);
    }
    @Override
    public CompositeConfig getConfig() {
        return this.config;
    }
    @Override
    public <T> Property<T> get(String key, Class<T> type) {
        return super.get(key, type);
    }
    @Override
    public <T> Property<T> get(String key, Type type) {
        return super.get(key, type);
    }
    public <T> ScopedProperty<T> getScoped(String key, Class<T> type, T defaultValue) {
        return new ScopedPropertyWithDefaultValue<T>(key, this, type, config, defaultValue);
    }
    public <T> ScopedProperty<T> getScoped(String key, TypeToken<T> type, T defaultValue) {
        return new ScopedPropertyWithDefaultValue<>(key, this, type.getType(), config, defaultValue);
    }
    @Deprecated(forRemoval = true)
    // use getScoped with TypeToken instead
    public <T> ScopedProperty<T> getScoped(String key, Function<String, Property<T>> fetcher) {
        return new ScopedComputedProperty<T>(key, config, fetcher);
    }
    public Property<Duration> rangeValue(String key, Duration defaultValue, Range<Duration> boundary) {
        Property<Duration> prop = get(key, Duration.class).orElse(defaultValue);
        Consumer<Duration> consumer = new Consumer<>() {
            private final AtomicBoolean report = new AtomicBoolean(false);

            @Override
            public void accept(Duration t) {
                if (Objects.nonNull(t)) {
                    if (boundary.contains(t)) {
                        if (report.get()) {
                            log.info("property: {} has been accepted and validated with new value: {}", prop.getKey(), t);
                        }
                        report.set(true);
                    } else {
                        log.error("property: {} is out of range: {}, new value: {}", prop.getKey(), boundary, t);
                    }
                }
            }
        };

        //
        // ~ validate immediately
        //
        consumer.accept(defaultValue);

        //
        // ~ subscribe for any further change
        //
        if (Objects.nonNull(boundary)) {
            prop.subscribe(consumer);
            log.debug("subscribed to: {} and will monitor value of range: {}", prop.getKey(), boundary);
        }

        return prop;
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<Integer>> listOfInts(String key) {
        TypeToken<List<Integer>> type = new TypeToken<>() {};
        Class<List<Integer>> rawType = (Class<List<Integer>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<Long>> listOfLongs(String key) {
        TypeToken<List<Long>> type = new TypeToken<>() {};
        Class<List<Long>> rawType = (Class<List<Long>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<String>> listOfStrings(String key) {
        TypeToken<List<String>> type = new TypeToken<>() {};
        Class<List<String>> rawType = (Class<List<String>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<Double>> listOfDoubles(String key) {
        TypeToken<List<Double>> type = new TypeToken<>() {};
        Class<List<Double>> rawType = (Class<List<Double>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<BigDecimal>> listOfBigDecimals(String key) {
        TypeToken<List<BigDecimal>> type = new TypeToken<>() {};
        Class<List<BigDecimal>> rawType = (Class<List<BigDecimal>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<LocalDate>> setOfLocalDates(String key) {
        TypeToken<Set<LocalDate>> type = new TypeToken<>() {};
        Class<Set<LocalDate>> rawType = (Class<Set<LocalDate>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<Pattern>> setOfPatterns(String key) {
        TypeToken<Set<Pattern>> type = new TypeToken<>() {};
        Class<Set<Pattern>> rawType = (Class<Set<Pattern>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<Integer>> setOfInts(String key) {
        TypeToken<Set<Integer>> type = new TypeToken<>() {};
        Class<Set<Integer>> rawType = (Class<Set<Integer>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<Long>> setOfLongs(String key) {
        TypeToken<Set<Long>> type = new TypeToken<>() {};
        Class<Set<Long>> rawType = (Class<Set<Long>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<String>> setOfStrings(String key) {
        TypeToken<Set<String>> type = new TypeToken<>() {};
        Class<Set<String>> rawType = (Class<Set<String>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<Double>> setOfDoubles(String key) {
        TypeToken<Set<Double>> type = new TypeToken<>() {};
        Class<Set<Double>> rawType = (Class<Set<Double>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Set<BigDecimal>> setOfBigDecimals(String key) {
        TypeToken<Set<BigDecimal>> type = new TypeToken<>() {};
        Class<Set<BigDecimal>> rawType = (Class<Set<BigDecimal>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<LocalDate>> listOfLocalDates(String key) {
        TypeToken<List<LocalDate>> type = new TypeToken<>() {};
        Class<List<LocalDate>> rawType = (Class<List<LocalDate>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<List<Pattern>> listOfPatterns(String key) {
        TypeToken<List<Pattern>> type = new TypeToken<>() {};
        Class<List<Pattern>> rawType = (Class<List<Pattern>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    public Property<Map<String, String>> mapOfStringStrings(String key) {
        return mapOfNullableStringStrings(key).orElse(Collections.emptyMap());
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Map<String, String>> mapOfNullableStringStrings(String key) {
        TypeToken<Map<String, String>> type = new TypeToken<>() {};
        Class<Map<String, String>> rawType = (Class<Map<String, String>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast);
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Map<String, Integer>> mapOfStringInts(String key) {
        TypeToken<Map<String, Integer>> type = new TypeToken<>() {};
        Class<Map<String, Integer>> rawType = (Class<Map<String, Integer>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast).orElse(Collections.emptyMap());
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Map<String, Long>> mapOfStringLongs(String key) {
        TypeToken<Map<String, Long>> type = new TypeToken<>() {};
        Class<Map<String, Long>> rawType = (Class<Map<String, Long>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast).orElse(Collections.emptyMap());
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Map<String, Double>> mapOfStringDoubles(String key) {
        TypeToken<Map<String, Double>> type = new TypeToken<>() {};
        Class<Map<String, Double>> rawType = (Class<Map<String, Double>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast).orElse(Collections.emptyMap());
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Map<String, BigDecimal>> mapOfStringBigDecimals(String key) {
        TypeToken<Map<String, BigDecimal>> type = new TypeToken<>() {};
        Class<Map<String, BigDecimal>> rawType = (Class<Map<String, BigDecimal>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast).orElse(Collections.emptyMap());
    }
    @SuppressWarnings({ "unchecked", "serial" })
    public Property<Map<String, BigDecimal>> mapOfStringBigDecimals(String key, Map<String, BigDecimal> defaultValue) {
        TypeToken<Map<String, BigDecimal>> type = new TypeToken<>() {};
        Class<Map<String, BigDecimal>> rawType = (Class<Map<String, BigDecimal>>) type.getRawType();
        return get(key, type.getType()).map(rawType::cast).orElse(defaultValue);
    }
    public Property<Map<String, String>> mapOfStringStrings(String key, Map<String, String> defaultMap) {
        return mapOfNullableStringStrings(key).orElse(defaultMap);
    }
}
