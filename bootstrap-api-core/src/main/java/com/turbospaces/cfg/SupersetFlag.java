package com.turbospaces.cfg;

import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Consumer;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.DisposableBean;

import com.netflix.archaius.api.Property;
import com.turbospaces.api.AsFlux;

import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitFailureHandler;
import reactor.core.publisher.Sinks.EmitResult;

@Slf4j
public class SupersetFlag implements Sinks.Many<Boolean>, Property<Boolean>, DisposableBean, AsFlux<Boolean> {
    private final Sinks.Many<Boolean> theirs;
    private final Sinks.Many<Boolean> ours;
    private final Property<Boolean> delegate;
    private final Flux<Boolean> latest;
    private final Subscription subscription;

    public SupersetFlag(Property<Boolean> delegate) {
        this.theirs = Sinks.many().replay().latestOrDefault(delegate.get()); // ~ preserve current value as initial
        this.ours = Sinks.many().replay().latestOrDefault(false); // ~ turn off by default
        this.delegate = Objects.requireNonNull(delegate);

        this.subscription = delegate.subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean val) {
                StopWatch stopWatch = StopWatch.createStarted();
                EmitResult result = theirs.tryEmitNext(val); // ~ notify all in current thread
                stopWatch.stop();
                if (result.isSuccess()) {
                    log.trace("accepted new value: {} in: {}", val, stopWatch);
                }
            }
        });

        //
        // ~ combine latest values of each processor and ignore any non-distint output so that we are not firing repeated value
        //
        this.latest = Flux.combineLatest(
                theirs.asFlux(),
                ours.asFlux(),
                new BiFunction<Boolean, Boolean, Boolean>() {
                    @Override
                    public Boolean apply(Boolean left, Boolean right) {
                        return BooleanUtils.and(new boolean[] { left, right });
                    }
                }).distinctUntilChanged();
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(subscription)) {
            subscription.unsubscribe();
        }
    }
    @Override
    public Boolean get() {
        //
        // ~ actually non-blocking since 2 processors always have default value
        //
        return latest.blockFirst();
    }
    @Override
    public String getKey() {
        return delegate.getKey();
    }
    @Override
    public Subscription subscribe(Consumer<Boolean> consumer) {
        Disposable disposable = latest.subscribe(consumer);
        return new Subscription() {
            @Override
            public void unsubscribe() {
                disposable.dispose();
            }
        };
    }
    @Override
    @SuppressWarnings("rawtypes")
    public Object scanUnsafe(Attr key) {
        return ours.scanUnsafe(key);
    }
    @Override
    public EmitResult tryEmitNext(Boolean t) {
        return ours.tryEmitNext(t);
    }
    @Override
    public EmitResult tryEmitComplete() {
        return ours.tryEmitComplete();
    }
    @Override
    public EmitResult tryEmitError(Throwable error) {
        return ours.tryEmitError(error);
    }
    @Override
    public void emitNext(Boolean t, EmitFailureHandler failureHandler) {
        ours.emitNext(t, failureHandler);
    }
    @Override
    public void emitComplete(EmitFailureHandler failureHandler) {
        ours.emitComplete(failureHandler);
    }
    @Override
    public void emitError(Throwable error, EmitFailureHandler failureHandler) {
        ours.emitError(error, failureHandler);
    }
    @Override
    public int currentSubscriberCount() {
        return ours.currentSubscriberCount();
    }
    @Override
    public Flux<Boolean> asFlux() {
        return latest;
    }
}
