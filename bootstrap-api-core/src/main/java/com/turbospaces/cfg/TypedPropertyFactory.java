package com.turbospaces.cfg;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Properties;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.reflect.FieldUtils;

import com.google.common.collect.ImmutableMap;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.config.CompositeConfig;

import reactor.core.scheduler.Scheduler;
import reactor.util.retry.Retry;

public interface TypedPropertyFactory {
    int PRIMARY_PORT = 8089;
    int SECONDARY_PORT = 8091;
    int TERTIARY_PORT = 8093;
    String APPLICATION_PROPERTIES = "application.properties";

    DynamicPropertyFactory factory();
    List<FragmentProperties> fragments();
    String externalIp();
    Retry retry(Scheduler scheduler);

    default CompositeConfig cfg() {
        return factory().getConfig();
    }
    default void analyze(URL url) throws Exception {
        Map<String, Property<?>> map = readFieldAsPropMap();

        Properties table = new Properties();
        try (InputStream io = url.openStream()) {
            table.load(io);
        }

        for (Entry<Object, Object> it : table.entrySet()) {
            String key = it.getKey().toString();
            String value = it.getValue().toString();
            if (BooleanUtils.isFalse(map.containsKey(key))) {
                System.err.printf("unknown: %s%n", key);
            } else {
                String toString = Objects.toString(map.get(key).get());
                if (Objects.equals(toString, value)) {
                    System.out.printf("toRemove: %s%n", key);
                }
            }
        }
    }
    default Map<String, Object> readFieldAsMap() {
        return readFieldAsPropMap()
                .entrySet().stream()
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue().get()), HashMap::putAll);
    }
    default Map<String, Property<?>> readFieldAsPropMap() {
        Map<String, Property<?>> map = new HashMap<>();
        try {
            collectProperties(getClass(), this, map);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        //
        // ~ remove common props
        //
        Arrays.stream(FieldUtils.getAllFields(CloudOptions.class))
                .map(Field::getName)
                .forEach(map::remove);

        return map;
    }

    private void collectProperties(Class<?> clazz, Object instance, Map<String, Property<?>> map) throws IllegalAccessException {
        for (Field field : FieldUtils.getAllFields(clazz)) {
            if (!Modifier.isPublic(field.getModifiers()) || Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            Object fieldValue = FieldUtils.readField(field, instance, true);
            if (fieldValue == null) {
                continue;
            }

            if (FragmentProperties.class.isAssignableFrom(field.getType())) {
                // Recursively collect properties from fragments
                FragmentProperties fragment = (FragmentProperties) fieldValue;
                collectProperties(fragment.getClass(), fragment, map);
            } else if (Property.class.isAssignableFrom(field.getType())) {
                Property<?> prop = (Property<?>) fieldValue;
                map.put(prop.getKey(), prop);
            }
        }
    }
    default void putValueIfPresent(String key, ImmutableMap.Builder<String, String> map) {
        if (factory().getConfig().containsKey(key)) {
            String raw = Objects.requireNonNull(factory().getConfig().getString(key));
            map.put(key, raw);
        }
    }
    default void putValue(String key, ImmutableMap.Builder<String, String> map, String defaultValue) {
        if (factory().getConfig().containsKey(key)) {
            String raw = Objects.requireNonNull(factory().getConfig().getString(key));
            map.put(key, raw);
        } else {
            map.put(key, Objects.requireNonNull(defaultValue));
        }
    }
}
