package com.turbospaces.cfg;

import java.util.Objects;
import java.util.function.Function;

import com.netflix.archaius.api.Config;
import com.netflix.archaius.api.Property;

// todo ready for remove in next release
@Deprecated
final class ScopedComputedProperty<T> extends AbstractScopedProperty<T> {
    private final Function<String, Property<T>> fetcher;

    public ScopedComputedProperty(String key, Config config, Function<String, Property<T>> fetcher) {
        super(key, config);
        this.fetcher = Objects.requireNonNull(fetcher);
    }
    @Override
    public T get() {
        var l = lock.readLock();
        l.lock();
        try {
            return properties.computeIfAbsent(getKey(), fetcher).get();
        } finally {
            l.unlock();
        }
    }
    @Override
    public T get(String scope) {
        var l = lock.readLock();
        l.lock();
        try {
            T scoped = properties.computeIfAbsent(scope + "." + getKey(), fetcher).get();
            return isNullOrEmpty(scoped) ? get() : scoped;
        } finally {
            l.unlock();
        }
    }

    @Override
    public T get(String scope, T defaultValue) {
        throw new UnsupportedOperationException();
    }
}
