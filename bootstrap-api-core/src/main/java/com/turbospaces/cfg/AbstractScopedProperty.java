package com.turbospaces.cfg;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import org.jctools.maps.NonBlockingHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.netflix.archaius.api.Config;
import com.netflix.archaius.api.ConfigListener;
import com.netflix.archaius.api.Property;

import lombok.Getter;

abstract class AbstractScopedProperty<T> implements ScopedProperty<T> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ReadWriteLock lock = new ReentrantReadWriteLock();
    protected final Map<String, Property<T>> properties = new NonBlockingHashMap<>();
    @Getter
    protected final String key;

    public AbstractScopedProperty(String key, Config config) {
        this.key = Objects.requireNonNull(key);

        //
        // ~ invalidate properties just in case
        //
        config.addListener(new ConfigListener() {
            @Override
            public void onError(Throwable error, Config cfg) {

            }
            @Override
            public void onConfigUpdated(Config cfg) {
                logger.trace("({}) cfg: {} has been updated", getKey(), cfg.getName());
                clear();
            }
            @Override
            public void onConfigRemoved(Config cfg) {
                logger.trace("({}) cfg: {} has been removed", getKey(), cfg.getName());
                clear();
            }
            @Override
            public void onConfigAdded(Config cfg) {
                logger.trace("({}) cfg: {} has been added", getKey(), cfg.getName());
                clear();
            }
        });
    }
    protected boolean isNullOrEmpty(T value) {
        if (value instanceof Collection<?> c) {
            return c.isEmpty();
        } else if (value instanceof Map<?, ?> m) {
            return m.isEmpty();
        }
        return Objects.isNull(value);
    }
    protected void clear() {
        var l = lock.writeLock();
        l.lock();
        try {
            properties.clear();
        } finally {
            l.unlock();
        }
    }
}
