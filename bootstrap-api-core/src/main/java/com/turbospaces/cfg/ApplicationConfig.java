package com.turbospaces.cfg;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.net.URL;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Random;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.ResourceUtils;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.netflix.archaius.CustomDecoder;
import com.netflix.archaius.api.TypeConverter;
import com.netflix.archaius.api.TypeConverter.Factory;
import com.netflix.archaius.api.TypeConverter.Registry;
import com.netflix.archaius.api.config.SettableConfig;
import com.netflix.archaius.api.exceptions.ConfigException;
import com.netflix.archaius.config.DefaultCompositeConfig;
import com.netflix.archaius.config.DefaultSettableConfig;
import com.netflix.archaius.config.EmptyConfig;
import com.netflix.archaius.config.MapConfig;
import com.netflix.archaius.config.SystemConfig;
import com.netflix.archaius.exceptions.ParseException;
import com.turbospaces.common.EnvUtil;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import com.typesafe.config.ConfigParseOptions;
import com.typesafe.config.ConfigSyntax;
import com.typesafe.config.ConfigValue;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ApplicationConfig extends DefaultCompositeConfig implements DynamicCompositeConfig {
    private final DynamicPropertyFactory factory;

    //
    // ~ keep package protected
    //
    ApplicationConfig() {
        //
        // ~ no need to add sensitive properties
        //
        List<String> ignorePattern = new LinkedList<>();
        ignorePattern.add(EnvUtil.ENV_UPS_PREFIX);
        ignorePattern.add(EnvUtil.ENV_SPACE_NAME);
        ignorePattern.add(EnvUtil.ENV_HOSTNAME);

        // replace
        Map<String, String> oldgetenv = new HashMap<>(System.getenv());
        Map<String, String> newgetenv = new HashMap<>();
        for (Entry<String, String> it : oldgetenv.entrySet()) {
            String oldProp = it.getKey();
            String newProp = oldProp.toLowerCase().replace("_", ".");

            boolean allowed = true;
            for (String s : ignorePattern) {
                if (oldProp.startsWith(s)) {
                    allowed = false;
                    break;
                }
            }
            if (allowed) {
                log.trace("adding ENV prop {} {}={}", oldProp, newProp, it.getValue());
                newgetenv.put(newProp, it.getValue());
            }
        }

        //
        // ~ ordered hierarchy
        //
        try {
            addConfig(RUNTIME_CFG_NAME, new DefaultSettableConfig());
            addConfig(LOCAL_CFG_NAME, new DefaultSettableConfig());
            addConfig(CLOUD_CFG_NAME, new DefaultSettableConfig());
            addConfig(CMD_LINE_CFG_NAME, new DefaultSettableConfig());
            addConfig(SYSTEM_ENV_CFG_NAME, new MapConfig(newgetenv));
            addConfig(SYSTEM_PROPS_CFG_NAME, new SystemConfig());
            addConfig(CONFIG_CAT_CFG_NAME, new DefaultSettableConfig());
            addConfig(GIT_CFG_NAME, new DefaultCompositeConfig());
            addConfig(LEGACY_CFG_NAME, EmptyConfig.INSTANCE);
            addConfig(DEFAULT_APP_CFG_NAME, new DefaultSettableConfig());
            addBuildMetaConfig(new DefaultSettableConfig());
        } catch (ConfigException err) {
            ExceptionUtils.wrapAndThrow(err);
        }

        factory = DynamicPropertyFactory.from(this);

        setDecoder(CustomDecoder.create(Collections.singleton(new Factory() {
            @Override
            public Optional<TypeConverter<?>> get(Type type, Registry registry) {
                if (type.equals(Pattern.class)) {
                    return Optional.of(Pattern::compile);
                }
                return Optional.empty();
            }
        })));
    }
    @Override
    public DynamicPropertyFactory factory() {
        return factory;
    }
    @Override
    public void clearLocalProperty(String key) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(LOCAL_CFG_NAME);
        log.info("clearing local prop: {}", key);
        c.clearProperty(key);
    }
    @Override
    public void setLocalProperty(String key, Object value) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(LOCAL_CFG_NAME);
        log.info("setting local prop: {} -> {}", key, value);
        c.setProperty(key, value);
    }
    @Override
    public void setLocalProperties(Properties props) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(LOCAL_CFG_NAME);
        log.info("setting local props: {}", props);
        c.setProperties(props);
    }
    @Override
    public void clearCmdLineParams(String key) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(CMD_LINE_CFG_NAME);
        log.info("clearing command line prop: {}", key);
        c.clearProperty(key);
    }
    @Override
    public void setCmdLineParams(String key, Object value) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(CMD_LINE_CFG_NAME);
        log.info("setting command line prop: {} -> {}", key, value);
        c.setProperty(key, value);
    }
    @Override
    public void setCmdLineParams(Properties props) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(CMD_LINE_CFG_NAME);
        log.info("setting command line props: {}", props);
        c.setProperties(props);
    }
    @Override
    public void clearDefaultProperty(String key) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(DEFAULT_APP_CFG_NAME);
        log.info("clearing default prop: {}", key);
        c.clearProperty(key);
    }
    @Override
    public void setDefaultProperty(String key, Object value) {
        SettableConfig c = (SettableConfig) getConfig(DEFAULT_APP_CFG_NAME);
        log.info("setting default prop: {} -> {}", key, value);
        c.setProperty(key, value);
    }
    @Override
    public void setDefaultProperties(Properties props) {
        DefaultSettableConfig c = (DefaultSettableConfig) getConfig(DEFAULT_APP_CFG_NAME);
        log.info("setting default props: {}", props);
        c.setProperties(props);
    }
    @Override
    public void clearAllDefaultProperties() throws Exception {
        log.info("clearing default cfg: {}", ImmutableList.copyOf(getConfig(DEFAULT_APP_CFG_NAME).keys()));
        replaceConfig(DEFAULT_APP_CFG_NAME, new DefaultSettableConfig());
    }
    @Override
    public Object getRawProperty(String key) {
        var normalized = super.getRawProperty(normalizePropertyKey(key));
        return Objects.nonNull(normalized) ? normalized : super.getRawProperty(key);
    }
    @Override
    public ApplicationConfig loadLocalDevProperties(String username) throws Exception {
        for (String pattern : new String[] { "template", username }) {
            String userOverrideProps = String.format(ResourceUtils.CLASSPATH_URL_PREFIX + "local-dev-%s.properties", pattern);
            String userOverrideCfg = String.format(ResourceUtils.CLASSPATH_URL_PREFIX + "local-dev-%s.conf", pattern);

            URL propsURL = null;
            URL cfgURL = null;

            try {
                propsURL = ResourceUtils.getURL(userOverrideProps);
            } catch (FileNotFoundException err) {
                log.trace(err.getMessage(), err);
            }

            try {
                cfgURL = ResourceUtils.getURL(userOverrideCfg);
            } catch (FileNotFoundException err) {
                log.trace(err.getMessage(), err);
            }

            if (propsURL != null) {
                try (InputStream io = propsURL.openStream()) {
                    Properties tmp = new Properties();
                    tmp.load(io);
                    setLocalProperties(tmp);
                }
            }

            if (cfgURL != null) {
                File file = new File(cfgURL.toURI());

                ConfigParseOptions parseOptions = ConfigParseOptions.defaults().setAllowMissing(false).setSyntax(ConfigSyntax.CONF);
                Config typesafeConfig = ConfigFactory.parseFile(file, parseOptions).resolve();

                for (Map.Entry<String, ConfigValue> entry : typesafeConfig.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue().unwrapped();

                    if (Objects.isNull(value)) {
                        clearLocalProperty(key);
                    } else if (value instanceof List) {
                        List<?> list = (List<?>) value;
                        setLocalProperty(key, Joiner.on(getListDelimiter()).join(list));
                    } else if (value instanceof Set) {
                        Set<?> set = (Set<?>) value;
                        setLocalProperty(key, Joiner.on(getListDelimiter()).join(set));
                    } else if (value instanceof Map) {
                        Map<?, ?> map = (Map<?, ?>) value;
                        StringBuilder builer = new StringBuilder();
                        for (Iterator<?> it = map.entrySet().iterator(); it.hasNext();) {
                            Map.Entry<?, ?> next = (Entry<?, ?>) it.next();
                            builer.append(next.toString()).append("=").append(next);
                            if (it.hasNext()) {
                                builer.append(getListDelimiter());
                            }
                        }
                        setLocalProperty(key, builer);
                    } else {
                        setLocalProperty(key, value);
                    }
                }
            }
        }

        return this;
    }
    @Override
    public ApplicationConfig loadDefaultPropsFromResource(URL resource) throws IOException {
        try (InputStream io = resource.openStream()) {
            Properties props = new Properties();
            props.load(io);
            setDefaultProperties(props);
        }
        return this;
    }
    @Override
    public ApplicationConfig loadLocalDevProperties() throws Exception {
        String username = System.getProperty("user.name");
        loadLocalDevProperties(username);
        return this;
    }
    @Override
    public <T> T getValueWithDefault(Type type, String key, T defaultValue) {
        try {
            return super.getValueWithDefault(type, key, defaultValue);
        } catch (ParseException e) {
            log.error("Error when parsing property: {}", key, e);
            return defaultValue;
        }
    }
    private void addBuildMetaConfig(DefaultSettableConfig cfg) throws ConfigException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("git.properties")) {
            if (is != null) {
                addConfig(DynamicCompositeConfig.BUILD_META_CFG_NAME, cfg);
                Properties props = new Properties();
                props.load(is);
                cfg.setProperties(props);
            }
        } catch (IOException e) {
            log.error("Can't load git build properties", e);
        }
    }
    public static ApplicationConfig mock(int port) {
        ApplicationConfig cfg = mock();
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_PORT, port);
        return cfg;
    }
    public static ApplicationConfig mock() {
        ApplicationConfig cfg = new ApplicationConfig();

        String group = "groupId-" + Math.abs(System.identityHashCode(cfg));
        String app = "artifactId-" + Math.abs(new Random().nextLong());
        String vendor = System.getProperty("java.vendor");
        String version = System.getProperty("java.version");

        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_SPACE_NAME, vendor);
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_ID, app);
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_NAME, String.format("%s:%s:%s", group, app, version));

        return cfg;
    }
    private static String normalizePropertyKey(String key) {
        return key.replaceAll("[.-]", "_");
    }
}
