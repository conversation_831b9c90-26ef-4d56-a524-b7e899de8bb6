package com.turbospaces.ssl;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.lang.reflect.UndeclaredThrowableException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.PKIXParameters;
import java.security.cert.TrustAnchor;
import java.security.cert.X509Certificate;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import com.google.common.collect.ImmutableList;
import com.google.common.net.HostAndPort;
import com.turbospaces.ups.PlainServiceInfo;

public class SSL {
    private static final Logger LOGGER = LoggerFactory.getLogger(SSL.class);
    protected final Set<TrustManager> trustManagers = new LinkedHashSet<>();
    protected KeyManager[] keyManagers;

    public void loadKeyStore(File file, String password) throws Exception {
        Assert.isTrue(file.exists(), "keystore does not exist");

        try (FileInputStream is = new FileInputStream(file)) {
            KeyStore keystore = KeyStore.getInstance(KeyStore.getDefaultType());
            keystore.load(is, password.toCharArray());

            if (LOGGER.isTraceEnabled()) {
                PKIXParameters params = new PKIXParameters(keystore);
                for (TrustAnchor ta : params.getTrustAnchors()) {
                    X509Certificate cert = ta.getTrustedCert();
                    LOGGER.trace(cert.toString());
                }
            }

            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(keystore);
            X509TrustManager trustManager = (X509TrustManager) trustManagerFactory.getTrustManagers()[0];
            trustManagers.add(trustManager);

            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(keystore, password.toCharArray());
            keyManagers = kmf.getKeyManagers();
        }
    }
    public SSL addUntrustedCertificates(PlainServiceInfo... infos) {
        return addUntrustedCertificates(Arrays.stream(infos).map(info -> {
            if (info.getPort() > 0) {
                return HostAndPort.fromParts(info.getHost(), info.getPort());
            }
            return HostAndPort.fromParts(info.getHost(), 443);
        }).collect(Collectors.toList()));
    }
    public SSL addUntrustedCertificates(HostAndPort... hostAndPorts) {
        return addUntrustedCertificates(ImmutableList.copyOf(hostAndPorts));
    }
    public SSL addUntrustedCertificates(Collection<HostAndPort> hostAndPorts) {
        try {
            Set<X509Certificate> untrustedCertificates = new HashSet<>();
            for (HostAndPort hap : hostAndPorts) {
                List<X509Certificate> asList = Arrays.asList(collectChain(hap));
                untrustedCertificates.addAll(asList);
            }
            addUntrustedCertificatesFromChain(untrustedCertificates);
        } catch (Exception err) {
            ExceptionUtils.wrapAndThrow(err);
        }
        return this;
    }
    public SSLContext build() {
        return build("TLS");
    }
    public SSLContext build(String protocol) {
        try {
            SSLContext sslcontext = SSLContext.getInstance(protocol);

            sslcontext.init(keyManagers, trustManagers.toArray(new TrustManager[trustManagers.size()]), null);
            return sslcontext;
        } catch (Exception err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public Map.Entry<SSLSocketFactory, X509TrustManager> buildSSLFactory() {
        try {
            SSLContext sslcontext = SSLContext.getInstance("TLS");
            TrustManager[] tmanagers = trustManagers.toArray(new TrustManager[trustManagers.size()]);
            sslcontext.init(null, tmanagers, null);

            Iterator<TrustManager> it = trustManagers.iterator();
            X509TrustManager trustManager = (X509TrustManager) it.next();
            if (it.hasNext()) {
                throw new IllegalStateException("Unexpected default trust managers:" + Arrays.toString(tmanagers));
            }
            return new AbstractMap.SimpleEntry<>(sslcontext.getSocketFactory(), trustManager);
        } catch (Exception err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public void addUntrustedCertificatesFromChain(Collection<X509Certificate> chain) throws Exception {
        TrustManagerFactory tmfactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

        if (!chain.isEmpty()) {
            KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
            ks.load(null, null);

            int count = 0;
            for (X509Certificate cert : chain) {
                LOGGER.trace("adding trusted material {}", cert);
                String alias = String.valueOf(count++);
                ks.setCertificateEntry(alias, cert);
            }

            tmfactory.init(ks);
            trustManagers.addAll(Arrays.asList(tmfactory.getTrustManagers()));
        }
    }
    public void addUntrustedCertificatesFromFiles(Collection<File> files) throws Exception {
        TrustManagerFactory tmfactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

        for (File f : files) {
            LOGGER.trace("adding trusted material from {}", f);
            KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());

            String payload = Files.readString(f.toPath());
            LOGGER.trace(payload);
            ks.load(new ByteArrayInputStream(payload.getBytes(StandardCharsets.UTF_8)), null);

            tmfactory.init(ks);
            trustManagers.addAll(Arrays.asList(tmfactory.getTrustManagers()));
        }
    }
    public static File dumpTrustStore(KeyStore keyStore, String password) {
        try {
            File f = File.createTempFile("truststore", null);
            try (FileOutputStream out = new FileOutputStream(f)) {
                f.deleteOnExit();
                keyStore.store(out, password.toCharArray());
            }
            return f;
        } catch (Exception err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public static void addCertificates(KeyStore trustStore, Collection<Certificate> chain) throws Exception {
        if (Objects.nonNull(chain) && BooleanUtils.isFalse(chain.isEmpty())) {
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init((KeyStore) null);
            X509TrustManager defaultTrustManager = (X509TrustManager) trustManagerFactory.getTrustManagers()[0];
            X509Certificate[] cacerts = defaultTrustManager.getAcceptedIssuers();

            trustStore.load(null);
            int counter = 0;
            for (X509Certificate cert : cacerts) {
                trustStore.setCertificateEntry(String.valueOf(counter++), cert);
            }
            counter = 0;
            for (Certificate cert : chain) {
                String alias = "alias-" + counter++;
                LOGGER.debug("adding additional cert={} alias = {}", cert, alias);
                trustStore.setCertificateEntry(alias, cert);
            }
        }
    }
    public static X509Certificate[] collectChain(HostAndPort hostAndPort) throws Exception {
        LOGGER.debug("collecting certificate chain to {}", hostAndPort);

        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init((KeyStore) null);
        X509TrustManager defaultTrustManager = (X509TrustManager) trustManagerFactory.getTrustManagers()[0];

        SSLContext context = SSLContext.getInstance("TLS");
        List<X509Certificate> chain = new LinkedList<>();
        X509TrustManager collector = new X509TrustManager() {
            @Override
            public synchronized X509Certificate[] getAcceptedIssuers() {
                return defaultTrustManager.getAcceptedIssuers();
            }
            @Override
            public synchronized void checkClientTrusted(X509Certificate[] certChain, String authType) {
                try {
                    chain.addAll(Arrays.asList(certChain));
                    defaultTrustManager.checkClientTrusted(certChain, authType);
                } catch (CertificateException err) {
                    LOGGER.trace(err.getMessage(), err);
                }
            }
            @Override
            public synchronized void checkServerTrusted(X509Certificate[] certChain, String authType) {
                try {
                    chain.addAll(Arrays.asList(certChain));
                    defaultTrustManager.checkServerTrusted(certChain, authType);
                } catch (CertificateException err) {
                    LOGGER.trace(err.getMessage(), err);
                }
            }
        };
        context.init(null, new TrustManager[] { collector }, null);
        SSLSocketFactory factory = context.getSocketFactory();

        try {
            try (SSLSocket socket = (SSLSocket) factory.createSocket(hostAndPort.getHost(), hostAndPort.getPortOrDefault(443))) {
                socket.startHandshake();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return chain.toArray(new X509Certificate[chain.size()]);
    }
}
