package com.turbospaces.cache;

public interface ReplicatedCacheManager {
    void clearAll(boolean localOnly) throws Throwable;

    default void onCachePut(String cacheKey, byte[] id, byte[] data) throws Throwable {}
    default void onCachePutAll(String cacheKey, byte[] data) throws Throwable {}
    default void onCacheRemove(String cacheKey, byte[] id) throws Throwable {}
    default void onCacheRemoveAll(String cacheKey, byte[] data) throws Throwable {}
    default void onCacheClear(String cacheKey) throws Throwable {}
    default void onTablesModify(String tables) throws Throwable {}
    default void onCacheClearAll(boolean localOnly) throws Throwable {}
}
