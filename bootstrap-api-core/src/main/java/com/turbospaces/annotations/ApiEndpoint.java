package com.turbospaces.annotations;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.apache.commons.lang3.StringUtils;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiEndpoint {
    // ~ tracing
    String traceHeader() default StringUtils.EMPTY;

    // ~ metrics
    String metricsUri() default StringUtils.EMPTY;
    Tag[] metricTags() default {};

    //
    // ~ obfuscation
    //
    String[] headersToMask() default {};
    String[] queryParamsToMask() default {};
    boolean obfuscateRequestBody() default false;
    boolean doNotPrintResponseBody() default false;

    // ~ IP white listing
    String ipWhitelistKey() default StringUtils.EMPTY;
    String rateBarrierKey() default StringUtils.EMPTY;
    String rateLimiterKey() default StringUtils.EMPTY;
    String siteVerifyEnforceKey() default StringUtils.EMPTY;
}
