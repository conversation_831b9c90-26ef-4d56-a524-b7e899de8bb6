package com.turbospaces.api;

import com.turbospaces.cfg.ApplicationConfig;

public interface ReplyTopic extends ReadOnlyTopic {
    @Override
    default void configure(ApplicationConfig cfg) {
        cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, 4);
        cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, DEFAULT_REPLICATION_FACTOR);
        cfg.setDefaultProperty(name() + "." + Topic.RETENTION_MS, Topic.DEFAULT_RETENTION_MS);
        cfg.setDefaultProperty(name() + "." + Topic.MAX_MESSAGE_BYTES, 16 * Topic.DEFAULT_MAX_MESSAGE_BYTES);
    }
}
