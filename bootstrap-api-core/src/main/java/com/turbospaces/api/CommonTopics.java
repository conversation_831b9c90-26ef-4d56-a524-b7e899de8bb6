package com.turbospaces.api;

import java.util.Collection;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.netty.util.AsciiString;
import lombok.experimental.UtilityClass;

@UtilityClass
public class CommonTopics {
    public static final int EVENT_STREAMING_PARTITIONS = 8;

    public static Collection<Topic> asNotifies(ApplicationProperties props) {
        return props.APP_NOTIFY_TOPIC.get().stream().map(new Function<String, Topic>() {
            @Override
            public Topic apply(String name) {
                return new ReadOnlyTopic() {
                    @Override
                    public AsciiString name() {
                        return AsciiString.cached(name);
                    }
                    @Override
                    public void configure(ApplicationConfig cfg) {
                        cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, 16);
                        cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, DEFAULT_REPLICATION_FACTOR);
                        cfg.setDefaultProperty(name() + "." + "retention.ms", TimeUnit.MINUTES.toMillis(10));
                        cfg.setDefaultProperty(name() + "." + "max.message.bytes", Topic.DEFAULT_MAX_MESSAGE_BYTES);
                    }
                };
            }
        }).toList();
    }
    public static Topic asEvents(ApplicationProperties props) {
        var name = props.APP_EVENTS_TOPIC.get();
        return new Topic() {
            @Override
            public AsciiString name() {
                return AsciiString.cached(name);
            }
            @Override
            public void configure(ApplicationConfig cfg) {
                cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, EVENT_STREAMING_PARTITIONS);
                cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, DEFAULT_REPLICATION_FACTOR + 1);
                cfg.setDefaultProperty(name() + "." + "retention.ms", TimeUnit.DAYS.toMillis(1));
                cfg.setDefaultProperty(name() + "." + "max.message.bytes", Topic.DEFAULT_MAX_MESSAGE_BYTES);
            }
        };
    }
}
