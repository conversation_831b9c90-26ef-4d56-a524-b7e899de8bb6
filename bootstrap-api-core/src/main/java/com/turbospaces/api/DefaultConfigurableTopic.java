package com.turbospaces.api;

import java.util.regex.Pattern;

import com.turbospaces.cfg.ApplicationConfig;

import io.netty.util.AsciiString;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Getter
@Setter
public class DefaultConfigurableTopic implements ConfigurableTopic {
    private final String name;
    private final int partitions;
    private int replicationFactor = DEFAULT_REPLICATION_FACTOR;
    private long retentionsMs = DEFAULT_RETENTION_MS;
    private long maxMessageBytes = DEFAULT_MAX_MESSAGE_BYTES;

    public DefaultConfigurableTopic(String name, int partitions) {
        this.name = name;
        this.partitions = partitions;
    }

    @Override
    public AsciiString name() {
        return AsciiString.cached(name);
    }

    @Override
    public void configure(ApplicationConfig cfg) {
        cfg.setDefaultProperty(name() + "." + PARTITIONS, partitions);
        cfg.setDefaultProperty(name() + "." + REPLICATION_FACTOR, replicationFactor);
        cfg.setDefaultProperty(name() + "." + RETENTION_MS, retentionsMs);
        cfg.setDefaultProperty(name() + "." + MAX_MESSAGE_BYTES, maxMessageBytes);
    }

    @Override
    public Topic scopedTopic(AsciiString subName) {
        if (subName == null || subName.isEmpty()) {
            return this;
        }

        return new DefaultConfigurableTopic(name + "-" + subName, partitions)
                .setReplicationFactor(replicationFactor)
                .setRetentionsMs(retentionsMs)
                .setMaxMessageBytes(maxMessageBytes);
    }

    @Override
    public Pattern pattern() {
        return Pattern.compile(name().toString() + ".*");
    }
}
