package com.turbospaces.api;

import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.NotImplementedException;

import com.turbospaces.cfg.ApplicationConfig;

import io.netty.util.AsciiString;

public interface Topic {
    int DEFAULT_REPLICATION_FACTOR = 2;
    long DEFAULT_RETENTION_MS = TimeUnit.MINUTES.toMillis(5);
    long DEFAULT_MAX_MESSAGE_BYTES = 1 * FileUtils.ONE_MB;

    String PARTITIONS = "partitions";
    String REPLICATION_FACTOR = "replication.factor";
    String RETENTION_MS = "retention.ms";
    String MAX_MESSAGE_BYTES = "max.message.bytes";

    AsciiString name();

    void configure(ApplicationConfig cfg);

    default Topic scopedTopic(AsciiString subName) {
        throw new NotImplementedException();
    }

    default Pattern pattern() {
        throw new NotImplementedException();
    }
}
