package com.turbospaces.api;

import com.turbospaces.cfg.ApplicationConfig;

public interface AdminReplyTopic extends ReplyTopic {
    @Override
    default void configure(ApplicationConfig cfg) {
        cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, 1);
        cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, DEFAULT_REPLICATION_FACTOR);
        cfg.setDefaultProperty(name() + "." + "retention.ms", Topic.DEFAULT_RETENTION_MS);
        cfg.setDefaultProperty(name() + "." + "max.message.bytes", 16 * Topic.DEFAULT_MAX_MESSAGE_BYTES);
    }
}
