package com.turbospaces.api;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Strings;

import com.google.common.collect.ImmutableList;

public class AcceptorsTopicRegistry implements TopicRegistry {
    private final Collection<Topic> union;

    public AcceptorsTopicRegistry(Class<?> clazz) throws Exception {
        Field[] fields = clazz.getDeclaredFields();
        ImmutableList.Builder<Topic> l = ImmutableList.builder();
        for (Field field : fields) {
            int modifiers = field.getModifiers();
            if (Modifier.isStatic(modifiers) && Modifier.isFinal(modifiers)) {
                field.setAccessible(true);
                Object object = field.get(null);
                if (object instanceof Topic topic) {
                    l.add(topic);
                }
            }
        }
        this.union = l.build();
    }
    public AcceptorsTopicRegistry(Collection<Topic> topics) {
        this.union = ImmutableList.copyOf(topics);
    }
    public AcceptorsTopicRegistry(Iterable<Topic> topics) {
        this.union = ImmutableList.copyOf(topics);
    }
    public AcceptorsTopicRegistry(Iterable<Topic> topics1, Iterable<Topic> topics2) {
        this.union = ImmutableList.copyOf(CollectionUtils.union(topics1, topics2));
    }
    @Override
    public Topic forName(String name) {
        for (Topic next : union) {
            String qualifier = next.name().toString();
            if (Strings.CS.equals(qualifier, name)) {
                return next;
            }
        }
        throw new IllegalArgumentException("unknown topic: " + name);
    }
    @Override
    public Iterator<Topic> iterator() {
        return union.iterator();
    }
}
