package org.springframework.cloud;

import java.security.KeyStore;
import java.util.Map;

import org.springframework.cloud.service.ServiceInfo;

import com.turbospaces.api.ContainerNamespaceProvider;
import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.retry.RetryRegistry;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

public interface SmartCloudConnector extends CloudConnector, Disposable, ContainerNamespaceProvider {
    void load(ApplicationProperties props, KeyStore keyStore, RetryRegistry retryRegistry) throws Exception;
    Flux<Map<String, ServiceInfo>> asFlux();
}
