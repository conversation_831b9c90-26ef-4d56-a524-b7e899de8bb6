package org.springframework.cloud;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;

import org.jctools.maps.NonBlockingHashMap;
import org.springframework.cloud.service.ServiceConnectorCreator;
import org.springframework.cloud.service.ServiceInfo;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.net.HostAndPort;
import com.turbospaces.api.ContainerNamespaceProvider;
import com.turbospaces.ups.UPSs;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.core.publisher.Sinks.Many;

public class DynamicCloud extends Cloud implements SmartCloud, ContainerNamespaceProvider, UPSs, Disposable {
    private final Many<Map<String, ServiceInfo>> upss = Sinks.many().replay().latestOrDefault(new NonBlockingHashMap<>());
    private final SmartCloudConnector connector;

    @VisibleForTesting
    public DynamicCloud(SmartCloudConnector connector) {
        this(connector, Collections.emptyList());
    }
    public DynamicCloud(SmartCloudConnector connector, List<ServiceConnectorCreator<?, ? extends ServiceInfo>> serviceConnectorCreators) {
        super(connector, serviceConnectorCreators);
        this.connector = Objects.requireNonNull(connector);
    }
    @Override
    public String namespace() {
        return connector.namespace();
    }
    @Override
    public HostAndPort toFQSN(int port) {
        return connector.toFQSN(port);
    }
    @Override
    public List<ServiceInfo> getServiceInfos() {
        Map<String, ServiceInfo> map = upss.asFlux().blockFirst();
        if (map.isEmpty()) {
            return super.getServiceInfos(); // ~ optimise
        }

        ImmutableList.Builder<ServiceInfo> l = ImmutableList.builder();
        l.addAll(super.getServiceInfos());
        l.addAll(map.values());

        return l.build();
    }
    @Override
    public ServiceInfo getServiceInfo(String serviceId) {
        ServiceInfo si = upss.asFlux().blockFirst().get(serviceId);
        return Objects.nonNull(si) ? si : super.getServiceInfo(serviceId);
    }
    @SuppressWarnings({ "unchecked" })
    @Override
    public <T extends ServiceInfo> List<T> getServiceInfosByType(Class<T> serviceInfoType) {
        ImmutableList.Builder<T> l = ImmutableList.builder();
        l.addAll(super.getServiceInfosByType(serviceInfoType));

        for (ServiceInfo serviceInfo : upss.asFlux().blockFirst().values()) {
            if (serviceInfoType.isAssignableFrom(serviceInfo.getClass())) {
                l.add((T) serviceInfo);
            }
        }

        return l.build();
    }
    @Override
    public void addUps(ServiceInfo info) {
        List<ServiceInfo> l = super.getServiceInfos();
        for (ServiceInfo si : l) {
            if (si.getId().equals(info.getId())) {
                throw new IllegalArgumentException(String.format("ups: %s already exists and defined in cloud connector", info.getId()));
            }
        }

        //
        // ~ register override
        //
        Map<String, ServiceInfo> curr = upss.asFlux().blockFirst();
        curr.put(info.getId(), info);
        EmitResult emitResult = upss.tryEmitNext(curr);
        Preconditions.checkArgument(emitResult.isSuccess());
    }
    @Override
    public boolean removeUps(String id) {
        Map<String, ServiceInfo> curr = upss.asFlux().blockFirst();
        boolean toReturn = Objects.nonNull(curr.remove(id));
        EmitResult emitResult = upss.tryEmitNext(curr);
        Preconditions.checkArgument(emitResult.isSuccess());
        return toReturn;
    }
    @Override
    public <T extends ServiceInfo> Flux<ServiceInfo> serviceInfoByName(String ups) {
        return connector.asFlux().mergeWith(upss.asFlux()).filter(new Predicate<Map<String, ServiceInfo>>() {
            @Override
            public boolean test(Map<String, ServiceInfo> map) {
                return map.containsKey(ups);
            }
        }).map(new Function<Map<String, ServiceInfo>, ServiceInfo>() {
            @Override
            public ServiceInfo apply(Map<String, ServiceInfo> t) {
                return t.get(ups);
            }
        }).distinctUntilChanged(Function.identity(), new BiPredicate<ServiceInfo, ServiceInfo>() {
            @Override
            public boolean test(ServiceInfo t, ServiceInfo u) {
                return UPSs.isEquals(t, u);
            }
        });
    }
    @Override
    public <T extends ServiceInfo> Flux<ServiceInfo> scopedServiceInfoByName(String scope, String name) {
        String scoped = String.format("%s-%s", scope, name);
        return connector.asFlux().mergeWith(upss.asFlux()).filter(new Predicate<Map<String, ServiceInfo>>() {
            @Override
            public boolean test(Map<String, ServiceInfo> map) {
                for (String ups : Arrays.asList(scoped, name)) {
                    if (map.containsKey(ups)) {
                        return true;
                    }
                }
                return false;
            }
        }).map(new Function<Map<String, ServiceInfo>, ServiceInfo>() {
            @Override
            public ServiceInfo apply(Map<String, ServiceInfo> t) {
                ServiceInfo si = t.get(scoped);
                return Objects.nonNull(si) ? si : t.get(name);
            }
        }).distinctUntilChanged(Function.identity(), (t, u) -> {
            return UPSs.isEquals(t, u);
        });
    }
    @Override
    public void dispose() {
        connector.dispose();
    }
}
