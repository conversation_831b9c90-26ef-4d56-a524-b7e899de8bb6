package org.springframework.cloud;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

import org.springframework.cloud.service.ServiceInfo;

import com.google.common.io.ByteSource;
import com.google.common.net.HostAndPort;
import com.turbospaces.ups.H2ServiceInfo;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;

public interface SmartCloud {
    void addUps(ServiceInfo info);
    boolean removeUps(String id);
    default boolean removeUps(ServiceInfo si) {
        return removeUps(si.getId());
    }

    default void withKafka(int port) {
        withKafka(HostAndPort.fromParts("localhost", port));
    }
    default void withKafka(HostAndPort addr) {
        KafkaServiceInfo info = new KafkaServiceInfo(addr);
        addUps(info);
    }
    default void withRawService(String id, URL url) throws IOException {
        try (InputStream io = url.openStream()) {
            RawServiceInfo serviceInfo = new RawServiceInfo(id, new ByteSource() {
                @Override
                public InputStream openStream() throws IOException {
                    return io;
                }
            }.read());
            addUps(serviceInfo);
        }
    }
    default void withH2(boolean durable, String namespace) {
        String url = "mem:" + namespace;
        if (durable) {
            url += ";" + "DB_CLOSE_DELAY=-1";
        }
        addUps(new H2ServiceInfo(UPSs.H2_OWNER, url));
        addUps(new H2ServiceInfo(UPSs.H2_APP, url));
    }
}
