package reactor.blockhound.integration;

import java.util.function.Consumer;

import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.BlockingMethod;
import reactor.blockhound.BlockingOperationError;

@Slf4j
public class SentryBlockingMethodReporter implements Consumer<BlockingMethod> {
    @Override
    public void accept(BlockingMethod t) {
        Error err = new BlockingOperationError(t);
        log.error(err.getMessage(), err);
    }
}
