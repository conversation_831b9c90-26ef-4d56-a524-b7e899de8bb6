package reactor.blockhound.integration;

import java.util.function.Function;
import java.util.function.Predicate;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.turbospaces.executor.PlatformThread;
import com.turbospaces.executor.PlatformTransactionalThread;

import io.netty.util.concurrent.FastThreadLocalThread;
import io.vavr.CheckedFunction0;
import io.vavr.CheckedRunnable;
import io.vavr.Function0;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.BlockHound;

@Slf4j
@SuppressWarnings("deprecation")
public class DefaultBlockHoundIntegration extends LoggingIntegration {
    public static final String PREFIX = "$$BlockHound$$-";
    public static final ThreadLocal<Boolean> FLAG = new ThreadLocal<>() {
        @Override
        public void set(Boolean value) {
            super.set(value);
        }
        @Override
        protected Boolean initialValue() {
            return Boolean.FALSE;
        }
    };

    @Override
    public void applyTo(BlockHound.Builder builder) {
        super.applyTo(builder);

        //
        // ~ white list only for those classes to which we don't have a direct access and there is no way to patch them
        //

        //
        // ~ JDK
        //
        for (var method : new String[] { "getTask", "addWorker" }) {
            builder.allowBlockingCallsInside("java.util.concurrent.ThreadPoolExecutor", method);
        }
        builder.allowBlockingCallsInside("java.lang.VirtualThread", "start");
        builder.allowBlockingCallsInside("java.time.Zone", "of");
        builder.allowBlockingCallsInside("java.time.ZoneId", "of");
        //
        // ~ 3-party
        //
        for (var type : new String[] {
                "org.apache.http.nio.pool.AbstractNIOConnPool",
                "org.apache.http.impl.nio.pool.BasicNIOConnPool",
                "org.apache.http.impl.nio.conn.HttpNIOConnPool",
                "org.apache.http.impl.nio.conn.CPool" }) {
            builder.allowBlockingCallsInside(type, "lease");
        }
        for (var method : new String[] { "getCookies" }) {
            builder.allowBlockingCallsInside("org.apache.http.impl.client.BasicCookieStore", method);
        }
        for (var method : new String[] { "lazyInit" }) {
            builder.allowBlockingCallsInside("org.apache.logging.log4j.util.ProviderUtil", method);
        }
        for (var method : new String[] { "validateReturnValue", "validateAllParameters" }) {
            builder.allowBlockingCallsInside("org.jboss.resteasy.plugins.validation.GeneralValidatorImpl", method);
        }
        for (var method : new String[] { "contextGet", "contextGetWithOption", "contextPut", "contextPutIfAbsent", "contextRef", "contextClear", "contextDeleted" }) {
            builder.allowBlockingCallsInside("io.ebeaninternal.server.deploy.BeanDescriptor", method);
        }
        for (var method : new String[] { "log" }) {
            builder.allowBlockingCallsInside("io.avaje.applog.slf4j.Slf4jLogger", method);
        }
        for (var method : new String[] { "loadBean" }) {
            builder.allowBlockingCallsInside("io.ebean.bean.InterceptReadWrite", method);
        }
        for (var type : new String[] { "io.ebean.common.BeanList", "io.ebean.common.BeanSet", "io.ebean.common.BeanMap" }) {
            builder.allowBlockingCallsInside(type, "init");
        }
        for (var method : new String[] { "readSet", "readData", "read" }) {
            builder.allowBlockingCallsInside("io.ebeaninternal.server.deploy.BeanPropertyJsonMapper", method);
        }

        //
        // ~ determine mode by corresponding naming
        //
        boolean dev = true;
        String env = System.getenv("APP_DEV_MODE");
        if (StringUtils.isNotEmpty(env)) {
            dev = Boolean.parseBoolean(env);
        } else {
            String prop = System.getProperty("app.dev.mode", "true");
            if (StringUtils.isNotEmpty(prop)) {
                dev = Boolean.parseBoolean(prop);
            }
        }

        //
        // ~ we can't break the PROD execution logic - instead we can report to sentry
        //
        if (BooleanUtils.isFalse(dev)) {
            builder.blockingMethodCallback(new SentryBlockingMethodReporter());
        }

        builder.dynamicThreadPredicate(new Function<Predicate<Thread>, Predicate<Thread>>() {
            @Override
            public Predicate<Thread> apply(Predicate<Thread> prediction) {
                //
                // ~ we don't want to use any other integration relaying on OR predicate as per specification
                //
                return new Predicate<>() {
                    @Override
                    public boolean test(Thread t) {
                        if (t instanceof PlatformThread pt) {
                            return pt.blockhoundEnabled();
                        } else if (t.isVirtual()) {
                            return t.getName().startsWith(PREFIX);
                        } else if (t instanceof FastThreadLocalThread) {
                            return BooleanUtils.isFalse(((FastThreadLocalThread) t).permitBlockingCalls());
                        }
                        return false;
                    }
                };
            }
        });
        builder.nonBlockingThreadPredicate(new Function<Predicate<Thread>, Predicate<Thread>>() {
            @Override
            public Predicate<Thread> apply(Predicate<Thread> root) {
                //
                // ~ we don't want to use any other integration relaying on OR predicate as per specification
                //
                return new Predicate<>() {
                    @Override
                    public boolean test(Thread t) {
                        boolean toReturn = false;
                        if (t instanceof PlatformTransactionalThread ptt) {
                            if (ptt.blockhoundEnabled()) {
                                if (ptt.current().isPresent()) {
                                    toReturn = FLAG.get().equals(Boolean.TRUE);
                                }
                            }
                        } else if (t instanceof PlatformThread pt) {
                            if (pt.blockhoundEnabled()) {
                                toReturn = FLAG.get().equals(Boolean.TRUE);
                            }
                        } else if (t.isVirtual()) {
                            if (t.getName().startsWith(PREFIX)) {
                                toReturn = FLAG.get().equals(Boolean.TRUE);
                            }
                        } else if (t instanceof FastThreadLocalThread) {
                            if (BooleanUtils.isFalse(((FastThreadLocalThread) t).permitBlockingCalls())) {
                                toReturn = FLAG.get().equals(Boolean.TRUE);
                            }
                        }
                        return toReturn;
                    }
                };
            }
        });
    }
    public static void allowBlocking(Runnable runnable) {
        boolean toReset = FLAG.get();
        try {
            FLAG.set(false);
            runnable.run();
        } finally {
            FLAG.set(toReset);
        }
    }
    public static void allowBlockingUnchecked(CheckedRunnable runnable) throws Throwable {
        boolean toReset = FLAG.get();
        try {
            FLAG.set(false);
            runnable.run();
        } finally {
            FLAG.set(toReset);
        }
    }
    public static <R> R allowBlocking(Function0<R> runnable) {
        boolean toReset = FLAG.get();
        try {
            FLAG.set(false);
            return runnable.apply();
        } finally {
            FLAG.set(toReset);
        }
    }
    public static <R> R allowBlockingUnchecked(CheckedFunction0<R> runnable) throws Throwable {
        boolean toReset = FLAG.get();
        try {
            FLAG.set(false);
            return runnable.apply();
        } finally {
            FLAG.set(toReset);
        }
    }
}
