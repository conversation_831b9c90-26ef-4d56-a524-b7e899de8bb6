package com.turbospaces.mdc;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Set;

import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Sets;

class MdcTagsTest {
    @Test
    void unique() throws IllegalAccessException {
        Set<String> unique = Sets.newHashSet();
        Field[] fields = FieldUtils.getAllFields(MdcTags.class);
        for (Field f : fields) {
            if (Modifier.isPublic(f.getModifiers()) && Modifier.isStatic(f.getModifiers())) {
                String value = (String) FieldUtils.readStaticField(f);
                Assertions.assertTrue(unique.add(value));
            }
        }
    }
}
