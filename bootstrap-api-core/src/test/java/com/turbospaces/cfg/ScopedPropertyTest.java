package com.turbospaces.cfg;

import com.google.common.reflect.TypeToken;
import nl.altindag.log.LogCaptor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

class ScopedPropertyTest {
    @Test
    void bools() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ScopedProperty<Boolean> scoped = cfg.factory().getScoped("k1", boolean.class, true);
        Assertions.assertTrue(scoped.get());
        Assertions.assertTrue(scoped.get("namespace1"));
        Assertions.assertTrue(scoped.get("namespace2"));

        cfg.setLocalProperty("k1", false);
        Assertions.assertFalse(scoped.get());
        Assertions.assertFalse(scoped.get("namespace1"));
        Assertions.assertFalse(scoped.get("namespace2"));

        cfg.setLocalProperty("namespace1.k1", true);
        cfg.setLocalProperty("namespace2.k1", false);
        Assertions.assertFalse(scoped.get());
        Assertions.assertTrue(scoped.get("namespace1"));
        Assertions.assertFalse(scoped.get("namespace2"));
    }

    @Test
    void ints() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ScopedProperty<Integer> scoped = cfg.factory().getScoped("k2", int.class, 17);
        Assertions.assertEquals(17, scoped.get());
        Assertions.assertEquals(17, scoped.get("namespace1"));
        Assertions.assertEquals(17, scoped.get("namespace2"));

        cfg.setLocalProperty("k2", 12);
        cfg.setLocalProperty("namespace1.k2", 13);
        Assertions.assertEquals(12, scoped.get());
        Assertions.assertEquals(13, scoped.get("namespace1"));
        Assertions.assertEquals(12, scoped.get("namespace2"));

        cfg.setLocalProperty("namespace1.k2", 13);
        cfg.setLocalProperty("namespace2.k2", 15);
        Assertions.assertEquals(12, scoped.get());
        Assertions.assertEquals(13, scoped.get("namespace1"));
        Assertions.assertEquals(15, scoped.get("namespace2"));
    }

    @Test
    void testHappyPathListOfString() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ScopedProperty<List<String>> scoped = cfg.factory().getScoped("scoped", new TypeToken<>() {}, List.of("FOO"));
        Assertions.assertEquals(List.of("FOO"), scoped.get());
        Assertions.assertEquals(List.of("FOO"), scoped.get("namespace1"));
        Assertions.assertEquals(List.of("FOO"), scoped.get("namespace2"));

        cfg.setLocalProperty("scoped", "US");
        Assertions.assertEquals(List.of("US"), scoped.get());
        Assertions.assertEquals(List.of("US"), scoped.get("namespace1"));
        Assertions.assertEquals(List.of("US"), scoped.get("namespace2"));

        cfg.setLocalProperty("namespace1.scoped", "AU");
        Assertions.assertEquals(List.of("US"), scoped.get());
        Assertions.assertEquals(List.of("AU"), scoped.get("namespace1"));
        Assertions.assertEquals(List.of("US"), scoped.get("namespace2"));
    }

    @Test
    void testHappyPathListOfInts() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ScopedProperty<List<Integer>> scoped = cfg.factory().getScoped("scoped", new TypeToken<>() {}, List.of(42));
        Assertions.assertEquals(List.of(42), scoped.get());
        Assertions.assertEquals(List.of(42), scoped.get("namespace1"));
        Assertions.assertEquals(List.of(42), scoped.get("namespace2"));

        cfg.setLocalProperty("scoped", "43");
        Assertions.assertEquals(List.of(43), scoped.get());
        Assertions.assertEquals(List.of(43), scoped.get("namespace1"));
        Assertions.assertEquals(List.of(43), scoped.get("namespace2"));
    }

    @Test
    void testHappyPathScopedWithCustomName() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        String key = String.format("my.%s.property", ScopedProperty.SCOPE_PLACEHOLDER);
        ScopedProperty<Integer> scoped = cfg.factory().getScoped(key, int.class, 17);
        Assertions.assertEquals(17, scoped.get());
        Assertions.assertEquals(17, scoped.get("namespace1"));
        Assertions.assertEquals(17, scoped.get("namespace2"));

        // most likely won't be used that way
        cfg.setLocalProperty(key, 12);
        cfg.setLocalProperty("my.namespace1.property", 13);
        Assertions.assertEquals(12, scoped.get());
        Assertions.assertEquals(13, scoped.get("namespace1"));
        Assertions.assertEquals(12, scoped.get("namespace2"));

        cfg.setLocalProperty("my.namespace1.property", 13);
        cfg.setLocalProperty("my.namespace2.property", 15);
        Assertions.assertEquals(12, scoped.get());
        Assertions.assertEquals(13, scoped.get("namespace1"));
        Assertions.assertEquals(15, scoped.get("namespace2"));
    }

    @Test
    void testDefaultIsNull() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ScopedProperty<String> scoped = cfg.factory().getScoped("k2", String.class, null);
        Assertions.assertNull(scoped.get());
        Assertions.assertNull(scoped.get("namespace1"));
        Assertions.assertNull(scoped.get("namespace2"));
        Assertions.assertEquals("default", scoped.get("namespace1", "default"));
        Assertions.assertEquals("default", scoped.get("namespace2", "default"));
    }

    @Test
    void testMapParseException() {
        ApplicationConfig cfg = ApplicationConfig.mock();

        ScopedProperty<Map<String, String>> map = cfg.factory().getScoped("map", new TypeToken<>() {}, Map.of("key", "val"));
        cfg.setLocalProperty("map", "key=val,key2");
        // parse exception fallback to default
        try (var logCaptor = LogCaptor.forClass(ApplicationConfig.class)) {
            Assertions.assertEquals(Map.of("key", "val"), map.get());
            assertEquals("Error when parsing property: map", logCaptor.getErrorLogs().get(0));
        }
    }
}
