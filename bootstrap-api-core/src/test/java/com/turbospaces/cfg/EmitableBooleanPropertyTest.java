package com.turbospaces.cfg;

import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.netflix.archaius.api.Property;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class EmitableBooleanPropertyTest {
    @Test
    void withDefaultValue() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        Property<Boolean> prop = props.factory().get("key", boolean.class).orElse(true);
        SupersetFlag emitable = new SupersetFlag(prop);
        emitable.tryEmitNext(true);

        Assertions.assertTrue(emitable.get()); // both true
        emitable.tryEmitNext(false);
        Assertions.assertFalse(emitable.get()); // one false
    }
    @Test
    void subscribe() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        Property<Boolean> prop = props.factory().get("key", boolean.class).orElse(false);
        SupersetFlag emitable = new SupersetFlag(prop);
        AtomicReference<Boolean> bool = new AtomicReference<>();
        emitable.subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean t) {
                bool.set(t);
                log.debug("new value: {}", t);
            }
        });

        Assertions.assertFalse(bool.get()); // initial trigger with false
        bool.set(null);

        emitable.tryEmitNext(false);
        Assertions.assertFalse(emitable.get());
        Assertions.assertNull(bool.get()); // not changed

        cfg.setDefaultProperty("key", true); // keep only one false afterwards
        Assertions.assertFalse(emitable.get());
        Assertions.assertNull(bool.get()); // not changed

        cfg.setDefaultProperty("key", true); // both true
        emitable.tryEmitNext(true);
        Assertions.assertTrue(emitable.get());
        Assertions.assertTrue(bool.get());
    }
}
