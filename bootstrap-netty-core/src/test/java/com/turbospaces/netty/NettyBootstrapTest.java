package com.turbospaces.netty;

import java.util.Objects;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.base.Preconditions;
import com.turbospaces.boot.MockConfig.HttpFactory;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.healthchecks.SocketHealthCheck;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.inject.Inject;

public class NettyBootstrapTest {
    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        PlainServiceInfo si = new PlainServiceInfo(PlatformUtil.randomUUID().toString(), "http://localhost:" + port);
        bootstrap.healthCheckRegistry().register("tcp", new SocketHealthCheck(props, si));
        ApplicationContext ctx = bootstrap.run();
        Foo bean = ctx.getBean(Foo.class);

        try {
            Assertions.assertNotNull(bean.version(port));
            Assertions.assertNotNull(bean.threadDump(port));
            Assertions.assertNotNull(bean.ip(port));
            Assertions.assertEquals(HttpResponseStatus.OK.code(), bean.healthCheck(port));
            Assertions.assertEquals(HttpResponseStatus.NOT_FOUND.code(), bean.notFound(port));
        } finally {
            bootstrap.shutdown();
        }
    }

    public static class Foo implements InitializingBean, DisposableBean {
        private final CloseableHttpClient httpClient;

        @Inject
        public Foo(CloseableHttpClient httpClient) {
            this.httpClient = Objects.requireNonNull(httpClient);
        }
        @Override
        public void afterPropertiesSet() throws Exception {
        }
        @Override
        public void destroy() throws Exception {

        }
        public String version(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath("/v1/version").build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                Preconditions.checkArgument(resp.getStatusLine().getStatusCode() == HttpResponseStatus.OK.code());
                return EntityUtils.toString(resp.getEntity());
            }
        }
        public int notFound(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath("/v1/clock").build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                return resp.getStatusLine().getStatusCode();
            }
        }
        public int threadDump(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath("/v1/thread_dump").build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                Preconditions.checkArgument(resp.getStatusLine().getStatusCode() == HttpResponseStatus.OK.code());
                return resp.getStatusLine().getStatusCode();
            }
        }
        public int healthCheck(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath("/v1/health_check").build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                Preconditions.checkArgument(resp.getStatusLine().getStatusCode() == HttpResponseStatus.OK.code());
                return resp.getStatusLine().getStatusCode();
            }
        }
        public int ip(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath("/v1/ip").build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                Preconditions.checkArgument(resp.getStatusLine().getStatusCode() == HttpResponseStatus.OK.code());
                return resp.getStatusLine().getStatusCode();
            }
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public CommonObjectMapper mapper() {
            return new CommonObjectMapper();
        }
        @Bean
        public HttpFactory factory() {
            return new HttpFactory(HttpClients.custom());
        }
        @Bean
        public Foo foo(HttpFactory factory) throws Exception {
            return new Foo(factory.getObject());
        }
        @Bean
        public AdminHttpChannel adminChannel(
                ApplicationProperties props,
                CommonObjectMapper mapper,
                CompositeMeterRegistry meterRegistry,
                HealthCheckRegistry healthCheckRegistry) {
            return new AdminHttpChannel(props, mapper, meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get());
        }
    }
}
