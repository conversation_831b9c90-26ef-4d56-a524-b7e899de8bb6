package com.turbospaces.netty;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;

import io.micrometer.core.instrument.composite.CompositeMeterRegistry;

public class ProxyServerDemo {
    public static void main(String[] args) throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        bootstrap.run(args);
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public ProxyHttpChannel adminChannel(ApplicationProperties props, CompositeMeterRegistry meterRegistry, HealthCheckRegistry healthCheckRegistry) {
            return new ProxyHttpChannel(props, new CommonObjectMapper(), meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get());
        }
    }
}
