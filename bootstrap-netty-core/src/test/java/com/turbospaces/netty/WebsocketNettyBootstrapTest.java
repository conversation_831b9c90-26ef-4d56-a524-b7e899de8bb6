package com.turbospaces.netty;

import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheck;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.boot.MockConfig.HttpFactory;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolConfig;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler.HandshakeComplete;
import io.netty.handler.codec.http.websocketx.extensions.compression.WebSocketServerCompressionHandler;
import io.netty.util.AttributeKey;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

@Slf4j
public class WebsocketNettyBootstrapTest {
    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();
        int iteration = 64;

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        bootstrap.healthCheckRegistry().register("ok", new HealthCheck() {
            @Override
            protected Result check() throws Exception {
                return Result.healthy(Long.toString(System.nanoTime()));
            }
        });
        ApplicationContext ctx = bootstrap.run();
        Foo bean = ctx.getBean(Foo.class);

        try {
            Assertions.assertTrue(bean.postConstructed);

            bean.ws(port);

            for (int i = 0; i < iteration; i++) {
                log.debug(bean.version(port));
                log.debug(bean.healthCheck(port));
                log.debug(bean.ip(port));
            }
        } finally {
            bootstrap.shutdown();
        }

        Assertions.assertTrue(bean.preDestroyed);
    }

    public static class Foo implements InitializingBean, DisposableBean {
        private final CloseableHttpClient httpClient;
        private boolean postConstructed, preDestroyed;

        @Inject
        public Foo(CloseableHttpClient httpClient) {
            this.httpClient = Objects.requireNonNull(httpClient);
        }
        @Override
        public void afterPropertiesSet() throws Exception {
            postConstructed = true;
        }
        @Override
        public void destroy() throws Exception {
            preDestroyed = true;
        }
        public String version(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath(AdminHttpChannel.VERSION_PATH).build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                return EntityUtils.toString(resp.getEntity());
            }
        }
        public String healthCheck(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath(AdminHttpChannel.HEALTH_CHECK_PATH).build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                return EntityUtils.toString(resp.getEntity());
            }
        }
        public String ip(int port) throws Exception {
            HttpGet req = new HttpGet();
            req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath(AdminHttpChannel.IP_PATH).build());
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                return EntityUtils.toString(resp.getEntity());
            }
        }
        public void ws(int port) throws Exception {
            CountDownLatch l = new CountDownLatch(1);
            WebSocketListener wsClient = new WebSocketListener() {
                @Override
                public void onMessage(WebSocket webSocket, ByteString bytes) {
                    l.countDown();
                }
                @Override
                public void onMessage(WebSocket webSocket, String text) {
                    l.countDown();
                }
                @Override
                public void onOpen(WebSocket webSocket, Response response) {
                    webSocket.send("up");
                }
            };

            OkHttpClient client = new OkHttpClient.Builder().build();
            Request request = new Request.Builder().url(String.format("ws://localhost:%d/api?brandName=one&color=white", port)).build();
            client.newWebSocket(request, wsClient);
            Assertions.assertTrue(l.await(30, TimeUnit.SECONDS));

            client.dispatcher().executorService().shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public CommonObjectMapper mapper() {
            return new CommonObjectMapper();
        }
        @Bean
        public HttpFactory factory() {
            return new HttpFactory(HttpClientBuilder.create());
        }
        @Bean
        public Foo foo(HttpFactory factory) throws Exception {
            return new Foo(factory.getObject());
        }
        @Bean
        public AdminHttpChannel adminChannel(
                ApplicationProperties props,
                CommonObjectMapper mapper,
                CompositeMeterRegistry meterRegistry,
                HealthCheckRegistry healthCheckRegistry) {
            return new AdminHttpChannel(props, mapper, meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get()) {
                @Override
                protected AbstractHttpChannelInitializer channelInitializer() {
                    return new AbstractHttpChannelInitializer(props, sslContext) {
                        @Override
                        protected void configurePipeline(ChannelPipeline pipeline) {
                            WebSocketServerProtocolConfig.Builder wspcb = WebSocketServerProtocolConfig.newBuilder();
                            wspcb.checkStartsWith(true).handleCloseFrames(true).dropPongFrames(true).allowExtensions(true).websocketPath("/api");
                            WebSocketServerProtocolConfig wspc = wspcb.build();

                            pipeline.addLast(new WebSocketServerCompressionHandler(0));
                            pipeline.addLast(new WebSocketServerProtocolHandler(wspc));
                            pipeline.addLast(new AdminHttpRequestHandler(props, meterRegistry, healthCheckRegistry, new ObjectMapper(), nettyVersion));
                            pipeline.addLast(new SimpleChannelInboundHandler<WebSocketFrame>() {
                                @Override
                                public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
                                    if (evt instanceof HandshakeComplete handhake) {
                                        ctx.channel().attr(AttributeKey.valueOf("Handshake-Headers")).set(handhake.requestHeaders());
                                        ctx.channel().attr(AttributeKey.valueOf("Handshake-Query-Params")).set(handhake.requestUri());
                                    }
                                }
                                @Override
                                protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) throws Exception {
                                    String request = ((TextWebSocketFrame) frame).text();
                                    ctx.channel().writeAndFlush(new TextWebSocketFrame(request.toUpperCase(Locale.US)));
                                }
                            });
                        }
                    };
                }
            };
        }
    }
}
