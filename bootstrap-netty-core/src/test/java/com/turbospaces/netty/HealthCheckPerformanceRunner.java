package com.turbospaces.netty;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class HealthCheckPerformanceRunner {
    public static final Logger logger = LoggerFactory.getLogger(HealthCheckPerformanceRunner.class);

    public static void main(String[] args) throws InterruptedException {
        CountDownLatch l = new CountDownLatch(1_000_000);
        AtomicBoolean active = new AtomicBoolean(true);
        AtomicLong errors = new AtomicLong();

        OkHttpClient.Builder ok = new OkHttpClient.Builder();
        ok.connectTimeout(5, TimeUnit.SECONDS);
        ok.connectionPool(new ConnectionPool(100, 15, TimeUnit.SECONDS));
        ok.retryOnConnectionFailure(true);

        var httpClient = ok.build();

        for (int i = 0; i < 50; i++) {
            Thread t = new Thread(new Runnable() {
                @Override
                public void run() {
                    while (active.get()) {
                        Request.Builder reqb = new Request.Builder();
                        reqb.url("http://localhost:8044/v1/health_check"); // ~ CHANGE port here
                        try (Response response = httpClient.newCall(reqb.get().build()).execute()) {
                            if (response.isSuccessful()) {

                            } else {
                                errors.incrementAndGet();
                            }
                        } catch (Exception e) {
                            logger.error(e.getMessage(), e);
                        } finally {
                            l.countDown();
                            if (l.getCount() > 0) {
                                System.out.println(l.getCount());
                            }
                        }
                    }
                }
            });
            t.start();
        }

        l.await();
        active.set(false);
        Thread.sleep(1000);

        System.out.println("ERRORS ::: " + errors);

        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
}
