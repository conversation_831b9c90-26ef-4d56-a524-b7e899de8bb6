package com.turbospaces.netty;

import java.util.Objects;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.netty4.NettyEventExecutorMetrics;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.bootstrap.ServerWithMetricsBootstrap;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolConfig;

public abstract class NettyChannel implements InitializingBean, DisposableBean, BeanNameAware {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final int port;
    protected String beanName;

    private ServerBootstrap nettyBootstrap;
    private EventLoopGroup eventLoopGroup;
    private io.netty.channel.Channel channel;

    protected NettyChannel(ApplicationProperties props, MeterRegistry meterRegistry, int port) {
        this.meterRegistry = meterRegistry;
        this.props = Objects.requireNonNull(props);
        this.port = port;
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = name;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        int acceptors = props.NETTY_ACCEPTOR_POOL_SIZE.get();
        String threadNamePrefix = "netty-event-" + beanName;
        eventLoopGroup = NettyEpoll.eventLoopGroup(props, acceptors, threadNamePrefix);

        new NettyEventExecutorMetrics(eventLoopGroup).bindTo(meterRegistry);

        //
        // ~ bind to all network interfaces on 0.0.0.0
        //
        nettyBootstrap = new ServerWithMetricsBootstrap(meterRegistry);
        channel = configure(nettyBootstrap).bind(port).sync().channel();
        log.info("netty server is up and running on={} port", port);
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(eventLoopGroup)) {
            log.debug("shutting event loop group : {}", eventLoopGroup);
            eventLoopGroup.shutdownGracefully();
        }
        if (Objects.nonNull(channel)) {
            try {
                channel.closeFuture().sync();
            } catch (InterruptedException err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
    }
    protected ServerBootstrap configure(ServerBootstrap server) {
        return server
                .channel(NettyEpoll.serverChannel(props))
                .group(eventLoopGroup)
                .option(ChannelOption.SO_BACKLOG, props.TCP_SOCKET_BACKLOG.get())
                .option(ChannelOption.SO_REUSEADDR, props.APP_DEV_MODE.get())
                .childOption(ChannelOption.SO_KEEPALIVE, props.TCP_KEEP_ALIVE.get())
                .childOption(ChannelOption.TCP_NODELAY, props.TCP_NO_DELAY.get());
    }
    protected WebSocketServerProtocolConfig.Builder webSocketConfig() {
        WebSocketServerProtocolConfig.Builder wspcb = WebSocketServerProtocolConfig.newBuilder();

        wspcb.maxFramePayloadLength(props.HTTP_REQUEST_MAX_SIZE.get());
        wspcb.checkStartsWith(true);
        wspcb.allowMaskMismatch(false);
        wspcb.handleCloseFrames(true);
        wspcb.dropPongFrames(true);
        wspcb.allowExtensions(true);

        return wspcb;
    }
}
