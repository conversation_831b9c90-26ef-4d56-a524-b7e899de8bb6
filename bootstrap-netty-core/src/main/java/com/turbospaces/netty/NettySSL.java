package com.turbospaces.netty;

import java.security.KeyStore;

import javax.net.ssl.ManagerFactoryParameters;
import javax.net.ssl.TrustManager;

import com.turbospaces.ssl.SSL;

import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.SimpleTrustManagerFactory;

public class NettySSL extends SSL {
    public SslContext build(SslContextBuilder builder) throws Exception {
        return builder.trustManager(new SimpleTrustManagerFactory() {
            @Override
            protected void engineInit(ManagerFactoryParameters managerFactoryParameters) throws Exception {}
            @Override
            protected void engineInit(KeyStore keyStore) throws Exception {}
            @Override
            protected TrustManager[] engineGetTrustManagers() {
                return trustManagers.toArray(new TrustManager[trustManagers.size()]);
            }
        }).build();
    }
}
