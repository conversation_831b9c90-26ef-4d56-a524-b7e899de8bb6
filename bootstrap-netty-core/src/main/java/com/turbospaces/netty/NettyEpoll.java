package com.turbospaces.netty;

import com.turbospaces.cfg.ApplicationProperties;

import io.netty.channel.EventLoopGroup;
import io.netty.channel.ServerChannel;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class NettyEpoll {
    public static EventLoopGroup eventLoopGroup(ApplicationProperties props, int threads, String threadNamePrefix) {
        var useNativeTransport = props.NETTY_USE_NATIVE_TRANSPORT.get();
        if (useNativeTransport) {
            //
            // ~ use EPOLL if available, otherwise always report to sentry with a reason
            //
            if (Epoll.isAvailable()) {
                return new io.netty.channel.epoll.EpollEventLoopGroup(threads, new DefaultThreadFactory(threadNamePrefix, true));
            }

            if (props.isProdMode()) {
                log.error("epoll is not available, falling back to: " + NioEventLoopGroup.class.getSimpleName(), Epoll.unavailabilityCause());
            }
        }

        return new NioEventLoopGroup(threads, new DefaultThreadFactory(threadNamePrefix, true));
    }
    public static Class<? extends ServerChannel> serverChannel(ApplicationProperties props) {
        var useNativeTransport = props.NETTY_USE_NATIVE_TRANSPORT.get();
        if (useNativeTransport) {
            //
            // ~ use EPOLL if available, otherwise always report to sentry with a reason
            //
            if (Epoll.isAvailable()) {
                return io.netty.channel.epoll.EpollServerSocketChannel.class;
            }

            if (props.isProdMode()) {
                log.error("epoll is not available, falling back to: " + NioServerSocketChannel.class.getSimpleName(), Epoll.unavailabilityCause());
            }
        }

        return NioServerSocketChannel.class;
    }
    public static Class<? extends io.netty.channel.Channel> channel(ApplicationProperties props) {
        var useNativeTransport = props.NETTY_USE_NATIVE_TRANSPORT.get();
        if (useNativeTransport) {
            //
            // ~ use EPOLL if available, otherwise always report to sentry with a reason
            //
            if (Epoll.isAvailable()) {
                return io.netty.channel.epoll.EpollSocketChannel.class;
            }

            if (props.isProdMode()) {
                log.error("epoll is not available, falling back to: " + NioSocketChannel.class.getSimpleName(), Epoll.unavailabilityCause());
            }
        }

        return NioSocketChannel.class;
    }
}
