package com.turbospaces.netty;

import java.util.Map;
import java.util.Objects;

import javax.net.ssl.SSLContext;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.netty4.NettyEventExecutorMetrics;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.EventLoopGroup;
import io.netty.util.Version;
import lombok.Setter;
import lombok.experimental.Accessors;

public class AdminHttpChannel extends NettyChannel {
    public static final String VERSION_PATH = HttpProto.V1 + HttpProto.VERSION_PATH;
    public static final String THREAD_DUMP_PATH = HttpProto.V1 + HttpProto.THREAD_DUMP_PATH;
    public static final String HEALTH_CHECK_PATH = HttpProto.V1 + HttpProto.HEALTHCHECK_PATH;
    public static final String METRICS_PATH = HttpProto.V1 + HttpProto.METRICS_PATH;
    public static final String IP_PATH = HttpProto.V1 + HttpProto.IP_PATH;

    protected final HealthCheckRegistry healthCheckRegistry;
    protected final Map<String, Version> nettyVersion = Version.identify();
    protected final CommonObjectMapper mapper;
    protected EventLoopGroup eventLoopGroup;

    @Setter
    @Accessors(fluent = true)
    protected SSLContext sslContext;

    public AdminHttpChannel(
            ApplicationProperties props,
            CommonObjectMapper mapper,
            MeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            int port) {
        super(props, meterRegistry, port);
        this.mapper = Objects.requireNonNull(mapper);
        this.healthCheckRegistry = Objects.requireNonNull(healthCheckRegistry);
    }
    @Override
    public ServerBootstrap configure(ServerBootstrap server) {
        return super.configure(server).childHandler(channelInitializer());
    }
    protected AbstractHttpChannelInitializer channelInitializer() {
        return new AbstractHttpChannelInitializer(props, sslContext) {
            @Override
            protected void configurePipeline(ChannelPipeline pipeline) {
                pipeline.addLast(eventLoopGroup, new AdminHttpRequestHandler(props, meterRegistry, healthCheckRegistry, mapper, nettyVersion));
            }
        };
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        //
        // ~ ideally we should keep acceptor pool as thin as possible
        //
        int acceptors = Math.min(Runtime.getRuntime().availableProcessors(), props.NETTY_ACCEPTOR_POOL_SIZE.get());
        String threadNamePrefix = "netty-executor-" + beanName;
        eventLoopGroup = NettyEpoll.eventLoopGroup(props, acceptors, threadNamePrefix);

        super.afterPropertiesSet();
        new NettyEventExecutorMetrics(eventLoopGroup).bindTo(meterRegistry);
    }
    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
        } finally {
            if (Objects.nonNull(eventLoopGroup)) {
                log.debug("shutting executor loop : {}", eventLoopGroup);
                eventLoopGroup.shutdownGracefully();
            }
        }
    }
}
