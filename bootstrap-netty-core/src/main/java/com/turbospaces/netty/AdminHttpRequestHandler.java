package com.turbospaces.netty;

import java.io.ByteArrayOutputStream;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.SortedMap;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.Strings;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.codahale.metrics.health.HealthCheck;
import com.codahale.metrics.health.HealthCheck.Result;
import com.codahale.metrics.health.HealthCheckFilter;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.codahale.metrics.jvm.ThreadDump;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.boot.AbstractHealtchCheck;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.mdc.MdcTags;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.DefaultFullHttpResponse;
import io.netty.handler.codec.http.FullHttpMessage;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpHeaderValues;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.handler.codec.http.HttpVersion;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.util.Version;

@Sharable
public class AdminHttpRequestHandler extends SimpleChannelInboundHandler<FullHttpRequest> {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final HealthCheckRegistry healthCheckRegistry;
    protected final ObjectMapper mapper;
    protected final Map<String, Version> nettyVersion;

    protected AdminHttpRequestHandler(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            ObjectMapper mapper,
            Map<String, Version> nettyVersion) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.healthCheckRegistry = Objects.requireNonNull(healthCheckRegistry);
        this.mapper = Objects.requireNonNull(mapper);
        this.nettyVersion = Objects.requireNonNull(nettyVersion);
    }
    @Override
    public void channelRead0(ChannelHandlerContext ctx, FullHttpRequest req) throws Exception {
        HttpMethod method = req.method();
        String uri = req.uri();
        boolean forwarded = req.headers().contains(com.google.common.net.HttpHeaders.X_FORWARDED_FOR);
        QueryStringDecoder decoder = new QueryStringDecoder(uri);
        boolean keepAlive = io.netty.handler.codec.http.HttpUtil.isKeepAlive(req);

        if (log.isTraceEnabled()) {
            log.trace(req.toString());
        }

        //
        // ~ set some MDC at least to be able to react in case of sentry error
        //
        MDC.put(MdcTags.MDC_PATH, decoder.path());
        MDC.put(MdcTags.MDC_METHOD, req.method().name());

        try {
            if (HttpMethod.GET == method && Strings.CS.equals(AdminHttpChannel.VERSION_PATH, decoder.path())) {
                String version = PlatformUtil.version(props.CLOUD_APP_NAME);
                ByteBuf content = Unpooled.copiedBuffer(version, StandardCharsets.UTF_8);
                DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK, content);

                HttpHeaders headers = noCache(resp.headers());
                headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                headers.set(HttpHeaderNames.CONTENT_LENGTH, content.readableBytes());

                writeAndFlush(ctx, resp, keepAlive);
            } else if (HttpMethod.GET == method && Strings.CS.equals(AdminHttpChannel.THREAD_DUMP_PATH, decoder.path())) {
                if (forwarded) {
                    forbidden(ctx, req, keepAlive);
                } else {
                    ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
                    ThreadDump dump = new ThreadDump(threadMXBean);
                    try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                        dump.dump(out);

                        var content = Unpooled.copiedBuffer(out.toByteArray());
                        var resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK, content);
                        var headers = noCache(resp.headers());

                        //
                        // ~ well we have to set something to keep-alive to be working properly as per documentation
                        //
                        headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                        headers.set(HttpHeaderNames.CONTENT_LENGTH, content.readableBytes());

                        writeAndFlush(ctx, resp, keepAlive);
                    }
                }
            } else if (HttpMethod.GET == method && Strings.CS.equals(AdminHttpChannel.HEALTH_CHECK_PATH, decoder.path())) {
                if (forwarded) {
                    forbidden(ctx, req, keepAlive);
                } else {
                    SortedMap<String, Result> checks = healthCheckRegistry.runHealthChecks(new HealthCheckFilter() {
                        @Override
                        public boolean matches(String name, HealthCheck healthCheck) {
                            if (healthCheck instanceof AbstractHealtchCheck check) {
                                return check.isPermanent();
                            }
                            return true;
                        }
                    });


                    if (MapUtils.isEmpty(checks)) {
                        var resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.NO_CONTENT);
                        var headers = noCache(resp.headers());

                        headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                        headers.set(HttpHeaderNames.CONTENT_LENGTH, 0);

                        writeAndFlush(ctx, resp, keepAlive);
                    } else {
                        StopWatch stopWatch = StopWatch.createStarted();
                        boolean ok = true;
                        for (HealthCheck.Result result : checks.values()) {
                            if (result.isHealthy()) {

                            } else {
                                log.error(result.getMessage());
                                ok = false;
                            }
                        }

                        var content = Unpooled.copiedBuffer(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(checks), StandardCharsets.UTF_8);
                        var resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, ok ? HttpResponseStatus.OK : HttpResponseStatus.SERVICE_UNAVAILABLE, content);
                        var headers = noCache(resp.headers());

                        headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.APPLICATION_JSON);
                        headers.set(HttpHeaderNames.CONTENT_LENGTH, content.readableBytes());

                        stopWatch.stop();
                        log.info("health check result = {} and completed in: {}", resp.status().code(), stopWatch);

                        writeAndFlush(ctx, resp, keepAlive);
                    }
                }
            } else if (HttpMethod.GET == method && Strings.CS.equals(AdminHttpChannel.METRICS_PATH, decoder.path())) {
                if (forwarded) {
                    forbidden(ctx, req, keepAlive);
                } else {
                    DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.NOT_IMPLEMENTED);
                    if (meterRegistry instanceof CompositeMeterRegistry) {

                    }

                    HttpHeaders headers = noCache(resp.headers());
                    headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                    headers.set(HttpHeaderNames.CONTENT_LENGTH, 0);

                    writeAndFlush(ctx, resp, keepAlive);
                }
            } else if (HttpMethod.GET == method && Strings.CS.equals(AdminHttpChannel.IP_PATH, decoder.path())) {
                if (forwarded) {
                    forbidden(ctx, req, keepAlive);
                } else {
                    String ip = props.externalIp();
                    ByteBuf content = Unpooled.copiedBuffer(ip, StandardCharsets.UTF_8);
                    DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK, content);

                    HttpHeaders headers = noCache(resp.headers());
                    headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                    headers.set(HttpHeaderNames.CONTENT_LENGTH, content.readableBytes());

                    writeAndFlush(ctx, resp, keepAlive);
                }
            } else if (handleOther(ctx, req, decoder, keepAlive)) {

            }
        } finally {
            MDC.remove(MdcTags.MDC_PATH);
            MDC.remove(MdcTags.MDC_METHOD);
            MDC.remove(MdcTags.MDC_STATUS_CODE);
        }
    }
    protected void forbidden(ChannelHandlerContext ctx, FullHttpRequest req, boolean keepAlive) {
        DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.FORBIDDEN);

        HttpHeaders headers = noCache(resp.headers());
        headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
        headers.set(HttpHeaderNames.CONTENT_LENGTH, 0);

        writeAndFlush(ctx, resp, keepAlive);
    }
    protected boolean handleOther(ChannelHandlerContext ctx, FullHttpRequest req, QueryStringDecoder decoder, boolean keepAlive) throws Exception {
        DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.NOT_FOUND);

        HttpHeaders headers = noCache(resp.headers());
        headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
        headers.set(HttpHeaderNames.CONTENT_LENGTH, 0);

        writeAndFlush(ctx, resp, keepAlive);
        return true;
    }
    protected void writeAndFlush(ChannelHandlerContext ctx, DefaultFullHttpResponse resp, boolean keepAlive) {
        MDC.put(MdcTags.MDC_STATUS_CODE, Integer.toString(resp.status().code()));

        addVersionHeader(resp);
        io.netty.handler.codec.http.HttpUtil.setKeepAlive(resp, keepAlive);
        ctx.writeAndFlush(resp, ctx.voidPromise());
    }
    protected void addVersionHeader(FullHttpMessage resp) {
        if (props.isDevMode()) {
            resp.headers().add(props.NETTY_VERSION_HTTP_HEADER_NAME.get(), nettyVersion);
        }
    }
    public static HttpHeaders noCache(HttpHeaders headers) {
        headers.set(HttpHeaderNames.CACHE_CONTROL, "must-revalidate,no-cache,no-store");
        headers.set(HttpHeaderNames.PRAGMA, "no-cache");
        headers.set(HttpHeaderNames.EXPIRES, 0);
        return headers;
    }
}
