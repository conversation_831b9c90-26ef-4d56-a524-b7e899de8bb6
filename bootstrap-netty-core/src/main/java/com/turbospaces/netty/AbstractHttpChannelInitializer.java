package com.turbospaces.netty;

import java.util.Objects;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;

import com.turbospaces.cfg.ApplicationProperties;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.handler.codec.http.HttpContentCompressor;
import io.netty.handler.codec.http.HttpContentDecompressor;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpRequestDecoder;
import io.netty.handler.codec.http.HttpResponseEncoder;
import io.netty.handler.codec.http.HttpServerKeepAliveHandler;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.ssl.SslHandler;

public abstract class AbstractHttpChannelInitializer extends ChannelInitializer<io.netty.channel.Channel> {
    private final ApplicationProperties props;
    private final SSLContext sslContext;
    private final boolean compression;

    protected AbstractHttpChannelInitializer(ApplicationProperties props, SSLContext sslContext) {
        this(props, sslContext, true);
    }
    protected AbstractHttpChannelInitializer(ApplicationProperties props, SSLContext sslContext, boolean compression) {
        this.props = Objects.requireNonNull(props);
        this.sslContext = sslContext;
        this.compression = compression;
    }
    @Override
    protected void initChannel(io.netty.channel.Channel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

        if (props.isDevMode()) {
            pipeline.addFirst("logging", new LoggingHandler());
        }
        if (Objects.nonNull(sslContext)) {
            SSLEngine sslEngine = sslContext.createSSLEngine();
            sslEngine.setUseClientMode(false);
            pipeline.addLast(new SslHandler(sslEngine));
        }

        int initialLineMaxSize = props.HTTP_INITIAL_LINE_MAX_SIZE.get();
        int headerMaxSize = props.HTTP_HEADER_MAX_SIZE.get();
        int chunkMaxSize = props.HTTP_CHUNK_MAX_SIZE.get();

        pipeline.addLast(new HttpRequestDecoder(initialLineMaxSize, headerMaxSize, chunkMaxSize));
        if (compression) {
            pipeline.addLast(new HttpContentDecompressor(0));
        }
        pipeline.addLast(new HttpServerKeepAliveHandler());
        pipeline.addLast(new HttpObjectAggregator(props.HTTP_REQUEST_MAX_SIZE.get()));
        pipeline.addLast(new HttpResponseEncoder());
        pipeline.addLast(new HttpContentCompressor());

        configurePipeline(pipeline);
    }
    protected abstract void configurePipeline(ChannelPipeline pipeline);
}
