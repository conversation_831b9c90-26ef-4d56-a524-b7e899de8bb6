package com.turbospaces.netty;

import java.io.ObjectInputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;

import org.apache.commons.lang3.Strings;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.collect.Iterables;
import com.turbospaces.api.MutableReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.CloudEventWithRoutingKeyWorkUnit;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.mdc.MdcUtil;

import api.v1.ApiFactory;
import api.v1.CloudEventWithRoutingKey;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.buffer.ByteBufInputStream;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.DefaultFullHttpResponse;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpHeaderValues;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.handler.codec.http.HttpVersion;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.util.Version;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class RequestReplyHttpRequestHandler extends AdminHttpRequestHandler {
    private final Queue<CloudEventWithRoutingKeyWorkUnit> queue;

    public RequestReplyHttpRequestHandler(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            CommonObjectMapper mapper,
            Map<String, Version> nettyVersion,
            Queue<CloudEventWithRoutingKeyWorkUnit> queue) {
        super(props, meterRegistry, healthCheckRegistry, mapper, nettyVersion);
        this.queue = Objects.requireNonNull(queue);
    }
    @Override
    protected boolean handleOther(ChannelHandlerContext ctx, FullHttpRequest req, QueryStringDecoder decoder, boolean keepAlive) throws Exception {
        HttpMethod method = req.method();
        boolean forwarded = req.headers().contains(com.google.common.net.HttpHeaders.X_FORWARDED_FOR);

        if (HttpMethod.POST == method && Strings.CS.equals(HttpProto.V1 + HttpProto.REQUEST_REPLY_PATH, decoder.path())) {
            if (forwarded) {
                forbidden(ctx, req, forwarded);
            } else {
                String topic = props.CLOUD_APP_ID.get();
                Map<String, List<String>> parameters = decoder.parameters();
                if (parameters.containsKey(MutableReplyTopic.QUERY_PARAM_TOPIC)) {
                    topic = Iterables.getOnlyElement(parameters.get(MutableReplyTopic.QUERY_PARAM_TOPIC));
                }

                try (ObjectInputStream inputStream = new ObjectInputStream(new ByteBufInputStream(req.content()))) {
                    CloudEventWithRoutingKey event = new CloudEventWithRoutingKey();
                    event.readExternal(inputStream);

                    var workUnit = new CloudEventWithRoutingKeyWorkUnit(topic, event);
                    var operation = PlatformUtil.toLowerUnderscore(workUnit.getType());
                    var headers = api.v1.Headers.newBuilder().setMessageId(workUnit.getId()).build();

                    DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
                        @Override
                        public void run() {
                            ApiFactory.setMdc(workUnit, operation, headers);

                            //
                            // ~ we have to reply with NO_CONTENT or set content length otherwise keep-alive connection will not work
                            //
                            try {
                                if (queue.offer(workUnit)) {
                                    log.debug("added reply into queue (messageId: {}, type: {})", workUnit.getId(), workUnit.getType());

                                    DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.NO_CONTENT);
                                    noCache(resp.headers());
                                    writeAndFlush(ctx, resp, keepAlive);
                                } else {
                                    log.error("queue full, unable to add record: {}", workUnit);

                                    DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.TOO_MANY_REQUESTS);

                                    HttpHeaders httpHeaders = noCache(resp.headers());
                                    httpHeaders.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                                    httpHeaders.set(HttpHeaderNames.CONTENT_LENGTH, 0);

                                    writeAndFlush(ctx, resp, keepAlive);
                                }
                            } finally {
                                MdcUtil.clearMdc(workUnit);
                            }
                        }
                    });
                } catch (Throwable err) {
                    log.error(err.getMessage(), err);

                    DefaultFullHttpResponse resp = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.INTERNAL_SERVER_ERROR);

                    HttpHeaders headers = noCache(resp.headers());
                    headers.set(HttpHeaderNames.CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
                    headers.set(HttpHeaderNames.CONTENT_LENGTH, 0);

                    writeAndFlush(ctx, resp, keepAlive);
                }
            }
            return true;
        }

        //
        // ~ 404
        //
        return super.handleOther(ctx, req, decoder, keepAlive);
    }
}
