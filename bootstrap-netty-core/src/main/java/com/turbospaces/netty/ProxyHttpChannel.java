package com.turbospaces.netty;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.BooleanUtils;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.collect.Iterables;
import com.google.common.net.HostAndPort;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;

import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.FullHttpMessage;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpClientCodec;
import io.netty.handler.codec.http.HttpContent;
import io.netty.handler.codec.http.HttpContentDecompressor;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpObject;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpScheme;
import io.netty.handler.codec.http.LastHttpContent;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.handler.codec.http.QueryStringEncoder;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;

public class ProxyHttpChannel extends AdminHttpChannel {
    public ProxyHttpChannel(
            ApplicationProperties props,
            CommonObjectMapper mapper,
            CompositeMeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            int port) {
        super(props, mapper, meterRegistry, healthCheckRegistry, port);
    }
    @Override
    protected AbstractHttpChannelInitializer channelInitializer() {
        return new AbstractHttpChannelInitializer(props, sslContext) {
            @Override
            protected void configurePipeline(ChannelPipeline pipeline) {
                pipeline.addLast(new AdminHttpRequestHandler(props, meterRegistry, healthCheckRegistry, mapper, nettyVersion) {
                    @Override
                    protected boolean handleOther(ChannelHandlerContext ctx, FullHttpRequest req, QueryStringDecoder decoder, boolean keepAlive) throws Exception {
                        Map<String, List<String>> parameters = decoder.parameters();

                        if (decoder.parameters().containsKey(HttpProto.PARAM_PROXY_REF)) {
                            // @formatter:off
                                String newTarget = Iterables.getOnlyElement(parameters.get(HttpProto.PARAM_PROXY_REF));
                                URI newUri = new URI(newTarget);
                                int newDefaultPort = newUri.toURL().getDefaultPort();
                                HostAndPort newHost = newUri.getPort() > 0 ? HostAndPort.fromParts(newUri.getHost(), newUri.getPort()) : HostAndPort.fromHost(newUri.getHost());
                                // @formatter:on

                            //
                            // ~ add parameters back
                            //
                            QueryStringEncoder encoder = new QueryStringEncoder(newUri.getScheme() + "://" + newHost + decoder.path());
                            for (Entry<String, List<String>> pit : parameters.entrySet()) {
                                String key = pit.getKey();
                                if (BooleanUtils.isFalse(HttpProto.PARAM_PROXY_REF.equals(key))) {
                                    for (String value : pit.getValue()) {
                                        encoder.addParam(key, value);
                                    }
                                }
                            }

                            //
                            // ~ make copy and replace host header
                            //
                            FullHttpRequest newRequest = req.copy();
                            newRequest.setUri(encoder.toString());
                            newRequest.headers().set(req.headers());
                            newRequest.headers().set(HttpHeaderNames.HOST, newHost);

                            Bootstrap b = new Bootstrap();
                            b.group(ctx.channel().eventLoop());
                            b.channel(NettyEpoll.channel(props));
                            b.handler(new ChannelInitializer<SocketChannel>() {
                                @Override
                                public void channelInactive(ChannelHandlerContext proxy) throws Exception {
                                    if (ctx.channel().isActive()) {
                                        ctx.channel().close();
                                    }
                                }
                                @Override
                                public void exceptionCaught(ChannelHandlerContext proxy, Throwable cause) throws Exception {
                                    log.error(cause.getMessage(), cause);
                                }
                                @Override
                                protected void initChannel(SocketChannel ch) throws Exception {
                                    ChannelPipeline p = ch.pipeline();

                                    //
                                    // ~ maybe add SSL handler
                                    //
                                    if (HttpScheme.HTTPS.name().toString().equalsIgnoreCase(newUri.getScheme())) {
                                        SslContextBuilder forClient = SslContextBuilder.forClient();
                                        if (props.isDevMode()) {
                                            forClient.trustManager(InsecureTrustManagerFactory.INSTANCE);
                                        }
                                        SslContext sslCtx = forClient.build();

                                        p.addLast(sslCtx.newHandler(ch.alloc(), newUri.getHost(), newHost.getPortOrDefault(newDefaultPort)));
                                    }

                                    p.addLast(new HttpClientCodec());
                                    p.addLast(new HttpContentDecompressor(0));
                                    p.addLast(new HttpObjectAggregator(props.HTTP_REQUEST_MAX_SIZE.get()));
                                    p.addLast(new SimpleChannelInboundHandler<HttpObject>(false) {
                                        @Override
                                        public void channelInactive(ChannelHandlerContext proxy) throws Exception {
                                            super.channelInactive(proxy);
                                        }
                                        @Override
                                        protected void channelRead0(ChannelHandlerContext proxy, HttpObject reply) throws Exception {
                                            if (reply instanceof HttpContent) {
                                                if (reply instanceof LastHttpContent last) {
                                                    //
                                                    // ~ add headers if we like
                                                    //
                                                    if (last instanceof FullHttpMessage full) {
                                                        addVersionHeader(full);
                                                    }

                                                    ctx.writeAndFlush(last);
                                                    proxy.close();
                                                } else {
                                                    ctx.writeAndFlush(reply);
                                                }
                                            } else {
                                                super.channelRead(ctx, reply);
                                            }
                                        }
                                    });
                                }
                            });

                            //
                            // ~ connect
                            //
                            log.debug("about to perform {} connection to {} ...", newUri.getScheme(), newHost);
                            ChannelFuture f = b.connect(newUri.getHost(), newHost.getPortOrDefault(newDefaultPort));
                            f.addListener(new ChannelFutureListener() {
                                @Override
                                public void operationComplete(ChannelFuture future) {
                                    // ~ send out original request
                                    if (future.isSuccess()) {
                                        future.channel().writeAndFlush(newRequest);
                                    } else {
                                        ctx.channel().close();
                                    }
                                }
                            });
                            return true;
                        }

                        //
                        // ~ 404
                        //
                        return super.handleOther(ctx, req, decoder, keepAlive);
                    }
                });
            }
        };
    }
}
