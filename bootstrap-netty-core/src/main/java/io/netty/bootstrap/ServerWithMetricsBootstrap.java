package io.netty.bootstrap;

import java.util.concurrent.atomic.AtomicBoolean;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.netty4.NettyAllocatorMetrics;
import io.netty.buffer.ByteBufAllocatorMetricProvider;
import io.netty.channel.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class ServerWithMetricsBootstrap extends ServerBootstrap {
    private final AtomicBoolean metrics = new AtomicBoolean();
    private final MeterRegistry meterRegistry;

    @Override
    void init(Channel c) {
        super.init(c);
        //
        // ~ lazy installation of metrics adapter
        //
        if (metrics.compareAndSet(false, true)) {
            if (c.alloc() instanceof ByteBufAllocatorMetricProvider alloc) {
                new NettyAllocatorMetrics(alloc).bindTo(meterRegistry);
            }
        }
    }
}
