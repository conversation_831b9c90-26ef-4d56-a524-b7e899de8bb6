package com.turbospaces.nats;

import java.util.Objects;

import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.ups.NatsServiceInfo;

import io.micrometer.core.instrument.MeterRegistry;
import io.nats.client.Connection;
import io.nats.client.Nats;
import io.nats.client.Options;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NatsClientFactoryBean extends AbstractFactoryBean<Connection> implements BeanNameAware {
    private final ApplicationProperties props;
    private final NatsServiceInfo serviceInfo;
    private final DefaultPlatformExecutorService executor;

    public NatsClientFactoryBean(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            NatsServiceInfo si) {
        this.props = Objects.requireNonNull(props);
        this.serviceInfo = Objects.requireNonNull(si);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }
    @Override
    public Class<?> getObjectType() {
        return Connection.class;
    }
    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
        super.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
        } finally {
            if (Objects.nonNull(executor)) {
                executor.destroy();
            }
        }
    }
    @Override
    protected Connection createInstance() throws Exception {
        Options.Builder options = new Options.Builder();
        options.executor(executor);
        options.connectionTimeout(props.TCP_CONNECTION_TIMEOUT.get());
        options.server(serviceInfo.getUri());
        return Nats.connect(options.build());
    }
    @Override
    protected void destroyInstance(Connection instance) throws Exception {
        if (Objects.nonNull(instance)) {
            instance.close();
        }
    }
}
