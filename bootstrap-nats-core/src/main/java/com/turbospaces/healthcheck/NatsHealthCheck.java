package com.turbospaces.healthcheck;

import java.util.Objects;

import com.codahale.metrics.health.HealthCheck;
import com.turbospaces.boot.AbstractHealtchCheck;
import com.turbospaces.ups.NatsServiceInfo;

import io.nats.client.Connection;
import io.nats.client.Nats;

public class Nats<PERSON>ealthCheck extends AbstractHealtchCheck {
    private final Connection connection;

    public NatsHealthCheck(NatsServiceInfo nsi) throws Exception {
        connection = Nats.connect(nsi.getUri());
    }
    @Override
    protected Result check() throws Exception {
        try {
            return Result.healthy(connection.getStatistics().toString());
        } catch (Exception err) {
            logger.warn(err.getMessage(), err);
            return HealthCheck.Result.unhealthy(err);
        }
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(connection)) {
            connection.close();
        }
    }
    @Override
    public boolean isBootstrapOnly() {
        return false;
    }
}
