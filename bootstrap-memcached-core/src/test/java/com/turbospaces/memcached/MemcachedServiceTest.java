package com.turbospaces.memcached;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.concurrent.TimeoutException;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.service.MemcachedService;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.UPSs;

import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.exception.MemcachedException;

@Slf4j
@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MemcachedServiceTest {
    public static final String KEY = "key";
    public static final String VALUE = "value";
    private static final int MEMCACHED_PORT = 11211;
    private static final Duration TTL = Duration.ofSeconds(1);
    private static final int MEMCACHED_CONTAINER_EXPOSED_PORT = 11211;

    private SimpleBootstrap bootstrap;
    private MemcachedService memcachedService;
    private MemcachedClient memcachedClient;

    @Container
    private static GenericContainer<?> MEMCACHED = new GenericContainer<>(DockerImageName.parse("memcached")).withExposedPorts(MEMCACHED_CONTAINER_EXPOSED_PORT);

    @BeforeAll
    void setUp() throws Throwable {
        MemcachedServiceInfo serviceInfo = new MemcachedServiceInfo(UPSs.MEMCACHED, MEMCACHED.getHost(), MEMCACHED.getMappedPort(MEMCACHED_PORT));

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        bootstrap = new SimpleBootstrap(props);
        bootstrap.addUps(serviceInfo);
        bootstrap.run();

        MemcachedClientFactoryBean factoryBean = new MemcachedClientFactoryBean(props, bootstrap.cloud(), bootstrap.meterRegistry());
        factoryBean.afterPropertiesSet();
        memcachedClient = factoryBean.getObject();
        memcachedService = new RubyeyeMemcachedService(props, memcachedClient);
    }

    @AfterAll
    void tearDown() throws Exception {
        if (bootstrap != null) {
            bootstrap.shutdown();
        }
    }

    @BeforeAll
    void cleanUpBeforeAll() throws Exception {
        cleanUpAllTestKeys();
    }

    @AfterEach
    void cleanUpAfter() throws Exception {
        cleanUpAllTestKeys();
    }

    private void cleanUpAllTestKeys() throws Exception {
        String[] testKeys = {
            KEY,
        };

        for (String key : testKeys) {
            memcachedClient.delete(key);
        }
    }

    @Test
    void delete_shouldReturnTrueForExistingKey() throws TimeoutException, InterruptedException, MemcachedException {
        memcachedService.set(KEY, (int) TTL.toSeconds(), VALUE);

        boolean deleted = memcachedService.delete(KEY);
        assertTrue(deleted);

        // Verify key no longer exists
        assertFalse(memcachedService.exists(KEY));
    }

    @Test
    void delete_shouldReturnFalseForNonExistentKey() throws TimeoutException, InterruptedException, MemcachedException {
        boolean deleted = memcachedService.delete(KEY);
        assertFalse(deleted);
    }

    @Test
    void exists_shouldReturnTrueForExistingKey() throws TimeoutException, InterruptedException, MemcachedException {
        memcachedService.set(KEY, (int) TTL.toSeconds(), VALUE);

        assertTrue(memcachedService.exists(KEY));
    }

    @Test
    void exists_shouldReturnFalseForNonExistentKey() throws TimeoutException, InterruptedException, MemcachedException {
        assertFalse(memcachedService.exists(KEY));
    }

    @Test
    void get_shouldReturnNullForNonExistentKey() throws TimeoutException, InterruptedException, MemcachedException {
        String result = memcachedService.get("key");
        assertNull(result);
    }

    @Test
    void get_shouldReturnValueForExistingKey() throws TimeoutException, InterruptedException, MemcachedException {
        memcachedService.set(KEY, (int) Duration.ofHours(1).toSeconds(), VALUE);

        String result = memcachedService.get("key");
        assertEquals(VALUE, result);
    }

    @Test
    void set_shouldStoreAndRetrieveValue() throws TimeoutException, InterruptedException, MemcachedException {
        memcachedService.set(KEY, (int) Duration.ofHours(1).toSeconds(), VALUE);
        String retrieved = memcachedService.get(KEY);

        assertEquals(VALUE, retrieved);
    }

    @Test
    void setIfNotExists_shouldReturnNullWhenKeyDoesNotExist() throws TimeoutException, InterruptedException, MemcachedException {
        boolean result = memcachedService.add(KEY, (int) TTL.toSeconds(), VALUE);

        assertTrue(result);
        assertEquals(VALUE, memcachedService.get(KEY));
    }

    @Test
    void setIfNotExists_shouldReturnExistingValueWhenKeyExists() throws TimeoutException, InterruptedException, MemcachedException {
        String originalValue = "original-value";
        String newValue = "new-value";

        // Set initial value
        memcachedService.add(KEY, (int) TTL.toSeconds(), originalValue);

        // Try to set if not exists
        assertFalse(memcachedService.add(KEY, (int) TTL.toSeconds(), newValue));

        assertEquals(originalValue, memcachedService.get(KEY)); // Should return existing value
        assertEquals(originalValue, memcachedService.get(KEY)); // Original value should remain
    }

    @Test
    void increment_withExpireIfNotSet_shouldNotResetTTL() throws TimeoutException, InterruptedException, MemcachedException {
        Duration shortTTL = Duration.ofSeconds(1);
        Duration longTTL = Duration.ofSeconds(5);

        long result1 = memcachedService.incrementAndTouch(KEY, (int) shortTTL.toSeconds());
        assertEquals(1L, result1);

        long result2 = memcachedService.increment(KEY, (int) longTTL.toSeconds());
        assertEquals(2L, result2);

        Awaitility.await().atMost(Duration.ofSeconds(shortTTL.getSeconds() + 1))
                .until(() -> !memcachedService.exists(KEY));
    }

    @Test
    void increment_withExpireIfNotSet_shouldResetTTL() throws TimeoutException, InterruptedException, MemcachedException {
        Duration shortTTL = Duration.ofSeconds(2);
        Duration longTTL = Duration.ofSeconds(10);

        // First increment with long TTL and expireIfNotSet=false (should always set TTL)
        long result1 = memcachedService.increment(KEY, (int) longTTL.toSeconds());
        assertEquals(1L, result1);

        // Second increment with short TTL and expireIfNotSet=false (should update TTL)
        long result2 = memcachedService.incrementAndTouch(KEY, (int) shortTTL.toSeconds());
        assertEquals(2L, result2);

        // Key should expire after shortTTL since the second call updated the TTL
        Awaitility.await().atMost(Duration.ofSeconds(shortTTL.toSeconds() + 2))
                .until(() -> !memcachedService.exists(KEY));
    }
}
