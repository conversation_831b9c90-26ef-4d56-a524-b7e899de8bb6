package com.turbospaces.memcached;

import java.time.Duration;

import org.eclipse.collections.api.map.primitive.ImmutableIntFloatMap;
import org.eclipse.collections.api.map.primitive.IntFloatMap;
import org.eclipse.collections.impl.map.mutable.primitive.IntFloatHashMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.testcontainers.containers.GenericContainer;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.UPSs;

import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;

@Slf4j
class MemcachedClientFactoryBeanTest {
    private static final int MEMCACHED_PORT = 11211;

    @Test
    void works() throws Throwable {
        try (GenericContainer<?> container = new GenericContainer<>("memcached")) {
            container.withExposedPorts(MEMCACHED_PORT);
            container.start();

            String host = container.getHost();
            Integer port = container.getMappedPort(MEMCACHED_PORT);
            MemcachedServiceInfo si = new MemcachedServiceInfo(UPSs.MEMCACHED, host, port);

            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            SimpleBootstrap bootstrap = new SimpleBootstrap(props);
            bootstrap.addUps(si);
            bootstrap.run();

            MemcachedClientFactoryBean factoryBean = new MemcachedClientFactoryBean(props, bootstrap.cloud(), bootstrap.meterRegistry());
            factoryBean.afterPropertiesSet();

            try {
                MemcachedClient client = factoryBean.getObject();

                ImmutableIntFloatMap map = IntFloatHashMap.newWithKeysValues(1, 2.0f, 3, 4.0f).toImmutable();

                client.set("a:1:key", (int) Duration.ofHours(1).toSeconds(), map);

                IntFloatMap copy = client.get("a:1:key");
                Assertions.assertEquals(2.0f, copy.get(1));
                Assertions.assertEquals(4.0f, copy.get(3));
            } finally {
                factoryBean.destroy();
                bootstrap.shutdown();
            }
        }
    }
}
