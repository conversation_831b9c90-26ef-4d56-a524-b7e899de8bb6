package com.turbospaces.memcached;

import java.io.Closeable;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;

import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.CASOperation;
import net.rubyeye.xmemcached.Counter;
import net.rubyeye.xmemcached.GetsResponse;
import net.rubyeye.xmemcached.KeyIterator;
import net.rubyeye.xmemcached.KeyProvider;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.MemcachedClientCallable;
import net.rubyeye.xmemcached.MemcachedClientStateListener;
import net.rubyeye.xmemcached.auth.AuthInfo;
import net.rubyeye.xmemcached.buffer.BufferAllocator;
import net.rubyeye.xmemcached.exception.MemcachedException;
import net.rubyeye.xmemcached.impl.ReconnectRequest;
import net.rubyeye.xmemcached.networking.Connector;
import net.rubyeye.xmemcached.transcoders.Transcoder;
import net.rubyeye.xmemcached.utils.Protocol;

@Slf4j
@SuppressWarnings({ "deprecation", "rawtypes" })
public class ReloadableMemcachedClient implements MemcachedClient, Closeable {
    private final AtomicReference<MemcachedClient> updater = new AtomicReference<>();

    public void replace(MemcachedClient client) throws IOException {
        MemcachedClient old = updater.getAndSet(client);
        if (Objects.nonNull(old)) {
            log.trace("disposing old memcached client ... ");
            old.shutdown();
        }
    }
    @Override
    public void setMergeFactor(int mergeFactor) {
        updater.get().setMergeFactor(mergeFactor);
    }
    @Override
    public long getConnectTimeout() {
        return updater.get().getConnectTimeout();
    }
    @Override
    public void setConnectTimeout(long connectTimeout) {
        updater.get().setConnectTimeout(connectTimeout);
    }
    @Override
    public Connector getConnector() {
        return updater.get().getConnector();
    }
    @Override
    public void setOptimizeGet(boolean optimizeGet) {
        updater.get().setOptimizeGet(optimizeGet);
    }
    @Override
    public void setOptimizeMergeBuffer(boolean optimizeMergeBuffer) {
        updater.get().setOptimizeMergeBuffer(optimizeMergeBuffer);
    }
    @Override
    public boolean isShutdown() {
        return updater.get().isShutdown();
    }
    @Override
    public void addServer(String server, int port) throws IOException {
        updater.get().addServer(server, port);
    }
    @Override
    public void addServer(InetSocketAddress inetSocketAddress) throws IOException {
        updater.get().addServer(inetSocketAddress);
    }
    @Override
    public void addServer(String hostList) throws IOException {
        updater.get().addServer(hostList);
    }
    @Override
    public List<String> getServersDescription() {
        return updater.get().getServersDescription();
    }
    @Override
    public void removeServer(String hostList) {
        updater.get().removeServer(hostList);
    }
    @Override
    public void removeServer(InetSocketAddress address) {
        updater.get().removeServer(address);
    }
    @Override
    public void setBufferAllocator(BufferAllocator bufferAllocator) {
        updater.get().setBufferAllocator(bufferAllocator);
    }
    @Override
    public <T> T get(String key, long timeout, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(key, timeout, transcoder);
    }
    @Override
    public <T> T get(String key, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(key, timeout);
    }
    @Override
    public <T> T get(String key, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(key, transcoder);
    }
    @Override
    public <T> T get(String key) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(key);
    }
    @Override
    public <T> GetsResponse<T> gets(String key, long timeout, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(key, timeout, transcoder);
    }
    @Override
    public <T> GetsResponse<T> gets(String key) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(key);
    }
    @Override
    public <T> GetsResponse<T> gets(String key, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(key, timeout);
    }
    @Override
    public <T> GetsResponse<T> gets(String key, Transcoder transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(key, transcoder);
    }
    @Override
    public <T> Map<String, T> get(Collection<String> keyCollections, long opTimeout, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(keyCollections, opTimeout, transcoder);
    }
    @Override
    public <T> Map<String, T> get(Collection<String> keyCollections, Transcoder<T> transcoder)
            throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(keyCollections, transcoder);
    }
    @Override
    public <T> Map<String, T> get(Collection<String> keyCollections) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(keyCollections);
    }
    @Override
    public <T> Map<String, T> get(Collection<String> keyCollections, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().get(keyCollections, timeout);
    }
    @Override
    public <T> Map<String, GetsResponse<T>> gets(Collection<String> keyCollections, long opTime, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(keyCollections, opTime, transcoder);
    }
    @Override
    public <T> Map<String, GetsResponse<T>> gets(Collection<String> keyCollections) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(keyCollections);
    }
    @Override
    public <T> Map<String, GetsResponse<T>> gets(Collection<String> keyCollections, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(keyCollections, timeout);
    }
    @Override
    public <T> Map<String, GetsResponse<T>> gets(Collection<String> keyCollections, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().gets(keyCollections, transcoder);
    }
    @Override
    public <T> boolean set(String key, int exp, T value, Transcoder<T> transcoder, long timeout)
            throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().set(key, exp, value, transcoder, timeout);
    }
    @Override
    public boolean set(String key, int exp, Object value) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().set(key, exp, value);
    }
    @Override
    public boolean set(String key, int exp, Object value, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().set(key, exp, value, timeout);
    }
    @Override
    public <T> boolean set(String key, int exp, T value, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().set(key, exp, value, transcoder);
    }
    @Override
    public void setWithNoReply(String key, int exp, Object value) throws InterruptedException, MemcachedException {
        updater.get().setWithNoReply(key, exp, value);
    }
    @Override
    public <T> void setWithNoReply(String key, int exp, T value, Transcoder<T> transcoder) throws InterruptedException, MemcachedException {
        updater.get().setWithNoReply(key, exp, value, transcoder);
    }
    @Override
    public <T> boolean add(String key, int exp, T value, Transcoder<T> transcoder, long timeout)
            throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().add(key, exp, value, transcoder, timeout);
    }
    @Override
    public boolean add(String key, int exp, Object value) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().add(key, exp, value);
    }
    @Override
    public boolean add(String key, int exp, Object value, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().add(key, exp, value, timeout);
    }
    @Override
    public <T> boolean add(String key, int exp, T value, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().add(key, exp, value, transcoder);
    }
    @Override
    public void addWithNoReply(String key, int exp, Object value) throws InterruptedException, MemcachedException {
        updater.get().addWithNoReply(key, exp, value);
    }
    @Override
    public <T> void addWithNoReply(String key, int exp, T value, Transcoder<T> transcoder) throws InterruptedException, MemcachedException {
        updater.get().addWithNoReply(key, exp, value, transcoder);
    }
    @Override
    public <T> boolean replace(String key, int exp, T value, Transcoder<T> transcoder, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().replace(key, exp, value, transcoder, timeout);
    }
    @Override
    public boolean replace(String key, int exp, Object value) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().replace(key, exp, value);
    }
    @Override
    public boolean replace(String key, int exp, Object value, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().replace(key, exp, value, timeout);
    }
    @Override
    public <T> boolean replace(String key, int exp, T value, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().replace(key, exp, value, transcoder);
    }
    @Override
    public void replaceWithNoReply(String key, int exp, Object value) throws InterruptedException, MemcachedException {
        updater.get().replaceWithNoReply(key, exp, value);
    }
    @Override
    public <T> void replaceWithNoReply(String key, int exp, T value, Transcoder<T> transcoder) throws InterruptedException, MemcachedException {
        updater.get().replaceWithNoReply(key, exp, value, transcoder);
    }
    @Override
    public boolean append(String key, Object value) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().append(key, value);
    }
    @Override
    public boolean append(String key, Object value, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().append(key, value, timeout);
    }
    @Override
    public void appendWithNoReply(String key, Object value) throws InterruptedException, MemcachedException {
        updater.get().appendWithNoReply(key, value);
    }
    @Override
    public boolean prepend(String key, Object value) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().prepend(key, value);
    }
    @Override
    public boolean prepend(String key, Object value, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().prepend(key, value, timeout);
    }
    @Override
    public void prependWithNoReply(String key, Object value) throws InterruptedException, MemcachedException {
        updater.get().prependWithNoReply(key, value);
    }
    @Override
    public boolean cas(String key, int exp, Object value, long cas) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, value, cas);
    }
    @Override
    public <T> boolean cas(String key, int exp, T value, Transcoder<T> transcoder, long timeout, long cas) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, value, transcoder, timeout, cas);
    }
    @Override
    public boolean cas(String key, int exp, Object value, long timeout, long cas) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, value, timeout, cas);
    }
    @Override
    public <T> boolean cas(String key, int exp, T value, Transcoder<T> transcoder, long cas) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, value, transcoder, cas);
    }
    @Override
    public <T> boolean cas(String key, int exp, CASOperation<T> operation, Transcoder<T> transcoder)
            throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, operation, transcoder);
    }
    @Override
    public <T> boolean cas(String key, int exp, GetsResponse<T> getsReponse, CASOperation<T> operation, Transcoder<T> transcoder) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, getsReponse, operation, transcoder);
    }
    @Override
    public <T> boolean cas(String key, int exp, GetsResponse<T> getsReponse, CASOperation<T> operation)
            throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, getsReponse, operation);
    }
    @Override
    public <T> boolean cas(String key, GetsResponse<T> getsResponse, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, getsResponse, operation);
    }
    @Override
    public <T> boolean cas(String key, int exp, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, exp, operation);
    }
    @Override
    public <T> boolean cas(String key, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().cas(key, operation);
    }
    @Override
    public <T> void casWithNoReply(String key, GetsResponse<T> getsResponse, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().casWithNoReply(key, getsResponse, operation);
    }
    @Override
    public <T> void casWithNoReply(String key, int exp, GetsResponse<T> getsReponse, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().casWithNoReply(key, exp, getsReponse, operation);
    }
    @Override
    public <T> void casWithNoReply(String key, int exp, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().casWithNoReply(key, exp, operation);
    }
    @Override
    public <T> void casWithNoReply(String key, CASOperation<T> operation) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().casWithNoReply(key, operation);
    }
    @Override
    public boolean delete(String key, int time) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().delete(key, time);
    }
    @Override
    public boolean delete(String key, long opTimeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().delete(key, opTimeout);
    }
    @Override
    public boolean delete(String key, long cas, long opTimeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().delete(key, cas, opTimeout);
    }
    @Override
    public boolean touch(String key, int exp, long opTimeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().touch(key, exp, opTimeout);
    }
    @Override
    public boolean touch(String key, int exp) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().touch(key, exp);
    }
    @Override
    public <T> T getAndTouch(String key, int newExp, long opTimeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().getAndTouch(key, newExp, opTimeout);
    }
    @Override
    public <T> T getAndTouch(String key, int newExp) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().getAndTouch(key, newExp);
    }
    @Override
    public Map<InetSocketAddress, String> getVersions() throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().getVersions();
    }
    @Override
    public long incr(String key, long delta) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().incr(key, delta);
    }
    @Override
    public long incr(String key, long delta, long initValue) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().incr(key, delta, initValue);
    }
    @Override
    public long incr(String key, long delta, long initValue, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().incr(key, delta, initValue, timeout);
    }
    @Override
    public long decr(String key, long delta) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().decr(key, delta);
    }
    @Override
    public long decr(String key, long delta, long initValue) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().decr(key, delta, initValue);
    }
    @Override
    public long decr(String key, long delta, long initValue, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().decr(key, delta, initValue, timeout);
    }
    @Override
    public void flushAll() throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().flushAll();
    }
    @Override
    public void flushAllWithNoReply() throws InterruptedException, MemcachedException {
        updater.get().flushAllWithNoReply();
    }
    @Override
    public void flushAll(long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().flushAll(timeout);
    }
    @Override
    public void flushAll(InetSocketAddress address) throws MemcachedException, InterruptedException, TimeoutException {
        updater.get().flushAll(address);
    }
    @Override
    public void flushAllWithNoReply(InetSocketAddress address) throws MemcachedException, InterruptedException {
        updater.get().flushAllWithNoReply(address);
    }
    @Override
    public void flushAll(InetSocketAddress address, long timeout) throws MemcachedException, InterruptedException, TimeoutException {
        updater.get().flushAll(address, timeout);
    }
    @Override
    public void flushAll(String host) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().flushAll(host);
    }
    @Override
    public Map<String, String> stats(InetSocketAddress address) throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().stats(address);
    }
    @Override
    public Map<String, String> stats(InetSocketAddress address, long timeout) throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().stats(address, timeout);
    }
    @Override
    public Map<InetSocketAddress, Map<String, String>> getStats(long timeout) throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().getStats(timeout);
    }
    @Override
    public Map<InetSocketAddress, Map<String, String>> getStats() throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().getStats();
    }
    @Override
    public Map<InetSocketAddress, Map<String, String>> getStatsByItem(String itemName) throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().getStatsByItem(itemName);
    }
    @Override
    public boolean delete(String key) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().delete(key);
    }
    @Override
    public Transcoder getTranscoder() {
        return updater.get().getTranscoder();
    }
    @Override
    public void setTranscoder(Transcoder transcoder) {
        updater.get().setTranscoder(transcoder);
    }
    @Override
    public Map<InetSocketAddress, Map<String, String>> getStatsByItem(String itemName, long timeout)
            throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().getStatsByItem(itemName, timeout);
    }
    @Override
    public long getOpTimeout() {
        return updater.get().getOpTimeout();
    }
    @Override
    public void setOpTimeout(long opTimeout) {
        updater.get().setOpTimeout(opTimeout);
    }
    @Override
    public Map<InetSocketAddress, String> getVersions(long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().getVersions(timeout);
    }
    @Override
    public Collection<InetSocketAddress> getAvaliableServers() {
        return updater.get().getAvaliableServers();
    }
    @Override
    public Collection<InetSocketAddress> getAvailableServers() {
        return updater.get().getAvailableServers();
    }
    @Override
    public void addServer(String server, int port, int weight) throws IOException {
        updater.get().addServer(server, port, weight);
    }
    @Override
    public void addServer(InetSocketAddress inetSocketAddress, int weight) throws IOException {
        updater.get().addServer(inetSocketAddress, weight);
    }
    @Override
    public void deleteWithNoReply(String key, int time) throws InterruptedException, MemcachedException {
        updater.get().deleteWithNoReply(key, time);
    }
    @Override
    public void deleteWithNoReply(String key) throws InterruptedException, MemcachedException {
        updater.get().deleteWithNoReply(key);
    }
    @Override
    public void incrWithNoReply(String key, long delta) throws InterruptedException, MemcachedException {
        updater.get().incrWithNoReply(key, delta);
    }
    @Override
    public void decrWithNoReply(String key, long delta) throws InterruptedException, MemcachedException {
        updater.get().decrWithNoReply(key, delta);
    }
    @Override
    public void setLoggingLevelVerbosity(InetSocketAddress address, int level) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().setLoggingLevelVerbosity(address, level);
    }
    @Override
    public void setLoggingLevelVerbosityWithNoReply(InetSocketAddress address, int level) throws InterruptedException, MemcachedException {
        updater.get().setLoggingLevelVerbosityWithNoReply(address, level);
    }
    @Override
    public void addStateListener(MemcachedClientStateListener listener) {
        updater.get().addStateListener(listener);
    }
    @Override
    public void removeStateListener(MemcachedClientStateListener listener) {
        updater.get().removeStateListener(listener);
    }
    @Override
    public Collection<MemcachedClientStateListener> getStateListeners() {
        return updater.get().getStateListeners();
    }
    @Override
    public void flushAllWithNoReply(int exptime) throws InterruptedException, MemcachedException {
        updater.get().flushAllWithNoReply(exptime);
    }
    @Override
    public void flushAll(int exptime, long timeout) throws TimeoutException, InterruptedException, MemcachedException {
        updater.get().flushAll(exptime, timeout);
    }
    @Override
    public void flushAllWithNoReply(InetSocketAddress address, int exptime) throws MemcachedException, InterruptedException {
        updater.get().flushAllWithNoReply(address, exptime);
    }
    @Override
    public void flushAll(InetSocketAddress address, long timeout, int exptime) throws MemcachedException, InterruptedException, TimeoutException {
        updater.get().flushAll(address, timeout, exptime);
    }
    @Override
    public void setHealSessionInterval(long healConnectionInterval) {
        updater.get().setHealSessionInterval(healConnectionInterval);
    }
    @Override
    public void setEnableHealSession(boolean enableHealSession) {
        updater.get().setEnableHealSession(enableHealSession);
    }
    @Override
    public long getHealSessionInterval() {
        return updater.get().getHealSessionInterval();
    }
    @Override
    public Protocol getProtocol() {
        return updater.get().getProtocol();
    }
    @Override
    public void setPrimitiveAsString(boolean primitiveAsString) {
        updater.get().setPrimitiveAsString(primitiveAsString);
    }
    @Override
    public void setConnectionPoolSize(int poolSize) {
        updater.get().setConnectionPoolSize(poolSize);
    }
    @Override
    public void setEnableHeartBeat(boolean enableHeartBeat) {
        updater.get().setEnableHeartBeat(enableHeartBeat);
    }
    @Override
    public void setSanitizeKeys(boolean sanitizeKey) {
        updater.get().setSanitizeKeys(sanitizeKey);
    }
    @Override
    public boolean isSanitizeKeys() {
        return updater.get().isSanitizeKeys();
    }
    @Override
    public Counter getCounter(String key) {
        return updater.get().getCounter(key);
    }
    @Override
    public Counter getCounter(String key, long initialValue) {
        return updater.get().getCounter(key, initialValue);
    }
    @Override
    public KeyIterator getKeyIterator(InetSocketAddress address) throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().getKeyIterator(address);
    }
    @Override
    public void setAuthInfoMap(Map<InetSocketAddress, AuthInfo> map) {
        updater.get().setAuthInfoMap(map);
    }
    @Override
    public Map<InetSocketAddress, AuthInfo> getAuthInfoMap() {
        return updater.get().getAuthInfoMap();
    }
    @Override
    public Map<String, AuthInfo> getAuthInfoStringMap() {
        return updater.get().getAuthInfoStringMap();
    }
    @Override
    public long decr(String key, long delta, long initValue, long timeout, int exp) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().decr(key, delta, initValue, timeout, exp);
    }
    @Override
    public long incr(String key, long delta, long initValue, long timeout, int exp) throws TimeoutException, InterruptedException, MemcachedException {
        return updater.get().incr(key, delta, initValue, timeout, exp);
    }
    @Override
    public String getName() {
        return updater.get().getName();
    }
    @Override
    public void setName(String name) {
        updater.get().setName(name);
    }
    @Override
    public Queue<ReconnectRequest> getReconnectRequestQueue() {
        return updater.get().getReconnectRequestQueue();
    }
    @Override
    public void setFailureMode(boolean failureMode) {
        updater.get().setFailureMode(failureMode);
    }
    @Override
    public boolean isFailureMode() {
        return updater.get().isFailureMode();
    }
    @Override
    public void setKeyProvider(KeyProvider keyProvider) {
        updater.get().setKeyProvider(keyProvider);
    }
    @Override
    public int getTimeoutExceptionThreshold() {
        return updater.get().getTimeoutExceptionThreshold();
    }
    @Override
    public void setTimeoutExceptionThreshold(int timeoutExceptionThreshold) {
        updater.get().setTimeoutExceptionThreshold(timeoutExceptionThreshold);
    }
    @Override
    public void invalidateNamespace(String ns) throws MemcachedException, InterruptedException, TimeoutException {
        updater.get().invalidateNamespace(ns);
    }
    @Override
    public void invalidateNamespace(String ns, long opTimeout) throws MemcachedException, InterruptedException, TimeoutException {
        updater.get().invalidateNamespace(ns, opTimeout);
    }
    @Override
    public void endWithNamespace() {
        updater.get().endWithNamespace();
    }
    @Override
    public void beginWithNamespace(String ns) {
        updater.get().beginWithNamespace(ns);
    }
    @Override
    public <T> T withNamespace(String ns, MemcachedClientCallable<T> callable) throws MemcachedException, InterruptedException, TimeoutException {
        return updater.get().withNamespace(ns, callable);
    }
    @Override
    public void shutdown() throws IOException {
        MemcachedClient current = updater.get();
        if (Objects.nonNull(current)) {
            current.shutdown();
        }
    }
    @Override
    public void close() throws IOException {
        shutdown();
    }
}
