package com.turbospaces.memcached;

import java.util.Objects;
import java.util.concurrent.TimeoutException;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.service.MemcachedService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.exception.MemcachedException;

@Slf4j
@RequiredArgsConstructor
public class RubyeyeMemcachedService implements MemcachedService {
    private final ApplicationProperties props;
    @Delegate
    private final MemcachedClient client;

    @Override
    public boolean exists(String key) throws TimeoutException, InterruptedException, MemcachedException {
        return Objects.nonNull(client.get(key));
    }
    @Override
    public long increment(String key, int exp) throws TimeoutException, InterruptedException, MemcachedException {
        int delta = 1;
        int initialValue = 1;
        long timeout = props.TCP_SOCKET_TIMEOUT.get().toMillis();
        return client.incr(key, delta, initialValue, timeout, exp);
    }
    @Override
    public long incrementAndTouch(String key, int exp) throws TimeoutException, InterruptedException, MemcachedException {
        int delta = 1;
        int initialValue = 1;
        long timeout = props.TCP_SOCKET_TIMEOUT.get().toMillis();
        long value = client.incr(key, delta, initialValue, timeout, exp);
        if (value > initialValue) {
            client.touch(key, exp);
        }
        return value;
    }
}
