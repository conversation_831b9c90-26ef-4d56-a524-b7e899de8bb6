package com.turbospaces.memcached;

import java.util.Objects;

import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.cloud.DynamicCloud;

import com.google.code.yanf4j.core.impl.StandardSocketOption;
import com.google.common.net.HostAndPort;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.ServiceInfoSubscription;
import com.turbospaces.ups.UPSs;

import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.CheckedConsumer;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.MemcachedClientBuilder;
import net.rubyeye.xmemcached.XMemcachedClientBuilder;
import net.rubyeye.xmemcached.command.TextCommandFactory;
import net.rubyeye.xmemcached.impl.ArrayMemcachedSessionLocator;

@Slf4j
public class MemcachedClientFactoryBean extends AbstractFactoryBean<MemcachedClient> {
    private final ReloadableMemcachedClient reloadable = new ReloadableMemcachedClient();
    private final ApplicationProperties props;
    private final DynamicCloud cloud;
    private ServiceInfoSubscription<MemcachedServiceInfo> subscription;

    public MemcachedClientFactoryBean(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry) {
        this(props, cloud, meterRegistry, UPSs.MEMCACHED);
    }
    public MemcachedClientFactoryBean(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            String si) {
        this.props = Objects.requireNonNull(props);
        this.cloud = Objects.requireNonNull(cloud);

        //
        // ~ just to ensure that the service info is available
        //
        UPSs.findRequiredServiceInfoByName(cloud, si);
    }
    @Override
    public Class<?> getObjectType() {
        return MemcachedClient.class;
    }
    @Override
    protected MemcachedClient createInstance() throws Exception {
        subscription = ServiceInfoSubscription.of(cloud, UPSs.MEMCACHED, new CheckedConsumer<MemcachedServiceInfo>() {
            @Override
            public void accept(MemcachedServiceInfo updated) throws Throwable {
                log.info("memcached configuration changed, reloading client...");

                MemcachedClientBuilder builder = new XMemcachedClientBuilder(HostAndPort.fromParts(updated.getHost(), updated.getPort()).toString());

                builder.setCommandFactory(new TextCommandFactory());
                builder.setSessionLocator(new ArrayMemcachedSessionLocator());

                builder.setConnectTimeout(props.TCP_CONNECTION_TIMEOUT.get().toMillis());
                builder.setOpTimeout(props.TCP_SOCKET_TIMEOUT.get().toMillis());

                builder.setSocketOption(StandardSocketOption.SO_KEEPALIVE, props.TCP_KEEP_ALIVE.get());
                builder.setSocketOption(StandardSocketOption.TCP_NODELAY, props.TCP_NO_DELAY.get());
                builder.setSocketOption(StandardSocketOption.SO_REUSEADDR, props.TCP_REUSE_ADDRESS.get());

                reloadable.replace(builder.build());
            }
        });

        return reloadable;
    }
    @Override
    protected void destroyInstance(MemcachedClient instance) throws Exception {
        if (Objects.nonNull(instance)) {
            instance.shutdown();
        }
        if (Objects.nonNull(subscription)) {
            subscription.dispose();
        }
    }
}
