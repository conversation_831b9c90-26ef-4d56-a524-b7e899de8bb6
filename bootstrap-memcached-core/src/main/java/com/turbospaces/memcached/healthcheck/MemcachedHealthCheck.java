package com.turbospaces.memcached.healthcheck;

import java.io.IOException;
import java.net.InetSocketAddress;

import com.google.common.net.HostAndPort;
import com.turbospaces.boot.AbstractHealtchCheck;
import com.turbospaces.ups.MemcachedServiceInfo;

import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.XMemcachedClientBuilder;

public class MemcachedHealthCheck extends AbstractHealtchCheck {
    private final HostAndPort hostAndPort;
    private final MemcachedClient client;

    public MemcachedHealthCheck(MemcachedServiceInfo msi) throws IOException {
        hostAndPort = HostAndPort.fromParts(msi.getHost(), msi.getPort());
        client = new XMemcachedClientBuilder(hostAndPort.toString()).build();
    }
    @Override
    public boolean isBootstrapOnly() {
        return true;
    }
    @Override
    protected Result check() throws Exception {
        try {
            InetSocketAddress address = new InetSocketAddress(hostAndPort.getHost(), hostAndPort.getPort());
            client.stats(address);
        } catch (Exception err) {
            String msg = "stats is not available for: " + hostAndPort.toString();
            logger.warn(err.getMessage(), err);
            return Result.unhealthy(msg);
        }

        return Result.healthy();
    }
    @Override
    public void destroy() throws Exception {
        if (client != null) {
            try {
                client.shutdown();
            } catch (IOException err) {
                logger.warn(err.getMessage(), err);
            }
        }
    }
}
