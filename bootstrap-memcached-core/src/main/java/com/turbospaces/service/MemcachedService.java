package com.turbospaces.service;

import java.util.concurrent.TimeoutException;

import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.exception.MemcachedException;

public interface MemcachedService extends MemcachedClient {
    boolean exists(String key) throws TimeoutException, InterruptedException, MemcachedException;
    long increment(String key, int exp) throws TimeoutException, InterruptedException, MemcachedException;
    long incrementAndTouch(String key, int exp) throws TimeoutException, InterruptedException, MemcachedException;
}
