package com.turbospaces.api;

import java.util.concurrent.TimeUnit;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DebeziumHeartbeatTopic implements Topic {
    private final ApplicationProperties props;

    @Override
    public AsciiString name() {
        return AsciiString.cached("__debezium-heartbeat." + props.CLOUD_APP_SPACE_NAME.get());
    }
    @Override
    public void configure(ApplicationConfig config) {
        config.setDefaultProperty(name() + "." + Topic.PARTITIONS, 1);
        config.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, 2);
        config.setDefaultProperty(name() + "." + "retention.ms", TimeUnit.DAYS.toMillis(1));
    }
    @Override
    public String toString() {
        return name().toString();
    }
}
