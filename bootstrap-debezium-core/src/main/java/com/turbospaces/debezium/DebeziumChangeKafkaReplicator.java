package com.turbospaces.debezium;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.json.JsonConverter;
import org.apache.kafka.connect.source.SourceRecord;

import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import com.turbospaces.annotations.Dictionary;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.producer.DebeziumKafkaPostTemplate;

import io.debezium.DebeziumException;
import io.debezium.connector.AbstractSourceInfo;
import io.debezium.data.Envelope;
import io.debezium.engine.ChangeEvent;
import io.debezium.engine.DebeziumEngine.ChangeConsumer;
import io.debezium.engine.DebeziumEngine.RecordCommitter;
import io.ebean.plugin.BeanType;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple5;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Accessors(fluent = true)
@RequiredArgsConstructor
public class DebeziumChangeKafkaReplicator implements ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> {
    private final ApplicationProperties props;
    private final DebeziumKafkaPostTemplate postTemplate;
    private final IsDictionaryCache isDictionaryCache;
    private final JsonConverter jsonKey;
    private final JsonConverter jsonValue;
    @Setter
    private Supplier<Boolean> dryRun = Suppliers.ofInstance(false);

    @Override
    public void handleBatch(
            List<ChangeEvent<SourceRecord, SourceRecord>> records,
            RecordCommitter<ChangeEvent<SourceRecord, SourceRecord>> committer) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(records.size());
        AtomicReference<Throwable> throwable = new AtomicReference<>();

        if (Boolean.FALSE.equals(dryRun.get())) {
            //
            // ~ well we simply iterate and acknowledge processing only at the very late
            //
            List<Tuple5<String, byte[], Object, byte[], Object>> summary = Lists.newLinkedList();
            for (ChangeEvent<SourceRecord, SourceRecord> event : records) {
                if (log.isTraceEnabled()) {
                    log.trace(event.toString());
                }

                boolean sent = false;
                SourceRecord sourceRecord = event.value();
                if (Objects.nonNull(sourceRecord)) {
                    Struct struct = (Struct) sourceRecord.value();
                    if (Objects.nonNull(struct.schema().field(Envelope.FieldName.SOURCE))) {
                        Struct source = (Struct) struct.get(Envelope.FieldName.SOURCE);
                        if (Objects.nonNull(source)) {
                            String schema = source.get(AbstractSourceInfo.SCHEMA_NAME_KEY).toString();
                            String table = source.get(AbstractSourceInfo.TABLE_NAME_KEY).toString();

                            //
                            // ~ well we are interested only in CRUD (ignore rest)
                            //
                            if (StringUtils.isNotEmpty(schema) && StringUtils.isNotEmpty(table)) {
                                boolean toReplicate = true;

                                //
                                // ~ potentially if this is a dictionary, we may not want to replicate to MQ and use only as invalidation mechanism
                                //
                                Tuple2<Boolean, BeanType<?>> tuple = isDictionaryCache.getUnchecked(Tuple.of(schema, table));
                                if (tuple._1()) {
                                    if (Objects.nonNull(tuple._2())) {
                                        Class<?> type = tuple._2().type();
                                        Dictionary dictionary = type.getAnnotation(Dictionary.class);
                                        toReplicate = dictionary.replicate();
                                    }
                                }

                                if (toReplicate) {
                                    byte[] k = jsonKey.fromConnectData(event.destination(), sourceRecord.keySchema(), sourceRecord.key());
                                    byte[] v = jsonValue.fromConnectData(event.destination(), sourceRecord.valueSchema(), sourceRecord.value());
                                    summary.add(Tuple.of(event.destination(), k, sourceRecord.key(), v, sourceRecord.value()));

                                    sent = true;
                                    postTemplate.send(new ProducerRecord<>(event.destination(), k, v), new Callback() {
                                        @Override
                                        public void onCompletion(RecordMetadata metadata, Exception err) {
                                            try {
                                                if (Objects.nonNull(err)) {
                                                    throwable.set(err);
                                                }
                                            } finally {
                                                latch.countDown();
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    }
                }

                //
                // ~ well some events can be skipped, like custom CDC message or not marked non-replicated
                //
                if (BooleanUtils.isFalse(sent)) {
                    latch.countDown();
                }
            }

            //
            // ~ well do not commit unless we have full acknowledgement from MQ
            //
            latch.await();

            //
            // ~ well not POST anything sensitive into logs unless it it trace
            //
            if (log.isTraceEnabled()) {
                summary.forEach(new Consumer<>() {
                    @Override
                    public void accept(Tuple5<String, byte[], Object, byte[], Object> tuple) {
                        byte[] keyAsJson = tuple._2();
                        Object key = tuple._3();
                        byte[] valueAsJson = tuple._4();
                        Object value = tuple._5();
                        log.trace("published to {} {} => {} {} => {}", tuple._1(), new String(keyAsJson), new String(valueAsJson), key, value);
                    }
                });
            } else if (log.isDebugEnabled()) {
                //
                // ~ and maybe just group messages by topic and print only keys
                //
                summary.stream().collect(Collectors.groupingBy(Tuple5::_1)).forEach(new BiConsumer<>() {
                    @Override
                    public void accept(String destination, List<Tuple5<String, byte[], Object, byte[], Object>> u) {
                        if (props.isDevMode()) {
                            log.debug("published {} record(s) to {} {}", u.size(), destination, u.stream().map(Tuple5::_3).toList());
                        } else {
                            log.debug("published {} record(s) to {}", u.size(), destination);
                        }
                    }
                });
            }
        }

        //
        // ~ most important line - we store the state in MQ broker so that we can recover if needed
        //
        if (Objects.isNull(throwable.get())) {
            for (ChangeEvent<SourceRecord, SourceRecord> event : records) {
                committer.markProcessed(event);
            }
            committer.markBatchFinished();
        } else {
            throw new DebeziumException(throwable.get());
        }
    }
    @Override
    public boolean supportsTombstoneEvents() {
        return false;
    }
}
