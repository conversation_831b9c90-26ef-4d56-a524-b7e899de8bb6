package com.turbospaces.debezium;

import java.security.KeyStore;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.connect.json.JsonConverter;
import org.apache.kafka.connect.json.JsonConverterConfig;
import org.apache.kafka.connect.runtime.WorkerConfig;
import org.apache.kafka.connect.runtime.distributed.DistributedConfig;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.ConverterConfig;
import org.apache.kafka.connect.storage.ConverterType;
import org.apache.kafka.connect.storage.KafkaOffsetBackingStore;
import org.apache.kafka.connect.transforms.RegexRouter;
import org.slf4j.event.Level;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Suppliers;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.SettableFuture;
import com.google.common.util.concurrent.Uninterruptibles;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DebeziumLeaderFlag;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.kafka.KafkaSecurity;
import com.turbospaces.kafka.producer.DebeziumKafkaPostTemplate;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import io.debezium.DebeziumException;
import io.debezium.config.CommonConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnector;
import io.debezium.connector.postgresql.PostgresConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnectorConfig.AutoCreateMode;
import io.debezium.connector.postgresql.PostgresConnectorConfig.LogicalDecoder;
import io.debezium.connector.postgresql.PostgresConnectorConfig.SnapshotMode;
import io.debezium.connector.postgresql.connection.ReplicationConnection;
import io.debezium.embedded.Connect;
import io.debezium.embedded.EmbeddedEngineConfig;
import io.debezium.embedded.async.AsyncEmbeddedEngine;
import io.debezium.embedded.async.AsyncEngineConfig;
import io.debezium.embedded.async.ConvertingAsyncEngineBuilderFactory;
import io.debezium.engine.ChangeEvent;
import io.debezium.engine.DebeziumEngine.ChangeConsumer;
import io.debezium.engine.DebeziumEngine.CompletionCallback;
import io.debezium.engine.DebeziumEngine.ConnectorCallback;
import io.debezium.engine.DebeziumEngine.RecordCommitter;
import io.debezium.engine.format.KeyValueChangeEventFormat;
import io.debezium.engine.spi.OffsetCommitPolicy;
import io.debezium.relational.HistorizedRelationalDatabaseConnectorConfig;
import io.debezium.relational.RelationalDatabaseConnectorConfig;
import io.debezium.relational.history.SchemaHistory;
import io.debezium.storage.kafka.history.KafkaSchemaHistory;
import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.util.Loggers;

@Slf4j
@Accessors(fluent = true)
public class DebeziumChannel implements InitializingBean, DisposableBean, BeanNameAware {
    private final static String TYPE_CONFIG = ConverterConfig.TYPE_CONFIG;
    private final static String SCHEMAS_ENABLE_CONFIG = JsonConverterConfig.SCHEMAS_ENABLE_CONFIG;

    private final JsonConverter jsonKey = new JsonConverter();
    private final JsonConverter jsonValue = new JsonConverter();
    private final Object lifecycleMonitor = new Object();
    private final ApplicationProperties props;
    private final DynamicCloud cloud;
    private final KeyStore keyStore;
    private final RetryRegistry retryRegistry;
    private final PostgresqlServiceInfo psi;
    private final PlatformExecutorService executor;
    private final DebeziumLeaderFlag flag;
    private final DebeziumChangeKafkaReplicator replicator;
    private final IsDictionaryCache isDictionaryCache;
    @Setter
    private Properties dictionary = new Properties();
    private String beanName;
    private AsyncEmbeddedEngine<ChangeEvent<SourceRecord, SourceRecord>> engine;
    @Setter
    private List<Consumer<List<ChangeEvent<SourceRecord, SourceRecord>>>> handlers = Lists.newLinkedList();

    public DebeziumChannel(
            ApplicationProperties props,
            DynamicCloud cloud,
            KeyStore keyStore,
            MeterRegistry meterRegistry,
            RetryRegistry retryRegistry,
            PostgresqlServiceInfo psi,
            DebeziumKafkaPostTemplate postTemplate,
            EbeanJpaManager ebean,
            DebeziumLeaderFlag flag) {
        this.props = Objects.requireNonNull(props);
        this.cloud = Objects.requireNonNull(cloud);
        this.keyStore = Objects.requireNonNull(keyStore);
        this.retryRegistry = Objects.requireNonNull(retryRegistry);
        this.psi = Objects.requireNonNull(psi);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
        this.flag = Objects.requireNonNull(flag);
        this.isDictionaryCache = new IsDictionaryCache(ebean, meterRegistry);
        this.replicator = new DebeziumChangeKafkaReplicator(props, postTemplate, isDictionaryCache, jsonKey, jsonKey);

        jsonKey.configure(ImmutableMap.of(TYPE_CONFIG, ConverterType.KEY.name().toLowerCase(), SCHEMAS_ENABLE_CONFIG, Boolean.TRUE.toString()));
        jsonValue.configure(ImmutableMap.of(TYPE_CONFIG, ConverterType.VALUE.name().toLowerCase(), SCHEMAS_ENABLE_CONFIG, Boolean.TRUE.toString()));
        handlers.add(new EbeanQueryCacheInvalidationChangeConsumer(isDictionaryCache, ebean));
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = Objects.requireNonNull(name);
        executor.setBeanName(name);
    }
    public DebeziumChannel replicatorDryRun() {
        return replicatorDryRun(Suppliers.ofInstance(true));
    }
    public DebeziumChannel replicatorDryRun(Supplier<Boolean> value) {
        this.replicator.dryRun(value);
        return this;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();

        ImmutableMap.Builder<String, String> map = ImmutableMap.builder();
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        KafkaServiceInfo cdcksi = UPSs.<KafkaServiceInfo> findServiceInfoByName(cloud, UPSs.CDC_KAFKA).orElse(ksi);

        String pgApp = props.CLOUD_APP_ID.get().replaceAll("-", "_"); // ~ PG limitation, needs to be underscore
        String pgPublication = "%s_%s".formatted(pgApp, ReplicationConnection.Builder.DEFAULT_PUBLICATION_NAME);
        String pgReplicationSlot = "%s_%s".formatted(pgApp, ReplicationConnection.Builder.DEFAULT_SLOT_NAME);
        String mode = AutoCreateMode.DISABLED.getValue();
        int offsetsReplicationCount = props.isProdMode() ? Topic.DEFAULT_REPLICATION_FACTOR : 1; // ~ locally obviously replication factor = 1
        int maxRetries = Byte.MAX_VALUE;
        int parallelism = props.isDevMode() ? Runtime.getRuntime().availableProcessors() : 1; // ~ well in PROD we should be OK with just 1

        props.putValue(EmbeddedEngineConfig.ENGINE_NAME.name(), map, props.CLOUD_APP_ID.get());
        props.putValue(CommonConnectorConfig.TOPIC_PREFIX.name(), map, props.CLOUD_APP_SPACE_NAME.get());
        props.putValue(CommonConnectorConfig.MAX_RETRIES_ON_ERROR.name(), map, Integer.toString(maxRetries));
        props.putValue(CommonConnectorConfig.RETRIABLE_RESTART_WAIT.name(), map, Long.toString(TimeUnit.MINUTES.toMillis(1)));

        props.putValue(EmbeddedEngineConfig.CONNECTOR_CLASS.name(), map, PostgresConnector.class.getName());
        props.putValue(EmbeddedEngineConfig.OFFSET_STORAGE.name(), map, KafkaOffsetBackingStore.class.getName());
        props.putValue(AsyncEngineConfig.RECORD_PROCESSING_THREADS.name(), map, Integer.toString(parallelism));
        props.putValue(CommonConnectorConfig.INCREMENTAL_SNAPSHOT_CHUNK_SIZE.name(), map, Integer.toString(CommonConnectorConfig.DEFAULT_MAX_BATCH_SIZE * 32));
        props.putValue(CommonConnectorConfig.MAX_BATCH_SIZE.name(), map, Integer.toString(CommonConnectorConfig.DEFAULT_MAX_BATCH_SIZE * 16));
        props.putValue(CommonConnectorConfig.MAX_QUEUE_SIZE.name(), map, Integer.toString(CommonConnectorConfig.DEFAULT_MAX_QUEUE_SIZE * 16));

        map.put(RelationalDatabaseConnectorConfig.HOSTNAME.name(), psi.getHost());
        map.put(RelationalDatabaseConnectorConfig.PORT.name(), Integer.toString(psi.getPort()));
        map.put(RelationalDatabaseConnectorConfig.USER.name(), psi.getUserName());
        map.put(RelationalDatabaseConnectorConfig.PASSWORD.name(), psi.getPassword());
        map.put(RelationalDatabaseConnectorConfig.DATABASE_NAME.name(), psi.getPath());

        props.putValueIfPresent(RelationalDatabaseConnectorConfig.DATABASE_INCLUDE_LIST.name(), map);
        props.putValueIfPresent(RelationalDatabaseConnectorConfig.SCHEMA_INCLUDE_LIST.name(), map);
        props.putValueIfPresent(RelationalDatabaseConnectorConfig.TABLE_INCLUDE_LIST.name(), map);
        props.putValueIfPresent(CommonConnectorConfig.SIGNAL_DATA_COLLECTION.name(), map);

        //
        // ~ offsets
        //
        props.putValue(DistributedConfig.OFFSET_STORAGE_TOPIC_CONFIG, map, "%s.offsets".formatted(props.CLOUD_APP_ID.get()));
        props.putValue(DistributedConfig.OFFSET_STORAGE_PARTITIONS_CONFIG, map, Integer.toString(1));
        props.putValue(DistributedConfig.OFFSET_STORAGE_REPLICATION_FACTOR_CONFIG, map, Integer.toString(offsetsReplicationCount));

        props.putValue(DistributedConfig.GROUP_ID_CONFIG, map, "%s-debezium".formatted(props.CLOUD_APP_ID.get()));
        props.putValue(DistributedConfig.HEARTBEAT_INTERVAL_MS_CONFIG, map, Long.toString(props.KAFKA_HEARTBEAT_INTERVAL.get().toMillis()));
        props.putValue(DistributedConfig.SESSION_TIMEOUT_MS_CONFIG, map, Long.toString(props.KAFKA_SESSION_TIMEOUT.get().toMillis()));

        //
        // ~ main settings
        //
        props.putValue(PostgresConnectorConfig.PUBLICATION_NAME.name(), map, pgPublication);
        props.putValue(PostgresConnectorConfig.MAX_RETRIES.name(), map, Integer.toString(maxRetries));
        props.putValue(PostgresConnectorConfig.RETRY_DELAY_MS.name(), map, Long.toString(TimeUnit.MINUTES.toMillis(1)));
        props.putValue(PostgresConnectorConfig.SLOT_NAME.name(), map, pgReplicationSlot);
        props.putValue(PostgresConnectorConfig.PUBLICATION_AUTOCREATE_MODE.name(), map, mode);
        props.putValue(PostgresConnectorConfig.PLUGIN_NAME.name(), map, LogicalDecoder.PGOUTPUT.getValue());
        props.putValue(PostgresConnectorConfig.SNAPSHOT_MODE.name(), map, SnapshotMode.NO_DATA.getValue());
        props.putValue(PostgresConnectorConfig.DROP_SLOT_ON_STOP.name(), map, Boolean.FALSE.toString());
        props.putValue(CommonConnectorConfig.TOMBSTONES_ON_DELETE.name(), map, Boolean.FALSE.toString());

        //
        // ~ connect worker configuration
        //
        props.putValue(WorkerConfig.BOOTSTRAP_SERVERS_CONFIG, map, cdcksi.getBootstrapServers());
        props.putValue(WorkerConfig.TOPIC_CREATION_ENABLE_CONFIG, map, Boolean.toString(false));

        //
        // ~ schema changes history
        //
        props.putValue(HistorizedRelationalDatabaseConnectorConfig.SCHEMA_HISTORY.name(), map, KafkaSchemaHistory.class.getName());
        props.putValue(SchemaHistory.SKIP_UNPARSEABLE_DDL_STATEMENTS.name(), map, Boolean.toString(true));
        props.putValue(SchemaHistory.STORE_ONLY_CAPTURED_TABLES_DDL.name(), map, Boolean.toString(false));
        props.putValue(KafkaSchemaHistory.TOPIC.name(), map, "%s.debezium-history".formatted(props.CLOUD_APP_ID.get()));
        props.putValue(KafkaSchemaHistory.BOOTSTRAP_SERVERS.name(), map, cdcksi.getBootstrapServers());
        KafkaSecurity.configureSasl(cdcksi, keyStore).forEach(new BiConsumer<>() {
            @Override
            public void accept(String k, Object v) {
                props.putValue(k, map, v.toString());
                props.putValue(SchemaHistory.CONFIGURATION_FIELD_PREFIX_STRING + "consumer." + k, map, v.toString());
                props.putValue(SchemaHistory.CONFIGURATION_FIELD_PREFIX_STRING + "producer." + k, map, v.toString());
            }
        });

        var transform = dictionary.get(EmbeddedEngineConfig.TRANSFORMS.name());
        if (Objects.isNull(transform)) {
            dictionary.put(EmbeddedEngineConfig.TRANSFORMS.name(), "router");
        } else {
            dictionary.put(
                    EmbeddedEngineConfig.TRANSFORMS.name(),
                    Joiner.on(',').join(
                            ImmutableList.builder()
                                    .addAll(Splitter.on(',').omitEmptyStrings().split(transform.toString()))
                                    .add("router")
                                    .build()));
        }
        //
        // ~ remove topic prefix
        //
        dictionary.put(EmbeddedEngineConfig.TRANSFORMS.name() + ".router.type", RegexRouter.class.getName());
        dictionary.put(EmbeddedEngineConfig.TRANSFORMS.name() + ".router.regex", "([^.]+)\\.([^.]+)\\.([^.]+)");
        dictionary.put(EmbeddedEngineConfig.TRANSFORMS.name() + ".router.replacement", "$2.$3");

        //
        // ~ final list of properties passed
        //

        dictionary.putAll(map.build());
        dictionary.forEach(new BiConsumer<>() {
            @Override
            public void accept(Object t, Object u) {
                if (props.isDevMode()) {
                    log.debug("{}={}", t, u);
                }
            }
        });

        //
        // ~ might be sometimes to avoid by default
        //

        //
        // ~ well maybe it worth to subscribe just in case
        //
        flag.asFlux().log(Loggers.getLogger(getClass())).subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean value) {
                synchronized (lifecycleMonitor) {
                    if (value) {
                        try {
                            log.info("about to start debezium sink: {}", beanName);
                            SettableFuture<Boolean> future = recreateEngine();
                            executor.execute(engine);
                            log.info("started debezium sink: {}", beanName);
                            boolean ack = Uninterruptibles.getUninterruptibly(future, props.APP_WAIT_FOR_HEALTHCHECKS_TIMEOUT.get());
                            if (BooleanUtils.isFalse(ack)) {
                                log.error("unable to start debezium engine in {} sec(s)", props.APP_WAIT_FOR_HEALTHCHECKS_TIMEOUT.get().toSeconds());
                            }
                        } catch (ExecutionException err) {
                            log.atError().setCause(err.getCause()).log();
                        } catch (TimeoutException err) {
                            log.atError().setCause(err).log();
                        }
                    } else {
                        if (Objects.nonNull(engine)) {
                            log.info("about to stop debezium sink: {}", beanName);
                            //
                            // ~ well we want to stop gracefully the connector and wait for full termination in current thread
                            //
                            try {
                                StopWatch stopWatch = StopWatch.createStarted();
                                engine.close();
                                stopWatch.stop();
                                log.atInfo().log("stopped debezium sink: {} in {}", beanName, stopWatch);

                                //
                                // ~ well do we really need to keep the reference (most probably not, marker for GC)
                                //
                                engine = null;
                            } catch (IllegalStateException err) {
                                log.warn(err.getMessage(), err);
                            } catch (Exception err) {
                                ExceptionUtils.wrapAndThrow(err);
                            }
                        }
                    }
                }
            }
        });
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(engine)) {
            try {
                engine.close();
            } finally {
                executor.destroy();
            }
        }
    }
    private SettableFuture<Boolean> recreateEngine() {
        SettableFuture<Boolean> toReturn = SettableFuture.create();
        Sinks.One<Boolean> conntectorStarted = Sinks.one();
        Sinks.One<Boolean> taskStarted = Sinks.one();

        Mono.when(conntectorStarted.asMono(), taskStarted.asMono()).log(Loggers.getLogger(getClass())).doOnTerminate(new Runnable() {
            @Override
            public void run() {
                toReturn.set(true);
            }
        }).subscribe();

        StopWatch connectorStopWatch = StopWatch.createStarted();
        StopWatch taskStopWatch = StopWatch.createStarted();

        Duration initialInterval = props.APP_BACKOFF_RETRY_FIRST.get();
        Duration maxInterval = props.APP_BACKOFF_RETRY_MAX.get();
        int maxRetries = props.APP_BACKOFF_RETRY_NUM.get();
        IntervalFunction backoff = IntervalFunction.ofExponentialBackoff(initialInterval, IntervalFunction.DEFAULT_MULTIPLIER, maxInterval);
        RetryConfig retryConfig = RetryConfig.custom().maxAttempts(maxRetries).ignoreExceptions(InterruptedException.class).intervalFunction(backoff).build();

        engine = (AsyncEmbeddedEngine<ChangeEvent<SourceRecord, SourceRecord>>) new ConvertingAsyncEngineBuilderFactory()
                .builder(KeyValueChangeEventFormat.of(Connect.class, Connect.class))
                .using(dictionary)
                .notifying(new ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>>() {
                    @Override
                    public void handleBatch(
                            List<ChangeEvent<SourceRecord, SourceRecord>> records,
                            RecordCommitter<ChangeEvent<SourceRecord, SourceRecord>> committer) throws InterruptedException {
                        try {
                            //
                            // ~ well we have to wrap into retry block because hosted MQ may have temporal connectivity issues as it turns out
                            //
                            retryRegistry.retry(DebeziumChannel.class.getSimpleName(), retryConfig).executeCallable(new Callable<>() {
                                @Override
                                public Object call() throws Exception {
                                    replicator.handleBatch(records, committer);
                                    return new Object();
                                }
                            });
                        } catch (InterruptedException err) {
                            throw err;
                        } catch (DebeziumException err) {
                            throw err;
                        } catch (Exception err) {
                            throw new DebeziumException(err);
                        } finally {
                            try {
                                //
                                // ~ well we should ignore exception really if any additional consumer throws exception
                                //
                                handlers.forEach(new Consumer<>() {
                                    @Override
                                    public void accept(Consumer<List<ChangeEvent<SourceRecord, SourceRecord>>> consumer) {
                                        consumer.accept(records);
                                    }
                                });
                            } catch (Throwable err) {
                                log.error(err.getMessage(), err);
                            }
                        }
                    }
                })
                .using(new OffsetCommitPolicy.AlwaysCommitOffsetPolicy())
                .using(new ConnectorCallback() {
                    @Override
                    public void connectorStarted() {
                        EmitResult emitResult = conntectorStarted.tryEmitValue(true);
                        connectorStopWatch.stop();
                        if (emitResult.isSuccess()) {
                            log.info("debezium connector has started in {}", connectorStopWatch);
                        }
                    }
                    @Override
                    public void taskStarted() {
                        EmitResult emitResult = taskStarted.tryEmitValue(true);
                        taskStopWatch.stop();
                        if (emitResult.isSuccess()) {
                            log.info("debezium task has started in {}", taskStopWatch);
                        }
                    }
                }).using(new CompletionCallback() {
                    @Override
                    public void handle(boolean success, String message, Throwable error) {
                        try {
                            log.atLevel(success ? Level.INFO : Level.ERROR).log("success: {}, msg: {}", success, message);
                        } finally {
                            if (BooleanUtils.isFalse(toReturn.isDone())) {
                                if (Objects.isNull(error)) {
                                    toReturn.set(success);
                                } else {
                                    toReturn.setException(error);
                                }
                            }
                        }
                    }
                }).build();
        return toReturn;
    }
}
