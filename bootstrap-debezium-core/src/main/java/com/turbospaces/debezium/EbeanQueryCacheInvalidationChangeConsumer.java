package com.turbospaces.debezium;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.source.SourceRecord;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Sets;
import com.turbospaces.ebean.EbeanJpaManager;

import io.debezium.connector.AbstractSourceInfo;
import io.debezium.data.Envelope;
import io.debezium.engine.ChangeEvent;
import io.ebean.plugin.BeanType;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EbeanQueryCacheInvalidationChangeConsumer implements Consumer<List<ChangeEvent<SourceRecord, SourceRecord>>> {
    private final IsDictionaryCache isDictionaryCache;
    private final LoadingCache<BeanType<?>, Set<BeanType<?>>> dependent;

    public EbeanQueryCacheInvalidationChangeConsumer(IsDictionaryCache isDictionaryCache, EbeanJpaManager ebean) {
        this.isDictionaryCache = Objects.requireNonNull(isDictionaryCache);
        //
        // ~ resolve all dependent tables for type (globally for any 1-to-1 / 1-to-many / many-to-many where this key is part of mapping)
        //
        this.dependent = CacheBuilder.newBuilder().build(new CacheLoader<>() {
            @Override
            public Set<BeanType<?>> load(BeanType<?> key) throws Exception {
                return ebean.dependentQueryCacheTables(key);
            }
        });
    }
    @Override
    public void accept(List<ChangeEvent<SourceRecord, SourceRecord>> list) {
        //
        // ~ well since the batch can be large and include all updated rows, we first need to capture unique dictionaries
        //
        Set<BeanType<?>> entities = Sets.newHashSet();
        for (ChangeEvent<SourceRecord, SourceRecord> event : list) {
            SourceRecord sourceRecord = event.value();
            Struct struct = (Struct) sourceRecord.value();
            if (Objects.nonNull(struct.schema().field(Envelope.FieldName.SOURCE))) {
                Struct source = (Struct) struct.get(Envelope.FieldName.SOURCE);
                String schema = source.get(AbstractSourceInfo.SCHEMA_NAME_KEY).toString();
                String table = source.get(AbstractSourceInfo.TABLE_NAME_KEY).toString();

                if (Objects.nonNull(source)) {
                    Tuple2<Boolean, BeanType<?>> tuple = isDictionaryCache.getUnchecked(Tuple.of(schema, table));
                    if (tuple._1()) {
                        BeanType<?> beanType = tuple._2();
                        entities.add(beanType);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(entities)) {
            //
            // ~ first merge everything into one set (might be collisions still)
            //
            Set<BeanType<?>> toCleanUp = Sets.newHashSet();
            for (BeanType<?> beanType : entities) {
                toCleanUp.addAll(dependent.getUnchecked(beanType));
            }

            //
            // ~ then for each unique dictionary we simply invalidate 2level cache via API
            //
            toCleanUp.forEach(new Consumer<>() {
                @Override
                public void accept(BeanType<?> beanType) {
                    try {
                        log.info("about to clear query cache for: {}", beanType);
                        beanType.clearQueryCache();
                    } catch (Exception err) {
                        log.error(err.getMessage(), err);
                    }
                }
            });
        }
    }
}
