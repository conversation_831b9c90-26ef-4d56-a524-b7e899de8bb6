package com.turbospaces.debezium;

import java.util.Collections;
import java.util.Objects;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.turbospaces.annotations.Dictionary;
import com.turbospaces.ebean.EbeanJpaManager;

import io.ebean.plugin.BeanType;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import jakarta.persistence.Table;
import lombok.experimental.Delegate;

public class IsDictionaryCache implements LoadingCache<Tuple2<String, String>, Tuple2<Boolean, BeanType<?>>> {
    @Delegate
    private final LoadingCache<Tuple2<String, String>, Tuple2<Boolean, BeanType<?>>> cache;

    public IsDictionaryCache(EbeanJpaManager ebean, MeterRegistry meterRegistry) {
        this.cache = CacheBuilder.newBuilder().build(new CacheLoader<>() {
            @Override
            public Tuple2<Boolean, BeanType<?>> load(Tuple2<String, String> key) throws Exception {
                Tuple2<Boolean, BeanType<?>> toReturn = Tuple.of(Boolean.FALSE, null);
                String schema = key._1();
                String table = key._2();

                for (Class<?> it : ebean.config().classes()) {
                    Table annotation = it.getAnnotation(Table.class);
                    if (Objects.nonNull(annotation)) {
                        if (annotation.schema().equals(schema) && annotation.name().equals(table)) {
                            BeanType<?> beanType = ebean.beanType(it);
                            if (Objects.nonNull(beanType)) {
                                toReturn = Tuple.of(Objects.nonNull(it.getAnnotation(Dictionary.class)), beanType);
                            }
                        }
                    }
                }

                return toReturn;
            }
        });

        new GuavaCacheMetrics<>(cache, "is-dictionary-ebean", Collections.emptyList()).bindTo(meterRegistry);
    }
}
