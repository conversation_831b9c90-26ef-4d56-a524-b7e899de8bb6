package com.turbospaces.debezium;

import com.turbospaces.annotations.Dictionary;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Dictionary
@Entity
@Table(name = "condition", schema = "core")
@Getter
@Setter
public class Condition {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    @Column(nullable = false)
    private String value;

    @ManyToOne(optional = false)
    private RoutingRule rule;
}
