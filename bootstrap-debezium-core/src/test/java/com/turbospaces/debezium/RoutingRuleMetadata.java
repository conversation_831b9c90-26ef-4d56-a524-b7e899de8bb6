package com.turbospaces.debezium;

import com.turbospaces.annotations.Dictionary;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Dictionary
@Entity
@Table(name = "routing_rule_metadata", schema = "core")
@Getter
@Setter
public class RoutingRuleMetadata {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    @OneToOne(optional = false)
    @JoinColumn(name = "rule_id", referencedColumnName = "id", nullable = false)
    private RoutingRule rule;

    boolean active;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;
}
