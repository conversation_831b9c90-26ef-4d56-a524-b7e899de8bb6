package com.turbospaces.debezium;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import io.ebean.annotation.Cache;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "account_balance", schema = "core")
@Cache
@Getter
@Setter
public class AccountBalance {
    @EmbeddedId
    private AccountBalanceId pk;

    @Column
    private BigDecimal amount;

    @ManyToOne(optional = false)
    private Account account;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountBalance() {}
    public AccountBalance(Account player, String currency) {
        AccountBalanceId k = new AccountBalanceId(player, currency);
        setPk(k);
        setAccount(player);
        setAmount(BigDecimal.ZERO);
    }
    public BigDecimal add(BigDecimal delta) {
        BigDecimal newAmount = getAmount().add(delta);
        setAmount(newAmount);
        return newAmount;
    }
    public BigDecimal subtract(BigDecimal delta) {
        BigDecimal newAmount = getAmount().subtract(delta);
        setAmount(newAmount);
        return newAmount;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getPk()).build();
    }
    @Override
    public boolean equals(Object obj) {
        AccountBalance other = (AccountBalance) obj;
        return new EqualsBuilder().append(getPk(), other.getPk()).isEquals();
    }
}
