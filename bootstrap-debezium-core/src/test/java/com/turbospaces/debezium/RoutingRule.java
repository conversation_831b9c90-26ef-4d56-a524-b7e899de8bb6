package com.turbospaces.debezium;

import io.ebean.annotation.Cache;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Entity
@Table(name = "routing_rule", schema = "core")
@Cache(enableQueryCache = true)
@Getter
@Setter
public class RoutingRule {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    @OneToOne(mappedBy = "rule", cascade = CascadeType.ALL)
    private RoutingRuleMetadata metadata;

    @ManyToMany(cascade = CascadeType.ALL)
    @JoinTable(
            schema = "core",
            name = "routing_rule_providers",
            joinColumns = {@JoinColumn(name = "rule_id")},
            inverseJoinColumns = {@JoinColumn(name = "provider_id")})
    private List<Provider> providers;

    @OneToMany(cascade = CascadeType.ALL)
    private Set<Condition> conditions;

}
