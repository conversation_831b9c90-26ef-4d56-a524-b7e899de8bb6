package com.turbospaces.debezium;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyStore;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.runtime.WorkerConfig;
import org.apache.kafka.connect.runtime.distributed.DistributedConfig;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.KafkaOffsetBackingStore;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.test.EmbeddedKafkaZKBroker;
import org.testcontainers.containers.PostgreSQLContainer;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.FlywayUberRunner;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import io.debezium.config.CommonConnectorConfig;
import io.debezium.connector.AbstractSourceInfo;
import io.debezium.connector.postgresql.PostgresConnector;
import io.debezium.connector.postgresql.PostgresConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnectorConfig.AutoCreateMode;
import io.debezium.connector.postgresql.PostgresConnectorConfig.LogicalDecoder;
import io.debezium.connector.postgresql.PostgresConnectorConfig.SnapshotMode;
import io.debezium.data.Envelope;
import io.debezium.data.Envelope.Operation;
import io.debezium.embedded.Connect;
import io.debezium.embedded.EmbeddedEngineConfig;
import io.debezium.engine.ChangeEvent;
import io.debezium.engine.DebeziumEngine;
import io.debezium.engine.DebeziumEngine.ChangeConsumer;
import io.debezium.engine.DebeziumEngine.RecordCommitter;
import io.debezium.relational.RelationalDatabaseConnectorConfig;
import io.debezium.relational.history.SchemaHistory;
import io.ebean.platform.postgres.Postgres9Platform;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import jakarta.persistence.Table;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DebeziumTest {
    public static final String TBL_ACCOUNT = Account.class.getAnnotation(Table.class).name();
    public static final String TBL_ACCOUNT_BALANCE = AccountBalance.class.getAnnotation(Table.class).name();
    public static final String SCHEMA_CORE = "core";

    @Test
    public void works() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();

        try (PostgreSQLContainer<?> pg = new PostgreSQLContainer<>("postgres:14")) {
            pg.setCommand("postgres", "-c", "wal_level=logical");
            pg.start();

            var pgUri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                    PostgresqlServiceInfo.POSTGRES_SCHEME,
                    pg.getHost(),
                    pg.getFirstMappedPort(),
                    pg.getUsername(),
                    pg.getPassword(), pg.getDatabaseName()).getUriString();
            var pgJdbcUrl = pg.getJdbcUrl();

            try {
                int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();

                ApplicationConfig cfg = ApplicationConfig.mock();
                ApplicationProperties props = new ApplicationProperties(cfg.factory());

                Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
                bootstrap.addUps(new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri, pgJdbcUrl));
                bootstrap.withKafka(brokerPort);

                ConfigurableApplicationContext applicationContext = bootstrap.run();

                JpaManager jpaManager = applicationContext.getBean(JpaManager.class);
                jpaManager.execute(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 1; i <= Byte.MAX_VALUE; i++) {
                            Account acc = new Account();
                            acc.setId(i);
                            acc.setUsername(PlatformUtil.randomUUID().toString());
                            acc.setBirthDate(LocalDate.now());
                            jpaManager.save(acc);

                            AccountBalance balance1 = new AccountBalance(acc, "USD");
                            balance1.setAmount(BigDecimal.ONE);
                            jpaManager.save(balance1);

                            AccountBalance balance2 = new AccountBalance(acc, "EUR");
                            balance2.setAmount(BigDecimal.TEN);
                            jpaManager.save(balance2);
                        }
                    }
                });

                //
                // ~ start
                //
                for (SmartLifecycle channel : applicationContext.getBeansOfType(SmartLifecycle.class).values()) {
                    if (BooleanUtils.isFalse(channel.isRunning())) {
                        channel.start();
                    }
                }

                //
                // ~ await for publication to arrive
                //
                Awaitility.await().until(new Callable<>() {
                    @Override
                    public Boolean call() throws Exception {
                        Long own = jpaManager.sqlQuery("select pubowner from pg_catalog.pg_publication").mapToScalar(Long.class).findOne();
                        if (Objects.nonNull(own)) {
                            log.info("pubowner: {}", own);
                        }
                        return Objects.nonNull(own);
                    }
                });

                BatchConsumerSingleRecord consumer1 = applicationContext.getBean(BatchConsumerSingleRecord.class);
                consumer1.genData1();
                Assertions.assertTrue(consumer1.await());

                bootstrap.shutdown();
            } finally {
                pg.stop();
            }
        } finally {
            broker.destroy();
        }
    }

    public static class BatchConsumerSingleRecord implements ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> {
        private final CountDownLatch latch = new CountDownLatch(4); // account + 3 balances
        private final Long ID = System.currentTimeMillis();
        private final JpaManager jpaManager;
        private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

        @Autowired
        public BatchConsumerSingleRecord(EbeanFactoryBean factory, KafkaTemplate<byte[], byte[]> kafkaTemplate) throws Exception {
            this.kafkaTemplate = kafkaTemplate;
            this.jpaManager = factory.getObject();
        }
        public void genData1() throws Exception {
            Account acc = new Account();
            acc.setId(ID);
            acc.setUsername(PlatformUtil.randomUUID().toString());
            acc.setBirthDate(LocalDate.now());
            jpaManager.save(acc);

            jpaManager.execute(new Runnable() {
                @Override
                public void run() {
                    AccountBalance balance1 = new AccountBalance(acc, "USD");
                    balance1.setAmount(BigDecimal.ONE);
                    jpaManager.save(balance1);

                    AccountBalance balance2 = new AccountBalance(acc, "EUR");
                    balance2.setAmount(BigDecimal.ONE);
                    jpaManager.save(balance2);

                    AccountBalance balance3 = new AccountBalance(acc, "UAH");
                    balance3.setAmount(BigDecimal.ONE);
                    jpaManager.save(balance3);
                }
            });
        }
        public boolean await() throws InterruptedException {
            return latch.await(30, TimeUnit.SECONDS);
        }
        @Override
        public void handleBatch(
                List<ChangeEvent<SourceRecord, SourceRecord>> records,
                RecordCommitter<ChangeEvent<SourceRecord, SourceRecord>> committer) throws InterruptedException {
            CountDownLatch n = new CountDownLatch(records.size());

            for (ChangeEvent<SourceRecord, SourceRecord> event : records) {
                SourceRecord value = event.value();
                Struct envelope = (Struct) value.value();

                log.trace("publishing change: {} to: {}", envelope.toString(), event.destination());
                kafkaTemplate.send(new ProducerRecord<>(event.destination(), null, envelope.toString().getBytes()))
                        .whenComplete((result, ex) -> {
                            if (ex != null) {
                                log.error(ex.getMessage(), ex);
                            } else {
                                try {
                                    if (Objects.nonNull(envelope)) {
                                        Operation operation = Operation.forCode((String) envelope.get(Envelope.FieldName.OPERATION));

                                        if (ImmutableSet.of(Operation.CREATE, Operation.READ).contains(operation)) {
                                            Struct source = (Struct) envelope.get(Envelope.FieldName.SOURCE);
                                            String schema = source.get(AbstractSourceInfo.SCHEMA_NAME_KEY).toString();
                                            String table = source.get(AbstractSourceInfo.TABLE_NAME_KEY).toString();

                                            if (Objects.nonNull(source)) {
                                                Struct after = (Struct) envelope.get(Envelope.FieldName.AFTER);
                                                if (Objects.nonNull(after)) {
                                                    if (SCHEMA_CORE.equals(schema) && TBL_ACCOUNT.equals(table)) {
                                                        Long id = after.getInt64("id");

                                                        log.debug("schema: {}, table: {}, id: {}", schema, table, id);

                                                        if (ID.equals(id)) {
                                                            latch.countDown();
                                                        }
                                                    } else if (SCHEMA_CORE.equals(schema) && TBL_ACCOUNT_BALANCE.equals(table)) {
                                                        Long accountId = after.getInt64("account_id");
                                                        String currency = after.getString("currency");
                                                        BigDecimal amount = (BigDecimal) after.get("amount");

                                                        log.debug("schema: {}, table: {}, account_id: {}, currency: {}, amount: {}",
                                                                schema,
                                                                table,
                                                                accountId,
                                                                currency,
                                                                amount);

                                                        if (ID.equals(after.getInt64("account_id"))) {
                                                            latch.countDown();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } finally {
                                    n.countDown();
                                }
                            }
                        });
                committer.markProcessed(event);
            }

            Assertions.assertTrue(n.await(30, TimeUnit.SECONDS));
            committer.markBatchFinished();
        }
    }

    @Slf4j
    @Configuration
    public static class AppConfig {
        @Bean
        public KafkaTemplate<byte[], byte[]> kafkaTemplate(ApplicationProperties props, KeyStore keyStore, MeterRegistry meterRegistry, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            KafkaWithMetricsProducerFactory producerFactory = new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);

            return new KafkaTemplate<>(producerFactory);
        }
        @Bean
        public BatchConsumerSingleRecord batchConsumerSingleRecord(EbeanFactoryBean factory, KafkaTemplate<byte[], byte[]> kafkaTemplate) throws Exception {
            return new BatchConsumerSingleRecord(factory, kafkaTemplate);
        }
        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry, ApplicationConfig cfg) {
            PostgresqlServiceInfo si = UPSs.findRequiredScopedServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
            return new HikariDataSourceFactoryBean(props, meterRegistry, si);
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.setDatabasePlatform(new Postgres9Platform());
            cfg.addClass(Account.class);
            cfg.addClass(AccountBalance.class);
            cfg.addClass(AccountBalanceId.class);

            return cfg;
        }
        @Bean
        public EbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config) {
                @Override
                protected JpaManager createInstance() throws Exception {
                    EbeanJpaManager ebean = (EbeanJpaManager) super.createInstance();
                    FlywayUberRunner.run(ebean, SCHEMA_CORE);
                    return ebean;
                }
            };
        }
        @Bean
        public SmartLifecycle channel(ApplicationProperties props, DynamicCloud cloud, ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> consumer) {
            return new SmartLifecycle() {
                private final ExecutorService executor = Executors.newSingleThreadExecutor();
                private DebeziumEngine<ChangeEvent<SourceRecord, SourceRecord>> engine;
                private boolean running;

                @Override
                public boolean isAutoStartup() {
                    return false;
                }
                @Override
                public void start() {
                    PostgresqlServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
                    KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

                    String db = psi.getPath();
                    String schema = "core";
                    String tbl1 = schema + "." + TBL_ACCOUNT;
                    String tbl2 = schema + "." + TBL_ACCOUNT_BALANCE;

                    Map<String, String> map = Maps.newHashMap();

                    map.put(EmbeddedEngineConfig.ENGINE_NAME.name(), "default");
                    map.put(EmbeddedEngineConfig.CONNECTOR_CLASS.name(), PostgresConnector.class.getName());

                    map.put(CommonConnectorConfig.TOPIC_PREFIX.name(), "dev");
                    map.put(RelationalDatabaseConnectorConfig.HOSTNAME.name(), psi.getHost());
                    map.put(RelationalDatabaseConnectorConfig.PORT.name(), Integer.toString(psi.getPort()));
                    map.put(RelationalDatabaseConnectorConfig.USER.name(), psi.getUserName());
                    map.put(RelationalDatabaseConnectorConfig.PASSWORD.name(), psi.getPassword());
                    map.put(RelationalDatabaseConnectorConfig.DATABASE_NAME.name(), db);
                    map.put(RelationalDatabaseConnectorConfig.SCHEMA_INCLUDE_LIST.name(), SCHEMA_CORE);
                    map.put(RelationalDatabaseConnectorConfig.TABLE_INCLUDE_LIST.name(), Joiner.on(',').join(ImmutableList.of(tbl1, tbl2)));

                    map.put(PostgresConnectorConfig.PLUGIN_NAME.name(), LogicalDecoder.PGOUTPUT.getValue());
                    map.put(PostgresConnectorConfig.PUBLICATION_AUTOCREATE_MODE.name(), AutoCreateMode.ALL_TABLES.getValue());
                    map.put(PostgresConnectorConfig.SNAPSHOT_MODE.name(), SnapshotMode.INITIAL.getValue());

                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE.name(), KafkaOffsetBackingStore.class.getName());
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_TOPIC.name(), KafkaOffsetBackingStore.class.getName());
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_PARTITIONS.name(), Integer.toString(1));
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_REPLICATION_FACTOR.name(), Integer.toString(1));

                    map.put(DistributedConfig.OFFSET_STORAGE_TOPIC_CONFIG, "debezium-offset");
                    map.put(WorkerConfig.TOPIC_CREATION_ENABLE_CONFIG, Boolean.toString(true));
                    map.put(WorkerConfig.BOOTSTRAP_SERVERS_CONFIG, ksi.getBootstrapServers());

                    map.put(SchemaHistory.SKIP_UNPARSEABLE_DDL_STATEMENTS.name(), Boolean.toString(false));
                    map.put(SchemaHistory.STORE_ONLY_CAPTURED_TABLES_DDL.name(), Boolean.toString(false));

                    Properties toApply = new Properties();
                    toApply.putAll(map);

                    engine = DebeziumEngine.create(Connect.class).using(toApply).notifying(consumer).build();

                    executor.execute(engine);

                    this.running = true;
                }
                @Override
                public void stop() {
                    if (Objects.nonNull(engine)) {
                        try {
                            engine.close();
                        } catch (IOException err) {
                            log.error(err.getMessage(), err);
                        }
                    }
                    executor.shutdown();
                    this.running = false;
                }
                @Override
                public boolean isRunning() {
                    return running;
                }
            };
        }
    }
}
