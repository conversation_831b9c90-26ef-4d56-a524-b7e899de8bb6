package com.turbospaces.debezium;

import java.io.IOException;
import java.security.KeyStore;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.connect.data.SchemaAndValue;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.json.JsonConverter;
import org.apache.kafka.connect.json.JsonConverterConfig;
import org.apache.kafka.connect.runtime.WorkerConfig;
import org.apache.kafka.connect.runtime.distributed.DistributedConfig;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.ConverterConfig;
import org.apache.kafka.connect.storage.ConverterType;
import org.apache.kafka.connect.storage.KafkaOffsetBackingStore;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.support.SendResult;
import org.springframework.kafka.test.EmbeddedKafkaZKBroker;
import org.testcontainers.clickhouse.ClickHouseContainer;
import org.testcontainers.containers.PostgreSQLContainer;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.clickhouse.ClickhouseClient;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.FlywayUberRunner;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.ups.ClickhouseServiceInfo;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.debezium.config.CommonConnectorConfig;
import io.debezium.connector.AbstractSourceInfo;
import io.debezium.connector.postgresql.PostgresConnector;
import io.debezium.connector.postgresql.PostgresConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnectorConfig.AutoCreateMode;
import io.debezium.connector.postgresql.PostgresConnectorConfig.LogicalDecoder;
import io.debezium.connector.postgresql.PostgresConnectorConfig.SnapshotMode;
import io.debezium.data.Envelope;
import io.debezium.data.Envelope.Operation;
import io.debezium.embedded.Connect;
import io.debezium.embedded.EmbeddedEngineConfig;
import io.debezium.engine.ChangeEvent;
import io.debezium.engine.DebeziumEngine;
import io.debezium.engine.DebeziumEngine.ChangeConsumer;
import io.debezium.engine.DebeziumEngine.RecordCommitter;
import io.debezium.relational.RelationalDatabaseConnectorConfig;
import io.debezium.relational.history.SchemaHistory;
import io.ebean.platform.postgres.Postgres9Platform;
import io.ebean.plugin.BeanType;
import io.ebean.plugin.Property;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import jakarta.persistence.Table;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DebeziumClickhouseTest {
    public static final String USERNAME = "test";
    public static final String PASSWORD = "test";
    public static final String DB = "defaultdb";
    public static final String SCHEMA_CORE = "core";
    public static final String TBL_ACCOUNT = Account.class.getAnnotation(Table.class).name();
    public static final List<Class<?>> ENTITIES = List.of(Account.class, AccountBalance.class, AccountBalanceId.class);

    @Test
    public void works() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();

        try (ClickHouseContainer clickhouseContainer = new ClickHouseContainer("clickhouse/clickhouse-server")) {
            clickhouseContainer.withUsername(USERNAME);
            clickhouseContainer.withPassword(PASSWORD);
            clickhouseContainer.withDatabaseName(DB);
            clickhouseContainer.start();

            try (PostgreSQLContainer<?> pgContainer = new PostgreSQLContainer<>("postgres:14")) {
                pgContainer.withUsername(USERNAME);
                pgContainer.withPassword(PASSWORD);
                pgContainer.withDatabaseName(DB);
                pgContainer.setCommand("postgres", "-c", "wal_level=logical");
                pgContainer.start();

                var pgUri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                        PostgresqlServiceInfo.POSTGRES_SCHEME,
                        pgContainer.getHost(),
                        pgContainer.getFirstMappedPort(),
                        pgContainer.getUsername(),
                        pgContainer.getPassword(),
                        pgContainer.getDatabaseName()).getUriString();
                var clickhouseUri = UPSs.toUriInfo(UPSs.CLICKHOUSE,
                        ClickhouseServiceInfo.CLICKHOUSE_SCHEME,
                        clickhouseContainer.getHost(),
                        clickhouseContainer.getFirstMappedPort(),
                        clickhouseContainer.getUsername(),
                        clickhouseContainer.getPassword(),
                        clickhouseContainer.getDatabaseName(),
                        Map.of("clickhouse.jdbc.v1", Boolean.TRUE.toString())).getUriString();

                try {
                    int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();

                    ApplicationConfig cfg = ApplicationConfig.mock();
                    ApplicationProperties props = new ApplicationProperties(cfg.factory());

                    Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
                    bootstrap.addUps(new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri));
                    bootstrap.addUps(new ClickhouseServiceInfo(UPSs.CLICKHOUSE, clickhouseUri));
                    bootstrap.withKafka(brokerPort);

                    ConfigurableApplicationContext applicationContext = bootstrap.run();

                    JpaManager pg = applicationContext.getBean(PGEbeanFactoryBean.class).getObject();
                    pg.execute(new Runnable() {
                        @Override
                        public void run() {
                            for (int i = 1; i <= Byte.MAX_VALUE; i++) {
                                Account acc = new Account();
                                acc.setId(i);
                                acc.setUsername(PlatformUtil.randomUUID().toString());
                                acc.setBirthDate(LocalDate.now());
                                pg.save(acc);
                            }
                        }
                    });

                    //
                    // ~ start
                    //
                    for (SmartLifecycle channel : applicationContext.getBeansOfType(SmartLifecycle.class).values()) {
                        if (BooleanUtils.isFalse(channel.isRunning())) {
                            channel.start();
                        }
                    }

                    //
                    // ~ await for publication to arrive
                    //
                    Awaitility.await().until(new Callable<>() {
                        @Override
                        public Boolean call() throws Exception {
                            Long own = pg.sqlQuery("select pubowner from pg_catalog.pg_publication").mapToScalar(Long.class).findOne();
                            if (Objects.nonNull(own)) {
                                log.info("pubowner: {}", own);
                            }
                            return Objects.nonNull(own);
                        }
                    });

                    BatchConsumer consumer = applicationContext.getBean(BatchConsumer.class);
                    consumer.genData();
                    Assertions.assertTrue(consumer.await());

                    bootstrap.shutdown();
                } finally {
                    pgContainer.stop();
                }
            } finally {
                broker.destroy();
            }
        }
    }

    @Slf4j
    public static class BatchConsumer implements ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> {
        private final int count = ApiFactory.RANDOM.nextInt(64, 128);
        private final JsonConverter jsonKey = new JsonConverter();
        private final JsonConverter jsonValue = new JsonConverter();
        private final Set<Long> ids = Sets.newConcurrentHashSet();
        private final CountDownLatch latch = new CountDownLatch(count);
        private final JpaManager pg;
        private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

        @Autowired
        public BatchConsumer(PGEbeanFactoryBean pg, KafkaTemplate<byte[], byte[]> kafkaTemplate) throws Exception {
            this.pg = pg.getObject();
            this.kafkaTemplate = Objects.requireNonNull(kafkaTemplate);

            jsonKey.configure(ImmutableMap.of(ConverterConfig.TYPE_CONFIG, ConverterType.KEY.name().toLowerCase(), JsonConverterConfig.SCHEMAS_ENABLE_CONFIG, Boolean.TRUE.toString()));
            jsonValue.configure(ImmutableMap.of(ConverterConfig.TYPE_CONFIG, ConverterType.VALUE.name().toLowerCase(), JsonConverterConfig.SCHEMAS_ENABLE_CONFIG, Boolean.FALSE.toString()));
        }
        public void genData() throws Exception {
            for (int i = 0; i < count; i++) {
                long id = ApiFactory.RANDOM.nextLong();
                Account acc = new Account();
                acc.setId(id);
                acc.setBirthDate(PlatformUtil.toLocalUTCDate().minusYears(21));
                acc.setUsername(PlatformUtil.randomUUID().toString());
                acc.setFirstName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
                acc.setLastName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
                pg.save(acc);
                acc.setBirthDate(LocalDate.now());
                pg.save(acc);
                // pg.delete(acc);
                ids.add(id);
            }
        }
        public boolean await() throws InterruptedException {
            return latch.await(30, TimeUnit.SECONDS);
        }
        @Override
        public void handleBatch(
                List<ChangeEvent<SourceRecord, SourceRecord>> records,
                RecordCommitter<ChangeEvent<SourceRecord, SourceRecord>> committer) throws InterruptedException {
            CountDownLatch n = new CountDownLatch(records.size());

            for (ChangeEvent<SourceRecord, SourceRecord> event : records) {
                SourceRecord sourceRecord = event.value();

                byte[] kbytes = jsonKey.fromConnectData(event.destination(), sourceRecord.keySchema(), sourceRecord.key());
                byte[] vbytes = jsonValue.fromConnectData(event.destination(), sourceRecord.valueSchema(), sourceRecord.value());

                log.info(event.toString());
                log.info("publishing change: key={}, value={}", new String(kbytes), Objects.nonNull(vbytes) ? new String(vbytes) : null);

                kafkaTemplate.send(new ProducerRecord<>(event.destination(), kbytes, vbytes))
                        .whenComplete(new BiConsumer<SendResult<byte[], byte[]>, Throwable>() {
                            @Override
                            public void accept(SendResult<byte[], byte[]> result, Throwable ex) {
                                if (ex != null) {
                                    log.error(ex.getMessage(), ex);
                                } else {
                                    try {
                                        if (Objects.nonNull(sourceRecord.value())) {
                                            SchemaAndValue connectData = jsonKey.toConnectData(event.destination(), kbytes);
                                            Struct kstruct = (Struct) connectData.value();

                                            Struct vstruct = (Struct) sourceRecord.value();
                                            Operation operation = Operation.forCode((String) vstruct.get(Envelope.FieldName.OPERATION));

                                            if (ImmutableSet.of(Operation.CREATE).contains(operation)) {
                                                Struct source = (Struct) vstruct.get(Envelope.FieldName.SOURCE);
                                                String schema = source.get(AbstractSourceInfo.SCHEMA_NAME_KEY).toString();
                                                String table = source.get(AbstractSourceInfo.TABLE_NAME_KEY).toString();

                                                if (Objects.nonNull(source)) {
                                                    Struct after = (Struct) vstruct.get(Envelope.FieldName.AFTER);

                                                    if (Objects.nonNull(after)) {
                                                        for (Class<?> it : pg.config().classes()) {
                                                            Table annotation = it.getAnnotation(Table.class);
                                                            if (Objects.nonNull(annotation)) {
                                                                if (annotation.schema().equals(schema) && annotation.name().equals(table)) {
                                                                    BeanType<?> beanType = pg.beanType(it);
                                                                    Property idProperty = beanType.idProperty();
                                                                    Long kid = kstruct.getInt64(idProperty.name());
                                                                    Long vid = after.getInt64(idProperty.name());

                                                                    Assertions.assertEquals(kid, vid);

                                                                    if (ids.remove(vid)) {
                                                                        latch.countDown();

                                                                        Object object = pg.find(it, vid);
                                                                        pg.beanState(object).resetForInsert();
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } catch (Exception err) {
                                        log.error(err.getMessage(), err);
                                    } finally {
                                        n.countDown();
                                    }
                                }
                            }
                        });
                committer.markProcessed(event);
            }

            Assertions.assertTrue(n.await(30, TimeUnit.SECONDS));
            committer.markBatchFinished();
        }
    }

    public static class PGHikariDataSourceFactoryBean extends HikariDataSourceFactoryBean {
        public PGHikariDataSourceFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, PostgresqlServiceInfo si) {
            super(props, meterRegistry, si);
        }
    }

    public static class PGEbeanFactoryBean extends EbeanFactoryBean {
        public PGEbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            super(props, meterRegistry, tracer, config);
        }
    }

    @Slf4j
    @Configuration
    public static class AppConfig {
        @Bean
        public KafkaTemplate<byte[], byte[]> kafkaTemplate(ApplicationProperties props, KeyStore keyStore, MeterRegistry meterRegistry, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            KafkaWithMetricsProducerFactory producerFactory = new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);

            return new KafkaTemplate<>(producerFactory);
        }
        @Bean
        public BatchConsumer batchConsumerSingleRecord(PGEbeanFactoryBean pg, KafkaTemplate<byte[], byte[]> kafkaTemplate) throws Exception {
            return new BatchConsumer(pg, kafkaTemplate);
        }
        @Bean
        public PGHikariDataSourceFactoryBean postgresDatasource(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                ApplicationConfig cfg) {
            PostgresqlServiceInfo si = UPSs.findRequiredScopedServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
            return new PGHikariDataSourceFactoryBean(props, meterRegistry, si);
        }
        @Bean
        public PGEbeanFactoryBean pgEbean(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                PGHikariDataSourceFactoryBean factory,
                Tracer tracer) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.setDatabasePlatform(new Postgres9Platform());
            ENTITIES.forEach(cfg::addClass);

            return new PGEbeanFactoryBean(props, meterRegistry, tracer, cfg) {
                @Override
                protected JpaManager createInstance() throws Exception {
                    EbeanJpaManager ebean = (EbeanJpaManager) super.createInstance();
                    FlywayUberRunner.run(ebean, SCHEMA_CORE);
                    return ebean;
                }
            };
        }
        @Bean
        public ClickhouseClient clickhouseClient(ApplicationProperties props, DynamicCloud cloud, RetryRegistry retryRegistry) {
            return new ClickhouseClient(props, cloud, retryRegistry, ENTITIES);
        }
        @Bean
        public SmartLifecycle channel(ApplicationProperties props, DynamicCloud cloud, ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> consumer) {
            return new SmartLifecycle() {
                private final ExecutorService executor = Executors.newSingleThreadExecutor();
                private DebeziumEngine<ChangeEvent<SourceRecord, SourceRecord>> engine;
                private boolean running;

                @Override
                public boolean isAutoStartup() {
                    return false;
                }
                @Override
                public void start() {
                    PostgresqlServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
                    KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

                    String db = psi.getPath();
                    String schema = "core";
                    String tbl1 = schema + "." + TBL_ACCOUNT;

                    Map<String, String> map = Maps.newHashMap();

                    map.put(EmbeddedEngineConfig.ENGINE_NAME.name(), "default");
                    map.put(EmbeddedEngineConfig.CONNECTOR_CLASS.name(), PostgresConnector.class.getName());

                    map.put(CommonConnectorConfig.TOPIC_PREFIX.name(), "dev");
                    map.put(RelationalDatabaseConnectorConfig.HOSTNAME.name(), psi.getHost());
                    map.put(RelationalDatabaseConnectorConfig.PORT.name(), Integer.toString(psi.getPort()));
                    map.put(RelationalDatabaseConnectorConfig.USER.name(), psi.getUserName());
                    map.put(RelationalDatabaseConnectorConfig.PASSWORD.name(), psi.getPassword());
                    map.put(RelationalDatabaseConnectorConfig.DATABASE_NAME.name(), db);
                    map.put(RelationalDatabaseConnectorConfig.SCHEMA_INCLUDE_LIST.name(), SCHEMA_CORE);
                    map.put(RelationalDatabaseConnectorConfig.TABLE_INCLUDE_LIST.name(), Joiner.on(',').join(ImmutableList.of(tbl1)));

                    map.put(PostgresConnectorConfig.PLUGIN_NAME.name(), LogicalDecoder.PGOUTPUT.getValue());
                    map.put(PostgresConnectorConfig.PUBLICATION_AUTOCREATE_MODE.name(), AutoCreateMode.ALL_TABLES.getValue());
                    map.put(PostgresConnectorConfig.SNAPSHOT_MODE.name(), SnapshotMode.NO_DATA.getValue());

                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE.name(), KafkaOffsetBackingStore.class.getName());
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_TOPIC.name(), KafkaOffsetBackingStore.class.getName());
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_PARTITIONS.name(), Integer.toString(1));
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_REPLICATION_FACTOR.name(), Integer.toString(1));

                    map.put(DistributedConfig.OFFSET_STORAGE_TOPIC_CONFIG, "debezium-offset");
                    map.put(WorkerConfig.TOPIC_CREATION_ENABLE_CONFIG, Boolean.toString(true));
                    map.put(WorkerConfig.BOOTSTRAP_SERVERS_CONFIG, ksi.getBootstrapServers());

                    map.put(SchemaHistory.SKIP_UNPARSEABLE_DDL_STATEMENTS.name(), Boolean.toString(false));
                    map.put(SchemaHistory.STORE_ONLY_CAPTURED_TABLES_DDL.name(), Boolean.toString(false));

                    Properties toApply = new Properties();
                    toApply.putAll(map);

                    engine = DebeziumEngine.create(Connect.class).using(toApply).notifying(consumer).build();

                    executor.execute(engine);

                    this.running = true;
                }
                @Override
                public void stop() {
                    log.info("about to stop debezium ...");
                    if (Objects.nonNull(engine)) {
                        try {
                            engine.close();
                        } catch (IOException err) {
                            log.error(err.getMessage(), err);
                        }
                    }
                    PlatformUtil.shutdownExecutor(executor, Duration.ofSeconds(30));
                    this.running = false;
                }
                @Override
                public boolean isRunning() {
                    return running;
                }
            };
        }
    }
}
