package com.turbospaces.debezium;

import java.time.LocalDate;
import java.util.Date;
import java.util.Set;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import io.ebean.annotation.Cache;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "account", schema = "core")
@Cache(enableQueryCache = true)
@Getter
@Setter
public class Account {
    @Id
    private long id;

    @Column(unique = true)
    private String username;

    private String firstName;
    private String lastName;
    private LocalDate birthDate;

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL)
    private Set<AccountBalance> balances;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Version
    private int version;

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getUsername()).build();
    }
    @Override
    public boolean equals(Object obj) {
        Account other = (Account) obj;
        return new EqualsBuilder().append(getUsername(), other.getUsername()).isEquals();
    }
}
