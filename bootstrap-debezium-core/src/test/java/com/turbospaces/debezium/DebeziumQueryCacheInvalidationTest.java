package com.turbospaces.debezium;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.kafka.connect.runtime.WorkerConfig;
import org.apache.kafka.connect.runtime.distributed.DistributedConfig;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.KafkaOffsetBackingStore;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.test.EmbeddedKafkaZKBroker;
import org.testcontainers.containers.PostgreSQLContainer;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.FlywayUberRunner;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import io.debezium.config.CommonConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnector;
import io.debezium.connector.postgresql.PostgresConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnectorConfig.AutoCreateMode;
import io.debezium.connector.postgresql.PostgresConnectorConfig.LogicalDecoder;
import io.debezium.connector.postgresql.PostgresConnectorConfig.SnapshotMode;
import io.debezium.embedded.Connect;
import io.debezium.embedded.EmbeddedEngineConfig;
import io.debezium.engine.ChangeEvent;
import io.debezium.engine.DebeziumEngine;
import io.debezium.engine.DebeziumEngine.ChangeConsumer;
import io.debezium.engine.DebeziumEngine.RecordCommitter;
import io.debezium.relational.RelationalDatabaseConnectorConfig;
import io.debezium.relational.history.SchemaHistory;
import io.ebean.platform.postgres.Postgres9Platform;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import jakarta.persistence.Table;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DebeziumQueryCacheInvalidationTest {

    public static final String TBL_PROVIDER = Provider.class.getAnnotation(Table.class).name();
    public static final String TBL_ROUTING_RULE = RoutingRule.class.getAnnotation(Table.class).name();
    public static final String TBL_ROUTING_RULE_METADATA = RoutingRuleMetadata.class.getAnnotation(Table.class).name();
    public static final String TBL_CONDITION = Condition.class.getAnnotation(Table.class).name();
    public static final String SCHEMA_CORE = "core";

    @Test
    void oneToOne() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();

        try (PostgreSQLContainer<?> pg = new PostgreSQLContainer<>("postgres:14")) {
            pg.setCommand("postgres", "-c", "wal_level=logical");
            pg.start();

            var pgUri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                    PostgresqlServiceInfo.POSTGRES_SCHEME,
                    pg.getHost(),
                    pg.getFirstMappedPort(),
                    pg.getUsername(),
                    pg.getPassword(), pg.getDatabaseName()).getUriString();
            var pgJdbcUrl = pg.getJdbcUrl();

            try {
                int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();

                ApplicationConfig cfg = ApplicationConfig.mock();
                ApplicationProperties props = new ApplicationProperties(cfg.factory());

                Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
                bootstrap.addUps(new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri, pgJdbcUrl));
                bootstrap.withKafka(brokerPort);

                ConfigurableApplicationContext applicationContext = bootstrap.run();

                JpaManager jpaManager = applicationContext.getBean(JpaManager.class);
                jpaManager.execute(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 1; i <= Byte.MAX_VALUE; i++) {
                            var metadata = new RoutingRuleMetadata();
                            metadata.setActive(true);
                            var rule = new RoutingRule();
                            rule.setMetadata(metadata);
                            metadata.setRule(rule);
                            jpaManager.saveAll(rule, metadata);
                        }
                    }
                });

                //
                // ~ start
                //
                for (SmartLifecycle channel : applicationContext.getBeansOfType(SmartLifecycle.class).values()) {
                    if (BooleanUtils.isFalse(channel.isRunning())) {
                        channel.start();
                    }
                }

                //
                // ~ await for publication to arrive
                //
                Awaitility.await().until(new Callable<>() {
                    @Override
                    public Boolean call() throws Exception {
                        Long own = jpaManager.sqlQuery("select pubowner from pg_catalog.pg_publication").mapToScalar(Long.class).findOne();
                        if (Objects.nonNull(own)) {
                            log.info("pubowner: {}", own);
                        }
                        return Objects.nonNull(own);
                    }
                });

                var ruleQueryCache = jpaManager.cacheManager().queryCache(RoutingRule.class);
                ruleQueryCache.statistics(true);
                assertEquals(0, ruleQueryCache.statistics(false).getClearCount());

                var consumer = applicationContext.getBean(BatchConsumerSingleRecord.class);
                consumer.deactivateRule();
                Assertions.assertTrue(consumer.await());

                assertEquals(1, ruleQueryCache.statistics(false).getClearCount());

                bootstrap.shutdown();
            } finally {
                pg.stop();
            }
        } finally {
            broker.destroy();
        }
    }

    @Test
    void oneToMany() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();

        try (PostgreSQLContainer<?> pg = new PostgreSQLContainer<>("postgres:14")) {
            pg.setCommand("postgres", "-c", "wal_level=logical");
            pg.start();

            var pgUri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                    PostgresqlServiceInfo.POSTGRES_SCHEME,
                    pg.getHost(),
                    pg.getFirstMappedPort(),
                    pg.getUsername(),
                    pg.getPassword(), pg.getDatabaseName()).getUriString();
            var pgJdbcUrl = pg.getJdbcUrl();

            try {
                int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();

                ApplicationConfig cfg = ApplicationConfig.mock();
                ApplicationProperties props = new ApplicationProperties(cfg.factory());

                Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
                bootstrap.addUps(new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri, pgJdbcUrl));
                bootstrap.withKafka(brokerPort);

                ConfigurableApplicationContext applicationContext = bootstrap.run();

                JpaManager jpaManager = applicationContext.getBean(JpaManager.class);
                jpaManager.execute(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 1; i <= Byte.MAX_VALUE; i++) {
                            var condition = new Condition();
                            condition.setValue("card.brand == 'VISA'");

                            RoutingRule rule = new RoutingRule();
                            rule.setConditions(Set.of(condition));

                            jpaManager.saveAll(rule, condition);
                        }
                    }
                });

                //
                // ~ start
                //
                for (SmartLifecycle channel : applicationContext.getBeansOfType(SmartLifecycle.class).values()) {
                    if (BooleanUtils.isFalse(channel.isRunning())) {
                        channel.start();
                    }
                }

                //
                // ~ await for publication to arrive
                //
                Awaitility.await().until(new Callable<>() {
                    @Override
                    public Boolean call() throws Exception {
                        Long own = jpaManager.sqlQuery("select pubowner from pg_catalog.pg_publication").mapToScalar(Long.class).findOne();
                        if (Objects.nonNull(own)) {
                            log.info("pubowner: {}", own);
                        }
                        return Objects.nonNull(own);
                    }
                });

                var ruleQueryCache = jpaManager.cacheManager().queryCache(RoutingRule.class);
                ruleQueryCache.statistics(true);
                assertEquals(0, ruleQueryCache.statistics(false).getClearCount());

                var consumer = applicationContext.getBean(BatchConsumerSingleRecord.class);
                consumer.routeToMasterCard();
                Assertions.assertTrue(consumer.await());

                assertEquals(1, ruleQueryCache.statistics(false).getClearCount());

                bootstrap.shutdown();
            } finally {
                pg.stop();
            }
        } finally {
            broker.destroy();
        }
    }

    @Test
    void manyToMany() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();

        try (PostgreSQLContainer<?> pg = new PostgreSQLContainer<>("postgres:14")) {
            pg.setCommand("postgres", "-c", "wal_level=logical");
            pg.start();

            var pgUri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                    PostgresqlServiceInfo.POSTGRES_SCHEME,
                    pg.getHost(),
                    pg.getFirstMappedPort(),
                    pg.getUsername(),
                    pg.getPassword(), pg.getDatabaseName()).getUriString();
            var pgJdbcUrl = pg.getJdbcUrl();

            try {
                int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();

                ApplicationConfig cfg = ApplicationConfig.mock();
                ApplicationProperties props = new ApplicationProperties(cfg.factory());

                Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
                bootstrap.addUps(new PostgresqlServiceInfo(UPSs.POSTGRES_OWNER, pgUri, pgJdbcUrl));
                bootstrap.withKafka(brokerPort);

                ConfigurableApplicationContext applicationContext = bootstrap.run();

                JpaManager jpaManager = applicationContext.getBean(JpaManager.class);
                jpaManager.execute(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 1; i <= Byte.MAX_VALUE; i++) {
                            Provider provider = new Provider();
                            provider.setCode("apple-pay");
                            jpaManager.save(provider);

                            RoutingRule rule = new RoutingRule();
                            rule.setProviders(List.of(provider));
                            jpaManager.save(rule);
                        }
                    }
                });

                //
                // ~ start
                //
                for (SmartLifecycle channel : applicationContext.getBeansOfType(SmartLifecycle.class).values()) {
                    if (BooleanUtils.isFalse(channel.isRunning())) {
                        channel.start();
                    }
                }

                //
                // ~ await for publication to arrive
                //
                Awaitility.await().until(new Callable<>() {
                    @Override
                    public Boolean call() throws Exception {
                        Long own = jpaManager.sqlQuery("select pubowner from pg_catalog.pg_publication").mapToScalar(Long.class).findOne();
                        if (Objects.nonNull(own)) {
                            log.info("pubowner: {}", own);
                        }
                        return Objects.nonNull(own);
                    }
                });

                var ruleQueryCache = jpaManager.cacheManager().queryCache(RoutingRule.class);
                ruleQueryCache.statistics(true);
                assertEquals(0, ruleQueryCache.statistics(false).getClearCount());

                BatchConsumerSingleRecord consumer = applicationContext.getBean(BatchConsumerSingleRecord.class);
                consumer.saveGooglePay();
                Assertions.assertTrue(consumer.await());

                assertEquals(1, ruleQueryCache.statistics(false).getClearCount());

                bootstrap.shutdown();
            } finally {
                pg.stop();
            }
        } finally {
            broker.destroy();
        }
    }

    public static class BatchConsumerSingleRecord implements ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> {
        private final CountDownLatch latch = new CountDownLatch(1);
        private final EbeanJpaManager jpaManager;
        private final EbeanQueryCacheInvalidationChangeConsumer cacheInvalidation;

        @Autowired
        public BatchConsumerSingleRecord(MeterRegistry meterRegistry, EbeanFactoryBean factory) throws Exception {
            this.jpaManager = (EbeanJpaManager) factory.getObject();
            this.cacheInvalidation = new EbeanQueryCacheInvalidationChangeConsumer(new IsDictionaryCache(jpaManager, meterRegistry), jpaManager);
        }

        public void deactivateRule() {
            jpaManager.execute(new Runnable() {
                @Override
                public void run() {
                    var metadata = jpaManager.find(RoutingRuleMetadata.class, 1);
                    metadata.setActive(false);
                    jpaManager.save(metadata);
                }
            });
        }

        public void saveGooglePay() {
            jpaManager.execute(new Runnable() {
                @Override
                public void run() {
                    var gp = new Provider();
                    gp.setCode("google_pay");
                    gp.setActive(true);
                    jpaManager.save(gp);
                }
            });
        }

        public void routeToMasterCard() {
            jpaManager.execute(new Runnable() {
                @Override
                public void run() {
                    var condition = jpaManager.find(Condition.class, 1);
                    condition.setValue("card.brand == 'MASTERCARD'");
                    jpaManager.save(condition);
                }
            });
        }

        public boolean await() throws InterruptedException {
            return latch.await(30, TimeUnit.SECONDS);
        }

        @Override
        public void handleBatch(
                List<ChangeEvent<SourceRecord, SourceRecord>> records,
                RecordCommitter<ChangeEvent<SourceRecord, SourceRecord>> committer) throws InterruptedException {
            try {
                cacheInvalidation.accept(records);
                committer.markBatchFinished();
            } finally {
                latch.countDown();
            }
        }
    }

    @Slf4j
    @Configuration
    public static class AppConfig {

        @Bean
        public BatchConsumerSingleRecord batchConsumerSingleRecord(MeterRegistry meterRegistry, EbeanFactoryBean factory) throws Exception {
            return new BatchConsumerSingleRecord(meterRegistry, factory);
        }

        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry, ApplicationConfig cfg) {
            PostgresqlServiceInfo si = UPSs.findRequiredScopedServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
            return new HikariDataSourceFactoryBean(props, meterRegistry, si);
        }

        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.setDatabasePlatform(new Postgres9Platform());
            cfg.addClass(Provider.class);
            cfg.addClass(RoutingRule.class);
            cfg.addClass(RoutingRuleMetadata.class);
            cfg.addClass(Condition.class);

            return cfg;
        }

        @Bean
        public EbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config) {
                @Override
                protected JpaManager createInstance() throws Exception {
                    EbeanJpaManager ebean = (EbeanJpaManager) super.createInstance();
                    FlywayUberRunner.run(ebean, SCHEMA_CORE);
                    return ebean;
                }
            };
        }

        @Bean
        public SmartLifecycle channel(ApplicationProperties props, DynamicCloud cloud, ChangeConsumer<ChangeEvent<SourceRecord, SourceRecord>> consumer) {
            return new SmartLifecycle() {
                private final ExecutorService executor = Executors.newSingleThreadExecutor();
                private DebeziumEngine<ChangeEvent<SourceRecord, SourceRecord>> engine;
                private boolean running;

                @Override
                public boolean isAutoStartup() {
                    return false;
                }

                @Override
                public void start() {
                    PostgresqlServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
                    KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

                    String db = psi.getPath();
                    String schema = "core";
                    String tbl1 = schema + "." + TBL_PROVIDER;
                    String tbl2 = schema + "." + TBL_ROUTING_RULE;
                    String tbl3 = schema + "." + TBL_CONDITION;
                    String tbl4 = schema + "." + TBL_ROUTING_RULE_METADATA;

                    Map<String, String> map = Maps.newHashMap();

                    map.put(EmbeddedEngineConfig.ENGINE_NAME.name(), "default");
                    map.put(EmbeddedEngineConfig.CONNECTOR_CLASS.name(), PostgresConnector.class.getName());

                    map.put(CommonConnectorConfig.TOPIC_PREFIX.name(), "dev");
                    map.put(RelationalDatabaseConnectorConfig.HOSTNAME.name(), psi.getHost());
                    map.put(RelationalDatabaseConnectorConfig.PORT.name(), Integer.toString(psi.getPort()));
                    map.put(RelationalDatabaseConnectorConfig.USER.name(), psi.getUserName());
                    map.put(RelationalDatabaseConnectorConfig.PASSWORD.name(), psi.getPassword());
                    map.put(RelationalDatabaseConnectorConfig.DATABASE_NAME.name(), db);
                    map.put(RelationalDatabaseConnectorConfig.SCHEMA_INCLUDE_LIST.name(), SCHEMA_CORE);
                    map.put(RelationalDatabaseConnectorConfig.TABLE_INCLUDE_LIST.name(), Joiner.on(',').join(ImmutableList.of(tbl1, tbl2, tbl3, tbl4)));

                    map.put(PostgresConnectorConfig.PLUGIN_NAME.name(), LogicalDecoder.PGOUTPUT.getValue());
                    map.put(PostgresConnectorConfig.PUBLICATION_AUTOCREATE_MODE.name(), AutoCreateMode.ALL_TABLES.getValue());
                    map.put(PostgresConnectorConfig.SNAPSHOT_MODE.name(), SnapshotMode.INITIAL.getValue());

                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE.name(), KafkaOffsetBackingStore.class.getName());
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_TOPIC.name(), KafkaOffsetBackingStore.class.getName());
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_PARTITIONS.name(), Integer.toString(1));
                    map.put(EmbeddedEngineConfig.OFFSET_STORAGE_KAFKA_REPLICATION_FACTOR.name(), Integer.toString(1));

                    map.put(DistributedConfig.OFFSET_STORAGE_TOPIC_CONFIG, "debezium-offset");
                    map.put(WorkerConfig.TOPIC_CREATION_ENABLE_CONFIG, Boolean.toString(true));
                    map.put(WorkerConfig.BOOTSTRAP_SERVERS_CONFIG, ksi.getBootstrapServers());

                    map.put(SchemaHistory.SKIP_UNPARSEABLE_DDL_STATEMENTS.name(), Boolean.toString(false));
                    map.put(SchemaHistory.STORE_ONLY_CAPTURED_TABLES_DDL.name(), Boolean.toString(false));

                    Properties toApply = new Properties();
                    toApply.putAll(map);

                    engine = DebeziumEngine.create(Connect.class).using(toApply).notifying(consumer).build();

                    executor.execute(engine);

                    this.running = true;
                }

                @Override
                public void stop() {
                    if (Objects.nonNull(engine)) {
                        try {
                            engine.close();
                        } catch (IOException err) {
                            log.error(err.getMessage(), err);
                        }
                    }
                    executor.shutdown();
                    this.running = false;
                }

                @Override
                public boolean isRunning() {
                    return running;
                }
            };
        }
    }
}
