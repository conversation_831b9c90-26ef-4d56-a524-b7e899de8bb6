package com.turbospaces.healthchecks;

import java.util.Objects;

import org.web3j.protocol.Web3j;

import com.codahale.metrics.health.HealthCheck;
import com.turbospaces.boot.AbstractHealtchCheck;

public class Web3jHealthCheck extends AbstractHealtchCheck {
    private final Web3j web3j;

    public Web3jHealthCheck(Web3j web3j) {
        this.web3j = Objects.requireNonNull(web3j);
    }
    @Override
    protected Result check() throws Exception {
        try {
            String version = web3j.netVersion().send().getNetVersion();
            return Result.healthy("net version: " + version);
        } catch (Exception err) {
            logger.warn(err.getMessage(), err);
            return HealthCheck.Result.unhealthy(err);
        }
    }
    @Override
    public boolean isBootstrapOnly() {
        return false;
    }
    @Override
    public void destroy() throws Exception {

    }
}
