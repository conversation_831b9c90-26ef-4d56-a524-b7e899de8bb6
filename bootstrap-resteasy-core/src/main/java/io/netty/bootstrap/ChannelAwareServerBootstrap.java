package io.netty.bootstrap;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.lang3.time.StopWatch;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.netty4.NettyAllocatorMetrics;
import io.netty.buffer.ByteBufAllocatorMetricProvider;
import io.netty.channel.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;

@Slf4j
@RequiredArgsConstructor
public class ChannelAwareServerBootstrap extends ServerBootstrap implements Disposable {
    private final AtomicBoolean metrics = new AtomicBoolean();
    private final MeterRegistry meterRegistry;
    private Channel channel;

    @Override
    void init(Channel c) {
        super.init(c);
        this.channel = c;

        //
        // ~ lazy installation of metrics adapter
        //
        if (metrics.compareAndSet(false, true)) {
            if (c.alloc() instanceof ByteBufAllocatorMetricProvider alloc) {
                new NettyAllocatorMetrics(alloc).bindTo(meterRegistry);
            }
        }
    }
    @Override
    public void dispose() {
        if (Objects.nonNull(channel)) {
            StopWatch stopWatch = StopWatch.createStarted();
            channel.close().syncUninterruptibly();
            stopWatch.stop();
            log.info("stopped resteasy netty server in: {}", stopWatch.toString());
        }
    }
}
