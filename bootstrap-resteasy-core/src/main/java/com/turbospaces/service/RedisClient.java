package com.turbospaces.service;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import org.redisson.api.RAtomicLongAsync;
import org.redisson.api.RBatch;
import org.redisson.api.RBucket;
import org.redisson.api.RFuture;
import org.redisson.api.RSetAsync;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;

public interface RedisClient extends RedissonClient {
    default String get(String key) {
        RBucket<String> bucket = getBucket(key);
        return bucket.get();
    }
    default CompletionStage<String> getAsync(String key) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        CompletableFuture<String> toReturn = new CompletableFuture<>();
        RBucket<String> bucket = getBucket(key);
        bucket.getAsync().whenComplete((value, throwable) -> {
            if (Objects.nonNull(mdc)) {
                MDC.setContextMap(mdc);
            }
            try {
                if (Objects.isNull(throwable)) {
                    toReturn.complete(value);
                } else {
                    toReturn.completeExceptionally(throwable);
                }
            } finally {
                MDC.clear();
            }
        });
        return toReturn;
    }
    default void set(String key, String value, Duration ttl) {
        RBucket<String> bucket = getBucket(key);
        bucket.set(value, ttl);
    }
    default CompletionStage<?> setAsync(String key, String value, Duration ttl) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        CompletableFuture<Void> toReturn = new CompletableFuture<>();
        RBucket<String> bucket = getBucket(key);
        bucket.setAsync(value, ttl).whenComplete((result, throwable) -> {
            if (Objects.nonNull(mdc)) {
                MDC.setContextMap(mdc);
            }
            try {
                if (Objects.isNull(throwable)) {
                    toReturn.complete(null);
                } else {
                    toReturn.completeExceptionally(throwable);
                }
            } finally {
                MDC.clear();
            }
        });
        return toReturn;
    }
    default String setIfNotExists(String key, String value, Duration ttl) {
        RBucket<String> bucket = getBucket(key);
        boolean isUpdated = bucket.setIfAbsent(value, ttl);
        return isUpdated ? null : bucket.get();
    }
    default long addToSet(String key, String value, Duration ttl) {
        return addToSetAsync(key, value, ttl).toCompletableFuture().join();
    }
    default CompletionStage<Integer> addToSetAsync(String key, String value, Duration ttl) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        CompletableFuture<Integer> toReturn = new CompletableFuture<>();
        RBatch batch = createBatch();

        RSetAsync<Object> set = batch.getSet(key);
        set.addAsync(value);
        set.expireAsync(ttl);
        RFuture<Integer> size = set.sizeAsync();

        batch.executeAsync().whenComplete((batchResult, outerThrowable) -> {
            if (Objects.nonNull(mdc)) {
                MDC.setContextMap(mdc);
            }
            try {
                if (Objects.isNull(outerThrowable)) {
                    size.whenComplete((innersResult, innerThrowable) -> {
                        if (Objects.isNull(innerThrowable)) {
                            toReturn.complete(innersResult);
                        } else {
                            toReturn.completeExceptionally(innerThrowable);
                        }
                    });
                } else {
                    toReturn.completeExceptionally(outerThrowable);
                }
            } finally {
                MDC.clear();
            }
        });

        return toReturn;
    }
    default long increment(String key, Duration ttl) {
        return incrementAsync(key, ttl, false).toCompletableFuture().join();
    }
    default long increment(String key, Duration ttl, boolean expireIfNotSet) {
        return incrementAsync(key, ttl, expireIfNotSet).toCompletableFuture().join();
    }
    default CompletionStage<Long> incrementAsync(String key, Duration ttl) {
        return incrementAsync(key, ttl, false);
    }
    default CompletionStage<Long> incrementAsync(String key, Duration ttl, boolean expireIfNotSet) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        CompletableFuture<Long> toReturn = new CompletableFuture<>();
        RBatch batch = createBatch();

        RAtomicLongAsync count = batch.getAtomicLong(key);
        RFuture<Long> incremented = count.incrementAndGetAsync();

        if (expireIfNotSet) {
            count.expireIfNotSetAsync(ttl);
        } else {
            count.expireAsync(ttl);
        }

        batch.executeAsync().whenComplete((batchResult, throwable) -> {
            if (Objects.nonNull(mdc)) {
                MDC.setContextMap(mdc);
            }
            try {
                if (Objects.isNull(throwable)) {
                    incremented.whenComplete((incrementResult, incrementThrowable) -> {
                        if (Objects.isNull(incrementThrowable)) {
                            toReturn.complete(incrementResult);
                        } else {
                            toReturn.completeExceptionally(incrementThrowable);
                        }
                    });
                } else {
                    toReturn.completeExceptionally(throwable);
                }
            } finally {
                MDC.clear();
            }
        });

        return toReturn;
    }
    default boolean exists(String key) {
        RBucket<String> bucket = getBucket(key);
        return bucket.isExists();
    }
    default CompletionStage<Boolean> existsAsync(String key) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        CompletableFuture<Boolean> toReturn = new CompletableFuture<>();
        RBucket<String> bucket = getBucket(key);
        bucket.isExistsAsync().whenComplete((result, throwable) -> {
            if (Objects.nonNull(mdc)) {
                MDC.setContextMap(mdc);
            }
            try {
                if (Objects.isNull(throwable)) {
                    toReturn.complete(result);
                } else {
                    toReturn.completeExceptionally(throwable);
                }
            } finally {
                MDC.clear();
            }
        });
        return toReturn;
    }
}
