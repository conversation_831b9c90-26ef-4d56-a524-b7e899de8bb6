package com.turbospaces.resteasy;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.security.KeyStore;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import javax.net.ssl.SSLContext;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jboss.resteasy.core.AsynchronousDispatcher;
import org.jboss.resteasy.core.ResourceMethodRegistry;
import org.jboss.resteasy.core.ResteasyDeploymentImpl;
import org.jboss.resteasy.plugins.server.servlet.ResteasyContextParameters;
import org.jboss.resteasy.plugins.spring.SpringBeanProcessor;
import org.jboss.resteasy.spi.ResourceInvoker;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.jakarta.xmlbind.JakartaXmlBindAnnotationModule;
import com.google.common.collect.Maps;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.resteasy.marshaller.JsonMarshaller;
import com.turbospaces.resteasy.marshaller.PayloadEncryptor;
import com.turbospaces.resteasy.marshaller.XMLMarshaller;
import com.turbospaces.service.MemcachedService;
import com.turbospaces.ssl.SSL;
import com.turbospaces.ssl.SelfSignedCertificateGenerator;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.SeBootstrap;
import jakarta.ws.rs.core.Application;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Accessors(fluent = true)
public class ResteasyChannel implements SmartApplicationListener, BeanFactoryPostProcessor, InitializingBean, DisposableBean, ApplicationRunner {
    private final SeBootstrap.Configuration.Builder builder = SeBootstrap.Configuration.builder();
    private final DefaultResteasyProviderFactory providerFactory;
    private final ResourceMethodRegistry registry;
    private final AsynchronousDispatcher dispatcher;
    private final ResteasyDeploymentImpl deployment;
    private final SpringBeanProcessor processor;

    private final ApplicationProperties props;
    private final DynamicCloud cloud;
    private final MeterRegistry meterRegistry;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final PayloadEncryptor payloadEncryptor;
    private final XmlMapper xmlMapper;
    private final int port;
    private final MemcachedService memcachedService;

    @Setter
    private ObjectMapper mapper;

    @Setter
    private boolean enableAsn;

    @Setter
    private SSLContext sslContext;

    @Setter
    private Application application;

    private GracefulShutdownNettyJaxrsServer server;

    private ResteasyChannel(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            int port) {
        this(props,
                cloud,
                meterRegistry,
                rateLimiterRegistry, port,
                (MemcachedService) Proxy.newProxyInstance(
                        Thread.currentThread().getContextClassLoader(),
                        new Class[] { MemcachedService.class }, new InvocationHandler() {
                            @Override
                            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                                if (props.isProdMode()) {
                                    log.error("memcached service is not supposed to be used. illegal method invocation: {}, args: {}", method.getName(), args);
                                }
                                return new UnsupportedOperationException(method.getName());
                            }
                        }));
    }
    public ResteasyChannel(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            int port,
            MemcachedService memcachedService) {
        this.port = port;
        this.props = Objects.requireNonNull(props);
        this.cloud = Objects.requireNonNull(cloud);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.rateLimiterRegistry = Objects.requireNonNull(rateLimiterRegistry);
        this.providerFactory = new DefaultResteasyProviderFactory(props);
        this.memcachedService = Objects.requireNonNull(memcachedService);

        builder.host("0.0.0.0"); // ~ bind to all (important in docker ENV and local ENV)
        builder.port(port);

        Map<String, Object> map = Maps.newHashMap();
        map.put(ResteasyContextParameters.RESTEASY_USE_BUILTIN_PROVIDERS, Boolean.TRUE.toString());
        map.put(ResteasyContextParameters.RESTEASY_SCAN_PROVIDERS, Boolean.FALSE.toString());
        map.put(ResteasyContextParameters.RESTEASY_SCAN, Boolean.FALSE.toString());
        map.put(ResteasyContextParameters.RESTEASY_STATISTICS_ENABLED, Boolean.FALSE.toString());
        map.put(ResteasyContextParameters.RESTEASY_TRACING_TYPE, ResteasyContextParameters.RESTEASY_TRACING_TYPE_OFF);

        providerFactory.setProperties(map);

        registry = new ResourceMethodRegistry(providerFactory);
        dispatcher = new AsynchronousDispatcher(providerFactory, registry);

        processor = new SpringBeanProcessor();
        processor.setProviderFactory(providerFactory);
        processor.setRegistry(registry);
        processor.setDispatcher(dispatcher);

        deployment = new ResteasyDeploymentImpl();

        mapper = new ObjectMapper();
        mapper.setDateFormat(new StdDateFormat());
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.setSerializationInclusion(Include.NON_NULL);

        xmlMapper = new XmlMapper();
        xmlMapper.setDateFormat(new StdDateFormat());
        xmlMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        xmlMapper.setSerializationInclusion(Include.NON_NULL);
        xmlMapper.registerModule(new JavaTimeModule());
        xmlMapper.registerModule(new JakartaXmlBindAnnotationModule());

        payloadEncryptor = new PayloadEncryptor();
    }
    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
        return processor.supportsEventType(eventType);
    }
    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        return processor.supportsSourceType(sourceType);
    }
    @Override
    public int getOrder() {
        return processor.getOrder();
    }
    @Override
    public String getListenerId() {
        return processor.getListenerId();
    }
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        log.info("got event: {}", event);
        processor.onApplicationEvent(event);
    }
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        processor.postProcessBeanFactory(beanFactory);
    }
    @Override
    public void run(ApplicationArguments args) throws Exception {
        server.start(builder.build());

        Map<String, List<ResourceInvoker>> paths = registry.getBounded();
        log.debug("listing {} registered path(s)", paths.size());

        for (Entry<String, List<ResourceInvoker>> entry : paths.entrySet()) {
            log.debug("path: {} -> {}", entry.getKey(), entry.getValue().stream().map(ResourceInvoker::getMethod).toList());
        }

        log.info("resteasy server {} is up and running on {} port", props.CLOUD_APP_ID.get(), port);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        //
        // ~ generate self-signed certificate if necessary
        //
        if (Objects.isNull(sslContext)) {
            if (props.APP_USE_SELF_SIGNED_CERTIFICATE.get()) {
                if (props.isDevMode()) {
                    File f = new File(FileUtils.getUserDirectory().getAbsolutePath() + "/self_signed_keystore");

                    try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                        SelfSignedCertificateGenerator ssc = new SelfSignedCertificateGenerator(props);
                        KeyStore keystore = ssc.call();
                        keystore.store(out, SelfSignedCertificateGenerator.PASSWORD.toCharArray());
                        FileUtils.writeByteArrayToFile(f, out.toByteArray());
                    }

                    SSL ssl = new SSL();
                    ssl.loadKeyStore(f, SelfSignedCertificateGenerator.PASSWORD);
                    sslContext = ssl.build();
                }
            }
        }

        //
        // ~ start HTTP server
        //
        server = new GracefulShutdownNettyJaxrsServer(meterRegistry);
        server.setBacklog(props.TCP_SOCKET_BACKLOG.get());
        server.setIoWorkerCount(props.NETTY_ACCEPTOR_POOL_SIZE.get());
        server.setExecutorThreadCount(props.NETTY_WORKER_POOL_SIZE.get());
        server.setDeployment(deployment);

        server.setMaxInitialLineLength(props.HTTP_INITIAL_LINE_MAX_SIZE.get());
        server.setMaxChunkSize(props.HTTP_CHUNK_MAX_SIZE.get());
        server.setMaxHeaderSize(props.HTTP_HEADER_MAX_SIZE.get());
        server.setMaxRequestSize(props.HTTP_REQUEST_MAX_SIZE.get());

        if (Objects.nonNull(sslContext)) {
            server.setSSLContext(sslContext);
            builder.sslContext(sslContext).protocol("https");
        }

        providerFactory.registerProviderInstance(new ObjectMapperContextResolver(mapper));
        providerFactory.registerProviderInstance(new XMLMarshaller(xmlMapper, payloadEncryptor));
        providerFactory.registerProviderInstance(new JsonMarshaller(mapper, payloadEncryptor));

        Application app = getOrCreateApplication();
        if (application instanceof RestApplication) {
            ((RestApplication) application).afterPropertiesSet();
        }

        deployment.setApplication(app);
        deployment.setProviderFactory(providerFactory);
        deployment.setRegistry(registry);
        deployment.setDispatcher(dispatcher);
        deployment.start();

        log.info("jackson modules: {}", mapper.getRegisteredModuleIds());
        log.info("xml jackson modules: {}", xmlMapper.getRegisteredModuleIds());
    }
    @Override
    public void destroy() throws Exception {
        try {
            if (Objects.nonNull(server)) {
                StopWatch stopWatch = StopWatch.createStarted();
                server.stop();
                stopWatch.stop();
                log.info("stopped resteasy channel in: {}", stopWatch);
            }
        } finally {
            if (Objects.nonNull(application)) {
                if (application instanceof RestApplication) {
                    ((RestApplication) application).destroy();
                }
            }
        }
    }
    protected Application getOrCreateApplication() throws Exception {
        if (Objects.isNull(application)) {
            application = new RestApplication(
                    props,
                    cloud,
                    meterRegistry,
                    rateLimiterRegistry,
                    mapper,
                    xmlMapper,
                    enableAsn,
                    memcachedService);
        }
        return application;
    }
    public static ResteasyChannel s2s(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            int port) {
        return new ResteasyChannel(props, cloud, meterRegistry, rateLimiterRegistry, port);
    }
    public static ResteasyChannel s2c(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            int port,
            MemcachedService memcachedService) {
        return new ResteasyChannel(props, cloud, meterRegistry, rateLimiterRegistry, port, memcachedService);
    }
}
