package com.turbospaces.resteasy;

import java.io.IOException;

import com.turbospaces.http.HttpProto;

import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path(HttpProto.V1)
public interface AdminEndpoint {
    @GET
    @Path("/mock")
    @Produces(MediaType.TEXT_PLAIN)
    Response mock(@QueryParam("timeout") Integer timeout) throws IOException;

    @GET
    @Path(HttpProto.VERSION_PATH)
    @Produces(MediaType.TEXT_PLAIN)
    void version(@Suspended AsyncResponse async, @QueryParam("timeout") Integer timeout) throws IOException;

    @GET
    @Path(HttpProto.HEALTHCHECK_PATH)
    @Produces(MediaType.APPLICATION_JSON)
    void healthCheck(@Suspended AsyncResponse async, @Context HttpHeaders headers, @QueryParam("timeout") Integer timeout) throws IOException;

    @GET
    @Path(HttpProto.METRICS_PATH)
    @Produces(MediaType.TEXT_PLAIN)
    void metrics(@Suspended AsyncResponse async, @Context HttpHeaders headers, @QueryParam("timeout") Integer timeout) throws IOException;
}
