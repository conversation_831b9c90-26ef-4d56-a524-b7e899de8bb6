package com.turbospaces.resteasy.printer;

import java.io.IOException;

import jakarta.ws.rs.container.ContainerRequestContext;

public interface HttpRequestPrinter extends HttpBodyPrinter {
    void writeStart(ContainerRequestContext context) throws IOException;

    void writeQueryParams(ContainerRequestContext context) throws IOException;

    void writeHeaders(ContainerRequestContext context) throws IOException;

    void writeCookies(ContainerRequestContext context) throws IOException;

    void writeBody(ContainerRequestContext context) throws IOException;

    void doOutPut(ContainerRequestContext context);
}
