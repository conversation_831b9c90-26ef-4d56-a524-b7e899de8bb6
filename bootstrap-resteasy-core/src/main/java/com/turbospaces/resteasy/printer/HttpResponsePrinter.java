package com.turbospaces.resteasy.printer;

import java.io.IOException;

import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;

public interface HttpResponsePrinter extends HttpBodyPrinter {
    void writeStart(ContainerRequestContext req, ContainerResponseContext resp) throws IOException;
    void writeStatus(ContainerRequestContext req, ContainerResponseContext resp) throws IOException;
    void writeHeaders(ContainerRequestContext req, ContainerResponseContext resp) throws IOException;
    void writeCookies(ContainerRequestContext req, ContainerResponseContext resp) throws IOException;
    void writeBody(ContainerRequestContext req, ContainerResponseContext resp) throws IOException;

    void doOutPut(ContainerRequestContext req, ContainerResponseContext resp);
}
