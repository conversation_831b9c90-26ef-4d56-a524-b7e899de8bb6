package com.turbospaces.resteasy.printer;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Consumer;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.jboss.resteasy.core.ResourceMethodInvoker;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.github.robtimus.obfuscation.Obfuscator;
import com.github.robtimus.obfuscation.http.HeaderObfuscator;
import com.github.robtimus.obfuscation.http.RequestParameterObfuscator;
import com.google.common.net.HttpHeaders;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.cfg.ApplicationProperties;

import jakarta.validation.Valid;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PostMatchHttpRequestPrinter extends PreMatchHttpRequestPrinter {
    private final ObjectMapper mapper;
    private final ObjectMapper xmlMapper;

    public PostMatchHttpRequestPrinter(ApplicationProperties props, ObjectMapper mapper, ObjectMapper xmlMapper) {
        super(props);
        this.mapper = Objects.requireNonNull(mapper);
        this.xmlMapper = Objects.requireNonNull(xmlMapper);
    }
    @Override
    public void writeQueryParams(ContainerRequestContext context) throws IOException {
        MultivaluedMap<String, String> query = context.getUriInfo().getQueryParameters();
        ResourceMethodInvoker invoker = ((PostMatchContainerRequestContext) context).getResourceMethod();

        if (BooleanUtils.isFalse(query.isEmpty())) {
            RequestParameterObfuscator.Builder b = addQueryParamsToMask(invoker, RequestParameterObfuscator.builder());

            List<String> l = props.HTTP_QUERY_PARAMS_TO_MASK.get();
            if (CollectionUtils.isNotEmpty(l)) {
                for (String it : l) {
                    b.withParameter(it, Obfuscator.all());
                }
            }

            RequestParameterObfuscator obfuscator = b.build();
            for (Entry<String, List<String>> next : query.entrySet()) {
                String name = next.getKey();

                if (CollectionUtils.isNotEmpty(next.getValue())) {
                    for (String value : next.getValue()) {
                        buffer.write(String.format("Query: %s = %s", name, obfuscator.obfuscateParameter(name, value)));
                        buffer.newLine();
                    }
                }
            }
        }
    }
    @Override
    public void writeHeaders(ContainerRequestContext context) throws IOException {
        MultivaluedMap<String, String> headers = context.getHeaders();
        ResourceMethodInvoker invoker = ((PostMatchContainerRequestContext) context).getResourceMethod();

        if (BooleanUtils.isFalse(headers.isEmpty())) {
            HeaderObfuscator.Builder b = addHeadersToMask(invoker, HeaderObfuscator.builder());

            List<String> l = props.HTTP_HEADERS_TO_MASK.get();
            if (CollectionUtils.isNotEmpty(l)) {
                for (String it : l) {
                    b.withHeader(it, Obfuscator.all());
                }
            }

            HeaderObfuscator obfuscator = b.build();
            for (Entry<String, List<String>> next : headers.entrySet()) {
                String name = next.getKey();
                boolean isNotCookie = BooleanUtils.isFalse(Strings.CI.equals(name, HttpHeaders.COOKIE));

                if (isNotCookie) {
                    if (CollectionUtils.isNotEmpty(next.getValue())) {
                        for (String value : next.getValue()) {
                            buffer.write(String.format("Header: %s = %s", name, obfuscator.obfuscateHeader(name, value)));
                            buffer.newLine();
                        }
                    }
                }
            }
        }
    }
    @Override
    public void writeBody(ContainerRequestContext context) throws IOException {
        PostMatchContainerRequestContext requestContext = (PostMatchContainerRequestContext) context;
        ResourceMethodInvoker invoker = requestContext.getResourceMethod();
        Method method = invoker.getMethod();
        Class<?> resource = invoker.getResourceClass();
        ApiEndpoint annotation = method.getAnnotation(ApiEndpoint.class);
        String body = StringUtils.EMPTY;
        boolean tryObfuscate = false;

        //
        // ~ on method level
        //
        if (Objects.nonNull(annotation)) {
            if (annotation.obfuscateRequestBody()) {
                tryObfuscate = true;
            }
        }

        //
        // ~ on class level
        //
        annotation = resource.getAnnotation(ApiEndpoint.class);
        if (Objects.nonNull(annotation)) {
            if (annotation.obfuscateRequestBody()) {
                tryObfuscate = true;
            }
        }

        if (context.hasEntity()) {
            try (InputStream in = context.getEntityStream()) {
                if (Objects.nonNull(in)) {
                    in.reset();
                    try {
                        body = IOUtils.toString(in, StandardCharsets.UTF_8);
                    } finally {
                        in.reset();
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(body)) {
            if (tryObfuscate) {
                MediaType mediaType = context.getMediaType();
                if (MediaType.APPLICATION_JSON_TYPE.isCompatible(mediaType)) {
                    body = getBody(method, mapper, body);
                } else if (MediaType.APPLICATION_XML_TYPE.isCompatible(mediaType) || MediaType.TEXT_XML_TYPE.isCompatible(mediaType)) {
                    body = getBody(method, xmlMapper, body);
                }
            }

            buffer.write("Body: " + body);
        }
    }
    private static String getBody(Method method, ObjectMapper toUse, String body) {
        String toReturn = body;
        for (Parameter parameter : method.getParameters()) {
            JavaType javaType = toUse.constructType(parameter.getType());
            Valid valid = parameter.getAnnotation(Valid.class); // ~ only if valid annotation

            if (Objects.nonNull(valid)) {
                try {
                    if (toUse.canDeserialize(javaType)) {
                        ObjectReader readerFor = toUse.readerFor(javaType);
                        Object readValue = readerFor.readValue(body);
                        if (Objects.nonNull(readValue)) {
                            toReturn = toUse.writeValueAsString(readValue);
                        }
                    }
                } catch (Exception err) {
                    log.warn(err.getMessage(), err);
                }

                break;
            }
        }
        return toReturn;
    }
    protected RequestParameterObfuscator.Builder addQueryParamsToMask(ResourceMethodInvoker invoker, RequestParameterObfuscator.Builder obfuscator) {
        Class<?> resource = invoker.getResourceClass();
        Method method = invoker.getMethod();

        Consumer<ApiEndpoint> action = new Consumer<>() {
            @Override
            public void accept(ApiEndpoint annotation) {
                if (Objects.nonNull(annotation)) {
                    String[] toMask = annotation.queryParamsToMask();
                    if (Objects.nonNull(toMask)) {
                        for (String param : toMask) {
                            obfuscator.withParameter(param, Obfuscator.all());
                        }
                    }
                }
            }
        };

        action.accept(resource.getAnnotation(ApiEndpoint.class));
        action.accept(method.getAnnotation(ApiEndpoint.class));

        return obfuscator;
    }
    protected HeaderObfuscator.Builder addHeadersToMask(ResourceMethodInvoker invoker, HeaderObfuscator.Builder obfuscator) {
        Class<?> resource = invoker.getResourceClass();
        Method method = invoker.getMethod();

        Consumer<ApiEndpoint> action = new Consumer<>() {
            @Override
            public void accept(ApiEndpoint annotation) {
                if (Objects.nonNull(annotation)) {
                    String[] toMask = annotation.headersToMask();
                    if (Objects.nonNull(toMask)) {
                        for (String param : toMask) {
                            obfuscator.withHeader(param, Obfuscator.all());
                        }
                    }
                }
            }
        };

        action.accept(resource.getAnnotation(ApiEndpoint.class));
        action.accept(method.getAnnotation(ApiEndpoint.class));

        return obfuscator;
    }
}
