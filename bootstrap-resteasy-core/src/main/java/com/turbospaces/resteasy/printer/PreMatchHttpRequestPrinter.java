package com.turbospaces.resteasy.printer;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;

import com.github.robtimus.obfuscation.Obfuscator;
import com.github.robtimus.obfuscation.http.HeaderObfuscator;
import com.github.robtimus.obfuscation.http.RequestParameterObfuscator;
import com.google.common.base.Strings;
import com.google.common.net.HttpHeaders;
import com.turbospaces.cfg.ApplicationProperties;

import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Cookie;
import jakarta.ws.rs.core.MultivaluedMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PreMatchHttpRequestPrinter implements HttpRequestPrinter {
    protected final ApplicationProperties props;
    protected final StringWriter writer = new StringWriter();
    protected final BufferedWriter buffer = new BufferedWriter(writer);

    public PreMatchHttpRequestPrinter(ApplicationProperties props) {
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public void writeStart(ContainerRequestContext context) throws IOException {
        buffer.write("Request:");
        buffer.newLine();

        buffer.write(context.getMethod() + ": " + context.getUriInfo().getPath());
        buffer.newLine();
    }
    @Override
    public void writeQueryParams(ContainerRequestContext context) throws IOException {
        MultivaluedMap<String, String> query = context.getUriInfo().getQueryParameters();

        if (BooleanUtils.isFalse(query.isEmpty())) {
            RequestParameterObfuscator.Builder b = RequestParameterObfuscator.builder();

            List<String> l = props.HTTP_QUERY_PARAMS_TO_MASK.get();
            if (CollectionUtils.isNotEmpty(l)) {
                for (String it : l) {
                    b.withParameter(it, Obfuscator.all());
                }
            }

            RequestParameterObfuscator obfuscator = b.build();
            for (Entry<String, List<String>> next : query.entrySet()) {
                String name = next.getKey();

                if (CollectionUtils.isNotEmpty(next.getValue())) {
                    for (String value : next.getValue()) {
                        buffer.write(String.format("Query: %s = %s", name, obfuscator.obfuscateParameter(name, value)));
                        buffer.newLine();
                    }
                }
            }
        }
    }
    @Override
    public void writeHeaders(ContainerRequestContext context) throws IOException {
        MultivaluedMap<String, String> headers = context.getHeaders();

        if (BooleanUtils.isFalse(headers.isEmpty())) {
            HeaderObfuscator.Builder b = HeaderObfuscator.builder();

            List<String> l = props.HTTP_HEADERS_TO_MASK.get();
            if (CollectionUtils.isNotEmpty(l)) {
                for (String it : l) {
                    b.withHeader(it, Obfuscator.all());
                }
            }

            HeaderObfuscator obfuscator = b.build();
            for (Entry<String, List<String>> next : headers.entrySet()) {
                String name = next.getKey();
                boolean isNotCookie = BooleanUtils.isFalse(org.apache.commons.lang3.Strings.CI.equals(name, HttpHeaders.COOKIE));

                if (isNotCookie) {
                    if (CollectionUtils.isNotEmpty(next.getValue())) {
                        for (String value : next.getValue()) {
                            buffer.write(String.format("Header: %s = %s", name, obfuscator.obfuscateHeader(name, value)));
                            buffer.newLine();
                        }
                    }
                }
            }
        }
    }
    @Override
    public void writeCookies(ContainerRequestContext context) throws IOException {
        Map<String, Cookie> cookies = context.getCookies();

        if (BooleanUtils.isFalse(cookies.isEmpty())) {

            List<String> cookieNamesToObfuscate = props.HTTP_COOKIES_TO_MASK.get();

            for (Entry<String, Cookie> next : cookies.entrySet()) {
                Cookie cookie = next.getValue();

                String name = cookie.getName();
                String value = cookie.getValue();
                if (CollectionUtils.isNotEmpty(cookieNamesToObfuscate) && cookieNamesToObfuscate.contains(name)) {
                    value = Strings.repeat("*", value.length());
                }
                buffer.write(String.format("Cookie: %s = %s", name, value));
                buffer.newLine();
            }
        }
    }
    @Override
    public void writeBody(ContainerRequestContext context) throws IOException {
        try (InputStream in = context.getEntityStream()) {
            if (Objects.nonNull(in)) {
                in.reset();
                try {
                    buffer.write("Body: " + IOUtils.toString(in, StandardCharsets.UTF_8));
                } finally {
                    in.reset();
                }
            }
        }
    }
    @Override
    public void doOutPut(ContainerRequestContext context) {
        try {
            writeStart(context);
            writeHeaders(context);
            writeQueryParams(context);
            writeCookies(context);
            writeBody(context);
            buffer.flush();

            LOGGING_FILTER_LOGGER.debug(writer.toString());

            buffer.close();
            writer.close();
        } catch (IOException err) {
            log.error(err.getMessage(), err);
        }
    }
}
