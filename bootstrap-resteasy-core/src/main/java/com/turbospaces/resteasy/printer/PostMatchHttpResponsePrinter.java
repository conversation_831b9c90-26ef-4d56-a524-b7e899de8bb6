package com.turbospaces.resteasy.printer;

import java.io.IOException;
import java.util.Objects;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jboss.resteasy.api.validation.ViolationReport;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;

import io.netty.util.internal.StringUtil;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;
import jakarta.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PostMatchHttpResponsePrinter extends PreMatchHttpResponsePrinter {
    private final ObjectMapper mapper;
    private final ObjectMapper xmlMapper;

    public PostMatchHttpResponsePrinter(ApplicationProperties props, ObjectMapper mapper, ObjectMapper xmlMapper) {
        super(props);
        this.mapper = Objects.requireNonNull(mapper);
        this.xmlMapper = Objects.requireNonNull(xmlMapper);
    }
    @Override
    public void writeBody(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        Object entity = resp.getEntity();
        Object doNotPrintResponseBodyCtx = req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_RESPONSE_DO_NOT_PRINT_BODY));

        if (Objects.nonNull(entity)) {
            if (entity instanceof ViolationReport) {
                buffer.write("Body: " + ReflectionToStringBuilder.toString(entity, ToStringStyle.SHORT_PREFIX_STYLE));
            } else {
                if (Objects.nonNull(doNotPrintResponseBodyCtx)) {
                    boolean doNotPrintResponseBody = (boolean) doNotPrintResponseBodyCtx;
                    if (doNotPrintResponseBody) {
                        buffer.write("Body: Hidden{*}");
                    } else {
                        buffer.write("Body: " + getBody(resp));
                    }
                } else {
                    buffer.write("Body: " + getBody(resp));
                }
            }
        }
    }

    private String getBody(ContainerResponseContext resp) {
        MediaType mediaType = resp.getMediaType();
        try {
            if (MediaType.APPLICATION_JSON_TYPE.isCompatible(mediaType)) {
                return mapper.writeValueAsString(resp.getEntity());
            } else if (MediaType.APPLICATION_XML_TYPE.isCompatible(mediaType) || MediaType.TEXT_XML_TYPE.isCompatible(mediaType)) {
                return xmlMapper.writeValueAsString(resp.getEntity());
            } else {
                return resp.getEntity().toString();
            }
        } catch (Exception e) {
            log.error("Unable to get body, reason: {}", e.getMessage());
        }

        return StringUtil.EMPTY_STRING;
    }
}
