package com.turbospaces.resteasy.printer;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jboss.resteasy.api.validation.ViolationReport;

import com.github.robtimus.obfuscation.Obfuscator;
import com.github.robtimus.obfuscation.http.HeaderObfuscator;
import com.google.common.base.Strings;
import com.google.common.net.HttpHeaders;
import com.turbospaces.cfg.ApplicationProperties;

import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;
import jakarta.ws.rs.core.Cookie;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.NewCookie;
import jakarta.ws.rs.ext.RuntimeDelegate;
import jakarta.ws.rs.ext.RuntimeDelegate.HeaderDelegate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PreMatchHttpResponsePrinter implements HttpResponsePrinter {
    private static HeaderDelegate<Cookie> COOKIE_PRINTER = RuntimeDelegate.getInstance().createHeaderDelegate(Cookie.class);

    protected final ApplicationProperties props;
    protected final StringWriter writer = new StringWriter();
    protected final BufferedWriter buffer = new BufferedWriter(writer);

    public PreMatchHttpResponsePrinter(ApplicationProperties props) {
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public void writeStart(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        buffer.write("Response:");
        buffer.newLine();

        buffer.write(req.getMethod() + ": " + req.getUriInfo().getPath());
        buffer.newLine();
    }
    @Override
    public void writeStatus(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        buffer.write("Status: " + resp.getStatus());
        buffer.newLine();

        if (resp.getLength() > 0) {
            buffer.write("Length: " + resp.getLength());
            buffer.newLine();
        }
    }
    @Override
    public void writeHeaders(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        MultivaluedMap<String, String> headers = resp.getStringHeaders();

        if (BooleanUtils.isFalse(headers.isEmpty())) {
            HeaderObfuscator.Builder b = HeaderObfuscator.builder();

            List<String> l = props.HTTP_HEADERS_TO_MASK.get();
            if (CollectionUtils.isNotEmpty(l)) {
                for (String it : l) {
                    b.withHeader(it, Obfuscator.all());
                }
            }

            HeaderObfuscator obfuscator = b.build();
            for (Entry<String, List<String>> next : headers.entrySet()) {
                String name = next.getKey();

                if (BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(name, HttpHeaders.SET_COOKIE))) {
                    if (CollectionUtils.isNotEmpty(next.getValue())) {

                        if (CollectionUtils.isNotEmpty(next.getValue())) {
                            for (String value : next.getValue()) {
                                buffer.write(String.format("Header: %s = %s", name, obfuscator.obfuscateHeader(name, value)));
                                buffer.newLine();
                            }
                        }
                    }
                }
            }
        }
    }
    @Override
    public void writeCookies(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        Map<String, NewCookie> cookies = resp.getCookies();

        if (BooleanUtils.isFalse(cookies.isEmpty())) {
            for (Entry<String, NewCookie> next : cookies.entrySet()) {
                NewCookie cookie = next.getValue();
                if (cookie.isHttpOnly()) {
                    String cookieValue = StringUtils.isNotEmpty(cookie.getValue()) ? cookie.getValue() : StringUtils.EMPTY;
                    buffer.write(String.format("Cookie: %s=%s Domain: %s, Path: %s, MaxAge: %s, Expire: %s, HttpOnly: %s, Secure: %s",
                            cookie.getName(),
                            cookie.isHttpOnly() ? Strings.repeat("*", cookieValue.length()) : cookieValue,
                            cookie.getDomain(),
                            cookie.getPath(),
                            cookie.getMaxAge(),
                            cookie.getExpiry(),
                            cookie.isHttpOnly(), cookie.isSecure()));
                } else {
                    buffer.write(String.format("Cookie: %s", COOKIE_PRINTER.toString(cookie)));
                }
                buffer.newLine();
            }
        }
    }
    @Override
    public void writeBody(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        Object entity = resp.getEntity();

        if (Objects.nonNull(entity)) {
            if (entity instanceof ViolationReport) {
                buffer.write("Body: " + ReflectionToStringBuilder.toString(entity, ToStringStyle.SHORT_PREFIX_STYLE));
            } else {
                buffer.write("Body: " + entity);
            }
        }
    }
    @Override
    public void doOutPut(ContainerRequestContext req, ContainerResponseContext resp) {
        try {
            writeStart(req, resp);
            writeStatus(req, resp);
            writeHeaders(req, resp);
            writeCookies(req, resp);
            writeBody(req, resp);
            buffer.flush();

            LOGGING_FILTER_LOGGER.debug(writer.toString());

            buffer.close();
            writer.close();
        } catch (IOException err) {
            log.error(err.getMessage(), err);
        }
    }
}
