package com.turbospaces.resteasy;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.jboss.resteasy.plugins.interceptors.CorsFilter;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.resteasy.filter.IpRateLimiterApiFilter;
import com.turbospaces.resteasy.filter.IpWhitelistApplicationFilter;
import com.turbospaces.resteasy.filter.PostMatchApplicationFilter;
import com.turbospaces.resteasy.filter.RecaptchaApplicationFilter;
import com.turbospaces.resteasy.filter.ReloadableCorsFilter;
import com.turbospaces.resteasy.filter.RootApplicationFilter;
import com.turbospaces.resteasy.filter.TurnstilemTLSApplicationFilter;
import com.turbospaces.resteasy.filter.UriPathRateLimiterApiFilter;
import com.turbospaces.service.MemcachedService;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.core.Application;

public class RestApplication extends Application implements InitializingBean, DisposableBean {
    private final CorsFilter corsFilter;
    private final IpWhitelistApplicationFilter ipApplicationFilter;
    private final UriPathRateLimiterApiFilter uriRateLimiterApiFilter;
    private final IpRateLimiterApiFilter ipRateLimiterApiFilter;
    private final PostMatchApplicationFilter applicationFilter;
    private final RootApplicationFilter rootFilter;
    private final TurnstilemTLSApplicationFilter turnstileCaptchaFilter;
    private final RecaptchaApplicationFilter recaptchaCaptchaFilter;

    public RestApplication(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            ObjectMapper mapper,
            XmlMapper xmlMapper,
            boolean enableAsn,
            MemcachedService memcachedService) {
        corsFilter = new ReloadableCorsFilter(props);

        ipApplicationFilter = new IpWhitelistApplicationFilter(props);
        turnstileCaptchaFilter = new TurnstilemTLSApplicationFilter(props, cloud, meterRegistry, mapper, memcachedService);
        recaptchaCaptchaFilter = new RecaptchaApplicationFilter(props, cloud, mapper);
        uriRateLimiterApiFilter = new UriPathRateLimiterApiFilter(props, rateLimiterRegistry);
        ipRateLimiterApiFilter = new IpRateLimiterApiFilter(props, memcachedService);
        applicationFilter = new PostMatchApplicationFilter(props, meterRegistry, mapper, xmlMapper);
        rootFilter = new RootApplicationFilter(props, cloud, enableAsn);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        turnstileCaptchaFilter.afterPropertiesSet();
        recaptchaCaptchaFilter.afterPropertiesSet();
        rootFilter.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        ipApplicationFilter.destroy();
        turnstileCaptchaFilter.destroy();
        recaptchaCaptchaFilter.destroy();
        ipRateLimiterApiFilter.destroy();
        applicationFilter.destroy();
        rootFilter.destroy();
    }

    @Override
    public Set<Object> getSingletons() {
        return new HashSet<>(Arrays.asList(
                corsFilter,
                ipApplicationFilter,
                turnstileCaptchaFilter,
                recaptchaCaptchaFilter,
                applicationFilter,
                rootFilter,
                uriRateLimiterApiFilter,
                ipRateLimiterApiFilter));
    }
}
