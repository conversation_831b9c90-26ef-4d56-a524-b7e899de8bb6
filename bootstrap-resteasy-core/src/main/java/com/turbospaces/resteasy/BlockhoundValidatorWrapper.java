package com.turbospaces.resteasy;

import java.util.Set;

import io.vavr.Function0;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.validation.executable.ExecutableValidator;
import jakarta.validation.metadata.BeanDescriptor;
import lombok.RequiredArgsConstructor;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@RequiredArgsConstructor
public class BlockhoundValidatorWrapper implements Validator {
    private final Validator validator;

    @Override
    public <T> Set<ConstraintViolation<T>> validate(T object, Class<?>... groups) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public Set<ConstraintViolation<T>> apply() {
                return validator.validate(object, groups);
            }
        });
    }
    @Override
    public <T> Set<ConstraintViolation<T>> validateProperty(T object, String propertyName, Class<?>... groups) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public Set<ConstraintViolation<T>> apply() {
                return validator.validateProperty(object, propertyName, groups);
            }
        });
    }
    @Override
    public <T> Set<ConstraintViolation<T>> validateValue(Class<T> beanType, String propertyName, Object value, Class<?>... groups) {
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public Set<ConstraintViolation<T>> apply() {
                return validator.validateValue(beanType, propertyName, value, groups);
            }
        });
    }
    @Override
    public BeanDescriptor getConstraintsForClass(Class<?> clazz) {
        return validator.getConstraintsForClass(clazz);
    }
    @Override
    public <T> T unwrap(Class<T> type) {
        return validator.unwrap(type);
    }
    @Override
    public ExecutableValidator forExecutables() {
        return validator.forExecutables();
    }
}
