package com.turbospaces.resteasy;

import org.jboss.resteasy.plugins.server.netty.NettyJaxrsServer;

import io.micrometer.core.instrument.MeterRegistry;
import io.netty.bootstrap.ChannelAwareServerBootstrap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GracefulShutdownNettyJaxrsServer extends NettyJaxrsServer {
    public GracefulShutdownNettyJaxrsServer(MeterRegistry meterRegistry) {
        this.bootstrap = new ChannelAwareServerBootstrap(meterRegistry);
    }
    @Override
    public void stop() {
        super.stop();
        ((ChannelAwareServerBootstrap) bootstrap).dispose();
    }
}
