package com.turbospaces.resteasy.marshaller;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

import javax.crypto.AEADBadTagException;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import com.turbospaces.common.PlatformUtil;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.annotations.VisibleForTesting;
import com.turbospaces.http.HttpProto;

import jakarta.ws.rs.core.MultivaluedMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PayloadEncryptor {
    private static final int GCM_TAG_LENGTH = 128;
    private static final int GCM_IV_LENGTH = 12;

    public boolean isEnabled(MultivaluedMap<String, Object> httpHeaders) {
        var encryptionKey = httpHeaders.getFirst(HttpProto.HEADER_X_ENCRYPT);
        return encryptionKey != null && StringUtils.isNotEmpty(encryptionKey.toString());
    }

    public OutputStream getOut(OutputStream out, ByteArrayOutputStream bout, MultivaluedMap<String, Object> httpHeaders) {
        var encryptionKey = httpHeaders.getFirst(HttpProto.HEADER_X_ENCRYPT);
        var algorithmHeader = String.valueOf(httpHeaders.getFirst(HttpProto.HEADER_X_ALGORITHM));
        var algorithm = EncryptionAlgorithm.fromHeaderName(algorithmHeader);

        try {
            var modifiedKey = DigestUtils.sha256(StringUtils.reverse(encryptionKey.toString()));
            SecretKeySpec secretKeySpec = new SecretKeySpec(modifiedKey, "AES");
            Cipher cipher = Cipher.getInstance(algorithm.getCipherAlgorithm());

            switch (algorithm) {
                case GCM -> {
                    byte[] iv = new byte[GCM_IV_LENGTH];
                    PlatformUtil.RANDOM.nextBytes(iv);  // Random 12-byte IV

                    GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
                    cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmSpec);
                    // Write IV first (prepend)
                    bout.write(iv);
                    return new CipherOutputStream(bout, cipher);
                }
                case CFB -> {
                    IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(modifiedKey, 0, cipher.getBlockSize()));
                    cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, iv);
                    return new CipherOutputStream(bout, cipher);
                }
                default -> throw new IllegalArgumentException("Unsupported encryption algorithm: " + algorithm);
            }
        } catch (Exception e) {
            log.error("Error while encrypting payload, skipping", e);
            return out;
        }
    }

    @VisibleForTesting
    public static String decrypt(InputStream encryptedText, String secretKey, EncryptionAlgorithm algorithm) throws Exception {
        var keyBytes = DigestUtils.sha256(StringUtils.reverse(secretKey));
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance(algorithm.getCipherAlgorithm());

        byte[] base64Bytes = encryptedText.readAllBytes();
        byte[] allBytes = Base64.getDecoder().decode(base64Bytes);

        switch (algorithm) {
            case GCM -> {

                byte[] iv = Arrays.copyOfRange(allBytes, 0, GCM_IV_LENGTH);
                byte[] encryptedPayload = Arrays.copyOfRange(allBytes, GCM_IV_LENGTH, allBytes.length);

                GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
                cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmSpec);

                try (CipherInputStream io = new CipherInputStream(new ByteArrayInputStream(encryptedPayload), cipher)) {
                    return new String(io.readAllBytes());
                }

            }
            case CFB -> {
                IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(keyBytes, 0, cipher.getBlockSize()));
                cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, iv);

                // hint for decrypting on FE side
                // https://stackoverflow.com/questions/27375908/crypto-js-read-and-decrypt-file
                try (CipherInputStream io = new CipherInputStream(new ByteArrayInputStream(allBytes), cipher)) {
                    return new String(io.readAllBytes());
                }
            }
        }
        throw new IllegalArgumentException("Unsupported encryption algorithm: " + algorithm);
    }
}