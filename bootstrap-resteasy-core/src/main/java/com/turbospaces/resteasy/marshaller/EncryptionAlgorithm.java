package com.turbospaces.resteasy.marshaller;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum EncryptionAlgorithm {
    CFB("cf", "AES/CFB8/NoPadding"),
    GCM("gm", "AES/GCM/NoPadding");

    private final String headerName;
    private final String cipherAlgorithm;

    EncryptionAlgorithm(String headerName, String cipherAlgorithm) {
        this.headerName = headerName;
        this.cipherAlgorithm = cipherAlgorithm;
    }

    public static EncryptionAlgorithm fromHeaderName(String name) {
        return Arrays.stream(values())
                .filter(e -> e.headerName.equalsIgnoreCase(name))
                .findFirst()
                .orElse(CFB); // Default fallback
    }
}
