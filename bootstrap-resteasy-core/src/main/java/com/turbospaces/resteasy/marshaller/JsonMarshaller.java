package com.turbospaces.resteasy.marshaller;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Type;
import java.util.Base64;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.jakarta.rs.json.JacksonJsonProvider;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.ext.ContextResolver;
import jakarta.ws.rs.ext.Provider;
import lombok.SneakyThrows;

@Provider
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class JsonMarshaller extends JacksonJsonProvider implements ContextResolver<ObjectMapper> {
    private final ObjectMapper mapper;
    private final PayloadEncryptor payloadEncryptor;

    public JsonMarshaller(ObjectMapper mapper, PayloadEncryptor payloadEncryptor) {
        super(mapper);
        this.mapper = mapper;
        this.payloadEncryptor = payloadEncryptor;
    }

    @Override
    @SneakyThrows
    public void writeTo(Object obj,
                        Class<?> type,
                        Type genericType,
                        Annotation[] annotations,
                        MediaType mediaType,
                        MultivaluedMap<String, Object> httpHeaders,
                        OutputStream out) throws WebApplicationException {
        ByteArrayOutputStream bout = null;
        OutputStream finalOut = out;
        if (payloadEncryptor.isEnabled(httpHeaders)) {
            bout = new ByteArrayOutputStream();
            finalOut = payloadEncryptor.getOut(out, bout, httpHeaders);
        }
        super.writeTo(obj, type, genericType, annotations, mediaType, httpHeaders, finalOut);
        if (bout != null) {
            finalOut.close(); // Must close to finalize GCM tag
            out.write(Base64.getEncoder().encode(bout.toByteArray()));
        }
    }
    @Override
    public ObjectMapper getContext(Class<?> type) {
        return mapper;
    }
}