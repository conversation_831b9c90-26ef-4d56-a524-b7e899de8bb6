package com.turbospaces.resteasy.marshaller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Type;
import java.util.Base64;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;

import jakarta.ws.rs.Produces;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.ext.MessageBodyWriter;
import jakarta.ws.rs.ext.Provider;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.SneakyThrows;

@Provider
@Produces({ MediaType.TEXT_XML, MediaType.APPLICATION_XML, MediaType.TEXT_HTML })
public class XMLMarshaller implements MessageBodyWriter<Object> {
    private final XmlMapper xmlMapper;
    private final PayloadEncryptor payloadEncryptor;

    public XMLMarshaller(XmlMapper xmlMapper, PayloadEncryptor payloadEncryptor) {
        this.xmlMapper = xmlMapper;
        this.payloadEncryptor = payloadEncryptor;
    }

    @Override
    public boolean isWriteable(Class<?> type, Type genericType, Annotation[] annotations, MediaType mediaType) {
        return true;
    }

    @Override
    public long getSize(Object obj, Class<?> type, Type genericType, Annotation[] annotations, MediaType mediaType) {
        return -1;
    }

    @Override
    @SneakyThrows
    public void writeTo(
            Object obj,
            Class<?> type,
            Type genericType,
            Annotation[] annotations,
            MediaType mediaType,
            MultivaluedMap<String, Object> httpHeaders,
            OutputStream out) throws WebApplicationException {
        ByteArrayOutputStream bout = null;
        OutputStream finalOut = out;
        if (payloadEncryptor.isEnabled(httpHeaders)) {
            bout = new ByteArrayOutputStream();
            finalOut = payloadEncryptor.getOut(out, bout, httpHeaders);
        }
        if (type.isAnnotationPresent(XmlRootElement.class)) {
            jaxbMarshall(obj, type, finalOut);
        } else {
            try {
                xmlMapper.writeValue(finalOut, obj);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        if (bout != null) {
            finalOut.close();
            out.write(Base64.getEncoder().encode(bout.toByteArray()));
        }
    }

    private static void jaxbMarshall(Object obj, Class<?> type, OutputStream entityStream) {
        try {
            JAXBContext ctx = JAXBContext.newInstance(type);
            var marshaller = ctx.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
            marshaller.marshal(obj, entityStream);
        } catch (JAXBException ex) {
            throw new RuntimeException(ex);
        }
    }
}
