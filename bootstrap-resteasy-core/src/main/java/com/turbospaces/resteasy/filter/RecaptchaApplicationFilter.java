package com.turbospaces.resteasy.filter;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.jboss.resteasy.core.ResourceMethodInvoker;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HostAndPort;
import com.google.common.net.HttpHeaders;
import com.netflix.archaius.api.Property;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;
import com.turbospaces.http.UrlUtils;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.logging.HttpLoggingInterceptor.Level;

@Slf4j
@Priority(Priorities.USER + 4)
public class RecaptchaApplicationFilter extends AbstractContainerRequestFilter implements InitializingBean {
    private final AtomicReference<PlainServiceInfo> si = new AtomicReference<>();
    private final AtomicReference<PlainServiceInfo> invisibleSi = new AtomicReference<>();
    private final AtomicReference<String> reCaptchaBypassSecret = new AtomicReference<>();
    private final AtomicReference<String> invisibleReCaptchaBypassSecret = new AtomicReference<>();
    private final DynamicCloud cloud;
    private final ObjectMapper mapper;
    private OkHttpClient httpClient;
    private Property<List<String>> publicDomains;

    public RecaptchaApplicationFilter(ApplicationProperties props, DynamicCloud cloud, ObjectMapper mapper) {
        super(props);
        this.cloud = Objects.requireNonNull(cloud);
        this.mapper = Objects.requireNonNull(mapper);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        String domainsKey = props.CONNECTION_PUBLIC_DOMAINS.getKey();
        long connectTimeout = props.TCP_CONNECTION_TIMEOUT.get().toSeconds();
        long socketTimeout = props.TCP_SOCKET_TIMEOUT.get().toSeconds();
        boolean retry = props.HTTP_RETRY_REQUEST_ENABLED.get();

        publicDomains = props.factory().listOfStrings(domainsKey);

        OkHttpClient.Builder ok = new OkHttpClient.Builder();
        ok.connectTimeout(connectTimeout, TimeUnit.SECONDS);
        ok.readTimeout(socketTimeout, TimeUnit.SECONDS);
        ok.retryOnConnectionFailure(retry);

        //
        // ~ add corresponding level
        //
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(props.isDevMode() ? Level.BODY : Level.BASIC);
        ok.addInterceptor(logging);

        httpClient = ok.build();

        //
        // ~ subscribe for changes instead of doing lookup every time
        //
        cloud.serviceInfoByName(UPSs.INVISIBLE_RECAPTCHA).subscribe(serviceInfo -> {
            var plainServiceInfo = (PlainServiceInfo) serviceInfo;
            invisibleSi.set(plainServiceInfo);

            var reCaptchaBypassParamNameStr = props.API_RECAPTCHA_BYPASS_PARAM.get();
            try {
                var paramMap = UrlUtils.decodeQuery(plainServiceInfo.getQuery());
                var bypassRecaptcha = UrlUtils.getParam(reCaptchaBypassParamNameStr, paramMap);
                bypassRecaptcha.ifPresent(invisibleReCaptchaBypassSecret::set);
            } catch (Exception e) {
                log.warn("Can't parse invisible recaptcha {} parameter", reCaptchaBypassParamNameStr, e);
            }
        });
        cloud.serviceInfoByName(UPSs.RECAPTCHA).subscribe(serviceInfo -> {
            var plainServiceInfo = (PlainServiceInfo) serviceInfo;
            si.set(plainServiceInfo);

            var reCaptchaBypassParamNameStr = props.API_RECAPTCHA_BYPASS_PARAM.get();
            try {
                var paramMap = UrlUtils.decodeQuery(plainServiceInfo.getQuery());
                var bypassRecaptcha = UrlUtils.getParam(reCaptchaBypassParamNameStr, paramMap);
                bypassRecaptcha.ifPresent(reCaptchaBypassSecret::set);
            } catch (Exception e) {
                log.warn("Can't parse recaptcha {} parameter", reCaptchaBypassParamNameStr, e);
            }
        });
    }

    @Override
    public void filter(ContainerRequestContext req) throws IOException {
        var siteVerifyHeader = req.getHeaderString(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY);
        var invisibleSiteVerifyHeader = req.getHeaderString(HttpProto.HEADER_X_SITE_INVISIBLE_ONE_TIME_VERIFY);

        var canSkipReCaptcha = false;
        if (StringUtils.isNotEmpty(siteVerifyHeader)) {
            String val = reCaptchaBypassSecret.get();
            canSkipReCaptcha = Objects.equals(val, siteVerifyHeader);
        }

        var canSkipInvisibleReCaptcha = false;
        if (StringUtils.isNotEmpty(invisibleSiteVerifyHeader)) {
            String val = invisibleReCaptchaBypassSecret.get();
            canSkipInvisibleReCaptcha = Objects.equals(val, invisibleSiteVerifyHeader);
        }

        //
        // ~ value of the 'X-Site-OT-Verify' header equals the 'reCaptchaBypassSecret'
        // ~ value of the 'X-Site-Invisible-OT-Verify' header equals the 'invisibleReCaptchaBypassSecret'.
        //
        if (props.API_RECAPTCHA_BYPASS_ENABLED.get()) {
            if (BooleanUtils.or(new boolean[] { canSkipReCaptcha, canSkipInvisibleReCaptcha })) {
                return;
            }
        }

        //
        // ~ we should not perform any checks if there is no one time filter
        //
        List<String> domains = publicDomains.get();
        var hostHeader = HostAndPort.fromString(req.getHeaderString(HttpHeaders.HOST)).getHost();

        //
        // ~ if there are regular mTLS features present and it is UBER domain - we should skip and let other filters to handle it
        //
        if (StringUtils.isNotEmpty(req.getHeaderString(HttpProto.HEADER_X_SITE_VERIFY))) {
            if (CollectionUtils.isNotEmpty(domains)) {
                if (domains.contains(hostHeader)) {
                    return;
                }
            }
        }

        PostMatchContainerRequestContext ctx = (PostMatchContainerRequestContext) req;
        ResourceMethodInvoker invoker = ctx.getResourceMethod();
        MutableBoolean siteVerify = new MutableBoolean(false);

        //
        // ~ check on resource method level
        //
        Method method = invoker.getMethod();
        ApiEndpoint api = method.getAnnotation(ApiEndpoint.class);
        if (Objects.nonNull(api) && StringUtils.isNotEmpty(api.siteVerifyEnforceKey())) {
            if (props.SITE_VERIFY_ENFORCE_ENABLED.get(api.siteVerifyEnforceKey())) {
                log.trace("resource method: {} requires site verification", method);
                siteVerify.setTrue();
            }
        }

        //
        // ~ check globally on resource level
        //
        Class<?> resourceClass = invoker.getResourceClass();
        api = resourceClass.getAnnotation(ApiEndpoint.class);
        if (Objects.nonNull(api) && StringUtils.isNotEmpty(api.siteVerifyEnforceKey())) {
            if (props.SITE_VERIFY_ENFORCE_ENABLED.get(api.siteVerifyEnforceKey())) {
                log.trace("resource class: {} requires site verification", resourceClass);
                siteVerify.setTrue();
            }
        }

        //
        // ~ for any request if the token is present - we want to override
        //
        if (siteVerify.isFalse()) {
            if (StringUtils.isNotEmpty(siteVerifyHeader)) {
                siteVerify.setTrue();
            }
            if (StringUtils.isNotEmpty(invisibleSiteVerifyHeader)) {
                siteVerify.setTrue();
            }
        }

        var remoteIp = remoteIp(req);
        var psi = si.get();
        var ipsi = invisibleSi.get();
        var configuredUps = BooleanUtils.or(new boolean[] { Objects.nonNull(psi), Objects.nonNull(ipsi) });
        var mapBuilder = ImmutableMap.<String, PlainServiceInfo> builder();

        if (siteVerify.booleanValue() && configuredUps) {
            if (StringUtils.isNotEmpty(siteVerifyHeader)) {
                mapBuilder.put(siteVerifyHeader, psi);
            }
            if (StringUtils.isNotEmpty(invisibleSiteVerifyHeader)) {
                mapBuilder.put(invisibleSiteVerifyHeader, ipsi);
            }
        }
        var map = mapBuilder.build();

        var reportAbuse = props.CONNECTION_SITE_VERIFICATION_REPORT_TO_SENTRY.get();
        var maxHeaderLength = props.CONNECTION_SITE_VERIFICATION_HEADER_MAX_LENGTH.get();

        if (siteVerify.booleanValue()) {
            //
            // ~ so if we have manually configured domains in GIT, then we expect this X-Site-OT-Verify / X-Site-Invisible-OT-Verify to be present
            //
            if (!map.containsKey(siteVerifyHeader) && !map.containsKey(invisibleSiteVerifyHeader)) {
                log.warn("site verification token is missing for: {}", req.getUriInfo().getAbsolutePath());
                req.abortWith(noCache(Response.status(HttpResponseStatus.NOT_IMPLEMENTED.code())));
            }

            if (StringUtils.isNotEmpty(remoteIp)) {
                for (var entry : map.entrySet()) {
                    var serviceInfo = entry.getValue();
                    var verify = entry.getKey();

                    //
                    // ~ we don't want to store large headers in memory since it is potentially place for DDOS
                    //
                    if (StringUtils.isEmpty(verify) || verify.length() > maxHeaderLength) {
                        req.abortWith(noCache(Response.status(HttpResponseStatus.REQUEST_ENTITY_TOO_LARGE.code())));
                        return;
                    }

                    HttpUrl.Builder url = new HttpUrl.Builder();
                    url.scheme(serviceInfo.getScheme());
                    url.host(serviceInfo.getHost());
                    if (serviceInfo.getPort() > 0) {
                        url.port(serviceInfo.getPort());
                    }
                    url.addPathSegment("recaptcha").addPathSegment("api").addPathSegment("siteverify");

                    FormBody.Builder form = new FormBody.Builder()
                            .add("secret", serviceInfo.getPassword())
                            .add("response", verify)
                            .add("remoteip", remoteIp);

                    okhttp3.Request.Builder reqb = new okhttp3.Request.Builder().url(url.build()).post(form.build());

                    //
                    // ~ we don't want to block thread in which the filter is executed (we use ASYNC filter API)
                    //
                    ctx.suspend();
                    try {
                        Stopwatch stopwatch = Stopwatch.createStarted();
                        httpClient.newCall(reqb.build()).enqueue(new Callback() {
                            @Override
                            public void onResponse(Call call, okhttp3.Response response) {
                                try {
                                    if (response.isSuccessful()) {
                                        try (InputStream io = response.body().byteStream()) {
                                            JsonNode tree = mapper.readTree(io);
                                            stopwatch.stop();

                                            log.debug(
                                                    "got captcha verification result: {}, token: {}, uri: {},  took: {}",
                                                    tree.toString(),
                                                    verify,
                                                    req.getUriInfo().getAbsolutePath(),
                                                    stopwatch);

                                            //
                                            // ~ might be OK if 2 requests reach different nodes simultaneously
                                            //
                                            boolean success = tree.get("success").asBoolean();
                                            if (success) {
                                                ctx.resume();
                                            } else {
                                                ctx.abortWith(noCache(Response.status(HttpResponseStatus.FORBIDDEN.code())));
                                            }
                                        }
                                    } else {
                                        try (InputStream io = response.body().byteStream()) {
                                            if (reportAbuse) {
                                                log.error(
                                                        "unable to verify captcha token: {}, uri: {}, code: {}, body: {}",
                                                        verify,
                                                        req.getUriInfo().getAbsolutePath(),
                                                        response.code(),
                                                        IOUtils.toString(io, StandardCharsets.UTF_8));
                                            }
                                        }
                                        ctx.abortWith(noCache(Response.status(HttpResponseStatus.TOO_MANY_REQUESTS.code())));
                                    }
                                } catch (Exception err) {
                                    log.error(err.getMessage(), err);
                                    ctx.resume(err);
                                }
                            }

                            @Override
                            public void onFailure(Call call, IOException err) {
                                log.warn(err.getMessage(), err);
                                if (props.CONNECTION_SITE_VERIFICATION_PROPAGATE_IO_EXCEPTIONS.get()) {
                                    ctx.resume(err);
                                } else {
                                    ctx.resume();
                                }
                            }
                        });
                    } catch (Throwable err) {
                        log.error(err.getMessage(), err);
                        ctx.abortWith(noCache(Response.serverError()));
                    }
                }
            }
        }
    }
}
