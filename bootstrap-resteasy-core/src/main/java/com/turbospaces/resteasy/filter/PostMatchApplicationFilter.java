package com.turbospaces.resteasy.filter;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.jboss.resteasy.core.ResourceMethodInvoker;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;
import org.slf4j.MDC;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.resteasy.obfuscate.ObfuscateUtil;
import com.turbospaces.resteasy.printer.PostMatchHttpRequestPrinter;
import com.turbospaces.resteasy.printer.PostMatchHttpResponsePrinter;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.netty.util.Version;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;
import jakarta.ws.rs.container.ContainerResponseFilter;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Priority(Priorities.USER + 1)
public class PostMatchApplicationFilter extends AbstractContainerRequestFilter implements ContainerResponseFilter {
    private final MeterRegistry meterRegistry;
    private final ObjectMapper mapper;
    private final ObjectMapper xmlMapper;
    private final Map<String, Version> nettyInfo;

    public PostMatchApplicationFilter(ApplicationProperties props, MeterRegistry meterRegistry, ObjectMapper mapper, XmlMapper xmlMapper) {
        super(props);

        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.mapper = Objects.requireNonNull(mapper.copy().registerModule(ObfuscateUtil.LOGGING_OBFUSCATED_MODULE));
        this.xmlMapper = Objects.requireNonNull(xmlMapper.copy().registerModule(ObfuscateUtil.LOGGING_OBFUSCATED_MODULE));
        this.nettyInfo = Version.identify();
    }
    @Override
    public void filter(ContainerRequestContext req) throws IOException {
        PostMatchContainerRequestContext ctx = (PostMatchContainerRequestContext) req;
        ResourceMethodInvoker invoker = ctx.getResourceMethod();

        try {
            PostMatchHttpRequestPrinter requestPrinter = new PostMatchHttpRequestPrinter(props, mapper, xmlMapper);
            PostMatchHttpResponsePrinter responsePrinter = new PostMatchHttpResponsePrinter(props, mapper, xmlMapper);

            String methodCtx = (String) req.getProperty(HttpProto.toCtxName(MdcTags.MDC_METHOD));
            String traceIdCtx = extractTraceId(req, invoker).orElse((String) req.getProperty(HttpProto.toCtxName(MdcTags.MDC_TRACE_ID)));
            boolean doNotPrintResponseBody = doNotPrintResponse(invoker);
            String pathCxt = maskPath(invoker).orElse((String) req.getProperty(HttpProto.toCtxName(MdcTags.MDC_PATH)));

            req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_MATCHED), true); // ~ mark as matched
            req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_REQ_PRINTER), requestPrinter);
            req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_RESPONSE_PRINTER), responsePrinter);
            req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_RESPONSE_DO_NOT_PRINT_BODY), doNotPrintResponseBody);
            req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_METRICS_TAGS), getMetricsTags(invoker));

            if (Objects.nonNull(methodCtx)) {
                MDC.put(MdcTags.MDC_METHOD, methodCtx);
            }
            if (Objects.nonNull(traceIdCtx)) {
                MDC.put(MdcTags.MDC_TRACE_ID, traceIdCtx);
                req.setProperty(HttpProto.toCtxName(MdcTags.MDC_TRACE_ID), traceIdCtx);
            }
            if (Objects.nonNull(pathCxt)) {
                MDC.put(MdcTags.MDC_PATH, pathCxt);
            }

            requestPrinter.doOutPut(ctx); // ~ now log the whole body
        } catch (Throwable err) {
            throw new IOException(err);
        }
    }

    @Override
    public void filter(ContainerRequestContext req, ContainerResponseContext resp) throws IOException {
        Object traceId = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_TRACE_ID));
        Object remoteIp = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_REMOTE_IP));
        Object browser = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_BROWSER));
        Object asn = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_ASN));
        Object asnOrg = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_ASN_ORG));
        Object path = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_PATH));
        Object method = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_METHOD));
        Object startedAt = req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_STARTED_AT));

        if (resp.getStatus() > 0) {
            MDC.put(MdcTags.MDC_STATUS_CODE, Integer.toString(resp.getStatus()));
        }
        if (Objects.nonNull(traceId)) {
            MDC.put(MdcTags.MDC_TRACE_ID, traceId.toString());
        }
        if (Objects.nonNull(remoteIp)) {
            MDC.put(MdcTags.MDC_REMOTE_IP, remoteIp.toString());
        }
        if (Objects.nonNull(browser)) {
            MDC.put(MdcTags.MDC_BROWSER, browser.toString());
        }
        if (Objects.nonNull(asn)) {
            MDC.put(MdcTags.MDC_ASN, asn.toString());
        }
        if (Objects.nonNull(asnOrg)) {
            MDC.put(MdcTags.MDC_ASN_ORG, asnOrg.toString());
        }
        if (Objects.nonNull(path)) {
            MDC.put(MdcTags.MDC_PATH, path.toString());
        }
        if (Objects.nonNull(method)) {
            MDC.put(MdcTags.MDC_METHOD, method.toString());
        }
        if (Objects.nonNull(startedAt)) {
            MDC.put(MdcTags.MDC_TOOK, Long.toString(System.currentTimeMillis() - (long) startedAt));
        }

        //
        // ~ for local testing actually
        //
        if (props.isDevMode()) {
            resp.getHeaders().add(props.NETTY_VERSION_HTTP_HEADER_NAME.get(), nettyInfo);
        }

        //
        // ~ capture metrics and allow temporary blocking call in metrics
        //
        DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
            @Override
            public void run() {
                captureMetrics(req, resp);
            }
        });
    }

    protected Optional<String> maskPath(ResourceMethodInvoker invoker) {
        Optional<String> opt = Optional.empty();
        ApiEndpoint annotation = invoker.getMethod().getAnnotation(ApiEndpoint.class);

        if (Objects.nonNull(annotation)) {
            if (StringUtils.isNotEmpty(annotation.metricsUri())) {
                return Optional.of(annotation.metricsUri());
            }
        }

        return opt;
    }

    protected Optional<String> extractTraceId(ContainerRequestContext req, ResourceMethodInvoker invoker) {
        Method method = invoker.getMethod();
        Class<?> resourceClass = invoker.getResourceClass();

        Optional<String> opt = Optional.empty();
        ApiEndpoint annotation = Optional.ofNullable(method.getAnnotation(ApiEndpoint.class)).orElse(resourceClass.getAnnotation(ApiEndpoint.class));

        if (Objects.nonNull(annotation)) {
            if (StringUtils.isNotEmpty(annotation.traceHeader())) {
                String value = req.getHeaderString(annotation.traceHeader());
                if (StringUtils.isNotEmpty(value)) {
                    return Optional.of(value);
                }
            }
        }

        return opt;
    }

    protected boolean doNotPrintResponse(ResourceMethodInvoker invoker) {
        Method method = invoker.getMethod();
        Class<?> resourceClass = invoker.getResourceClass();
        boolean doNotPrintResponse = false;

        ApiEndpoint annotation = Optional.ofNullable(method.getAnnotation(ApiEndpoint.class)).orElse(resourceClass.getAnnotation(ApiEndpoint.class));
        if (Objects.nonNull(annotation)) {
            doNotPrintResponse = annotation.doNotPrintResponseBody();
        }

        return doNotPrintResponse;
    }

    public void captureMetrics(ContainerRequestContext req, ContainerResponseContext resp) {
        Object path = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_PATH));
        Object startedAt = req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_STARTED_AT));
        var metricsTags = (com.turbospaces.annotations.Tag[]) req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_METRICS_TAGS));

        if (Objects.nonNull(path) && Objects.nonNull(startedAt)) {
            String metric = props.HTTP_METRICS_LATENCY_KEY.get();
            long took = System.currentTimeMillis() - (long) startedAt;
            List<io.micrometer.core.instrument.Tag> tags = new ArrayList<>(Arrays.asList(
                    io.micrometer.core.instrument.Tag.of("path", path.toString()), //
                    io.micrometer.core.instrument.Tag.of("method", req.getMethod()), //
                    io.micrometer.core.instrument.Tag.of("status", Long.toString(resp.getStatus())) //
            ));
            if (Objects.nonNull(metricsTags) && metricsTags.length > 0) {
                tags.addAll(Arrays.stream(metricsTags)
                        .map(t -> io.micrometer.core.instrument.Tag.of(t.key(), t.value()))
                        .toList());
            }

            Timer timer = meterRegistry.timer(metric, tags);
            timer.record(took, TimeUnit.MILLISECONDS);
        }
    }

    protected Tag[] getMetricsTags(ResourceMethodInvoker invoker) {
        Method method = invoker.getMethod();
        Class<?> resourceClass = invoker.getResourceClass();
        Map<String, Tag> additionalTags = new HashMap<>();

        extractAdditionalTags(additionalTags, resourceClass.getAnnotation(ApiEndpoint.class));
        extractAdditionalTags(additionalTags, method.getAnnotation(ApiEndpoint.class));

        return additionalTags.values().toArray(new Tag[] {});
    }

    private static void extractAdditionalTags(Map<String, Tag> additionalTags, ApiEndpoint annotation) {
        if (Objects.nonNull(annotation)) {
            Arrays.stream(annotation.metricTags()).forEach(t -> additionalTags.put(t.key(), t));
        }
    }
}
