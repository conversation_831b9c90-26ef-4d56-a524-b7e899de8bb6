package com.turbospaces.resteasy.filter;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.apache.commons.net.util.SubnetUtils;
import org.jboss.resteasy.core.ResourceMethodInvoker;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.net.InetAddresses;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.cfg.ApplicationProperties;

import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Priority(Priorities.USER + 2)
public class IpWhitelistApplicationFilter extends AbstractContainerRequestFilter {
    public IpWhitelistApplicationFilter(ApplicationProperties props) {
        super(props);
    }
    @Override
    public void filter(ContainerRequestContext req) throws IOException {
        PostMatchContainerRequestContext ctx = (PostMatchContainerRequestContext) req;
        ResourceMethodInvoker invoker = ctx.getResourceMethod();

        Method method = invoker.getMethod();
        Class<?> resourceClass = invoker.getResourceClass();
        Set<String> keys = Sets.newHashSet();
        String remoteIp = remoteIp(req);

        if (StringUtils.isNotEmpty(remoteIp)) {
            // ~ method level
            ApiEndpoint api = method.getAnnotation(ApiEndpoint.class);
            if (Objects.nonNull(api)) {
                if (StringUtils.isNotEmpty(api.ipWhitelistKey())) {
                    keys.add(api.ipWhitelistKey());
                }
            }

            // ~ resource level
            api = resourceClass.getAnnotation(ApiEndpoint.class);
            if (Objects.nonNull(api)) {
                if (StringUtils.isNotEmpty(api.ipWhitelistKey())) {
                    keys.add(api.ipWhitelistKey());
                }
            }

            // ~ validate remote IP inclusion
            for (String key : keys) {
                var prop = props.factory().listOfStrings(key);
                var left = prop.get();
                var right = props.IP_WHITELIST.get(key);

                List<String> list = Lists.newArrayList();
                if (Objects.nonNull(left)) {
                    list.addAll(left);
                }
                if (Objects.nonNull(right)) {
                    list.addAll(right);
                }

                if (CollectionUtils.isNotEmpty(list)) {
                    boolean ok = false;

                    for (String allow : list) {
                        if (InetAddresses.isInetAddress(allow)) {
                            if (Strings.CS.equals(allow, remoteIp)) {
                                ok = true;
                            }
                        } else {
                            try {
                                SubnetUtils utils = new SubnetUtils(allow);
                                if (utils.getInfo().isInRange(remoteIp)) {
                                    ok = true;
                                }
                            } catch (Exception err) {
                                log.error(err.getMessage(), err);
                            }
                        }
                    }

                    if (BooleanUtils.isFalse(ok)) {
                        String entity = "Please contact system administator, remoteIp: " + remoteIp;
                        req.abortWith(noCache(Response.status(HttpResponseStatus.FORBIDDEN.code()).entity(entity)));
                    }
                }
            }
        }
    }
}
