package com.turbospaces.resteasy.filter;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jboss.resteasy.plugins.interceptors.CorsFilter;
import org.jboss.resteasy.resteasy_jaxrs.i18n.Messages;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.turbospaces.cfg.ApplicationProperties;

import jakarta.ws.rs.ForbiddenException;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.PreMatching;

@PreMatching
public class ReloadableCorsFilter extends CorsFilter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReloadableCorsFilter.class);
    private final ApplicationProperties props;

    public ReloadableCorsFilter(ApplicationProperties props) {
        this.props = props;

        maxAge();
        allowedOrigins();
        allowedHeaders();
        allowedMethods();
        exposedHeaders();

        LOGGER.info("CORS: {}", ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE));
    }

    @Override
    protected void checkOrigin(ContainerRequestContext requestContext, String origin) {
        if (!getAllowedOrigins().contains("*") && !getAllowedOrigins().contains(origin) && !isAllowedByPattern(origin)) {
            requestContext.setProperty("cors.failure", true);
            throw new ForbiddenException(Messages.MESSAGES.originNotAllowed(origin));
        }
    }

    private boolean isAllowedByPattern(String origin) {
        return props.CORS_MATCH_ORIGINS_BY_PATTERN.get() && getAllowedOrigins().stream().anyMatch(origin::matches);
    }

    protected void allowedOrigins() {
        List<String> origin = props.CORS_ALLOWED_ORIGINS.get();

        if (CollectionUtils.isEmpty(origin)) {
            origin = ImmutableList.of("*");
        }

        allowedOrigins = ImmutableSet.copyOf(origin);
        props.CORS_ALLOWED_ORIGINS.subscribe(new Consumer<List<String>>() {
            @Override
            public void accept(List<String> t) {
                if (CollectionUtils.isEmpty(t)) {
                    allowedOrigins = ImmutableSet.of("*");
                } else {
                    LOGGER.info("setting new origins: {}", t);
                    allowedOrigins = ImmutableSet.copyOf(t);
                }
            }
        });
    }

    protected void maxAge() {
        Duration maxAge = props.CORS_MAX_AGE.get();

        if (Objects.nonNull(maxAge)) {
            corsMaxAge = (int) maxAge.toSeconds();
        }

        props.CORS_MAX_AGE.subscribe(new Consumer<Duration>() {
            @Override
            public void accept(Duration t) {
                if (Objects.nonNull(t)) {
                    corsMaxAge = (int) t.toSeconds();
                } else {
                    corsMaxAge = -1;
                }
            }
        });
    }

    protected void allowedHeaders() {
        String delimiter = props.cfg().getListDelimiter();
        List<String> headers = props.CORS_ALLOWED_HEADERS.get();

        if (CollectionUtils.isNotEmpty(headers)) {
            setAllowedHeaders(Joiner.on(delimiter).join(headers));
        }
        props.CORS_ALLOWED_HEADERS.subscribe(new Consumer<List<String>>() {
            @Override
            public void accept(List<String> t) {
                if (CollectionUtils.isNotEmpty(t)) {
                    LOGGER.info("setting new allowed headers: {}", t);
                    setAllowedHeaders(Joiner.on(delimiter).join(t));
                } else {
                    setAllowedHeaders(null); // ~ reset to NULL specifically
                }
            }
        });
    }

    protected void allowedMethods() {
        String delimiter = props.cfg().getListDelimiter();
        List<String> methods = props.CORS_ALLOWED_METHODS.get();

        if (CollectionUtils.isNotEmpty(methods)) {
            setAllowedMethods(Joiner.on(delimiter).join(methods));
        }

        props.CORS_ALLOWED_METHODS.subscribe(new Consumer<List<String>>() {
            @Override
            public void accept(List<String> t) {
                if (CollectionUtils.isNotEmpty(t)) {
                    LOGGER.info("setting new allowed methods: {}", t);
                    setAllowedMethods(Joiner.on(delimiter).join(t));
                } else {
                    setAllowedMethods(null); // ~ reset to NULL specifically
                }
            }
        });
    }

    protected void exposedHeaders() {
        String delimiter = props.cfg().getListDelimiter();
        List<String> exposed = props.CORS_EXPOSED_HEADERS.get();

        if (CollectionUtils.isNotEmpty(exposed)) {
            setExposedHeaders(Joiner.on(delimiter).join(exposed));
        }

        props.CORS_EXPOSED_HEADERS.subscribe(new Consumer<List<String>>() {
            @Override
            public void accept(List<String> t) {
                if (CollectionUtils.isNotEmpty(t)) {
                    LOGGER.info("setting new exposed headers: {}", t);
                    setExposedHeaders(Joiner.on(delimiter).join(t));
                } else {
                    setExposedHeaders(null); // ~ reset to NULL specifically
                }
            }
        });
    }

    protected void setAllowCredentials(ApplicationProperties props) {
        boolean credentials = props.CORS_ALLOW_CREDENTIALS.get();
        setAllowCredentials(credentials);

        props.CORS_ALLOW_CREDENTIALS.subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean t) {
                LOGGER.info("setting new allow credentials: {}", t);
                setAllowCredentials(t);
            }
        });
    }
}
