package com.turbospaces.resteasy.filter;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.regex.Pattern;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.hibernate.validator.internal.engine.messageinterpolation.DefaultLocaleResolver;
import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.google.common.collect.ImmutableSet;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import com.google.common.net.HostAndPort;
import com.google.common.net.InetAddresses;
import com.maxmind.geoip2.DatabaseProvider;
import com.maxmind.geoip2.model.AsnResponse;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.geoip.GeoDatabaseType;
import com.turbospaces.geoip.MaxMindFactoryBean;
import com.turbospaces.http.HttpProto;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.resteasy.BlockhoundValidatorWrapper;
import com.turbospaces.resteasy.printer.PreMatchHttpRequestPrinter;
import com.turbospaces.resteasy.printer.PreMatchHttpResponsePrinter;

import io.netty.handler.codec.http.HttpHeaderNames;
import jakarta.annotation.Priority;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;
import jakarta.ws.rs.container.ContainerResponseFilter;
import jakarta.ws.rs.container.PreMatching;
import jakarta.ws.rs.core.Cookie;
import jakarta.ws.rs.core.NewCookie;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
@PreMatching
@Priority(Priorities.USER)
public class RootApplicationFilter extends AbstractContainerRequestFilter implements InitializingBean, ContainerResponseFilter {
    private MaxMindFactoryBean factory;
    private final BlockhoundValidatorWrapper validator;

    public RootApplicationFilter(ApplicationProperties props, DynamicCloud cloud, boolean enableAsn) {
        super(props);
        HibernateValidatorConfiguration config = Validation.byProvider(HibernateValidator.class).configure();
        Set<Locale> locales = ImmutableSet.of(Locale.ENGLISH, Locale.US, Locale.UK);
        ResourceBundleMessageInterpolator interpolator = new ResourceBundleMessageInterpolator(locales, Locale.getDefault(), new DefaultLocaleResolver(), true);
        try (ValidatorFactory validatorFactory = config.messageInterpolator(interpolator).ignoreXmlConfiguration().buildValidatorFactory()) {
            validator = new BlockhoundValidatorWrapper(validatorFactory.getValidator());
        }
        if (enableAsn) {
            factory = new MaxMindFactoryBean(props, cloud, GeoDatabaseType.ASN);
        }
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        if (Objects.nonNull(factory)) {
            factory.afterPropertiesSet();
        }
    }
    @Override
    public void destroy() throws Exception {
        super.destroy();
        if (Objects.nonNull(factory)) {
            factory.destroy();
        }
    }
    @Override
    public void filter(ContainerRequestContext req) throws IOException {
        String remoteIp = remoteIp(req);
        Map<String, Cookie> cookies = req.getCookies();

        //
        // ~ set provided validator
        //
        req.setProperty(Validator.class.getName(), validator);

        //
        // ~ copy content (GC overhead and not efficient use, but causes later problems with asynchronous processing)
        //
        if (req.hasEntity()) {
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                try (InputStream in = req.getEntityStream()) {
                    if (Objects.nonNull(in)) {
                        in.reset();

                        IOUtils.copy(in, out);
                        byte[] payload = out.toByteArray();
                        req.setEntityStream(new ByteArrayInputStream(payload));
                    }
                }
            }
        }

        //
        // ~ default trace
        //
        String traceId = req.getHeaderString(HttpProto.HEADER_X_TRACE_ID);
        if (StringUtils.isEmpty(traceId)) {
            traceId = PlatformUtil.randomUUID().toString();
        }
        req.setProperty(HttpProto.toCtxName(MdcTags.MDC_TRACE_ID), traceId);

        try {
            if (StringUtils.isNotEmpty(remoteIp)) {
                req.setProperty(HttpProto.toCtxName(MdcTags.MDC_REMOTE_IP), remoteIp);
                if (Objects.nonNull(factory)) {
                    DatabaseProvider db = factory.getObject();
                    InetAddress remoteIpAddress = InetAddresses.forString(HostAndPort.fromString(remoteIp).getHost());
                    db.tryAsn(remoteIpAddress).ifPresent(new Consumer<AsnResponse>() {
                        @Override
                        public void accept(AsnResponse asn) {
                            if (asn.getAutonomousSystemNumber() > 0) {
                                String value = asn.getAutonomousSystemNumber().toString();
                                req.getHeaders().add(HttpProto.HEADER_X_ASN, value);
                                req.setProperty(HttpProto.toCtxName(MdcTags.MDC_ASN), value);
                            }
                            if (StringUtils.isNotEmpty(asn.getAutonomousSystemOrganization())) {
                                String value = asn.getAutonomousSystemOrganization();
                                req.getHeaders().add(HttpProto.HEADER_X_ASN_ORG, value);
                            }
                        }
                    });
                }
            }
        } catch (Exception err) {
            log.error(err.getMessage(), err);
        }

        Cookie cookie = cookies.get(HttpProto.COOKIE_CF_TRACE);
        if (Objects.isNull(cookie)) {
            Hasher newHasher = Hashing.sha256().newHasher();
            newHasher.putLong(System.currentTimeMillis());
            newHasher.putString(req.toString(), StandardCharsets.UTF_8);
            if (StringUtils.isNotEmpty(remoteIp)) {
                newHasher.putString(remoteIp, StandardCharsets.UTF_8);
            }
            String browser = newHasher.hash().toString();
            req.setProperty(HttpProto.toCtxName(MdcTags.MDC_BROWSER), browser);
        } else {
            req.setProperty(HttpProto.toCtxName(MdcTags.MDC_BROWSER), cookie.getValue());
        }

        //
        // ~ default path masking
        //
        String path = req.getUriInfo().getPath();
        List<Pattern> mask = props.HTTP_METRICS_INBOUND_PATH_MASK.get();
        if (CollectionUtils.isNotEmpty(mask)) {
            for (Pattern p : mask) {
                if (p.asPredicate().test(req.getUriInfo().getPath())) {
                    path = p.pattern();
                    break;
                }
            }
        }

        PreMatchHttpRequestPrinter requestPrinter = new PreMatchHttpRequestPrinter(props);
        PreMatchHttpResponsePrinter responsePrinter = new PreMatchHttpResponsePrinter(props);

        req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_STARTED_AT), System.currentTimeMillis()); // ~ 100% defined
        req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_MATCHED), false); // ~ unmatched from the begging
        req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_REQ_PRINTER), requestPrinter); // ~ put default printer
        req.setProperty(HttpProto.toCtxName(HttpProto.CONTEXT_RESPONSE_PRINTER), responsePrinter); // ~ put default printer
        req.setProperty(HttpProto.toCtxName(MdcTags.MDC_PATH), path); // ~ 100% defined
        req.setProperty(HttpProto.toCtxName(MdcTags.MDC_METHOD), req.getMethod()); // ~ 100% defined
    }
    @Override
    public void filter(ContainerRequestContext req, ContainerResponseContext resp) {
        Object matchedCtx = req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_MATCHED));
        Object requestPrinterCtx = req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_REQ_PRINTER));
        Object responsePrinterCtx = req.getProperty(HttpProto.toCtxName(HttpProto.CONTEXT_RESPONSE_PRINTER));
        Object traceIdCtx = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_TRACE_ID));
        Object browserCtx = req.getProperty(HttpProto.toCtxName(MdcTags.MDC_BROWSER));

        //
        // ~ add HTTP response header
        //
        if (Objects.nonNull(traceIdCtx)) {
            resp.getHeaders().add(HttpProto.HEADER_X_TRACE_ID, traceIdCtx);
        }
        //
        // ~ echo HTTP encrypt header
        //
        if (props.HTTP_REQUEST_ENCRYPTION_ENABLED.get() && StringUtils.isNotEmpty(req.getHeaders().getFirst(HttpProto.HEADER_X_ENCRYPT))) {
            resp.getHeaders().add(HttpProto.HEADER_X_ENCRYPT, req.getHeaders().getFirst(HttpProto.HEADER_X_ENCRYPT));
            if (StringUtils.isNotEmpty(req.getHeaders().getFirst(HttpProto.HEADER_X_ALGORITHM))) {
                resp.getHeaders().add(HttpProto.HEADER_X_ALGORITHM, req.getHeaders().getFirst(HttpProto.HEADER_X_ALGORITHM));
            }
        }

        //
        // ~ log request (not a case in happy path scenario, method not found)
        //
        if (Objects.nonNull(matchedCtx)) {
            DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
                @Override
                public void run() {
                    PreMatchHttpRequestPrinter requestPrinter = new PreMatchHttpRequestPrinter(props);
                    if (Objects.nonNull(requestPrinterCtx)) {
                        requestPrinter = (PreMatchHttpRequestPrinter) requestPrinterCtx;
                    }
                    if (BooleanUtils.isFalse((boolean) matchedCtx)) {
                        requestPrinter.doOutPut(req);
                    }
                }
            });
        }

        //
        // ~ add tracking HTTP only cookie
        //
        if (Objects.nonNull(browserCtx)) {
            int days = PlatformUtil.toLocalUTCDate().getMonth().maxLength();
            NewCookie addCookie = addCookie(req, HttpProto.COOKIE_CF_TRACE, browserCtx.toString(), (int) TimeUnit.DAYS.toSeconds(days), true);
            resp.getHeaders().add(HttpHeaderNames.SET_COOKIE.toString(), addCookie);
        }

        //
        // ~ log response anyway just before cleaning all MDC(s)
        //
        if (Objects.nonNull(matchedCtx)) {
            DefaultBlockHoundIntegration.allowBlocking(new Runnable() {
                @Override
                public void run() {
                    PreMatchHttpResponsePrinter responsePrinter = new PreMatchHttpResponsePrinter(props);
                    if (Objects.nonNull(responsePrinterCtx)) {
                        responsePrinter = (PreMatchHttpResponsePrinter) responsePrinterCtx;
                    }
                    responsePrinter.doOutPut(req, resp);
                }
            });
        }

        MDC.remove(MdcTags.MDC_PATH);
        MDC.remove(MdcTags.MDC_METHOD);
        MDC.remove(MdcTags.MDC_TRACE_ID);
        MDC.remove(MdcTags.MDC_REMOTE_IP);
        MDC.remove(MdcTags.MDC_ASN);
        MDC.remove(MdcTags.MDC_ASN_ORG);
        MDC.remove(MdcTags.MDC_STATUS_CODE);
        MDC.remove(MdcTags.MDC_BROWSER);
        MDC.remove(MdcTags.MDC_TOOK);

        //
        // ~ remove all properties
        //
        req.removeProperty(HttpProto.CONTEXT_REQ_PRINTER);
        req.removeProperty(HttpProto.CONTEXT_RESPONSE_PRINTER);
        req.removeProperty(HttpProto.CONTEXT_RESPONSE_DO_NOT_PRINT_BODY);
        req.removeProperty(HttpProto.CONTEXT_MATCHED);
        req.removeProperty(HttpProto.CONTEXT_STARTED_AT);
        req.removeProperty(HttpProto.CONTEXT_URI);
        req.removeProperty(Validator.class.getName());
    }
}
