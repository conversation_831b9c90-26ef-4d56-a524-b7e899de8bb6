package com.turbospaces.resteasy.filter;

import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeoutException;
import java.util.function.BiFunction;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.resteasy.core.ResourceMethodInvoker;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;

import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableList;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import com.netflix.archaius.api.Property;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.service.MemcachedService;

import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Provider
@Priority(Priorities.USER + 6)
public class IpRateLimiterApiFilter extends AbstractContainerRequestFilter {
    public static final java.util.function.Function<String, String> GENERATE_BAN_KEY = new Function<>() {
        @Override
        public String apply(String remoteIp) {
            Hasher hasher = Hashing.sha256().newHasher();
            return hasher
                    .putString(remoteIp, StandardCharsets.UTF_8)
                    .putString("rate-limiter-ban", StandardCharsets.UTF_8)
                    .hash()
                    .toString();
        }
    };
    public static final java.util.function.BiFunction<String, String, String> GENERATE_COUNTER_KEY = new BiFunction<>() {
        @Override
        public String apply(String remoteIp, String endpoint) {
            Hasher hasher = Hashing.sha256().newHasher();
            return hasher
                    .putString(remoteIp, StandardCharsets.UTF_8)
                    .putString("rate-limiter-counter", StandardCharsets.UTF_8)
                    .putString(endpoint, StandardCharsets.UTF_8)
                    .hash()
                    .toString();
        }
    };

    private final MemcachedService memcachedService;
    private final Property<List<String>> whitelist;

    public IpRateLimiterApiFilter(ApplicationProperties props, MemcachedService memcachedService) {
        super(props);
        this.memcachedService = Objects.requireNonNull(memcachedService);
        whitelist = props.factory().listOfStrings(props.IP_RATE_LIMITER_WHITELIST.getKey());
    }

    @Override
    public void filter(ContainerRequestContext req) throws IOException {
        if (props.IP_RATE_LIMITER_ENABLED.get()) {
            String remoteIp = remoteIp(req);
            if (StringUtils.isNotEmpty(remoteIp)) {
                List<String> ips = whitelist.get();

                if (CollectionUtils.isNotEmpty(ips) && ips.contains(remoteIp)) {
                    return;
                }

                PostMatchContainerRequestContext suspendableRequestContext = (PostMatchContainerRequestContext) req;
                ResourceMethodInvoker invoker = suspendableRequestContext.getResourceMethod();
                Method method = invoker.getMethod();
                Class<?> resourceClass = invoker.getResourceClass();

                String resourceName = resourceClass.getSimpleName();
                String methodName = method.getName();

                ImmutableList.Builder<String> l = ImmutableList.builder();
                l.add(resourceName);
                l.add(resourceName + "." + methodName);

                //
                // ~ check on resource method level
                //
                ApiEndpoint api = method.getAnnotation(ApiEndpoint.class);
                if (Objects.nonNull(api) && StringUtils.isNotEmpty(api.rateLimiterKey())) {
                    l.add(api.rateLimiterKey());
                }

                //
                // ~ check globally on resource level
                //
                api = resourceClass.getAnnotation(ApiEndpoint.class);
                if (Objects.nonNull(api) && StringUtils.isNotEmpty(api.rateLimiterKey())) {
                    l.add(api.rateLimiterKey());
                }

                for (String it : l.build()) {
                    var key = "ip-rate-limiter." + it;
                    var enabled = props.cfg().getBoolean(key + ".enabled", Boolean.FALSE);

                    if (enabled) {
                        var banKey = GENERATE_BAN_KEY.apply(remoteIp);
                        var counterKey = GENERATE_COUNTER_KEY.apply(remoteIp, it);

                        //
                        // ~ Check if IP has been blacklisted - return immediately
                        //
                        try {
                            boolean forbidden = Objects.nonNull(memcachedService.get(banKey));
                            if (forbidden) {
                                log.debug("is banned by ip: {}, value: {}", remoteIp, forbidden);
                                req.abortWith(noCache(Response.status(HttpResponseStatus.FORBIDDEN.code())));
                                return;
                            }
                        } catch (TimeoutException err) {
                            req.abortWith(noCache(Response.status(HttpResponseStatus.GATEWAY_TIMEOUT.code())));
                            return;
                        } catch (Exception err) {
                            log.error(err.getMessage(), err);
                        }

                        Duration period = props.cfg().get(Duration.class, key + ".period", props.IP_RATE_LIMITER_PERIOD.get());
                        Duration cooldownPeriod = props.cfg().get(Duration.class, key + ".cooldown-period", props.IP_RATE_LIMITER_COOLDOWN_PERIOD.get());
                        Integer max = props.cfg().get(int.class, key + ".count", props.IP_RATE_LIMITER_COUNT.get());

                        //
                        // ~ increment the counter and set key expiration if not exists
                        //
                        try {
                            Stopwatch incrementStopWatch = Stopwatch.createStarted();
                            long current = memcachedService.increment(counterKey, (int) period.toSeconds());
                            incrementStopWatch.stop();
                            log.trace("incr key: {}, value: {}, took: {}", counterKey, current, incrementStopWatch);

                            //
                            // ~ Cool down the IP (ban) and set key expiration
                            //
                            if (current > max) {
                                Stopwatch banStopWatch = Stopwatch.createStarted();
                                memcachedService.set(banKey, (int) cooldownPeriod.toSeconds(), Boolean.TRUE.toString());
                                banStopWatch.stop();

                                log.debug("set key: {}, value: {}, took: {}", banKey, Boolean.TRUE, banStopWatch);
                                req.abortWith(noCache(Response.status(HttpResponseStatus.TOO_MANY_REQUESTS.code())));
                                break;
                            }
                        } catch (TimeoutException err) {
                            req.abortWith(noCache(Response.status(HttpResponseStatus.GATEWAY_TIMEOUT.code())));
                        } catch (Exception err) {
                            log.error(err.getMessage(), err);
                        }
                    }
                }
            }
        }
    }
}
