package com.turbospaces.resteasy.filter;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.jboss.resteasy.core.ResourceMethodInvoker;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.Provider;
import lombok.RequiredArgsConstructor;

@Provider
@Priority(Priorities.USER + 5)
@RequiredArgsConstructor
public class UriPathRateLimiterApiFilter implements ContainerRequestFilter {
    private final ApplicationProperties props;
    private final RateLimiterRegistry rateLimiterRegistry;

    @Override
    public void filter(ContainerRequestContext requestContext) {
        if (!props.API_RATE_LIMIT_ENABLED.get()) {
            return;
        }

        PostMatchContainerRequestContext suspendableRequestContext = (PostMatchContainerRequestContext) requestContext;
        ResourceMethodInvoker invoker = suspendableRequestContext.getResourceMethod();

        // ~ method level
        Method method = invoker.getMethod();
        ApiEndpoint api = method.getAnnotation(ApiEndpoint.class);
        if (Objects.nonNull(api) && StringUtils.isNotEmpty(api.rateLimiterKey())) {
            testLimit(suspendableRequestContext, api.rateLimiterKey());
            return;
        }

        // ~ resource level
        Class<?> resourceClass = invoker.getResourceClass();
        api = resourceClass.getAnnotation(ApiEndpoint.class);
        if (Objects.nonNull(api) && StringUtils.isNotEmpty(api.rateLimiterKey())) {
            testLimit(suspendableRequestContext, api.rateLimiterKey());
            return;
        }

        String resourceName = resourceClass.getSimpleName();
        String methodName = method.getName();
        String qualifiedMethodName = resourceName + "." + methodName;
        testLimit(suspendableRequestContext, qualifiedMethodName);
    }

    private void testLimit(PostMatchContainerRequestContext suspendableRequestContext, String key) {
        String rateLimiterKey = "rate-limiter." + key;

        boolean rateLimiterApiEnabled = props.RATE_LIMITER_ENABLED.get(key);
        if (!rateLimiterApiEnabled) {
            return;
        }
        var cfg = rateLimiterRegistry.getConfiguration(rateLimiterKey).orElseGet(() -> {
            RateLimiterConfig rateLimiterConfig = createConfig(props, key);
            rateLimiterRegistry.addConfiguration(rateLimiterKey, rateLimiterConfig);
            return rateLimiterConfig;
        });

        if (!rateLimiterRegistry.rateLimiter(rateLimiterKey, cfg).acquirePermission()) {
            suspendableRequestContext
                    .abortWith(Response.status(HttpResponseStatus.TOO_MANY_REQUESTS.code()).build());
        }
    }

    private static RateLimiterConfig createConfig(ApplicationProperties props, String scope) {
        Duration period = props.RATE_LIMITER_PERIOD.get(scope);
        Integer count = props.RATE_LIMITER_COUNT.get(scope);
        Duration timeout = props.RATE_LIMITER_TIMEOUT.get(scope);
        return RateLimiterConfig.custom()
                .limitRefreshPeriod(period)
                .limitForPeriod(count)
                .timeoutDuration(timeout)
                .build();
    }
}
