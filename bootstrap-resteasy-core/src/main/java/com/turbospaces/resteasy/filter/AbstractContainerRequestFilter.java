package com.turbospaces.resteasy.filter;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Date;
import java.util.Iterator;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.DisposableBean;

import com.google.common.base.Splitter;
import com.google.common.net.HostAndPort;
import com.google.common.net.HttpHeaders;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;

import io.netty.handler.codec.http.HttpHeaderNames;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.core.Cookie;
import jakarta.ws.rs.core.NewCookie;
import jakarta.ws.rs.core.NewCookie.SameSite;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.ResponseBuilder;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class AbstractContainerRequestFilter implements ContainerRequestFilter, DisposableBean {
    protected final ApplicationProperties props;

    @Override
    public void destroy() throws Exception {

    }
    protected String remoteIp(ContainerRequestContext req) {
        var realClientHost = req.getHeaderString(HttpProto.HEADER_X_CLIENT_REAL_IP);
        if (StringUtils.isEmpty(realClientHost)) {
            realClientHost = req.getHeaderString(HttpProto.HEADER_X_VERCEL_CONNECTING_IP);
        }
        var host = StringUtils.isNotEmpty(realClientHost) ? realClientHost : req.getHeaderString(HttpProto.HEADER_CF_CONNECTING_IP);

        if (StringUtils.isEmpty(host)) {
            host = req.getHeaderString(HttpHeaders.X_FORWARDED_FOR);
            if (host != null) {
                Iterator<String> it = Splitter.on(',').omitEmptyStrings().split(host).iterator();
                if (it.hasNext()) {
                    host = it.next();
                }
            }
        }

        if (StringUtils.isNotEmpty(host)) {
            if (PlatformUtil.isLocalHost(host)) {
                host = props.externalIp();
            }
        }

        return host;
    }
    protected NewCookie addCookie(ContainerRequestContext req, String name, String value, int maxAge, boolean httpOnly) {
        String domain = "localhost";

        String host = req.getHeaderString(HttpHeaderNames.HOST.toString());
        String referer = req.getHeaderString(HttpHeaderNames.REFERER.toString());
        HostAndPort hp = HostAndPort.fromString(host);
        host = hp.getHost();

        int idx = host.indexOf('.');
        if (idx > 0) {
            domain = "." + host.substring(idx + 1);
        }

        String path = "/";
        boolean secure = true;

        if (props.isDevMode()) {
            secure = false;

            try {
                if (StringUtils.isNotEmpty(referer)) {
                    URL url = new URI(referer).toURL();
                    String protocol = url.getProtocol();
                    secure = "https".equalsIgnoreCase(protocol);
                }
            } catch (MalformedURLException | URISyntaxException err) {
                log.warn(err.getMessage(), err);
            }
        }

        Date expireAt = maxAge > 0 ? DateUtils.addSeconds(new Date(), maxAge) : new Date(0);
        return new NewCookie.Builder(name)
                .value(value)
                .path(path)
                .domain(domain)
                .version(Cookie.DEFAULT_VERSION)
                .maxAge(maxAge)
                .expiry(expireAt)
                .secure(secure)
                .httpOnly(httpOnly)
                .sameSite(SameSite.NONE)
                .build();
    }
    protected static Response noCache(ResponseBuilder builder) {
        builder.header(HttpHeaderNames.CACHE_CONTROL.toString(), "must-revalidate,no-cache,no-store");
        builder.header(HttpHeaderNames.PRAGMA.toString(), "no-cache");
        builder.header(HttpHeaderNames.EXPIRES.toString(), 0);
        return builder.build();
    }
}
