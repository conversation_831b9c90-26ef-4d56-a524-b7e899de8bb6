package com.turbospaces.resteasy.filter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.apache.commons.lang3.mutable.MutableObject;
import org.jboss.resteasy.core.interception.jaxrs.PostMatchContainerRequestContext;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Stopwatch;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalCause;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.google.common.collect.Sets;
import com.google.common.hash.Hashing;
import com.google.common.net.HostAndPort;
import com.google.common.net.HttpHeaders;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.service.MemcachedService;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics;
import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.exception.MemcachedException;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.logging.HttpLoggingInterceptor.Level;

@Slf4j
@Priority(Priorities.USER + 3)
public class TurnstilemTLSApplicationFilter extends AbstractContainerRequestFilter implements InitializingBean {
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final EnumSet<RemovalCause> REASONS = EnumSet.of(RemovalCause.EXPIRED, RemovalCause.SIZE);

    public static final Function<String, String> GENERATE_VERIFICATION_CACHE_KEY = new Function<>() {
        @Override
        public String apply(String token) {
            return Hashing.sha512().newHasher()
                    .putString(token, StandardCharsets.UTF_8)
                    .putString("turnstile-verification", StandardCharsets.UTF_8)
                    .hash()
                    .toString();
        }
    };
    public static final BiFunction<String, String, String> GENERATE_IP_PATH_TRACKING_KEY = new BiFunction<>() {
        @Override
        public String apply(String token, String suffix) {
            return Hashing.sha512().newHasher()
                    .putString(token, StandardCharsets.UTF_8)
                    .putString("turnstile-tracking", StandardCharsets.UTF_8)
                    .putString(suffix, StandardCharsets.UTF_8)
                    .hash()
                    .toString();
        }
    };
    public static final BiFunction<String, String, String> GENERATE_IDEMPOTENCY_KEY = new BiFunction<>() {
        @Override
        public String apply(String token, String suffix) {
            return Hashing.sha512()
                    .newHasher()
                    .putString(token, StandardCharsets.UTF_8)
                    .putString(suffix, StandardCharsets.UTF_8)
                    .hash()
                    .toString();
        }
    };

    private final AtomicReference<PlainServiceInfo> si = new AtomicReference<>();
    private final ScheduledExecutorService timer;
    private final DynamicCloud cloud;
    private final MeterRegistry meterRegistry;
    private final ObjectMapper mapper;
    private OkHttpClient httpClient;
    private Property<List<String>> publicDomains;
    private Property<List<String>> userAgents;
    private Property<List<String>> clientCertsSha256;
    private Cache<String, Integer> cache; // ~ token <-> status mapping
    private LoadingCache<String, Set<String>> cacheIps; // ~ token <-> unique IPs
    private final MemcachedService memcachedService;

    public TurnstilemTLSApplicationFilter(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            ObjectMapper mapper,
            MemcachedService memcachedService) {
        super(props);
        this.cloud = Objects.requireNonNull(cloud);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.mapper = Objects.requireNonNull(mapper);
        this.memcachedService = Objects.requireNonNull(memcachedService);

        //
        // ~ just one thread
        //
        this.timer = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        });
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        String domainsKey = props.CONNECTION_PUBLIC_DOMAINS.getKey();
        String userAgentsKey = props.CONNECTION_USER_AGENT_WHITELIST.getKey();
        String clientCertSha256Key = props.CONNECTION_CLIENT_CERT_SHA256_WHITELIST.getKey();
        long connectTimeout = props.TCP_CONNECTION_TIMEOUT.get().toSeconds();
        long socketTimeout = props.TCP_SOCKET_TIMEOUT.get().toSeconds();
        boolean retry = props.HTTP_RETRY_REQUEST_ENABLED.get();
        int cacheMaxSize = props.CACHE_DEFAULT_MAX_SIZE.get();
        Duration cacheMaxIdle = props.CACHE_DEFAULT_MAX_IDLE.get();
        Duration cacheMaxTTL = props.CACHE_DEFAULT_MAX_TTL.get();
        Duration cleanupInterval = props.APP_TIMER_INTERVAL.get();

        publicDomains = props.factory().listOfStrings(domainsKey);
        userAgents = props.factory().listOfStrings(userAgentsKey);
        clientCertsSha256 = props.factory().listOfStrings(clientCertSha256Key);

        OkHttpClient.Builder ok = new OkHttpClient.Builder();
        ok.connectTimeout(connectTimeout, TimeUnit.SECONDS);
        ok.readTimeout(socketTimeout, TimeUnit.SECONDS);
        ok.retryOnConnectionFailure(retry);

        //
        // ~ add corresponding level
        //
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(props.isDevMode() ? Level.BODY : Level.BASIC);
        ok.addInterceptor(logging);

        httpClient = ok.build();

        //
        // ~ subscribe for changes instead of doing lookup every time
        //
        cloud.serviceInfoByName(UPSs.TURNSTILE).subscribe(new Consumer<ServiceInfo>() {
            @Override
            public void accept(ServiceInfo t) {
                si.set((PlainServiceInfo) t);
            }
        });

        //
        // ~ local near cache
        //
        cache = CacheBuilder.newBuilder()
                .expireAfterWrite(cacheMaxIdle)
                .maximumSize(cacheMaxSize)
                .removalListener(new RemovalListener<String, Integer>() {
                    @Override
                    public void onRemoval(RemovalNotification<String, Integer> notification) {
                        if (REASONS.contains(notification.getCause())) {
                            String type = notification.getCause().name().toLowerCase().intern();
                            log.trace("onRemoval({}): {}", notification.getKey(), type);
                        }
                    }
                }).build();

        cacheIps = CacheBuilder.newBuilder()
                .expireAfterWrite(cacheMaxTTL)
                .maximumSize(cacheMaxSize * Runtime.getRuntime().availableProcessors())
                .removalListener(new RemovalListener<String, Set<String>>() {
                    @Override
                    public void onRemoval(RemovalNotification<String, Set<String>> notification) {
                        if (REASONS.contains(notification.getCause())) {
                            String type = notification.getCause().name().toLowerCase().intern();
                            log.trace("onRemoval({}): {}", notification.getKey(), type);
                        }
                    }
                }).build(new CacheLoader<String, Set<String>>() {
                    @Override
                    public Set<String> load(String key) throws Exception {
                        return Sets.newConcurrentHashSet();
                    }
                });

        new GuavaCacheMetrics<>(cache, "invisible-captcha-filter", Collections.emptyList()).bindTo(meterRegistry);
        new GuavaCacheMetrics<>(cacheIps, "invisible-captcha-filter-ips", Collections.emptyList()).bindTo(meterRegistry);

        timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                long size = cache.size();
                if (size > 0) {
                    log.debug("about to cleanUp captcha cache of {} items ...", size);
                }
                cache.cleanUp();

                long ipsSize = cacheIps.size();
                if (ipsSize > 0) {
                    log.debug("about to cleanUp captcha ips cache of {} items ...", ipsSize);
                }
                cacheIps.cleanUp();
            }
        }, cleanupInterval.toSeconds(), cleanupInterval.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
        } finally {
            PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
        }
    }
    @Override
    public void filter(ContainerRequestContext req) throws IOException {
        PostMatchContainerRequestContext ctx = (PostMatchContainerRequestContext) req;

        var userAgentHeader = req.getHeaderString(HttpHeaders.USER_AGENT);
        var clientCertSha256Header = req.getHeaderString(HttpProto.HEADER_CF_CLIENT_CERT_SHA256);
        var siteVerifyHeader = req.getHeaderString(HttpProto.HEADER_X_SITE_VERIFY);
        var headerToValidate = StringUtils.isNotEmpty(siteVerifyHeader) ? siteVerifyHeader : req.getHeaderString(HttpProto.HEADER_X_SITE_INVISIBLE_VERIFY);
        var hostHeader = HostAndPort.fromString(req.getHeaderString(HttpHeaders.HOST)).getHost();

        MutableBoolean siteVerify = new MutableBoolean(false);

        PlainServiceInfo psi = si.get();
        String remoteIp = remoteIp(req);
        Collection<String> domains = publicDomains.get();
        Collection<String> agents = userAgents.get();
        Collection<String> clientCerts = clientCertsSha256.get();

        //
        // ~ and then check if this per global host verification (unprotected from DDOS URL)
        //
        if (CollectionUtils.isNotEmpty(domains)) {
            siteVerify.setValue(domains.contains(hostHeader));
        }

        //
        // ~ for any request if the token is present - we want to override
        //
        if (siteVerify.isFalse() && StringUtils.isNotEmpty(headerToValidate)) {
            siteVerify.setTrue();
        }

        boolean reportAbuse = props.CONNECTION_SITE_VERIFICATION_REPORT_TO_SENTRY.get();
        int maxHeaderLenght = props.CONNECTION_SITE_VERIFICATION_HEADER_MAX_LENGTH.get();
        int uniqueIpsMax = props.CONNECTION_SITE_VERIFICATION_UNIQUE_IPS_MAX.get();
        int uniqueRequestsPerPathMax = props.CONNECTION_SITE_VERIFICATION_UNIQUE_REQUESTS_PER_PATH_MAX.get();

        if (siteVerify.booleanValue() && Objects.nonNull(psi)) {
            //
            // ~ allow certain Header(s)/client certificates to be accepted
            //
            if (CollectionUtils.isNotEmpty(agents) && agents.contains(userAgentHeader)) {
                return;
            }
            if (CollectionUtils.isNotEmpty(clientCerts) && clientCerts.contains(clientCertSha256Header)) {
                return;
            }

            //
            // ~ so if we have manually configured domains in GIT, then we expect this X-Site-Verify to be present
            //
            if (StringUtils.isEmpty(headerToValidate)) {
                if (reportAbuse) {
                    log.warn("site verification token is missing for: {}", req.getUriInfo().getAbsolutePath());
                }
                req.abortWith(noCache(Response.status(HttpResponseStatus.NOT_IMPLEMENTED.code())));
                return;
            }

            if (StringUtils.isNotEmpty(headerToValidate) && StringUtils.isNotEmpty(remoteIp)) {
                //
                // ~ we don't want to store large headers in memory since it is potentially place for DDOS
                //
                if (headerToValidate.length() > maxHeaderLenght) {
                    req.abortWith(noCache(Response.status(HttpResponseStatus.REQUEST_ENTITY_TOO_LARGE.code())));
                    return;
                }

                //
                // ~ we don't want to accept tokens more than from N unique IP(s)
                //
                long uniqueIps = addAndReturnNumberOfUniqueRemoteIps(headerToValidate, remoteIp);
                if (uniqueIps > uniqueIpsMax) {
                    log.warn("site verification: {} has been used from: {} ip(s) which exceeded limit of: {}, remoteIp: {}",
                            headerToValidate,
                            uniqueIps,
                            uniqueIpsMax,
                            remoteIp);
                    req.abortWith(noCache(Response.status(HttpResponseStatus.FORBIDDEN.code())));
                    return;
                }

                //
                // ~ we don't want to accept more than from N unique requests per path(s)
                //
                try {
                    long uniqueInvocations = addAndReturnNumberOfUniqueInvocations(headerToValidate, req.getUriInfo());
                    if (uniqueInvocations > uniqueRequestsPerPathMax) {
                        log.warn("site verification: {} for path: {} has been used: {} times which exceeded limit of: {}, remoteIp: {}",
                                headerToValidate,
                                req.getUriInfo().getPath(),
                                uniqueInvocations,
                                uniqueRequestsPerPathMax,
                                remoteIp);
                        req.abortWith(noCache(Response.status(HttpResponseStatus.TOO_MANY_REQUESTS.code())));
                        return;
                    }
                } catch (TimeoutException err) {
                    req.abortWith(noCache(Response.status(HttpResponseStatus.GATEWAY_TIMEOUT.code())));
                    return;
                } catch (Exception err) {
                    log.error(err.getMessage(), err);
                }

                //
                // ~ maybe we can quickly validate it locally
                //
                String asString = GENERATE_VERIFICATION_CACHE_KEY.apply(headerToValidate);
                Integer cacheStatus = cache.getIfPresent(asString);
                if (Objects.nonNull(cacheStatus)) {
                    if (cacheStatus.equals(HttpResponseStatus.OK.code())) {
                        return;
                    }
                    req.abortWith(noCache(Response.status(cacheStatus)));
                    return;
                }
                try {
                    //
                    // ~ maybe load from remote cache and then cache locally if necessary
                    //
                    cacheStatus = cache.get(asString, () -> {
                        Integer value = memcachedService.get(asString);
                        if (Objects.isNull(value)) {
                            throw new IllegalArgumentException();
                        }
                        return value;
                    });

                    //
                    // ~ re-check
                    //
                    if (Objects.nonNull(cacheStatus)) {
                        if (cacheStatus.equals(HttpResponseStatus.OK.code())) {
                            return;
                        }
                        req.abortWith(noCache(Response.status(cacheStatus)));
                        return;
                    }
                } catch (Exception err) {
                    Throwable cause = ExceptionUtils.getRootCause(err);
                    if (Objects.isNull(cause)) {
                        cause = err;
                    }

                    if (BooleanUtils.isFalse(cause instanceof IllegalArgumentException)) {
                        log.error(cause.getMessage(), cause);
                        req.abortWith(noCache(Response.serverError()));
                        return;
                    }
                }

                HttpUrl.Builder url = new HttpUrl.Builder();
                url.scheme(psi.getScheme());
                url.host(psi.getHost());
                if (psi.getPort() > 0) {
                    url.port(psi.getPort());
                }
                url.addPathSegment("turnstile").addPathSegment("v0").addPathSegment("siteverify");

                ObjectNode data = mapper.createObjectNode();
                data.put("secret", psi.getPassword());
                data.put("response", headerToValidate);
                data.put("remoteip", remoteIp);

                //
                // ~ we want to be able to correctly handle situation with concurrent HTTP request by same token
                //
                MutableObject<UUID> idempotencyKey = new MutableObject<>();
                try {
                    getOrGenerateIdempotencyKey(headerToValidate).ifPresent(new Consumer<UUID>() {
                        @Override
                        public void accept(UUID key) {
                            idempotencyKey.setValue(key);
                            data.put("idempotency_key", key.toString());
                        }
                    });
                } catch (TimeoutException err) {
                    req.abortWith(noCache(Response.status(HttpResponseStatus.GATEWAY_TIMEOUT.code())));
                    return;
                } catch (Exception err) {
                    log.error(err.getMessage(), err);
                    req.abortWith(noCache(Response.status(HttpResponseStatus.INTERNAL_SERVER_ERROR.code())));
                    return;
                }

                okhttp3.Request.Builder reqb = new okhttp3.Request.Builder().url(url.build()).post(RequestBody.create(data.toString(), JSON));

                //
                // ~ we don't want to block thread in which the filter is executed (we use ASYNC filter API)
                //
                ctx.suspend();
                try {
                    Stopwatch stopwatch = Stopwatch.createStarted();
                    httpClient.newCall(reqb.build()).enqueue(new Callback() {
                        @Override
                        public void onResponse(Call call, okhttp3.Response response) throws IOException {
                            try (ResponseBody body = response.body()) {
                                if (response.isSuccessful()) {
                                    JsonNode tree = mapper.readTree(body.bytes());
                                    stopwatch.stop();

                                    log.debug("got captcha verification result: {}, token: {}, uri: {}, idempotencyKey: {},  took: {}",
                                            tree.toString(),
                                            headerToValidate,
                                            req.getUriInfo().getAbsolutePath(),
                                            idempotencyKey.get(),
                                            stopwatch);

                                    //
                                    // ~ might be OK if 2 requests reach different nodes simultaneously
                                    //
                                    boolean success = tree.get("success").asBoolean();
                                    if (success) {
                                        putCacheEntry(asString, HttpResponseStatus.OK.code());
                                        ctx.resume();
                                    } else {
                                        int status = putCacheEntry(asString, HttpResponseStatus.FORBIDDEN.code());
                                        if (HttpResponseStatus.OK.code() == status) {
                                            ctx.resume();
                                        } else {
                                            ctx.abortWith(noCache(Response.status(status)));
                                        }
                                    }
                                } else {
                                    if (reportAbuse) {
                                        log.error("unable to verify captcha token: {}, uri: {}, request body: {}, response code: {}, response body: {}",
                                                headerToValidate,
                                                req.getUriInfo().getAbsolutePath(),
                                                data,
                                                response.code(),
                                                new String(body.bytes(), StandardCharsets.UTF_8));
                                    }

                                    int status = putCacheEntry(asString, HttpResponseStatus.TOO_MANY_REQUESTS.code());
                                    if (HttpResponseStatus.OK.code() == status) {
                                        ctx.resume();
                                    } else {
                                        ctx.abortWith(noCache(Response.status(status)));
                                    }
                                }
                            } catch (Exception err) {
                                log.error(err.getMessage(), err);
                                ctx.resume(err);
                            }
                        }
                        @Override
                        public void onFailure(Call call, IOException err) {
                            log.warn(err.getMessage(), err);
                            if (props.CONNECTION_SITE_VERIFICATION_PROPAGATE_IO_EXCEPTIONS.get()) {
                                ctx.resume(err);
                            } else {
                                ctx.resume();
                            }
                        }
                    });
                } catch (Throwable err) {
                    log.error(err.getMessage(), err);
                    ctx.abortWith(noCache(Response.serverError()));
                }
            }
        }
    }
    private long addAndReturnNumberOfUniqueRemoteIps(String siteVerify, String ip) {
        String cacheKey = GENERATE_IP_PATH_TRACKING_KEY.apply(siteVerify, "ips");

        Set<String> set = cacheIps.getUnchecked(cacheKey);
        set.add(ip);
        return set.size();
    }
    private long addAndReturnNumberOfUniqueInvocations(String siteVerify, UriInfo uri) throws TimeoutException, InterruptedException, MemcachedException {
        String cacheKey = GENERATE_IP_PATH_TRACKING_KEY.apply(siteVerify, uri.getPath());

        Stopwatch stopWatch = Stopwatch.createStarted();
        Duration cacheMaxIdle = props.CACHE_DEFAULT_MAX_TTL.get();
        long count = memcachedService.incrementAndTouch(cacheKey, (int) cacheMaxIdle.toSeconds());
        stopWatch.stop();

        log.info("incr key: {} for: {}, value: {}, took: {}", cacheKey, siteVerify, count, stopWatch);
        return count;
    }
    private Optional<UUID> getOrGenerateIdempotencyKey(String siteVerify) throws TimeoutException, InterruptedException, MemcachedException {
        String cacheKey = GENERATE_IDEMPOTENCY_KEY.apply(siteVerify, "idempotence");

        UUID uuid = PlatformUtil.randomUUID();
        Stopwatch stopWatch = Stopwatch.createStarted();
        Duration cacheMaxIdle = props.CACHE_DEFAULT_MAX_IDLE.get();

        boolean success = memcachedService.add(cacheKey, (int) cacheMaxIdle.toSeconds(), uuid.toString());
        if (BooleanUtils.isFalse(success)) {
            uuid = UUID.fromString(memcachedService.get(cacheKey));
        }

        stopWatch.stop();
        log.info("setGet idempotence key: {} for: {}, ttl: {} sec(s), took: {}", cacheKey, siteVerify, cacheMaxIdle, stopWatch);
        return Optional.of(uuid);
    }
    private int putCacheEntry(String cacheKey, Integer status) throws TimeoutException, InterruptedException, MemcachedException {
        boolean isOK = status.equals(HttpResponseStatus.OK.code());

        if (isOK) {
            //
            // ~ 200 we simply put w/o not-exist notation
            //
            Stopwatch stopWatch = Stopwatch.createStarted();
            Duration cacheMaxTtl = props.CACHE_DEFAULT_MAX_TTL.get();

            memcachedService.set(cacheKey, (int) cacheMaxTtl.toSeconds(), status);
            cache.put(cacheKey, status);
            stopWatch.stop();

            return status.intValue();
        }

        Stopwatch stopWatch = Stopwatch.createStarted();
        Duration cacheMaxIdle = props.CACHE_DEFAULT_MAX_IDLE.get();

        memcachedService.add(cacheKey, (int) cacheMaxIdle.toSeconds(), status);
        cache.put(cacheKey, status);
        stopWatch.stop();

        return status;
    }
}
