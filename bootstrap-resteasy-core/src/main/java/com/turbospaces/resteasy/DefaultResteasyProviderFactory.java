package com.turbospaces.resteasy;

import org.jboss.resteasy.core.InjectorFactoryImpl;
import org.jboss.resteasy.core.MethodInjectorImpl;
import org.jboss.resteasy.core.providerfactory.ResteasyProviderFactoryImpl;
import org.jboss.resteasy.spi.ApplicationException;
import org.jboss.resteasy.spi.Failure;
import org.jboss.resteasy.spi.HttpRequest;
import org.jboss.resteasy.spi.HttpResponse;
import org.jboss.resteasy.spi.MethodInjector;
import org.jboss.resteasy.spi.ResteasyProviderFactory;
import org.jboss.resteasy.spi.metadata.ResourceLocator;

import com.turbospaces.cfg.ApplicationProperties;

import reactor.blockhound.BlockingOperationError;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class DefaultResteasyProviderFactory extends ResteasyProviderFactoryImpl {
    public DefaultResteasyProviderFactory(ApplicationProperties props) {
        super.registerBuiltin();

        //
        // ~ we want to supply custom method injector so that once it is called, we enforce non-blocking execution manner
        //
        if (props.APP_BLOCKHOUND_ENABLED.get()) {
            setInjectorFactory(new InjectorFactoryImpl() {
                @Override
                public MethodInjector createMethodInjector(ResourceLocator method, ResteasyProviderFactory factory) {
                    return new MethodInjectorImpl(method, factory) {
                        private final ThreadLocal<Boolean> flag = DefaultBlockHoundIntegration.FLAG;

                        @Override
                        public Object injectArguments(HttpRequest input, HttpResponse response) {
                            boolean curr = flag.get();
                            try {
                                flag.set(false);
                                return super.injectArguments(input, response);
                            } finally {
                                flag.set(curr);
                            }
                        }
                        @Override
                        public Object invoke(HttpRequest request, HttpResponse httpResponse, Object resource) throws Failure, ApplicationException {
                            boolean curr = flag.get();
                            try {
                                flag.set(true);
                                return super.invoke(request, httpResponse, resource);
                            } catch (BlockingOperationError err) {
                                throw new Failure(err);
                            } finally {
                                flag.set(curr);
                            }
                        }
                    };
                }
            });
        }
    }
}
