package com.turbospaces.resteasy;

import java.util.Objects;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.inject.Singleton;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.ext.ContextResolver;
import jakarta.ws.rs.ext.Provider;

@Provider
@Produces({ MediaType.APPLICATION_JSON })
@Singleton
public class ObjectMapperContextResolver implements ContextResolver<ObjectMapper> {
    private final ObjectMapper mapper;

    public ObjectMapperContextResolver(ObjectMapper mapper) {
        this.mapper = Objects.requireNonNull(mapper);
    }
    @Override
    public ObjectMapper getContext(Class<?> type) {
        return mapper;
    }
}
