package com.turbospaces.resteasy;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.SortedMap;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.codahale.metrics.health.HealthCheck;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.TimeoutHandler;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.ResponseBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DefaultAdminEndpoint implements AdminEndpoint, ApplicationListener<ApplicationReadyEvent> {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final HealthCheckRegistry healthCheckRegistry;

    private final Duration defaultTimeout = Duration.ofSeconds(30);
    private final SettableFuture<Void> applicationReadyStatus = SettableFuture.create();
    private final List<SettableFuture<Void>> readyStatuses = Lists.newArrayList();

    public DefaultAdminEndpoint(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            List<ReadyIndicator> probes) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.healthCheckRegistry = Objects.requireNonNull(healthCheckRegistry);

        //
        // ~ add additional debug information
        //
        for (int i = 0; i < probes.size(); i++) {
            ReadyIndicator it = probes.get(i);
            log.info("probe({}) has been registered=({})", i, it.getClass().getName());
        }

        readyStatuses.addAll(probes.stream().map(ReadyIndicator::isReady).toList());
        readyStatuses.add(applicationReadyStatus);
    }
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("got event: {}", event);
        applicationReadyStatus.set(null);
    }
    @Override
    public Response mock(Integer timeout) throws IOException {
        return Response.ok().build();
    }
    @Override
    public void version(AsyncResponse async, Integer timeout) {
        Duration seconds = Optional.ofNullable(timeout).map(Duration::ofSeconds).orElse(defaultTimeout);
        whenReadyThenExecute(async, seconds, new Runnable() {
            @Override
            public void run() {
                async.resume(noCache(Response.ok(PlatformUtil.version(props.CLOUD_APP_NAME))));
            }
        });
    }
    @Override
    public void healthCheck(AsyncResponse async, HttpHeaders headers, Integer timeout) throws IOException {
        Duration seconds = Optional.ofNullable(timeout).map(Duration::ofSeconds).orElse(defaultTimeout);
        List<String> forwarded = headers.getRequestHeader(com.google.common.net.HttpHeaders.X_FORWARDED_FOR);
        whenReadyThenExecute(async, seconds, new Runnable() {
            @Override
            public void run() {
                if (CollectionUtils.isEmpty(forwarded)) {
                    SortedMap<String, HealthCheck.Result> results = healthCheckRegistry.runHealthChecks();
                    if (results.isEmpty()) {
                        async.resume(noCache(Response.status(HttpResponseStatus.NOT_IMPLEMENTED.code())));
                    }

                    boolean isOK = true;
                    log.debug("about to check application health ...");
                    for (HealthCheck.Result result : results.values()) {
                        if (!result.isHealthy()) {
                            log.error(result.getMessage());
                            isOK = false;

                            try {
                                ObjectMapper mapper = new CommonObjectMapper();
                                ObjectWriter prettyPrinter = mapper.writerWithDefaultPrettyPrinter();
                                async.resume(noCache(Response.serverError().entity(prettyPrinter.writeValueAsString(results))));
                            } catch (JsonProcessingException err) {
                                async.resume(err);
                            }
                        }
                    }

                    if (isOK) {
                        async.resume(noCache(Response.ok()));
                    }
                } else {
                    async.resume(noCache(Response.status(HttpResponseStatus.FORBIDDEN.code())));
                }
            }
        });
    }
    @Override
    public void metrics(AsyncResponse async, HttpHeaders headers, Integer timeout) throws IOException {
        Duration seconds = Optional.ofNullable(timeout).map(Duration::ofSeconds).orElse(defaultTimeout);
        List<String> forwarded = headers.getRequestHeader(com.google.common.net.HttpHeaders.X_FORWARDED_FOR);
        whenReadyThenExecute(async, seconds, new Runnable() {
            @Override
            public void run() {
                if (CollectionUtils.isEmpty(forwarded)) {
                    boolean notImplemented = true;
                    if (meterRegistry instanceof CompositeMeterRegistry) {

                    }
                    if (notImplemented) {
                        async.resume(noCache(Response.status(HttpResponseStatus.NOT_IMPLEMENTED.code())));
                    }
                } else {
                    async.resume(noCache(Response.status(HttpResponseStatus.FORBIDDEN.code())));
                }
            }
        });
    }
    private void whenReadyThenExecute(AsyncResponse async, Duration timeout, Runnable action) {
        var stopWatch = StopWatch.createStarted();
        var future = Futures.allAsList(readyStatuses);

        async.setTimeout(timeout.toSeconds(), TimeUnit.SECONDS);
        async.setTimeoutHandler(new TimeoutHandler() {
            @Override
            public void handleTimeout(AsyncResponse asyncResponse) {
                //
                // ~ list probes status for the debug purpose
                //
                for (int i = 0; i < readyStatuses.size(); i++) {
                    SettableFuture<Void> f = readyStatuses.get(i);
                    log.info("probe({}) : ready=({})", i, f.isDone());
                }

                asyncResponse.resume(Response.status(HttpResponseStatus.GATEWAY_TIMEOUT.code()).build());
                future.cancel(true);
            }
        });

        //
        // ~ well just subscribe it might not be ready for indefinitely but then there is no need to start application
        //
        Futures.addCallback(future, new FutureCallback<>() {
            @Override
            public void onSuccess(List<Void> result) {
                stopWatch.stop();
                log.trace("completed await of application to become ready in {}", stopWatch);
                action.run();
            }
            @Override
            public void onFailure(Throwable t) {
                if (future.isCancelled()) {
                    //
                    // ~ NO-OP ignore it since we cancel it in timeout
                    //
                } else {
                    async.resume(t);
                }
            }
        }, MoreExecutors.directExecutor());
    }
    private static Response noCache(ResponseBuilder builder) {
        builder.header(HttpHeaderNames.CACHE_CONTROL.toString(), "must-revalidate,no-cache,no-store");
        builder.header(HttpHeaderNames.PRAGMA.toString(), "no-cache");
        builder.header(HttpHeaderNames.EXPIRES.toString(), 0);
        return builder.build();
    }
}
