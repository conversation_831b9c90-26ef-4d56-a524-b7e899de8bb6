{"$id": "https://patrianna.com/turnstile_request.schema.json", "$schema": "https://json-schema.org/draft-04/schema#", "title": "Verify Request", "type": "object", "properties": {"secret": {"type": "string", "description": "App secret. Configured on both client and server."}, "response": {"type": "string", "description": "A token to verify via Turnstile. Provided by client."}, "remoteip": {"type": "string", "description": "Remote IP address of the connected client"}, "idempotency_key": {"type": "string", "description": "A string used to uniquely identify each request."}}, "required": ["secret", "response", "remoteip", "idempotency_key"]}