{"$id": "https://patrianna.com/recaptcha_request.schema.json", "$schema": "https://json-schema.org/draft-04/schema#", "title": "Verify Request", "type": "object", "properties": {"secret": {"type": "string", "description": "App secret. Configured on both client and server."}, "response": {"type": "string", "description": "A token to verify via Turnstile. Provided by client."}, "remoteip": {"type": "string", "description": "Remote IP address of the connected client"}}, "required": ["secret", "response", "remoteip"]}