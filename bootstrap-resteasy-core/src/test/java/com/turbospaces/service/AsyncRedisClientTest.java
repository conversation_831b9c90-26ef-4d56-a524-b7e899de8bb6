package com.turbospaces.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.shaded.org.awaitility.Awaitility;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
class AsyncRedisClientTest {

    private static final int REDIS_PORT = 6379;
    private static final String TEST_KEY = "redis-test-key";
    private static final String TEST_VALUE = "redis-test-value";
    private static final Duration TEST_TTL = Duration.ofMillis(100);
    private static final String SET_KEY = "redis-set-key";

    @Container
    private static final GenericContainer<?> REDIS_CONTAINER = new GenericContainer<>(DockerImageName.parse("redis:7")).withExposedPorts(REDIS_PORT);

    private static AsyncRedisClient redisClient;

    @BeforeAll
    static void setUp() {
        String redisAddress = String.format("redis://%s:%d", REDIS_CONTAINER.getHost(), REDIS_CONTAINER.getMappedPort(REDIS_PORT));

        Config config = new Config();
        config.useSingleServer()
                .setAddress(redisAddress)
                .setConnectionMinimumIdleSize(1)
                .setConnectionPoolSize(2);
        config.setCodec(new StringCodec());

        RedissonClient redissonClient = Redisson.create(config);
        redisClient = new AsyncRedisClient(redissonClient);
    }

    @AfterEach
    void cleanUp() {
        redisClient.set(TEST_KEY, null, Duration.ZERO);
        redisClient.set(SET_KEY, null, Duration.ZERO);
    }

    @AfterAll
    static void tearDown() {
        REDIS_CONTAINER.stop();
    }

    @Test
    void getAndSet_shouldWorkCorrectly() {
        // Initially the key should not exist
        assertNull(redisClient.get(TEST_KEY));

        // Set a value and verify it was stored
        redisClient.set(TEST_KEY, TEST_VALUE, TEST_TTL);
        assertEquals(TEST_VALUE, redisClient.get(TEST_KEY));

        // Wait for TTL to expire and verify the key is gone using Awaitility
        Awaitility.await()
                .atMost(TEST_TTL.plusMillis(50))
                .pollInterval(10, TimeUnit.MILLISECONDS)
                .until(() -> redisClient.get(TEST_KEY) == null);
    }

    @Test
    void getAndSetAsync_shouldWorkCorrectly() throws ExecutionException, InterruptedException, TimeoutException {
        // Initially the key should not exist
        assertNull(redisClient.getAsync(TEST_KEY).toCompletableFuture().get(1, TimeUnit.SECONDS));

        // Set a value asynchronously and verify it was stored
        redisClient.setAsync(TEST_KEY, TEST_VALUE, TEST_TTL).toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertEquals(TEST_VALUE, redisClient.getAsync(TEST_KEY).toCompletableFuture().get(1, TimeUnit.SECONDS));
    }

    @Test
    void setIfNotExists_whenKeyDoesNotExist_shouldSetValueAndReturnNull() {
        String result = redisClient.setIfNotExists(TEST_KEY, TEST_VALUE, TEST_TTL);
        assertNull(result);
        assertEquals(TEST_VALUE, redisClient.get(TEST_KEY));
    }

    @Test
    void setIfNotExists_whenKeyExists_shouldNotUpdateAndReturnExistingValue() {
        String existingValue = "existing-value";

        // First set a value
        redisClient.set(TEST_KEY, existingValue, TEST_TTL);

        // Try to set if not exists
        String result = redisClient.setIfNotExists(TEST_KEY, TEST_VALUE, TEST_TTL);

        // Should return the existing value and not update
        assertEquals(existingValue, result);
        assertEquals(existingValue, redisClient.get(TEST_KEY));
    }

    @Test
    void increment_shouldIncrementValueAndRespectTTL() {
        // First increment should be 1
        long result1 = redisClient.increment(TEST_KEY, TEST_TTL);
        assertEquals(1L, result1);

        // Second increment should be 2
        long result2 = redisClient.increment(TEST_KEY, TEST_TTL);
        assertEquals(2L, result2);

        // Wait for TTL to expire and verify the counter is reset
        Awaitility.await()
                .atMost(TEST_TTL.plusMillis(50))
                .pollInterval(10, TimeUnit.MILLISECONDS)
                .until(() -> redisClient.get(TEST_KEY) == null);

        long result3 = redisClient.increment(TEST_KEY, TEST_TTL);
        assertEquals(1L, result3); // Should start from 1 again
    }

    @Test
    void incrementAsync_shouldIncrementValueAsynchronouslyAndRespectTTL() throws ExecutionException, InterruptedException, TimeoutException {
        // First increment should be 1
        long result1 = redisClient.incrementAsync(TEST_KEY, TEST_TTL).toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertEquals(1L, result1);

        // Second increment should be 2
        long result2 = redisClient.incrementAsync(TEST_KEY, TEST_TTL).toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertEquals(2L, result2);
    }

    @Test
    void addToSet_shouldAddValuesToSetAndReturnSize() {
        // Add first element
        long item1 = redisClient.addToSet(SET_KEY, "value1", TEST_TTL);
        assertEquals(1, item1);

        // Add second element
        long item2 = redisClient.addToSet(SET_KEY, "value2", TEST_TTL);
        assertEquals(2, item2);

        // Add duplicate element (should not increase size)
        long item3 = redisClient.addToSet(SET_KEY, "value1", TEST_TTL);
        assertEquals(2, item3);
    }

    @Test
    void addToSetAsync_shouldAddValuesToSetAsynchronouslyAndReturnSize() throws ExecutionException, InterruptedException, TimeoutException {
        // Add first element
        int item1 = redisClient.addToSetAsync(SET_KEY, "value1", TEST_TTL).toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertEquals(1, item1);

        // Add second element
        int item2 = redisClient.addToSetAsync(SET_KEY, "value2", TEST_TTL).toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertEquals(2, item2);

        // Add duplicate element (should not increase size)
        int item3 = redisClient.addToSetAsync(SET_KEY, "value1", TEST_TTL).toCompletableFuture().get(1, TimeUnit.SECONDS);
        assertEquals(2, item3);
    }

    @Test
    void getLock_shouldProvideWorkingLock() throws InterruptedException {
        var lock = redisClient.getLock("test-lock");

        assertTrue(lock.tryLock(1, 10, TimeUnit.SECONDS));

        try {
            // Lock is acquired
            assertTrue(lock.isHeldByCurrentThread());
        } finally {
            lock.unlock();
        }

        assertFalse(lock.isHeldByCurrentThread());
    }

    @Test
    void increment_withExpireIfNotSet_shouldRespectExistingTTL() {
        Duration longTTL = Duration.ofSeconds(5);

        long result1 = redisClient.increment(TEST_KEY, longTTL, true);
        assertEquals(1L, result1);

        long initialTTL = redisClient.getKeys().remainTimeToLive(TEST_KEY);
        assertTrue(initialTTL <= longTTL.toMillis());

        // This should NOT update the TTL since it's already set
        Duration shortTTL = Duration.ofMillis(500);
        long result2 = redisClient.increment(TEST_KEY, shortTTL, true);
        assertEquals(2L, result2);

        long updatedTTL = redisClient.getKeys().remainTimeToLive(TEST_KEY);
        assertTrue(updatedTTL > shortTTL.toMillis(), "TTL should still be close to original");

        // This should force update the TTL
        long result3 = redisClient.increment(TEST_KEY, shortTTL, false);
        assertEquals(3L, result3);

        // Check that TTL is now reset to the short TTL
        long finalTTL = redisClient.getKeys().remainTimeToLive(TEST_KEY);
        assertTrue(finalTTL <= shortTTL.toMillis(), "TTL should be reset to the short duration");
        assertTrue(finalTTL > 0, "TTL should be greater than 0");
    }
}
