package com.turbospaces.resteasy;

import static io.github.resilience4j.circuitbreaker.CircuitBreaker.State.CLOSED;
import static io.github.resilience4j.circuitbreaker.CircuitBreaker.State.HALF_OPEN;
import static io.github.resilience4j.circuitbreaker.CircuitBreaker.State.OPEN;

import java.time.Duration;
import java.util.Objects;

import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.testcontainers.containers.ToxiproxyContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.http.CloseableHttpClientFactoryBean;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.memcached.RubyeyeMemcachedService;

import eu.rekawek.toxiproxy.Proxy;
import eu.rekawek.toxiproxy.ToxiproxyClient;
import eu.rekawek.toxiproxy.model.ToxicDirection;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;

@Slf4j
public class CircuitBreakerTest {
    public static ToxiproxyContainer toxiproxy;
    public static Proxy proxy;
    private SimpleBootstrap bootstrap;
    private ResteasyWebTarget proxiedWebTarget;
    private ResteasyWebTarget directWebTarget;

    @BeforeAll
    public static void beforeAll() {
        toxiproxy = new ToxiproxyContainer("ghcr.io/shopify/toxiproxy:2.5.0");
        toxiproxy.start();
    }

    @AfterAll
    public static void afterAll() {
        if (Objects.nonNull(toxiproxy)) {
            toxiproxy.stop();
        }
    }

    @BeforeEach
    public void beforeMethod() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        props.cfg().setLocalProperty(props.HTTP_RETRY_REQUEST_ENABLED.getKey(), false);

        bootstrap = new SimpleBootstrap(props, RootContext.class);
        ConfigurableApplicationContext context = bootstrap.run();

        final ToxiproxyClient toxiproxyClient = new ToxiproxyClient(toxiproxy.getHost(), toxiproxy.getControlPort());
        proxy = toxiproxyClient.createProxy("testApi", "0.0.0.0:8666", "host.docker.internal:" + port);

        // obtainProxiedHostAndPortForHostMachine {
        final String ipAddressViaToxiproxy = toxiproxy.getHost();
        final int portViaToxiproxy = toxiproxy.getMappedPort(8666);

        CloseableHttpClient httpClient = context.getBean(CloseableHttpClient.class);
        var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
        JaxrsClientFactoryBean jaxrs = new JaxrsClientFactoryBean(props, bootstrap.rateLimiterRegistry(), httpClient, executor);
        ResteasyClient client = jaxrs.getObject().build();

        proxiedWebTarget = client.target(new URIBuilder().setScheme("http").setHost(ipAddressViaToxiproxy).setPort(portViaToxiproxy).build());
        directWebTarget = client.target(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).build());
    }

    @AfterEach
    public void afterMethod() throws Exception {
        proxy.delete();
        bootstrap.shutdown();
    }

    @Test
    void slidingWindowTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50)
                .waitDurationInOpenState(Duration.ofMillis(5))
                .minimumNumberOfCalls(10)
                .slidingWindowSize(10)
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("slidingWindowTest", config);

        // set up connection reset
        proxy.toxics().resetPeer("reset", ToxicDirection.DOWNSTREAM, 0);

        for (int i = 1; i <= 10; i++) {
            makeDirectHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(CLOSED, circuitBreaker.getState());
        Assertions.assertEquals(10, circuitBreaker.getMetrics().getNumberOfSuccessfulCalls());

        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
            if (i < 5) {
                Assertions.assertEquals(CLOSED, circuitBreaker.getState());
            } else {
                assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);
            }
        }
    }

    @Test
    void minimumNumberOfCallsTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50)
                .minimumNumberOfCalls(2)
                .slidingWindowSize(10)
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("minimumNumberOfCallsTest", config);

        // set up connection reset
        proxy.toxics().resetPeer("reset", ToxicDirection.DOWNSTREAM, 0);

        // first call is failed
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(CLOSED, circuitBreaker.getState());
        Assertions.assertEquals(1, circuitBreaker.getMetrics().getNumberOfFailedCalls());

        // second failed call opens circuit breaker
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(2, circuitBreaker.getMetrics().getNumberOfFailedCalls());
        // we have 4 calls, where 2 are successful and 2 are failed
        Assertions.assertEquals(OPEN, circuitBreaker.getState());
    }

    @Test
    void httpCallsAreNotPermittedInOpenStateTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50)
                .minimumNumberOfCalls(5)
                .slidingWindowSize(10)
                .waitDurationInOpenState(Duration.ofMillis(10))
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("httpCallsAreNotPermittedInHalfOpenStateTest", config);

        // set up connection reset
        proxy.toxics().resetPeer("reset", ToxicDirection.DOWNSTREAM, 0);

        // minimal number of calls. all are failed
        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfFailedCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);

        // all following calls aren't permitted until waitDurationInOpenState is exceeded
        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(OPEN, circuitBreaker.getState());
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfNotPermittedCalls());
    }

    @Disabled
    @Test
    void circuitBreakerOpensAfterHalfOpenStateTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .slowCallRateThreshold(50)
                .slowCallDurationThreshold(Duration.ofMillis(10))
                .minimumNumberOfCalls(5)
                .slidingWindowSize(10)
                .waitDurationInOpenState(Duration.ofMillis(10))
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("circuitBreakerOpensAfterHalfOpenStateTest", config);

        // set up latency
        proxy.toxics()
                .latency("latency", ToxicDirection.UPSTREAM, 11)
                .setJitter(1);

        // minimal number of calls. all are slow
        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfSlowSuccessfulCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);

        // wait for waitDurationInOpenState
        assertCircuitBreakerChangedStatusTo(circuitBreaker, HALF_OPEN);

        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfSlowSuccessfulCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);
    }

    @Test
    void permittedNumberOfCallsInHalfOpenStateTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50)
                .minimumNumberOfCalls(5)
                .slidingWindowSize(5)
                .waitDurationInOpenState(Duration.ofMillis(20))
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .permittedNumberOfCallsInHalfOpenState(5)
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("circuitBreakerClosesAfterHalfOpenStateTest", config);

        // set up connection reset
        proxy.toxics().resetPeer("reset", ToxicDirection.DOWNSTREAM, 0);

        // minimal number of calls. all are failed
        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfFailedCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);

        // wait for waitDurationInOpenState
        assertCircuitBreakerChangedStatusTo(circuitBreaker, HALF_OPEN);

        for (int i = 1; i <= 7; i++) {
            makeDirectHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(2, circuitBreaker.getMetrics().getNumberOfSuccessfulCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, CLOSED);
    }

    @Test
    void maxWaitDurationInHalfOpenStateTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50)
                .minimumNumberOfCalls(5)
                .slidingWindowSize(5)
                .waitDurationInOpenState(Duration.ofMillis(20))
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .maxWaitDurationInHalfOpenState(Duration.ofMillis(20))
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("maxWaitDurationInHalfOpenStateTest", config);

        // set up connection reset
        proxy.toxics().resetPeer("reset", ToxicDirection.DOWNSTREAM, 0);

        // minimal number of calls. all are failed
        for (int i = 1; i <= 5; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfFailedCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);

        // wait for waitDurationInOpenState
        assertCircuitBreakerChangedStatusTo(circuitBreaker, HALF_OPEN);
        // wait for waitDurationInHalfOpenState, then circuit breaker opens again
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);
    }

    @Disabled
    @Test
    void slowCallTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .minimumNumberOfCalls(5)
                .slidingWindowSize(5)
                .slowCallDurationThreshold(Duration.ofMillis(10))
                .slowCallRateThreshold(50)
                .waitDurationInOpenState(Duration.ofMillis(5))
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .build();

        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("slowCallTest", config);

        proxy.toxics()
                .latency("latency", ToxicDirection.UPSTREAM, 11)
                .setJitter(1);

        for (int i = 1; i <= 5; i++) {
            makeDirectHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(CLOSED, circuitBreaker.getState());
        Assertions.assertEquals(5, circuitBreaker.getMetrics().getNumberOfSuccessfulCalls());

        // 3 requests with slow latency open circuit breaker
        for (int i = 1; i <= 3; i++) {
            makeProxiedHttpCall(circuitBreaker);
        }
        Assertions.assertEquals(3, circuitBreaker.getMetrics().getNumberOfSlowSuccessfulCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);
    }

    @Disabled
    @Test
    void timedBasedSlidingWindowTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .minimumNumberOfCalls(4)
                .slidingWindowType(CircuitBreakerConfig.SlidingWindowType.TIME_BASED)
                .slidingWindowSize(2)
                .waitDurationInOpenState(Duration.ofMillis(100))
                .slowCallDurationThreshold(Duration.ofMillis(5))
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("timedBasedSlidingWindowTest", config);

        proxy.toxics()
                .latency("latency", ToxicDirection.UPSTREAM, 6)
                .setJitter(1);

        // circuit breaker is closed on the start of test
        Assertions.assertEquals(CLOSED, circuitBreaker.getState());

        // 2 requests with slow latency in 1 second
        makeProxiedHttpCall(circuitBreaker);
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(CLOSED, circuitBreaker.getState());

        // another 2 slow requests in next second open circuit breaker
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(CLOSED, circuitBreaker.getState());
        makeProxiedHttpCall(circuitBreaker);
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);
        // next requests aren't permitted
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(OPEN, circuitBreaker.getState());
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(OPEN, circuitBreaker.getState());
        Assertions.assertEquals(4, circuitBreaker.getMetrics().getNumberOfSuccessfulCalls());
        Assertions.assertEquals(2, circuitBreaker.getMetrics().getNumberOfNotPermittedCalls());

        // after 100 millis in open state breaker moves to half_open state with a next request
        assertCircuitBreakerChangedStatusTo(circuitBreaker, HALF_OPEN);
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(HALF_OPEN, circuitBreaker.getState());
        Assertions.assertEquals(1, circuitBreaker.getMetrics().getNumberOfSuccessfulCalls());

        // next 3 calls are allowed
        makeProxiedHttpCall(circuitBreaker);
        makeProxiedHttpCall(circuitBreaker);
        makeProxiedHttpCall(circuitBreaker);
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);

        // following calls aren't permitted
        makeProxiedHttpCall(circuitBreaker);
        makeProxiedHttpCall(circuitBreaker);
        Assertions.assertEquals(4, circuitBreaker.getMetrics().getNumberOfSuccessfulCalls());
        Assertions.assertEquals(2, circuitBreaker.getMetrics().getNumberOfNotPermittedCalls());
    }

    @Test
    void connectionResetTest() throws Throwable {
        var config = CircuitBreakerConfig.custom()
                .minimumNumberOfCalls(1)
                .enableAutomaticTransitionFromOpenToHalfOpen()
                .build();
        CircuitBreaker circuitBreaker = bootstrap.circuitBreakerRegistry().circuitBreaker("connectionResetTest", config);

        // set up connection reset
        proxy.toxics().resetPeer("reset", ToxicDirection.DOWNSTREAM, 0);

        ApiResource apiResource = proxiedWebTarget.proxy(ApiResource.class);

        var lastSuccessfulResponse = "someValueFromTheLastSuccessResponse";

        String response;
        try {
            response = circuitBreaker.executeCheckedSupplier(() -> {
                try (Response resp = apiResource.test()) {
                    return resp.getEntity().toString();
                }
            });
        } catch (Throwable t) {
            log.warn("Resource is unavailable, circuit breaker is CLOSED");
            response = lastSuccessfulResponse;
        }
        Assertions.assertEquals(1, circuitBreaker.getMetrics().getNumberOfFailedCalls());
        assertCircuitBreakerChangedStatusTo(circuitBreaker, OPEN);
        Assertions.assertEquals(response, lastSuccessfulResponse);
    }

    private void makeProxiedHttpCall(CircuitBreaker circuitBreaker) {
        try {
            ApiResource apiResource = proxiedWebTarget.proxy(ApiResource.class);
            circuitBreaker.executeCheckedSupplier(() -> {
                try (Response response = apiResource.test()) {
                    return response.getEntity();
                }
            });
        } catch (Throwable t) {
            log.warn("Unable to make http call", t);
        }
    }

    private void makeDirectHttpCall(CircuitBreaker circuitBreaker) {
        try {
            ApiResource apiResource = directWebTarget.proxy(ApiResource.class);
            circuitBreaker.executeCheckedSupplier(() -> {
                try (Response response = apiResource.test()) {
                    return response.getEntity();
                }
            });
        } catch (Throwable t) {
            log.warn("Unable to make http call", t);
        }
    }

    private static void assertCircuitBreakerChangedStatusTo(CircuitBreaker circuitBreaker, CircuitBreaker.State status) {
        Awaitility.await().pollInterval(Duration.ofMillis(1)).atMost(Duration.ofSeconds(1)).until(() -> circuitBreaker.getState().equals(status));
    }

    @Configuration
    @EnableAspectJAutoProxy(proxyTargetClass = true)
    public static class RootContext {
        @Bean
        public CommonObjectMapper mapper() {
            return new CommonObjectMapper();
        }

        @Bean
        public CloseableHttpClientFactoryBean httpClient(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new CloseableHttpClientFactoryBean(props, meterRegistry);
        }

        @Bean
        public ApiResource apiResource() {
            return new TestResource();
        }

        @Bean
        public RubyeyeMemcachedService memcachedService() {
            return Mockito.mock(RubyeyeMemcachedService.class);
        }

        @Bean
        public MemcachedClient memcachedClient() {
            return Mockito.mock(MemcachedClient.class);
        }

        @Bean
        public ResteasyChannel channel(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                RubyeyeMemcachedService memcachedService) {
            return ResteasyChannel.s2c(
                    props,
                    cloud,
                    meterRegistry,
                    rateLimiterRegistry,
                    props.CLOUD_APP_PORT.get(),
                    memcachedService);
        }
    }

    @Path("/v1")
    @ApiEndpoint
    public interface ApiResource {
        @GET
        @Path("/test")
        Response test();
    }

    @Slf4j
    public static class TestResource implements ApiResource {
        @Override
        public Response test() {
            return Response.ok("test").build();
        }
    }
}
