package com.turbospaces.resteasy.filter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.JsonSchemaBody.jsonSchema;

import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeoutException;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.matchers.TimeToLive;
import org.mockserver.matchers.Times;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.memcached.MemcachedClientFactoryBean;
import com.turbospaces.memcached.RubyeyeMemcachedService;
import com.turbospaces.resteasy.ResteasyChannel;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Application;
import jakarta.ws.rs.core.Response;
import lombok.Builder;
import lombok.Data;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.exception.MemcachedException;

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class TurnstileApplicationFilterTest {

    private static final List<String> WHITELISTED_AGENTS = List.of("WhitelistedAgent-1", "WhitelistedAgent-2");
    private static final String REMOTE_IP = "***********";

    private static final int MEMCACHED_CONTAINER_EXPOSED_PORT = 11211;
    private static final int TURNSTILE_API_PORT = PlatformUtil.findAvailableTcpPort();

    private static final String TURNSTILE_API_KEY = "turnstile-secret-api-key";
    private static final String TURNSTILE_REQUEST_JSON_SCHEMA_FILE = "turnstile_request_schema.json";

    private final ObjectMapper objectMapper = new ObjectMapper();
    private URI baseUri;
    private RubyeyeMemcachedService memcachedService;
    private ApplicationProperties props;
    private Bootstrap bootstrap;
    private ClientAndServer clientAndServer;

    @Container
    private static GenericContainer<?> MEMCACHED = new GenericContainer<>(DockerImageName.parse("memcached:1.6")).withExposedPorts(MEMCACHED_CONTAINER_EXPOSED_PORT);

    @BeforeAll
    public void startUpBootstrap() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();
        ApplicationConfig cfg = ApplicationConfig.mock(port);
        cfg.setDefaultProperty("connection.public.domains", "localhost");
        cfg.setDefaultProperty("connection.user-agent.whitelist", String.join(",", WHITELISTED_AGENTS));

        baseUri = new URIBuilder().setScheme("http").setHost("localhost").setPort(port).build();

        props = new ApplicationProperties(cfg.factory());
        bootstrap = new SimpleBootstrap(props, ApplicationContext.class);
        bootstrap.addUps(new PlainServiceInfo(UPSs.TURNSTILE, String.format("http://api_key:%s@localhost:%d", TURNSTILE_API_KEY, TURNSTILE_API_PORT)));
        bootstrap.addUps(new MemcachedServiceInfo(UPSs.MEMCACHED, MEMCACHED.getHost(), MEMCACHED.getMappedPort(MEMCACHED_CONTAINER_EXPOSED_PORT)));
        ConfigurableApplicationContext ctx = bootstrap.run();

        memcachedService = ctx.getBean(RubyeyeMemcachedService.class);

        clientAndServer = ClientAndServer.startClientAndServer(TURNSTILE_API_PORT);
    }

    @AfterAll
    public void afterAll() throws Exception {
        clientAndServer.close();
        bootstrap.shutdown();
    }

    @AfterEach
    public void afterEach() throws TimeoutException, InterruptedException, MemcachedException {
        clientAndServer.reset();
        memcachedService.flushAll();
    }

    @Test
    public void successfulVerification_baseScenario() throws Exception {
        String tokenToVerify = mockTurnstileResponse(MockTurnstileRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldBypassWhitelistedUserAgentsWithoutAnyExtraChecks() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            for (String userAgent : WHITELISTED_AGENTS) {
                HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
                post.setHeader(HttpHeaders.USER_AGENT, userAgent);
                try (CloseableHttpResponse resp = httpClient.execute(post)) {
                    assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                }
            }
        }
    }

    @Test
    public void shouldBypassRequestsToNotPreconfiguredDomainsWithoutAnyExtraChecks() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpHeaders.HOST, "some-not-preconfigured-public-domain");
            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortWhenVerifyHeaderIsMissing() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_NOT_IMPLEMENTED, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortWhenVerifyHeaderIsEmpty() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, "");

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_NOT_IMPLEMENTED, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortWhenVerifyHeaderIsTooLarge() throws Exception {
        int maxHeaderLength = props.CONNECTION_SITE_VERIFICATION_HEADER_MAX_LENGTH.get();
        String tooLongVerifyHeader = PlatformUtil.randomAlphanumeric(maxHeaderLength + 1);
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tooLongVerifyHeader);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_REQUEST_TOO_LONG, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortOnErrorVerificationResponse() throws Exception {
        String tokenToVerify = mockTurnstileResponse(MockTurnstileRequestResponse
                .defaultBuilder()
                .responseStatusCode(HttpStatus.SC_BAD_REQUEST) // ~ anything not from 2xx range
                .build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_TOO_MANY_REQUESTS, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortOnUnsuccessfulVerificationResponse() throws Exception {
        String tokenToVerify = mockTurnstileResponse(MockTurnstileRequestResponse
                .defaultBuilder()
                .isSuccess(false)
                .errorCodes(List.of("invalid-input-response"))
                .build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldStoreStatusInLocalCache() throws Exception {
        String tokenToVerify = mockTurnstileResponse(MockTurnstileRequestResponse.defaultBuilder().build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            int calls = 5; // Mock server will fail if more than one verification request was sent, so the local cache must work
            for (int i = 0; i < calls; i++) {
                HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
                post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
                post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);

                try (CloseableHttpResponse resp = httpClient.execute(post)) {
                    assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                }

                // Delete all keys after each iteration, it means that for all the requests starting the 2nd one
                // the filter must use local cache because Mock HTTP Server allows exactly one call and not more.
                memcachedService.flushAll();
            }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = { HttpStatus.SC_OK, HttpStatus.SC_NOT_IMPLEMENTED })
    public void shouldLoadSuccessfulVerificationStatusFromRemoteCacheWhenLocalCacheIsEmpty(int status) throws Exception {
        String tokenToVerify = PlatformUtil.randomUUID().toString();
        String shortenSiteVerifyHeader = TurnstilemTLSApplicationFilter.GENERATE_VERIFICATION_CACHE_KEY.apply(tokenToVerify);

        memcachedService.set(shortenSiteVerifyHeader, (int) props.CACHE_DEFAULT_MAX_TTL.get().toSeconds(), status);

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(status, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldBlockWhenReachesTheLimitOfCallsToTheSameResource() throws Exception {
        String tokenToVerify = mockTurnstileResponse(MockTurnstileRequestResponse.defaultBuilder().build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);

            int calls = props.CONNECTION_SITE_VERIFICATION_UNIQUE_REQUESTS_PER_PATH_MAX.get();
            assertTrue(calls > 0);
            for (int i = 0; i < calls; i++) {
                try (CloseableHttpResponse resp = httpClient.execute(post)) {
                    assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                }
            }

            // extra call above limit should fail
            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                int statusCode = resp.getStatusLine().getStatusCode();
                assertEquals(HttpStatus.SC_TOO_MANY_REQUESTS, statusCode);
            }
        }
    }

    @Test
    public void shouldBlockWhenReachesTheLimitOfCallsFromDifferentIps() throws Exception {
        String tokenToVerify = mockTurnstileResponse(MockTurnstileRequestResponse.defaultBuilder().build());

        int callsLimit = props.CONNECTION_SITE_VERIFICATION_UNIQUE_IPS_MAX.get();
        assertTrue(callsLimit <= 255, "The test assumes the number to be <= 255");

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_VERIFY, tokenToVerify);
            for (int requestNumber = 0; requestNumber < callsLimit + 1; requestNumber++) {
                String remoteIp = String.format("192.168.0.%d", requestNumber);
                post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, remoteIp);

                try (CloseableHttpResponse resp = httpClient.execute(post)) {
                    // extra call above limit should fail
                    if (requestNumber == callsLimit) {
                        assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
                    } else {
                        assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                    }
                }
            }
        }
    }

    private String mockTurnstileResponse(MockTurnstileRequestResponse requestResponse) throws Exception {
        String body = objectMapper.writeValueAsString(ImmutableMap.builder()
                .put("success", requestResponse.isSuccess)
                .put("error-codes", requestResponse.errorCodes)
                .build());

        String bodySchema = IOUtils.toString(
                new ClassPathResource(TURNSTILE_REQUEST_JSON_SCHEMA_FILE, getClass()).getInputStream(),
                Charset.defaultCharset());

        clientAndServer.when(
                request()
                        .withMethod("POST")
                        .withPath("/turnstile/v0/siteverify")
                        .withBody(jsonSchema(bodySchema)),
                Times.exactly(1), TimeToLive.unlimited())
                .respond(
                        response()
                                .withStatusCode(requestResponse.responseStatusCode)
                                .withBody(body));
        return requestResponse.siteVerifyToken;
    }

    @Path("/v1")
    @ApiEndpoint
    public static class SimpleApiResource {

        private final Executor executor = Executors.newSingleThreadExecutor();

        @POST
        @Path("/async")
        public void async(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("async").build()));
        }

    }

    public static class SimpleApplication extends Application {
        private final TurnstilemTLSApplicationFilter filter;

        public SimpleApplication(TurnstilemTLSApplicationFilter filter) {
            this.filter = filter;
        }
        @Override
        public Set<Object> getSingletons() {
            return Set.of(filter, new SimpleApiResource());
        }
    }

    @Configuration
    public static class ApplicationContext {

        @Bean
        public MemcachedClient memcached(
                ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry) throws Exception {
            MemcachedClientFactoryBean factoryBean = new MemcachedClientFactoryBean(props, cloud, meterRegistry);
            factoryBean.afterPropertiesSet();
            return factoryBean.getObject();
        }

        @Bean
        public RubyeyeMemcachedService memcachedService(ApplicationProperties props, MemcachedClient memcachedClient) {
            return new RubyeyeMemcachedService(props, memcachedClient);
        }

        @Bean
        public TurnstilemTLSApplicationFilter filter(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                RubyeyeMemcachedService memcachedService) throws Throwable {
            return new TurnstilemTLSApplicationFilter(props, cloud, meterRegistry, new ObjectMapper(), memcachedService);
        }
        @Bean
        public SimpleApplication simpleApplication(TurnstilemTLSApplicationFilter filter) {
            return new SimpleApplication(filter);
        }
        @Bean
        public ResteasyChannel resteasyChannel(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                SimpleApplication simpleApplication,
                RubyeyeMemcachedService memcachedService) throws Throwable {
            var resteasyChannel = ResteasyChannel.s2c(props, cloud, meterRegistry, rateLimiterRegistry, props.CLOUD_APP_PORT.get(), memcachedService);
            resteasyChannel.application(simpleApplication);
            return resteasyChannel;
        }
    }

    @Builder
    @Data
    public static class MockTurnstileRequestResponse {
        String expectedRemoteIp;
        String expectedApiKey;
        String siteVerifyToken;
        String idempotencyKey;

        int responseStatusCode;
        List<String> errorCodes;
        boolean isSuccess;

        public static MockTurnstileRequestResponseBuilder defaultBuilder() {
            return MockTurnstileRequestResponse.builder()
                    .expectedApiKey(TURNSTILE_API_KEY)
                    .expectedRemoteIp(REMOTE_IP)
                    .isSuccess(true)
                    .responseStatusCode(HttpStatus.SC_OK)
                    .errorCodes(new ArrayList<>())
                    .siteVerifyToken(PlatformUtil.randomAlphanumeric(250));
        }

    }
}
