package com.turbospaces.resteasy.filter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.JsonSchemaBody.jsonSchema;

import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import com.turbospaces.cfg.ScopedProperty;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.matchers.TimeToLive;
import org.mockserver.matchers.Times;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.memcached.RubyeyeMemcachedService;
import com.turbospaces.resteasy.ResteasyChannel;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Application;
import jakarta.ws.rs.core.Response;
import lombok.Builder;
import lombok.Data;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class RecaptchaApplicationFilterTest {
    private static final List<String> WHITELISTED_AGENTS = List.of("WhitelistedAgent-1", "WhitelistedAgent-2");
    private static final String REMOTE_IP = "***********";

    private static final int RECAPTCHA_API_PORT = PlatformUtil.findAvailableTcpPort();

    private static final String RECAPTCHA_API_KEY = "recaptcha-secret-api-key";
    private static final String RECAPTCHA_REQUEST_JSON_SCHEMA_FILE = "recaptcha_request_schema.json";
    private static final String RECAPTCHA_BYPASS_SECRET = "token";

    private final ObjectMapper objectMapper = new ObjectMapper();
    private ApplicationProperties props;
    private URI baseUri;
    private Bootstrap bootstrap;
    private ClientAndServer clientAndServer;

    @Path("/v1")
    public static class SimpleApiResource {

        private final Executor executor = Executors.newSingleThreadExecutor();

        @POST
        @ApiEndpoint(siteVerifyEnforceKey = "namespace")
        @Path("/method/apiendpoint/async")
        public void async(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("async").build()));
        }

        @POST
        @Path("/async")
        public void asyncWithoutApiEndpoint(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("async").build()));
        }
    }

    @Path("/v1/class/apiendpoint")
    @ApiEndpoint(siteVerifyEnforceKey = "namespace")
    public static class SimpleClassApiEndpointResource {

        private final Executor executor = Executors.newSingleThreadExecutor();

        @POST
        @Path("/async")
        public void async(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("async").build()));
        }
    }

    public static class SimpleApplication extends Application {

        private final RecaptchaApplicationFilter filter;

        public SimpleApplication(RecaptchaApplicationFilter filter) {
            this.filter = filter;
        }

        @Override
        public Set<Object> getSingletons() {
            return Set.of(filter, new SimpleApiResource(), new SimpleClassApiEndpointResource());
        }
    }

    @Configuration
    public static class ApplicationContext {
        @Bean
        public RecaptchaApplicationFilter filter(ApplicationProperties props, DynamicCloud cloud) throws Throwable {
            return new RecaptchaApplicationFilter(props, cloud, new ObjectMapper());
        }
        @Bean
        public SimpleApplication simpleApplication(RecaptchaApplicationFilter filter) {
            return new SimpleApplication(filter);
        }

        @Bean
        public RubyeyeMemcachedService memcachedService() {
            return Mockito.mock(RubyeyeMemcachedService.class);
        }

        @Bean
        public ResteasyChannel resteasyChannel(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                SimpleApplication simpleApplication,
                RubyeyeMemcachedService memcachedService) {
            var resteasyChannel = ResteasyChannel.s2c(props, cloud, meterRegistry, rateLimiterRegistry, props.CLOUD_APP_PORT.get(), memcachedService);
            resteasyChannel.application(simpleApplication);
            return resteasyChannel;
        }
    }

    @Builder
    @Data
    public static class MockTokenVerifyRequestResponse {
        String expectedRemoteIp;
        String expectedApiKey;
        String siteVerifyToken;
        String idempotencyKey;

        int responseStatusCode;
        List<String> errorCodes;
        boolean isSuccess;

        public static MockTokenVerifyRequestResponseBuilder defaultBuilder() {
            return MockTokenVerifyRequestResponse.builder()
                    .expectedApiKey(RECAPTCHA_API_KEY)
                    .expectedRemoteIp(REMOTE_IP)
                    .isSuccess(true)
                    .responseStatusCode(HttpStatus.SC_OK)
                    .errorCodes(new ArrayList<>())
                    .siteVerifyToken(PlatformUtil.randomAlphanumeric(500));

        }

    }

    @BeforeAll
    public void startUpBootstrap() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();
        ApplicationConfig cfg = ApplicationConfig.mock(port);
        props = new ApplicationProperties(cfg.factory());
        cfg.setDefaultProperty("connection.public.domains", "localhost");
        cfg.setDefaultProperty(props.SITE_VERIFY_ENFORCE_ENABLED.getKey().replace(ScopedProperty.SCOPE_PLACEHOLDER, "namespace"), true);
        cfg.setDefaultProperty("connection.user-agent.whitelist", String.join(",", WHITELISTED_AGENTS));
        cfg.setDefaultProperty("api.recaptcha.bypass.param", "bypass-recaptcha");
        cfg.setDefaultProperty("api.recaptcha.bypass.enabled", true);

        baseUri = new URIBuilder().setScheme("http").setHost("localhost").setPort(port).build();


        bootstrap = new SimpleBootstrap(props, ApplicationContext.class);
        bootstrap.addUps(new PlainServiceInfo(
                UPSs.RECAPTCHA, String.format("http://api_key:%s@localhost:%d?%s=%s",
                        RECAPTCHA_API_KEY,
                        RECAPTCHA_API_PORT,
                        props.API_RECAPTCHA_BYPASS_PARAM.get(),
                        RECAPTCHA_BYPASS_SECRET)));
        bootstrap.addUps(new PlainServiceInfo(
                UPSs.INVISIBLE_RECAPTCHA, String.format("http://api_key:%s@localhost:%d?%s=%s",
                RECAPTCHA_API_KEY,
                RECAPTCHA_API_PORT,
                props.API_RECAPTCHA_BYPASS_PARAM.get(),
                RECAPTCHA_BYPASS_SECRET)));
        bootstrap.run();
        clientAndServer = ClientAndServer.startClientAndServer(RECAPTCHA_API_PORT);
    }

    @AfterAll
    public void afterAll() throws Exception {
        bootstrap.shutdown();
        clientAndServer.close();
    }

    @AfterEach
    public void afterEach() {
        clientAndServer.reset();
    }

    private String mockRecaptchaResponse(MockTokenVerifyRequestResponse requestResponse) throws Exception {
        String body = objectMapper.writeValueAsString(ImmutableMap.builder()
                .put("success", requestResponse.isSuccess)
                .put("error-codes", requestResponse.errorCodes)
                .build());

        String bodySchema = IOUtils.toString(
                new ClassPathResource(RECAPTCHA_REQUEST_JSON_SCHEMA_FILE, getClass()).getInputStream(),
                Charset.defaultCharset());

        clientAndServer.when(
                request()
                        .withMethod("POST")
                        .withPath("/recaptcha/api/siteverify")
                        .withBody(jsonSchema(bodySchema)),
                Times.exactly(1), TimeToLive.unlimited())
                .respond(
                        response()
                                .withStatusCode(requestResponse.responseStatusCode)
                                .withBody(body));
        return requestResponse.siteVerifyToken;
    }

    @Test
    public void successfulVerificationWithMethodApiEndpoint_baseScenario() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void successfulVerificationWithClassApiEndpoint_baseScenario() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/class/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = { HttpProto.HEADER_X_SITE_VERIFY, HttpProto.HEADER_X_SITE_VERIFY })
    public void successHostCheckedByPublicDomainsProps(String header) throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(header, tokenToVerify);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = { HttpProto.HEADER_X_SITE_VERIFY, HttpProto.HEADER_X_SITE_VERIFY })
    public void successHostCheckedByPublicDomainsPropsByProtectedEndpoint(String header) throws Exception {
        String randomString = PlatformUtil.randomAlphanumeric(500);
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(header, randomString);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldBypassRequestsToNotPreconfiguredDomainsWithoutAnyExtraChecks() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpHeaders.HOST, "some-not-preconfigured-public-domain");
            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldBypassRequestsToNotConfiguredApiEndpoint() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortWhenRequiredVerifyHeaderByApiEndpointIsMissing() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_NOT_IMPLEMENTED, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortWhenRequiredVerifyHeaderByApiEndpointIsEmpty() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, "");

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_NOT_IMPLEMENTED, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortWhenVerifyHeaderIsTooLarge() throws Exception {
        int maxHeaderLength = props.CONNECTION_SITE_VERIFICATION_HEADER_MAX_LENGTH.get();
        String tooLongVerifyHeader = PlatformUtil.randomAlphanumeric(maxHeaderLength + 1);
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tooLongVerifyHeader);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_REQUEST_TOO_LONG, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortOnErrorVerificationResponse() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse
                .defaultBuilder()
                .responseStatusCode(HttpStatus.SC_BAD_REQUEST) // ~ anything not from 2xx range
                .build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(com.google.common.net.HttpHeaders.HOST, "some-not-preconfigured-public-domain");

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_TOO_MANY_REQUESTS, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortOnUnsuccessfulVerificationResponse() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse
                .defaultBuilder()
                .isSuccess(false)
                .errorCodes(List.of("invalid-input-response"))
                .build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tokenToVerify);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = { HttpProto.HEADER_X_SITE_VERIFY, HttpProto.HEADER_X_SITE_VERIFY })
    public void shouldAbortOnUnsuccessfulVerificationResponseWithVerifyHeaderAndAbsentOneTmeVerifyHeader(String header) throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse
                .defaultBuilder()
                .isSuccess(false)
                .errorCodes(List.of("invalid-input-response"))
                .build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(header, tokenToVerify);
            post.setHeader(HttpHeaders.HOST, "some-not-preconfigured-public-domain");

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_NOT_IMPLEMENTED, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldBypassRequestsWhenBypassFlagIsTrueAndTokenEqualsToConfiguredSecret() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, RECAPTCHA_BYPASS_SECRET);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void successfulVerificationWithInvisibleRecaptchaHeader() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_INVISIBLE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldAbortOnUnsuccessfulInvisibleRecaptchaVerification() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse
                .defaultBuilder()
                .isSuccess(false)
                .errorCodes(List.of("invalid-input-response"))
                .build());

        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_INVISIBLE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void successfulVerificationWithBothHeadersPresent() throws Exception {
        String tokenToVerify = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        String invisibleToken = mockRecaptchaResponse(MockTokenVerifyRequestResponse.defaultBuilder().build());
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_X_SITE_ONE_TIME_VERIFY, tokenToVerify);
            post.setHeader(HttpProto.HEADER_X_SITE_INVISIBLE_ONE_TIME_VERIFY, invisibleToken);
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    public void shouldBypassRequestsWhenInvisibleBypassFlagIsTrueAndTokenEqualsToConfiguredSecret() throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpPost post = new HttpPost(new URIBuilder(baseUri).setPath("/v1/method/apiendpoint/async").build());
            post.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, REMOTE_IP);
            post.setHeader(HttpProto.HEADER_X_SITE_INVISIBLE_ONE_TIME_VERIFY, RECAPTCHA_BYPASS_SECRET);

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            }
        }
    }
}
