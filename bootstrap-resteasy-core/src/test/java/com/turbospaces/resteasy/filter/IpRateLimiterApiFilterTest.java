package com.turbospaces.resteasy.filter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.net.URI;
import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.memcached.MemcachedClientFactoryBean;
import com.turbospaces.memcached.RubyeyeMemcachedService;
import com.turbospaces.resteasy.ResteasyChannel;
import com.turbospaces.service.MemcachedService;
import com.turbospaces.ups.MemcachedServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Application;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;

@Slf4j
@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class IpRateLimiterApiFilterTest {

    // Test constants following established patterns
    private static final String TEST_IP_ADDRESS = "*************";
    private static final String WHITELISTED_IP = "*************";
    private static final String BLACKLISTED_IP = "192.168.1.300";
    private static final List<String> WHITELISTED_IPS = List.of(WHITELISTED_IP, "********");

    private static final String RATE_LIMITER_KEY = "test-rate-limiter";
    private static final int DEFAULT_RATE_LIMIT = 5;
    private static final Duration DEFAULT_PERIOD = Duration.ofMinutes(1);
    private static final Duration DEFAULT_COOLDOWN = Duration.ofMinutes(5);

    // Test resource paths
    private static final String BASIC_ENDPOINT_PATH = "/v1/basic";
    private static final String RATE_LIMITED_ENDPOINT_PATH = "/v1/rate-limited";
    private static final String CLASS_RATE_LIMITED_ENDPOINT_PATH = "/v1/class-rate-limited/method";

    // Testcontainers setup
    private static final int MEMCACHED_CONTAINER_EXPOSED_PORT = 11211;

    @Container
    private static GenericContainer<?> MEMCACHED = new GenericContainer<>(DockerImageName.parse("memcached:1.6"))
            .withExposedPorts(MEMCACHED_CONTAINER_EXPOSED_PORT);

    private URI baseUri;
    private ApplicationProperties props;
    private Bootstrap bootstrap;
    private MemcachedService memcachedService;
    private ConfigurableApplicationContext applicationContext;

    @Path("/v1")
    public static class BasicApiResource {
        private final Executor executor = Executors.newSingleThreadExecutor();

        @POST
        @Path("/basic")
        public void basicEndpoint(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("basic").build()));
        }

        @POST
        @ApiEndpoint(rateLimiterKey = RATE_LIMITER_KEY)
        @Path("/rate-limited")
        public void rateLimitedEndpoint(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("rate-limited").build()));
        }
    }

    @Path("/v1/class-rate-limited")
    @ApiEndpoint(rateLimiterKey = RATE_LIMITER_KEY)
    public static class ClassRateLimitedApiResource {
        private final Executor executor = Executors.newSingleThreadExecutor();

        @POST
        @Path("/method")
        public void classRateLimitedMethod(@Suspended AsyncResponse async) {
            executor.execute(() -> async.resume(Response.ok("class-rate-limited").build()));
        }
    }

    public static class TestApplication extends Application {
        private final IpRateLimiterApiFilter filter;

        public TestApplication(IpRateLimiterApiFilter filter) {
            this.filter = filter;
        }

        @Override
        public Set<Object> getSingletons() {
            return Set.of(filter, new BasicApiResource(), new ClassRateLimitedApiResource());
        }
    }

    @Configuration
    public static class ApplicationContext {
        @Bean
        public IpRateLimiterApiFilter ipRateLimiterApiFilter(ApplicationProperties props, MemcachedService memcachedService) {
            return new IpRateLimiterApiFilter(props, memcachedService);
        }

        @Bean
        public TestApplication testApplication(IpRateLimiterApiFilter filter) {
            return new TestApplication(filter);
        }

        @Bean
        public MemcachedClient memcachedClient(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry) throws Exception {
            MemcachedClientFactoryBean factoryBean = new MemcachedClientFactoryBean(props, cloud, meterRegistry);
            factoryBean.afterPropertiesSet();
            return factoryBean.getObject();
        }

        @Bean
        public MemcachedService memcachedService(ApplicationProperties props, MemcachedClient memcachedClient) {
            return new RubyeyeMemcachedService(props, memcachedClient);
        }

        @Bean
        public ResteasyChannel resteasyChannel(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                TestApplication testApplication,
                RubyeyeMemcachedService memcachedService) {
            var resteasyChannel = ResteasyChannel.s2c(props, cloud, meterRegistry, rateLimiterRegistry, props.CLOUD_APP_PORT.get(), memcachedService);
            resteasyChannel.application(testApplication);
            return resteasyChannel;
        }
    }

    @BeforeAll
    public void startUpBootstrap() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();
        ApplicationConfig cfg = ApplicationConfig.mock(port);

        // Configure IP rate limiter settings
        cfg.setDefaultProperty("ip.rate-limiter.enabled", true);
        cfg.setDefaultProperty("ip.rate-limiter.whitelist", String.join(",", WHITELISTED_IPS));
        cfg.setDefaultProperty("ip.rate-limiter.period", DEFAULT_PERIOD);
        cfg.setDefaultProperty("ip.rate-limiter.cooldown-period", DEFAULT_COOLDOWN);
        cfg.setDefaultProperty("ip.rate-limiter.count", DEFAULT_RATE_LIMIT);

        // Configure specific rate limiter for test endpoints
        cfg.setDefaultProperty("ip-rate-limiter." + RATE_LIMITER_KEY + ".enabled", true);
        cfg.setDefaultProperty("ip-rate-limiter." + RATE_LIMITER_KEY + ".period", DEFAULT_PERIOD);
        cfg.setDefaultProperty("ip-rate-limiter." + RATE_LIMITER_KEY + ".cooldown-period", DEFAULT_COOLDOWN);
        cfg.setDefaultProperty("ip-rate-limiter." + RATE_LIMITER_KEY + ".count", DEFAULT_RATE_LIMIT);

        cfg.setDefaultProperty("connection.public.domains", "localhost");

        baseUri = new URIBuilder().setScheme("http").setHost("localhost").setPort(port).build();
        props = new ApplicationProperties(cfg.factory());

        bootstrap = new SimpleBootstrap(props, ApplicationContext.class);
        bootstrap.addUps(new MemcachedServiceInfo(
                UPSs.MEMCACHED,
                MEMCACHED.getHost(),
                MEMCACHED.getMappedPort(MEMCACHED_CONTAINER_EXPOSED_PORT)));
        applicationContext = bootstrap.run();

        memcachedService = applicationContext.getBean(MemcachedService.class);
    }

    @AfterAll
    public void shutdown() {
        if (applicationContext != null) {
            applicationContext.close();
        }
    }

    @BeforeEach
    public void setUp() throws Exception {
        cleanUpMemcachedData();
    }

    @AfterEach
    public void tearDown() throws Exception {
        cleanUpMemcachedData();
    }

    private void cleanUpMemcachedData() throws Exception {
        memcachedService.flushAll();
    }

    @Test
    public void shouldAllowRequestWhenRateLimiterDisabled() throws Exception {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(baseUri.resolve(BASIC_ENDPOINT_PATH));
            request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, TEST_IP_ADDRESS);

            try (CloseableHttpResponse response = client.execute(request)) {
                assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
            }
        }

        assertFalse(isIpBanned(TEST_IP_ADDRESS));
    }

    @Test
    public void shouldAllowWhitelistedIpWithoutRateLimit() throws Exception {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            for (int iteration = 0; iteration < DEFAULT_RATE_LIMIT + 1; iteration++) {
                HttpPost request = new HttpPost(baseUri.resolve(RATE_LIMITED_ENDPOINT_PATH));
                request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, WHITELISTED_IP);

                try (CloseableHttpResponse response = client.execute(request)) {
                    assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
                }
            }
        }

        // Verify no rate limiting data was created for whitelisted IP
        assertFalse(isIpBanned(WHITELISTED_IP));
    }

    @Test
    public void shouldBlockBlacklistedIp() throws Exception {
        // Pre-blacklist the IP by setting it in memcached with helper method
        banIpForTesting(BLACKLISTED_IP, (int) DEFAULT_COOLDOWN.toSeconds());

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(baseUri.resolve(RATE_LIMITED_ENDPOINT_PATH));
            request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, BLACKLISTED_IP);

            try (CloseableHttpResponse response = client.execute(request)) {
                assertEquals(HttpStatus.SC_FORBIDDEN, response.getStatusLine().getStatusCode());
            }
        }

        // Verify IP is still blacklisted and no counter was created
        assertTrue(isIpBanned(BLACKLISTED_IP));
    }

    @Test
    public void shouldAllowRequestUnderRateLimitAndBlockOverLimit() throws Exception {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            // Make requests up to the rate limit
            for (int iteration = 0; iteration < DEFAULT_RATE_LIMIT; iteration++) {
                HttpPost request = new HttpPost(baseUri.resolve(RATE_LIMITED_ENDPOINT_PATH));
                request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, TEST_IP_ADDRESS);

                try (CloseableHttpResponse response = client.execute(request)) {
                    assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
                }
            }
            assertFalse(isIpBanned(TEST_IP_ADDRESS));

            // Make one more request to exceed the rate limit
            HttpPost request = new HttpPost(baseUri.resolve(RATE_LIMITED_ENDPOINT_PATH));
            request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, TEST_IP_ADDRESS);
            try (CloseableHttpResponse response = client.execute(request)) {
                assertEquals(HttpStatus.SC_TOO_MANY_REQUESTS, response.getStatusLine().getStatusCode());
            }
            assertTrue(isIpBanned(TEST_IP_ADDRESS));
        }
    }

    @Test
    public void shouldApplyRateLimitToMethodLevelApiEndpoint() throws Exception {
        String testIp = "*************"; // Use different IP to avoid conflicts

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(baseUri.resolve(RATE_LIMITED_ENDPOINT_PATH));
            request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, testIp);

            try (CloseableHttpResponse response = client.execute(request)) {
                assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
            }
        }

        // Should still not be banned after 2 requests (limit is 3)
        assertFalse(isIpBanned(testIp));
    }

    @Test
    public void shouldApplyRateLimitToClassLevelApiEndpoint() throws Exception {
        String testIp = "*************"; // Use different IP to avoid conflicts

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(baseUri.resolve(CLASS_RATE_LIMITED_ENDPOINT_PATH));
            request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, testIp);

            try (CloseableHttpResponse response = client.execute(request)) {
                assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
            }
        }

        // Verify rate limiting was applied to class-level endpoint
        assertFalse(isIpBanned(testIp));
    }

    @Test
    public void shouldSkipRateLimitingForEndpointWithoutConfiguration() throws Exception {
        String testIp = "*************"; // Use different IP to avoid conflicts

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(baseUri.resolve(BASIC_ENDPOINT_PATH));
            request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, testIp);

            try (CloseableHttpResponse response = client.execute(request)) {
                assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
            }
        }

        // Verify no rate limiting data was created for basic endpoint
        assertFalse(isIpBanned(testIp));
    }

    @Test
    public void shouldNotUpdateTTLAfterEachRequest() throws Exception {
        // set lower period to make test shorter
        Duration period = Duration.ofSeconds(1);
        props.cfg().setLocalProperty("ip-rate-limiter.%s.period".formatted(RATE_LIMITER_KEY), period);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            // Make requests up to the rate limit
            for (int iteration = 0; iteration < DEFAULT_RATE_LIMIT * 2; iteration++) {
                HttpPost request = new HttpPost(baseUri.resolve(RATE_LIMITED_ENDPOINT_PATH));
                request.setHeader(HttpProto.HEADER_X_CLIENT_REAL_IP, TEST_IP_ADDRESS);

                try (CloseableHttpResponse response = client.execute(request)) {
                    assertEquals(HttpStatus.SC_OK, response.getStatusLine().getStatusCode());
                }
                Thread.sleep( period.toMillis() / DEFAULT_RATE_LIMIT);
            }
            assertFalse(isIpBanned(TEST_IP_ADDRESS));
        }
    }

    // Helper methods using public static constants
    private boolean isIpBanned(String remoteIp) throws Exception {
        String banKey = IpRateLimiterApiFilter.GENERATE_BAN_KEY.apply(remoteIp);
        return memcachedService.get(banKey) != null;
    }

    private void banIpForTesting(String remoteIp, int ttlSeconds) throws Exception {
        String banKey = IpRateLimiterApiFilter.GENERATE_BAN_KEY.apply(remoteIp);
        memcachedService.set(banKey, ttlSeconds, Boolean.TRUE.toString());
    }

}
