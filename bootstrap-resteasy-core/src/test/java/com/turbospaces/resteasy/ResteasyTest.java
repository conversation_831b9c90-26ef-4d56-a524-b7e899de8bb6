package com.turbospaces.resteasy;

import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Stream;

import com.turbospaces.resteasy.marshaller.EncryptionAlgorithm;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.http.Header;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpOptions;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.jboss.resteasy.spi.HttpRequest;
import org.jboss.resteasy.spi.MarshalledEntity;
import org.jboss.resteasy.spi.ResteasyDeployment;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.github.robtimus.obfuscation.Obfuscated;
import com.github.robtimus.obfuscation.Obfuscator;
import com.github.robtimus.obfuscation.annotation.ObfuscateAll;
import com.github.robtimus.obfuscation.jackson.databind.ObfuscationModule;
import com.google.common.base.Joiner;
import com.google.common.net.HostAndPort;
import com.google.common.reflect.TypeToken;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.http.HttpProto;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.memcached.RubyeyeMemcachedService;
import com.turbospaces.resteasy.marshaller.PayloadEncryptor;
import com.turbospaces.ssl.SSL;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.internal.InMemoryRateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.search.RequiredSearch;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpUtil;
import io.netty.handler.ssl.JdkSslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.SslProvider;
import io.netty.handler.ssl.util.SelfSignedCertificate;
import io.vavr.CheckedRunnable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.GenericType;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.NewCookie;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.ResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import nl.altindag.log.LogCaptor;

class ResteasyTest {
    Logger logger = LoggerFactory.getLogger(getClass());

    static Stream<Arguments> algorithms() {
        return Stream.of(
                Arguments.of("", EncryptionAlgorithm.CFB),
                Arguments.of(EncryptionAlgorithm.CFB.getHeaderName(), EncryptionAlgorithm.CFB),
                Arguments.of(EncryptionAlgorithm.GCM.getHeaderName(), EncryptionAlgorithm.GCM)
        );
    }

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private ApplicationProperties props;
    private Bootstrap bootstrap;
    private URI uri;
    private SSL ssl;

    @BeforeEach
    public void beforeAll() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        MAPPER.setDateFormat(new StdDateFormat());
        MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        MAPPER.setSerializationInclusion(Include.NON_NULL);
        MAPPER.registerModule(ObfuscationModule.defaultModule());

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        cfg.setDefaultProperty("app.ip.whitelist", Joiner.on(',').join("************/22", PlatformUtil.detectIp()));
        cfg.setDefaultProperty("app.throttling", 1);

        uri = new URIBuilder().setScheme("https").setHost("localhost").setPort(port).build();

        props = new ApplicationProperties(cfg.factory());
        bootstrap = new SimpleBootstrap(props, RootContext.class);
        bootstrap.run();

        ssl = new SSL();
        ssl.addUntrustedCertificates(HostAndPort.fromParts("localhost", port));
    }

    @AfterEach
    public void afterAll() throws Exception {
        if (bootstrap != null) {
            bootstrap.shutdown();
        }
    }

    @Test
    void validationErrorAsync() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Payload payload = new Payload();
            // password must not be printed
            payload.password = Obfuscator.all().obfuscateObject("hidden");
            HttpPost post = new HttpPost(new URIBuilder(uri).setPath("/v1/status-async").build());
            post.setEntity(new StringEntity(MAPPER.writeValueAsString(payload), ContentType.APPLICATION_JSON));
            post.setHeader("Accept", "application/json");
            post.setHeader("Content-type", "application/json");

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    void validationErrorSync() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Payload payload = new Payload();

            HttpPost post = new HttpPost(new URIBuilder(uri).setPath("/v1/status-sync").build());
            post.setEntity(new StringEntity(MAPPER.writeValueAsString(payload), ContentType.APPLICATION_JSON));

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Test
    void statuSync500() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Payload payload = new Payload();
            payload.email = "<EMAIL>";
            payload.password = Obfuscator.all().obfuscateObject("my-password");

            HttpPost post = new HttpPost(new URIBuilder(uri).setPath("/v1/status-async-500").build());
            post.setEntity(new StringEntity(MAPPER.writeValueAsString(payload), ContentType.APPLICATION_JSON));

            try (CloseableHttpResponse resp = httpClient.execute(post)) {
                Assertions.assertEquals(HttpStatus.SC_INTERNAL_SERVER_ERROR, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @SuppressWarnings({ "serial", "unchecked" })
    @Test
    void happyPath() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Payload payload = new Payload();
            payload.email = System.currentTimeMillis() + "@" + "mail.com";
            payload.password = Obfuscator.all().obfuscateObject("passw0rd");

            var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
            JaxrsClientFactoryBean factory = new JaxrsClientFactoryBean(props, new InMemoryRateLimiterRegistry(), httpClient, executor);
            factory.setMapper(MAPPER);
            factory.setRequestConfig(new Consumer<RequestConfig.Builder>() {
                @Override
                public void accept(RequestConfig.Builder b) {
                    b.setSocketTimeout((int) TimeUnit.SECONDS.toMillis(1));
                }
            });

            ResteasyClient resteasyClient = factory.getObject().build();
            ResteasyWebTarget webTarget = resteasyClient.target(new URIBuilder(uri).build());
            ApiResource proxy = webTarget.proxyBuilder(ApiResource.class)
                    .defaultProduces(MediaType.APPLICATION_JSON)
                    .defaultConsumes(MediaType.APPLICATION_JSON)
                    .build();

            TypeToken<MarshalledEntity<Payload>> type = new TypeToken<>() {};
            try (var logCaptor = LogCaptor.forName(HttpClientBodyPrinter.class.getPackage().getName() + "." + "ClientLoggingFilter")) {
                try (Response resp = proxy.statusSync(payload)) {
                    resp.bufferEntity();
                    resp.bufferEntity();

                    MarshalledEntity<Payload> marshalled = (MarshalledEntity<Payload>) resp.readEntity(new GenericType<>(type.getType()));
                    Payload tmp = marshalled.getEntity();

                    Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatus());
                    Assertions.assertEquals(payload.now, tmp.now);
                    Assertions.assertEquals(payload.email, tmp.email);
                    Assertions.assertNotNull(tmp.mdc.get(MdcTags.MDC_PATH));
                    Assertions.assertNotNull(tmp.mdc.get(MdcTags.MDC_METHOD));
                    Assertions.assertNotNull(tmp.mdc.get(MdcTags.MDC_TRACE_ID));

                    RequiredSearch search1 = bootstrap.meterRegistry().get(props.HTTP_METRICS_LATENCY_KEY.get());
                    Timer timer = search1.timer();
                    Assertions.assertNotNull(timer);
                    Assertions.assertEquals("/v1/status-sync", timer.getId().getTag("path"));
                    Assertions.assertEquals("POST", timer.getId().getTag("method"));
                    Assertions.assertEquals("200", timer.getId().getTag("status"));
                    Assertions.assertEquals("tagChild", timer.getId().getTag("custom"));
                    Assertions.assertEquals("tagRoot", timer.getId().getTag("customSecond"));
                    Assertions.assertEquals(1, timer.count());
                }
                // request
                Assertions.assertNotNull(logCaptor.getLogEvents().get(0).getDiagnosticContext().get(MdcTags.MDC_PATH));
                Assertions.assertNotNull(logCaptor.getLogEvents().get(0).getDiagnosticContext().get(MdcTags.MDC_METHOD));

                // response
                Assertions.assertNotNull(logCaptor.getLogEvents().get(1).getDiagnosticContext().get(MdcTags.MDC_PATH));
                Assertions.assertNotNull(logCaptor.getLogEvents().get(1).getDiagnosticContext().get(MdcTags.MDC_METHOD));
                Assertions.assertNotNull(logCaptor.getLogEvents().get(1).getDiagnosticContext().get(MdcTags.MDC_STATUS_CODE));
            }
        }
    }

    @ParameterizedTest
    @MethodSource("algorithms")
    void testEncryption(String algorithmHeaderName, EncryptionAlgorithm expectedAlgorithm) throws Throwable {

        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Payload payload = new Payload();
            payload.email = "<EMAIL>";
            payload.password = Obfuscator.all().obfuscateObject("password");
            payload.now = 42;

            var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
            JaxrsClientFactoryBean factory = new JaxrsClientFactoryBean(props, new InMemoryRateLimiterRegistry(), httpClient, executor);
            factory.setMapper(MAPPER);

            ResteasyClient resteasyClient = factory.getObject().build();
            ResteasyWebTarget webTarget = resteasyClient.target(new URIBuilder(uri).build());
            ApiResource proxy = webTarget.proxyBuilder(ApiResource.class)
                    .defaultProduces(MediaType.APPLICATION_JSON)
                    .defaultConsumes(MediaType.APPLICATION_JSON)
                    .build();

            String encryptKey1 = "encryptKey1";
            // testing two times decryption
            try (Response resp1 = proxy.encrypt(payload, encryptKey1,algorithmHeaderName)) {
                Assertions.assertEquals(
                        "{\"now\":42,\"email\":\"<EMAIL>\",\"password\":\"password\"}",
                        PayloadEncryptor.decrypt((InputStream) resp1.getEntity(), encryptKey1, expectedAlgorithm));
            }
            try (Response resp2 = proxy.encrypt(payload, encryptKey1,algorithmHeaderName)) {
                Assertions.assertEquals(
                        "{\"now\":42,\"email\":\"<EMAIL>\",\"password\":\"password\"}",
                        PayloadEncryptor.decrypt((InputStream) resp2.getEntity(), encryptKey1, expectedAlgorithm));
            }
            // testing new key
            String encryptKey2 = "encryptKey2";
            try (Response resp3 = proxy.encrypt(payload, encryptKey2,algorithmHeaderName)) {
                Assertions.assertEquals(
                        "{\"now\":42,\"email\":\"<EMAIL>\",\"password\":\"password\"}",
                        PayloadEncryptor.decrypt((InputStream) resp3.getEntity(), encryptKey2, expectedAlgorithm));
            }
        }
    }

    @Test
    void testEncryptionNoAlgorithm() throws Throwable {

        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Payload payload = new Payload();
            payload.email = "<EMAIL>";
            payload.password = Obfuscator.all().obfuscateObject("password");
            payload.now = 42;

            var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
            JaxrsClientFactoryBean factory = new JaxrsClientFactoryBean(props, new InMemoryRateLimiterRegistry(), httpClient, executor);
            factory.setMapper(MAPPER);

            ResteasyClient resteasyClient = factory.getObject().build();
            ResteasyWebTarget webTarget = resteasyClient.target(new URIBuilder(uri).build());
            ApiResource proxy = webTarget.proxyBuilder(ApiResource.class)
                    .defaultProduces(MediaType.APPLICATION_JSON)
                    .defaultConsumes(MediaType.APPLICATION_JSON)
                    .build();

            String encryptKey1 = "encryptKey1";

            try (Response resp1 = proxy.encrypt(payload, encryptKey1)) {
                Assertions.assertEquals(
                        "{\"now\":42,\"email\":\"<EMAIL>\",\"password\":\"password\"}",
                        PayloadEncryptor.decrypt((InputStream) resp1.getEntity(), encryptKey1, EncryptionAlgorithm.CFB));
            }
        }
    }

    @Test
    void infoPlatform() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            HttpGet get = new HttpGet(new URIBuilder(uri).setPath("/v1/info-platform").addParameter("q", StringUtils.EMPTY).build());
            get.setHeader("Accept", "application/json");
            get.setHeader("Content-type", "application/json");
            get.setHeader(HttpProto.HEADER_CF_CONNECTING_IP, PlatformUtil.detectIp());

            try (CloseableHttpResponse resp = httpClient.execute(get)) {
                EntityUtils.toString(resp.getEntity());

                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                Assertions.assertEquals(bootstrap.spaceName(), resp.getFirstHeader("X-Space").getValue());
                Assertions.assertEquals(bootstrap.appId(), resp.getFirstHeader("X-App").getValue());
                Assertions.assertEquals(bootstrap.release(), resp.getFirstHeader("X-Version").getValue());

                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_PATH).getValue());
                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_METHOD).getValue());
                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_TRACE_ID).getValue());
                Assertions.assertEquals(resp.getFirstHeader(MdcTags.MDC_TRACE_ID).getValue(), resp.getFirstHeader(HttpProto.HEADER_X_TRACE_ID).getValue());
            }
        }
    }

    @Test
    void infoPlatformTrace() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            String uuid = PlatformUtil.randomUUID().toString();

            HttpGet get = new HttpGet(new URIBuilder(uri).setPath("/v1/info-platform-trace").addParameter("q", "v1").addParameter("password", "v2").build());
            get.addHeader("X-My-Trace-Id", uuid);
            get.addHeader("X-Api-Key", "Secure-Token" + ":" + uuid);
            get.addHeader("X-Api-Key2", "Secure-Token2" + ":" + uuid);
            get.addHeader("X-Api-Key3", "Secure-Token3" + ":" + uuid);
            get.setHeader("Accept", "application/json");
            get.setHeader("Content-type", "application/json");

            try (CloseableHttpResponse resp = httpClient.execute(get)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                Assertions.assertEquals(bootstrap.spaceName(), resp.getFirstHeader("X-Space").getValue());
                Assertions.assertEquals(bootstrap.appId(), resp.getFirstHeader("X-App").getValue());
                Assertions.assertEquals(bootstrap.release(), resp.getFirstHeader("X-Version").getValue());

                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_PATH).getValue());
                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_METHOD).getValue());
                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_TRACE_ID).getValue());
                Assertions.assertEquals(uuid, resp.getFirstHeader(MdcTags.MDC_TRACE_ID).getValue());
                Assertions.assertEquals(resp.getFirstHeader(MdcTags.MDC_TRACE_ID).getValue(), resp.getFirstHeader(HttpProto.HEADER_X_TRACE_ID).getValue());
            }
        }
    }

    @Test
    void infoExecutor() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            HttpGet get = new HttpGet(new URIBuilder(uri).setPath("/v1/info-executor").addParameter("q", StringUtils.EMPTY).build());
            get.setHeader("Accept", "application/json");
            get.setHeader("Content-type", "application/json");

            try (CloseableHttpResponse resp = httpClient.execute(get)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                Assertions.assertEquals(bootstrap.spaceName(), resp.getFirstHeader("X-Space").getValue());
                Assertions.assertEquals(bootstrap.appId(), resp.getFirstHeader("X-App").getValue());
                Assertions.assertEquals(bootstrap.release(), resp.getFirstHeader("X-Version").getValue());

                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_PATH));
                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_METHOD));
                Assertions.assertNotNull(resp.getFirstHeader(MdcTags.MDC_TRACE_ID));
            }
        }
    }

    @Test
    void notFound() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            HttpGet get = new HttpGet(new URIBuilder(uri).setPath("/v1/not-found").build());
            get.setHeader("Accept", "application/json");
            get.setHeader("Content-type", "application/json");

            try (CloseableHttpResponse resp = httpClient.execute(get)) {
                Assertions.assertEquals(HttpStatus.SC_NOT_FOUND, resp.getStatusLine().getStatusCode());
                Assertions.assertNull(resp.getFirstHeader(MdcTags.MDC_PATH));
                Assertions.assertNull(resp.getFirstHeader(MdcTags.MDC_METHOD));
                Assertions.assertNull(resp.getFirstHeader(MdcTags.MDC_TRACE_ID));
            }

            // assert metrics on no match
            RequiredSearch search1 = bootstrap.meterRegistry().get(props.HTTP_METRICS_LATENCY_KEY.get());
            Timer timer = search1.timer();
            Assertions.assertNotNull(timer);
            Assertions.assertEquals("/v1/not-found", timer.getId().getTag("path"));
            Assertions.assertEquals("GET", timer.getId().getTag("method"));
            Assertions.assertEquals("404", timer.getId().getTag("status"));
            Assertions.assertEquals(1, timer.count());
        }
    }

    @Test
    void cors() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Header original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), "http://remote-server.com");

            HttpOptions options = new HttpOptions(new URIBuilder(uri).setPath("/v1/status-async").build());
            options.setHeader("Accept", "application/json");
            options.setHeader("Content-type", "application/json");
            options.setHeader(original);
            options.setHeader(new BasicHeader(HttpHeaderNames.ACCESS_CONTROL_REQUEST_HEADERS.toString(), "trace-id"));
            options.setHeader(new BasicHeader(HttpHeaderNames.ACCESS_CONTROL_REQUEST_METHOD.toString(), "post"));

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());

                boolean allowCredentials = Boolean.parseBoolean(resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_CREDENTIALS.toString()).getValue());
                String allowOrigin = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString()).getValue();
                String allowHeaders = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS.toString()).getValue();
                String allowMethods = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS.toString()).getValue();

                Assertions.assertEquals(props.CORS_ALLOW_CREDENTIALS.get(), allowCredentials);
                Assertions.assertEquals(Joiner.on(',').join(props.CORS_ALLOWED_HEADERS.get()), allowHeaders); // ~ override what is sent in request
                Assertions.assertEquals(Joiner.on(',').join(props.CORS_ALLOWED_METHODS.get()), allowMethods); // ~ override what is sent in request
                Assertions.assertEquals(original.getValue(), allowOrigin);

                Assertions.assertNull(resp.getFirstHeader(MdcTags.MDC_PATH));
                Assertions.assertNull(resp.getFirstHeader(MdcTags.MDC_METHOD));
                Assertions.assertNull(resp.getFirstHeader(MdcTags.MDC_TRACE_ID));
            }
        }
    }

    @Test
    void corsNoAllowRequestHeaders() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Header original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), "http://remote-server-1.com");

            HttpOptions options = new HttpOptions(new URIBuilder(uri).setPath("/v1/status-async").build());
            options.setHeader("Accept", "application/json");
            options.setHeader("Content-type", "application/json");
            options.setHeader(original);
            options.setHeader(new BasicHeader(HttpHeaderNames.ACCESS_CONTROL_REQUEST_METHOD.toString(), "post"));

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());

                boolean allowCredentials = Boolean.parseBoolean(resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_CREDENTIALS.toString()).getValue());
                String allowOrigin = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString()).getValue();
                Header allowHeaders = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS.toString());
                String allowMethods = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS.toString()).getValue();

                Assertions.assertEquals(props.CORS_ALLOW_CREDENTIALS.get(), allowCredentials);
                Assertions.assertNull(allowHeaders);
                Assertions.assertEquals(Joiner.on(',').join(props.CORS_ALLOWED_METHODS.get()), allowMethods); // ~ override what is sent in request
                Assertions.assertEquals(original.getValue(), allowOrigin);
            }
        }
    }

    @Test
    void corsNoMethod() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Header original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), "http://remote-server-2.com");

            HttpOptions options = new HttpOptions(new URIBuilder(uri).setPath("/v1/status-async").build());
            options.setHeader("Accept", "application/json");
            options.setHeader("Content-type", "application/json");
            options.setHeader(original);
            options.setHeader(new BasicHeader(HttpHeaderNames.ACCESS_CONTROL_REQUEST_HEADERS.toString(), "trace-id"));

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());

                boolean allowCredentials = Boolean.parseBoolean(resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_CREDENTIALS.toString()).getValue());
                String allowOrigin = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString()).getValue();
                String allowHeaders = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS.toString()).getValue();
                Header allowMethods = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS.toString());

                Assertions.assertEquals(props.CORS_ALLOW_CREDENTIALS.get(), allowCredentials);
                Assertions.assertEquals(Joiner.on(',').join(props.CORS_ALLOWED_HEADERS.get()), allowHeaders); // ~ override what is sent in request
                Assertions.assertNull(allowMethods);
                Assertions.assertEquals(original.getValue(), allowOrigin);
            }
        }
    }

    @Test
    void corsNoMethodAndNoHeader() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            Header original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), "http://remote-server-3.com");

            HttpOptions options = new HttpOptions(new URIBuilder(uri).setPath("/v1/status-async").build());
            options.setHeader("Accept", "application/json");
            options.setHeader("Content-type", "application/json");
            options.setHeader(original);

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());

                boolean allowCredentials = Boolean.parseBoolean(resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_CREDENTIALS.toString()).getValue());
                String allowOrigin = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString()).getValue();
                Header allowHeaders = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS.toString());
                Header allowMethods = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS.toString());

                Assertions.assertEquals(props.CORS_ALLOW_CREDENTIALS.get(), allowCredentials);
                Assertions.assertNull(allowHeaders);
                Assertions.assertNull(allowMethods);
                Assertions.assertEquals(original.getValue(), allowOrigin);
            }
        }
    }

    @Test
    void corsAllowOriginHeader() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            String myServerOrigin = "http://my-server.com";
            String myServerOriginByPattern = "http://my-server-by-pattern.*\\.test\\.com";

            var property = String.join(",", List.of(myServerOrigin, myServerOriginByPattern));
            props.cfg().setDefaultProperty(props.CORS_ALLOWED_ORIGINS.getKey(), property);

            Header original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), "http://remote-server.com-4");
            HttpOptions options = new HttpOptions(new URIBuilder(uri).setPath("/v1/status-async").build());
            options.setHeader("Accept", "application/json");
            options.setHeader("Content-type", "application/json");
            options.setHeader(original);

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
            }

            original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), myServerOrigin);
            options.setHeader(original);

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());

                String allowOrigin = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString()).getValue();
                Assertions.assertEquals(myServerOrigin, allowOrigin);
            }

            String myServerOriginByPatternUrl = "http://my-server-by-pattern-123.test.com";
            original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), myServerOriginByPatternUrl);
            options.setHeader(original);

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
            }

            props.cfg().setDefaultProperty(props.CORS_MATCH_ORIGINS_BY_PATTERN.getKey(), true);

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());

                String allowOrigin = resp.getFirstHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString()).getValue();
                Assertions.assertEquals(myServerOriginByPatternUrl, allowOrigin);
            }

            String myServerOriginByPatternWrongUrl = "http://my-server-by-pattern-123.wrong-test.com";
            original = new BasicHeader(HttpHeaderNames.ORIGIN.toString(), myServerOriginByPatternWrongUrl);
            options.setHeader(original);

            try (CloseableHttpResponse resp = httpClient.execute(options)) {
                Assertions.assertEquals(HttpStatus.SC_FORBIDDEN, resp.getStatusLine().getStatusCode());
            }
        }
    }

    @Disabled
    @Test
    void blockingCallTest() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            HttpGet get = new HttpGet(new URIBuilder(uri).setPath("/v1/aspect/blocking-call").build());
            var resp = httpClient.execute(get);
            var entity = EntityUtils.toString(resp.getEntity());
            Assertions.assertEquals(HttpStatus.SC_BAD_GATEWAY, resp.getStatusLine().getStatusCode());
            Assertions.assertEquals("api abuse detected: execution(DefaultTestServiceApi.blockingCall()), blocking remote call in HTTP controller", entity);
        }
    }

    @Test
    void nonBlockingCallTest() throws Throwable {
        try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(ssl.build()).build()) {
            HttpGet get = new HttpGet(new URIBuilder(uri).setPath("/v1/aspect/non-blocking-call").build());
            var resp = httpClient.execute(get);
            var entity = EntityUtils.toString(resp.getEntity());
            Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
            Assertions.assertTrue(StringUtils.isEmpty(entity));
        }
    }

    public static class Payload {
        public long now = System.currentTimeMillis();

        public Map<String, String> mdc;

        @NotEmpty
        @Email
        public String email;

        @NotNull
        @ObfuscateAll
        public Obfuscated<String> password;

        @Override
        public String toString() {
            return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
        }
    }

    @Configuration
    @EnableAspectJAutoProxy(proxyTargetClass = true)
    public static class RootContext {
        @Bean
        public TestServiceApi serviceApi() {
            return new DefaultTestServiceApi();
        }
        @Bean
        public ApiResource resource(ApplicationProperties props, MeterRegistry meterRegistry, TestServiceApi serviceApi) {
            return new StatusResource(props, meterRegistry, serviceApi);
        }
        @Bean
        public RubyeyeMemcachedService memcachedService() {
            return Mockito.mock(RubyeyeMemcachedService.class);
        }
        @Bean
        public ResteasyChannel channel(
                ApplicationProperties props,
                DynamicCloud cloud,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                RubyeyeMemcachedService memcachedService) throws Exception {
            SelfSignedCertificate ssc = new SelfSignedCertificate("localhost");
            JdkSslContext sslContext = (JdkSslContext) SslContextBuilder.forServer(ssc.certificate(), ssc.privateKey()).sslProvider(SslProvider.JDK).build();

            ResteasyChannel channel = ResteasyChannel.s2c(
                    props,
                    cloud,
                    meterRegistry,
                    rateLimiterRegistry,
                    props.CLOUD_APP_PORT.get(),
                    memcachedService);
            channel.sslContext(sslContext.context());
            channel.mapper(MAPPER);
            return channel;
        }
    }

    @Path("/v1")
    @ApiEndpoint(headersToMask = "X-Api-Key3", ipWhitelistKey = "app.ip.whitelist", rateBarrierKey = "app.throttling", obfuscateRequestBody = true,
            metricTags = { @Tag(key = "custom", value = "tagRoot"), @Tag(key = "customSecond", value = "tagRoot") })
    public interface ApiResource {
        @POST
        @Path("/status-async")
        void status(
                @Suspended AsyncResponse async,
                @Context ChannelHandlerContext ctx,
                @Context ResteasyDeployment deployment,
                @NotNull @Valid Payload payload,
                @Context HttpRequest req) throws Exception;

        @POST
        @Path("/status-sync")
        @ApiEndpoint(metricTags = { @Tag(key = "custom", value = "tagChild") })
        Response statusSync(@NotNull @Valid Payload payload) throws Exception;

        @POST
        @Path("/encrypt")
        Response encrypt(@NotNull @Valid Payload payload, @HeaderParam(HttpProto.HEADER_X_ENCRYPT) String encryptKey) throws Exception;

        @POST
        @Path("/encrypt")
        Response encrypt(@NotNull @Valid Payload payload, @HeaderParam(HttpProto.HEADER_X_ENCRYPT) String encryptKey, @HeaderParam(HttpProto.HEADER_X_ALGORITHM) String algorithm) throws Exception;

        @GET
        @Path("/info-executor")
        @ApiEndpoint(doNotPrintResponseBody = true)
        void infoExecutor(@Suspended AsyncResponse async) throws Exception;

        @GET
        @Path("/info-platform")
        void infoPlatform(@Suspended AsyncResponse async) throws Exception;

        @POST
        @Path("/status-async-500")
        void statusAsync500(@Suspended AsyncResponse async, @NotNull @Valid Payload payload) throws Exception;

        @GET
        @ApiEndpoint(
                traceHeader = "X-My-Trace-Id",
                headersToMask = { "X-Api-Key2" },
                queryParamsToMask = { "password2", "password3" })
        @Path("/info-platform-trace")
        void infoPlatformPreserveTraceId(@Suspended AsyncResponse async) throws Exception;

        @GET
        @Path("/aspect/blocking-call")
        Response blockingCallAspect();

        @GET
        @Path("/aspect/non-blocking-call")
        Response nonBlockingCallAspect();

        @GET
        @Path("/aspect/heavy-call")
        Response heavyCall();
    }

    @Slf4j
    public static class StatusResource implements ApiResource, InitializingBean, DisposableBean {
        private final ApplicationProperties props;
        private final PlatformExecutorService executor;
        private final TestServiceApi serviceApi;

        public StatusResource(ApplicationProperties props, MeterRegistry meterRegistry, TestServiceApi serviceApi) {
            this.props = props;
            this.serviceApi = serviceApi;
            this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
        }
        @Override
        public void afterPropertiesSet() throws Exception {
            executor.setBeanName(PlatformUtil.randomUUID().toString());
            executor.afterPropertiesSet();
        }
        @Override
        public void destroy() throws Exception {
            executor.destroy();
        }
        @Override
        public void status(
                AsyncResponse async,
                ChannelHandlerContext ctx,
                ResteasyDeployment deployment,
                Payload payload,
                HttpRequest req) throws Exception {
            InputStream io = req.getInputStream();
            io.reset();

            String json = new String(IOUtils.toByteArray(io));
            Payload clone = MAPPER.readValue(json, Payload.class);

            Assertions.assertTrue(EqualsBuilder.reflectionEquals(payload, clone));

            log.trace("hash: {}", payload.email.hashCode()); // ~ check NPE
            payload.mdc = MDC.getCopyOfContextMap();

            HttpUtil.formatHostnameForHttp((InetSocketAddress) ctx.channel().remoteAddress());
            CompletableFuture.runAsync(new Runnable() {
                @Override
                public void run() {
                    async.resume(Response.ok(payload).build());
                }
            });
        }

        @Override
        public Response statusSync(Payload payload) {
            log.trace("hash: {}", payload.email.hashCode()); // ~ check NPE
            payload.mdc = MDC.getCopyOfContextMap();

            return Response.ok(payload).build();
        }

        @Override
        public Response encrypt(Payload payload, String encryptKey) throws Exception {
            payload.now = 42;
            return Response.ok(payload).build();
        }

        @Override
        public Response encrypt(Payload payload, String encryptKey, String algorithm) throws Exception {
            payload.now = 42;
            return Response.ok(payload).build();
        }

        @Override
        public void statusAsync500(AsyncResponse async, Payload payload) {
            async.resume(new UnsupportedOperationException());
        }

        @Override
        public void infoExecutor(AsyncResponse async) throws Exception {
            Runnable task = task(async);
            executor.submit(new CheckedRunnable() {
                @Override
                public void run() throws Throwable {
                    task.run();
                }
            });
        }

        @Override
        public void infoPlatform(AsyncResponse async) {
            executor.execute(task(async));
        }

        @Override
        public void infoPlatformPreserveTraceId(AsyncResponse async) {
            executor.execute(task(async));
        }

        @Override
        public Response blockingCallAspect() {
            try {
                serviceApi.blockingCall();
                return Response.ok().build();
            } catch (Exception ex) {
                return Response.status(Response.Status.BAD_GATEWAY).entity(ex.getMessage()).build();
            }
        }

        @Override
        public Response nonBlockingCallAspect() {
            serviceApi.nonBlockingCall();
            return Response.ok().build();
        }

        @Override
        public Response heavyCall() {
            serviceApi.heavyCall();
            return Response.ok().build();
        }

        protected Runnable task(AsyncResponse async) {
            Map<String, String> mdc = MDC.getCopyOfContextMap();
            return new Runnable() {
                @Override
                public void run() {
                    try {
                        ObjectNode objectNode = MAPPER.createObjectNode();
                        objectNode.put("space", props.CLOUD_APP_SPACE_NAME.get());
                        objectNode.put("app", props.CLOUD_APP_ID.get());
                        objectNode.put("version", PlatformUtil.version(props.CLOUD_APP_NAME));

                        ResponseBuilder reply = Response.ok(objectNode.toPrettyString())
                                .header("X-Space", props.CLOUD_APP_SPACE_NAME.get())
                                .header("X-App", props.CLOUD_APP_ID.get())
                                .header("X-Version", PlatformUtil.version(props.CLOUD_APP_NAME))
                                .header("X-Class-Name", jakarta.el.ExpressionFactory.class.getName())
                                .cookie(new NewCookie.Builder("cookie-1").value("value-1").build(),
                                        new NewCookie.Builder("cookie-2").value("value-2").httpOnly(true).build());

                        if (mdc != null) {
                            for (Entry<String, String> it : mdc.entrySet()) {
                                reply.header(it.getKey(), it.getValue());
                            }
                        }

                        async.resume(reply.build());
                    } catch (Exception err) {
                        async.resume(err);
                    }
                }
            };
        }
    }

    public interface TestServiceApi {
        void blockingCall();
        void nonBlockingCall();
        void heavyCall();
    }

    @Slf4j
    public static class DefaultTestServiceApi implements TestServiceApi {
        @Override
        public void blockingCall() {
            log.info("Blocking call ...");
        }
        @Override
        public void nonBlockingCall() {
            log.info("Non blocking call ...");
        }
        @Override
        public void heavyCall() {
            log.info("Some heavy call ...");
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
