package com.turbospaces.zookeeper;

import java.util.Objects;
import java.util.function.Consumer;

import org.apache.curator.ensemble.fixed.FixedEnsembleProvider;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;

import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class CuratorFactoryBean extends AbstractFactoryBean<CuratorFramework> {
    private final ApplicationProperties props;
    private final DynamicCloud cloud;

    @Setter
    @Accessors(fluent = true)
    private int maxRetries = 3;

    @Override
    public Class<?> getObjectType() {
        return CuratorFramework.class;
    }
    @Override
    protected CuratorFramework createInstance() throws Exception {
        //
        // ~ initial subscribe occurs in current thread
        //
        var ensemble = new FixedEnsembleProvider("localhost:2181");
        cloud.serviceInfoByName(UPSs.ZOOKEEPER).subscribe(new Consumer<ServiceInfo>() {
            @Override
            public void accept(ServiceInfo si) {
                String connection = RawServiceInfo.class.cast(si).read();
                log.debug("about to initialize curator using connection: {}", connection);
                ensemble.setConnectionString(connection);
            }
        });

        CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder();
        builder.namespace(props.CLOUD_APP_SPACE_NAME.get());
        builder.connectionTimeoutMs((int) props.TCP_SOCKET_TIMEOUT.get().toMillis());
        builder.sessionTimeoutMs((int) props.ZOOKEEPER_SESSION_TIMEOUT.get().toMillis());
        builder.retryPolicy(new ExponentialBackoffRetry((int) props.APP_BACKOFF_RETRY_FIRST.get().toMillis(), maxRetries));
        builder.ensembleProvider(ensemble);

        CuratorFramework curator = builder.build();
        curator.start();

        return curator;
    }
    @Override
    protected void destroyInstance(CuratorFramework instance) throws Exception {
        if (Objects.nonNull(instance)) {
            instance.close();
        }
    }
}
