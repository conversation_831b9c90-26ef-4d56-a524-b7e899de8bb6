package com.turbospaces.zookeeper;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.leader.CancelLeadershipException;
import org.apache.curator.framework.recipes.leader.LeaderSelector;
import org.apache.curator.framework.recipes.leader.LeaderSelectorListener;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.curator.utils.ZKPaths;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.SupersetFlag;
import com.turbospaces.ebean.CacheManager;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks.EmitResult;

@Slf4j
public class LeaderActivatorByFlag
        implements BeanNameAware, InitializingBean, DisposableBean, ApplicationRunner, LeaderSelectorListener {
    private final AtomicBoolean active = new AtomicBoolean();
    private final ApplicationProperties props;
    private final CuratorFramework curator;
    private final CacheManager cacheManager;
    private final SupersetFlag flag;
    private final ExecutorService timer;
    private LeaderSelector selector;
    private String beanName;
    private String lpath;

    public LeaderActivatorByFlag(ApplicationProperties props, CuratorFramework curator, CacheManager cacheManager, SupersetFlag flag) {
        this.props = Objects.requireNonNull(props);
        this.curator = Objects.requireNonNull(curator);
        this.cacheManager = Objects.requireNonNull(cacheManager);
        this.flag = Objects.requireNonNull(flag);

        //
        // ~ just one thread
        //
        this.timer = Executors.newSingleThreadExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                return t;
            }
        });
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = Objects.requireNonNull(name);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        active.set(true);
        lpath = ZKPaths.makePath(props.CLOUD_APP_ID.get(), "leaders", beanName);
        selector = new LeaderSelector(curator, lpath, this);
        selector.autoRequeue();
    }
    @Override
    public void destroy() throws Exception {
        active.set(false);
        timer.shutdown();
        if (Objects.nonNull(selector)) {
            selector.close();
        }
    }
    @Override
    public void run(ApplicationArguments args) throws Exception {
        //
        // ~ actually do not block current thread
        //
        timer.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    curator.blockUntilConnected();
                    selector.start();
                } catch (InterruptedException err) {
                    log.error(err.getMessage(), err);
                }
            }
        });
    }
    @Override
    public void takeLeadership(CuratorFramework client) {
        log.info("acquired leadership role on: {}", props.CLOUD_APP_HOST.get());

        try {
            //
            // ~ we emit immediately the event and invalidate cache
            //
            if (activate().isSuccess()) {
                cacheManager.clearAll(true);
            }

            //
            // ~ and simply keep signal-ing forever (assuming distinctUntilChanged is in place)
            //
            while (active.get()) {
                flag.tryEmitNext(true);
                Thread.sleep(Duration.ofSeconds(1));
            }
        } catch (InterruptedException err) {
            log.debug(err.getMessage(), err);
            Thread.currentThread().interrupt();
        } catch (Throwable err) {
            log.error(err.getMessage(), err);
            ExceptionUtils.wrapAndThrow(err);
        } finally {
            passivate();
        }
    }
    @Override
    public void stateChanged(CuratorFramework client, ConnectionState newState) {
        if (client.getConnectionStateErrorPolicy().isErrorState(newState)) {
            try {
                passivate();
            } catch (Exception err) {
                log.error(err.getMessage(), err);
            } finally {
                try {
                    cacheManager.clearAll(true);
                } catch (Throwable t) {
                    log.error(t.getMessage(), t);
                }
            }
            throw new CancelLeadershipException();
        }
    }
    private EmitResult activate() {
        StopWatch stopWatch = StopWatch.createStarted();
        //
        // ~ on stream consumer (this is actually observable which propagates flag update to all subscribers)
        //
        EmitResult toReturn = flag.tryEmitNext(true);
        stopWatch.stop();
        log.info("activated by superset flag: {} on: {} lpath: {} in: {}", flag.getKey(), props.CLOUD_APP_HOST.get(), lpath, stopWatch);
        return toReturn;
    }
    private EmitResult passivate() {
        StopWatch stopWatch = StopWatch.createStarted();
        //
        // ~ off stream consumer (it will stop sink under the hood)
        //
        EmitResult toReturn = flag.tryEmitNext(false);
        stopWatch.stop();
        log.info("passivated by superset flag: {} on: {} lpath: {} in: {}", flag.getKey(), props.CLOUD_APP_HOST.get(), lpath, stopWatch);
        return toReturn;
    }
}
