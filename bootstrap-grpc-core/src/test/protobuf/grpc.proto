syntax = "proto3";
package com.turbospaces.api.v1;

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message GetApplicationVersionRequest {}
message GetExternalIpRequest {}
message HealthCheckRequest {}

message GetApplicationVersionResponse {
  string version = 1;
}

message GetExternalIpResponse {
  string ip = 1;
}

message HealthCheckResponse {
  bool healthy = 1;
}

message GetIntsRequest {
  int32 from = 1;
  int32 to = 2;
}

message GetIntsResponse {
  int32 value = 1;
}

service AdminApi {
  rpc getVersion (GetApplicationVersionRequest) returns (GetApplicationVersionResponse) {}
  rpc getExternalIp (GetExternalIpRequest) returns (GetExternalIpResponse) {}
  rpc healthCheck (HealthCheckRequest) returns (HealthCheckResponse) {}
  rpc getInts (GetIntsRequest) returns (stream GetIntsResponse) {}
}