package com.turbospaces.grpc;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.api.v1.AdminApiGrpc;
import com.turbospaces.api.v1.AdminApiGrpc.AdminApiBlockingStub;
import com.turbospaces.api.v1.GetApplicationVersionRequest;
import com.turbospaces.api.v1.GetIntsRequest;
import com.turbospaces.api.v1.GetIntsResponse;
import com.turbospaces.api.v1.HealthCheckRequest;
import com.turbospaces.api.v1.HealthCheckResponse;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall.SimpleForwardingClientCall;
import io.grpc.ForwardingClientCallListener.SimpleForwardingClientCallListener;
import io.grpc.ForwardingServerCall.SimpleForwardingServerCall;
import io.grpc.ManagedChannel;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import io.grpc.ServerCall;
import io.grpc.ServerCall.Listener;
import io.grpc.ServerCallExecutorSupplier;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.grpc.ServerInterceptors;
import io.grpc.StatusRuntimeException;
import io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.NettyServerBuilder;
import io.grpc.protobuf.StatusProto;
import io.grpc.stub.StreamObserver;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Generated;

public class AdminServiceApiTest {
    public static final Metadata.Key<String> X_NOW = Metadata.Key.of("X-NOW", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_MESSAGE_ID = Metadata.Key.of("X-MESSAGE-ID", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_GENERATED = Metadata.Key.of("X-GENERATED-NAME", Metadata.ASCII_STRING_MARSHALLER);
    public static Logger LOGGER = LoggerFactory.getLogger(AdminServiceApiTest.class);

    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        bootstrap.run();

        try {
            ManagedChannel channel = NettyChannelBuilder.forAddress("localhost", port).usePlaintext().disableRetry().build();

            try {
                int iterations = 16;
                for (int i = 0; i < iterations; i++) {
                    CountDownLatch latch = new CountDownLatch(1);
                    AdminApiGrpc.AdminApiFutureStub futureStub = AdminApiGrpc
                            .newFutureStub(channel)
                            .withInterceptors(new ClientInterceptor() {
                                @Override
                                public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
                                        MethodDescriptor<ReqT, RespT> method,
                                        CallOptions callOptions,
                                        Channel next) {
                                    return new SimpleForwardingClientCall<ReqT, RespT>(next.newCall(method, callOptions)) {
                                        @Override
                                        public void start(Listener<RespT> responseListener, Metadata meta) {
                                            meta.put(X_NOW, Long.toString(System.currentTimeMillis()));
                                            meta.put(X_MESSAGE_ID, PlatformUtil.randomUUID().toString());
                                            meta.put(X_GENERATED, Generated.class.getName());

                                            super.start(new SimpleForwardingClientCallListener<RespT>(responseListener) {
                                                @Override
                                                public void onHeaders(Metadata headers) {
                                                    super.onHeaders(headers);
                                                }
                                            }, meta);
                                        }
                                    };
                                }
                            });
                    ListenableFuture<HealthCheckResponse> healthCheck = futureStub.healthCheck(HealthCheckRequest.newBuilder().build());
                    healthCheck.addListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                boolean healthy = healthCheck.get().getHealthy();
                                LOGGER.debug("isHealthy: {}", healthy);
                                Assertions.assertTrue(healthy);
                            } catch (InterruptedException | ExecutionException err) {
                                LOGGER.error(err.getMessage(), err);
                            } finally {
                                latch.countDown();
                            }
                        }
                    }, Executors.newSingleThreadExecutor());
                    Assertions.assertTrue(latch.await(30, TimeUnit.SECONDS));
                }
                AdminApiBlockingStub blockingStub = AdminApiGrpc.newBlockingStub(channel);
                Assertions.assertNotNull(blockingStub.getVersion(GetApplicationVersionRequest.newBuilder().build()).getVersion());
            } finally {
                channel.shutdown();
            }
        } finally {
            bootstrap.shutdown();
        }
    }
    @Test
    public void stream() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppStreamingConfig.class);
        bootstrap.run();

        try {
            ManagedChannel channel = NettyChannelBuilder.forAddress("localhost", port).usePlaintext().disableRetry().build();

            try {
                int iterations = 1 * 1024;
                CountDownLatch latch = new CountDownLatch(iterations);
                CountDownLatch completed = new CountDownLatch(1);
                AdminApiGrpc.AdminApiStub futureStub = AdminApiGrpc.newStub(channel);
                GetIntsRequest req = GetIntsRequest.newBuilder().setFrom(0).setTo(iterations).build();
                futureStub.getInts(req, new StreamObserver<GetIntsResponse>() {
                    @Override
                    public void onNext(GetIntsResponse value) {
                        latch.countDown();
                    }
                    @Override
                    public void onError(Throwable t) {

                    }
                    @Override
                    public void onCompleted() {
                        completed.countDown();
                    }
                });
                Assertions.assertTrue(completed.await(30, TimeUnit.SECONDS));
                Assertions.assertTrue(latch.await(30, TimeUnit.SECONDS));
            } finally {
                channel.shutdown();
            }
        } finally {
            bootstrap.shutdown();
        }
    }
    @Test
    public void networkFailure() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppStreamingConfig.class);
        bootstrap.run();

        try {
            ManagedChannel channel = NettyChannelBuilder.forAddress("localhost", port + 1).usePlaintext().disableRetry().build();

            try {
                AdminApiBlockingStub futureStub = AdminApiGrpc.newBlockingStub(channel);
                futureStub.healthCheck(HealthCheckRequest.newBuilder().build());
                Assertions.fail();
            } catch (StatusRuntimeException err) {
                com.google.rpc.Status wrapped = StatusProto.fromThrowable(err);
                Assertions.assertEquals(com.google.rpc.Code.UNAVAILABLE.getNumber(), wrapped.getCode());
            } finally {
                channel.shutdown();
            }
        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public AdminServiceApi serviceApi(ApplicationProperties props) {
            return new AdminServiceApi(props);
        }
        @Bean
        public ServerInterceptor interceptor() {
            return new ServerInterceptor() {
                @Override
                public <ReqT, RespT> Listener<ReqT> interceptCall(
                        ServerCall<ReqT, RespT> call,
                        Metadata headers,
                        ServerCallHandler<ReqT, RespT> next) {
                    return next.startCall(new SimpleForwardingServerCall<ReqT, RespT>(call) {
                        @Override
                        public void sendMessage(RespT message) {
                            super.sendMessage(message);
                        }
                        @Override
                        public void sendHeaders(Metadata meta) {
                            super.sendHeaders(meta);
                        }
                    }, headers);
                }
            };
        }
        @Bean
        public ServerCallExecutorSupplier executorSupplier() {
            ExecutorService threadPool = Executors.newCachedThreadPool();
            return new ServerCallExecutorSupplier() {
                @Override
                public <ReqT, RespT> Executor getExecutor(ServerCall<ReqT, RespT> call, Metadata metadata) {
                    String now = metadata.get(X_NOW);
                    return StringUtils.isEmpty(now) ? MoreExecutors.directExecutor() : threadPool;
                }
            };
        }
        @Bean
        public GrpcChannel channel(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                AdminServiceApi api,
                ServerInterceptor interceptor,
                ServerCallExecutorSupplier callExecutor) {
            return new GrpcChannel(props, meterRegistry, props.CLOUD_APP_PORT.get()) {
                @Override
                public void accept(NettyServerBuilder builder) {
                    super.accept(builder);
                    builder.callExecutor(callExecutor).addService(ServerInterceptors.intercept(api, interceptor));
                }
            };
        }
    }

    @Configuration
    public static class AppStreamingConfig {
        @Bean
        public AdminServiceApi serviceApi(ApplicationProperties props) {
            return new AdminServiceApi(props);
        }
        @Bean
        public GrpcChannel channel(ApplicationProperties props, MeterRegistry meterRegistry, AdminServiceApi api) {
            return new GrpcChannel(props, meterRegistry, props.CLOUD_APP_PORT.get()) {
                @Override
                public void accept(NettyServerBuilder builder) {
                    super.accept(builder);
                    builder.addService(api);
                }
            };
        }
    }
}
