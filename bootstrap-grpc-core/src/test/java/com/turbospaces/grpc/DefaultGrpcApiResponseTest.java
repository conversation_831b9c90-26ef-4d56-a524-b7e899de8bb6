package com.turbospaces.grpc;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Any;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.rpc.ApiResponseEntity;
import com.turbospaces.rpc.DefaultGrpcApiResponse;

import api.v1.ApiFactory;
import api.v1.MockApiFactory;
import io.grpc.protobuf.StatusProto;
import io.vavr.CheckedConsumer;

public class DefaultGrpcApiResponseTest {
    private final CommonObjectMapper mapper = new CommonObjectMapper();

    @Test
    public void basicCancelled() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ApiFactory apiFactory = new MockApiFactory(props, mapper);
        SettableFuture<Timestamp> settable = SettableFuture.create();
        Timestamp req = Timestamps.fromMillis(System.currentTimeMillis());
        api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();

        DefaultGrpcApiResponse<Timestamp> apiCall = new DefaultGrpcApiResponse<Timestamp>(
                props,
                new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers, Any.pack(req)),
                settable,
                apiFactory,
                Timestamp.getDefaultInstance());

        Assertions.assertFalse(apiCall.isCancelled());
        Assertions.assertFalse(apiCall.isDone());

        apiCall.cancel(true);
        Assertions.assertTrue(apiCall.isCancelled());
        Assertions.assertTrue(apiCall.isDone());

        try {
            apiCall.get(1, TimeUnit.NANOSECONDS);
            Assertions.fail();
        } catch (Exception err) {

        }

        settable.set(Timestamp.newBuilder(req).build());
    }
    @Test
    public void basicDone() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ApiFactory apiFactory = new MockApiFactory(props, mapper);
        SettableFuture<Timestamp> settable = SettableFuture.create();
        CountDownLatch latch = new CountDownLatch(1);
        Timestamp req = Timestamps.fromMillis(System.currentTimeMillis());
        api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();

        DefaultGrpcApiResponse<Timestamp> apiCall = new DefaultGrpcApiResponse<Timestamp>(
                props,
                new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers, Any.pack(req)),
                settable,
                apiFactory,
                Timestamp.getDefaultInstance());
        apiCall.addCallback(new FutureCallback<ApiResponseEntity<Timestamp>>() {
            @Override
            public void onSuccess(ApiResponseEntity<Timestamp> result) {
                latch.countDown();
            }
            @Override
            public void onFailure(Throwable t) {

            }
        });

        //
        // ~ complete in ASYNC manner
        //
        new Thread(new Runnable() {
            @Override
            public void run() {
                settable.set(Timestamp.newBuilder(req).build());
            }
        }).start();

        apiCall.get();
        Assertions.assertFalse(apiCall.isCancelled());
        Assertions.assertTrue(apiCall.isDone());

        latch.await();
    }
    @Test
    public void thenVerifyHappyPath() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ApiFactory apiFactory = new MockApiFactory(props, mapper);

        SettableFuture<Timestamp> settable = SettableFuture.create();
        CountDownLatch latch = new CountDownLatch(1);
        Timestamp req = Timestamps.fromMillis(System.currentTimeMillis());
        api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();

        DefaultGrpcApiResponse<Timestamp> apiCall = new DefaultGrpcApiResponse<Timestamp>(
                props,
                new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers, Any.pack(req)),
                settable,
                apiFactory,
                Timestamp.getDefaultInstance());

        var tmp = apiCall.thenVerifyOk(MoreExecutors.directExecutor());
        tmp.addCallback(new FutureCallback<ApiResponseEntity<Timestamp>>() {
            @Override
            public void onSuccess(ApiResponseEntity<Timestamp> result) {
                latch.countDown();
            }
            @Override
            public void onFailure(Throwable t) {

            }
        });


        //
        // ~ complete in ASYNC manner
        //
        new Thread(new Runnable() {
            @Override
            public void run() {
                settable.set(Timestamp.newBuilder(req).build());
            }
        }).start();

        tmp.get();
        Assertions.assertFalse(tmp.isCancelled());
        Assertions.assertTrue(tmp.isDone());

        latch.await();
        tmp.get();
    }
    @Test
    public void thenVerifyHappyError() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ApiFactory apiFactory = new MockApiFactory(props, mapper);

        SettableFuture<Timestamp> settable = SettableFuture.create();
        CountDownLatch latch = new CountDownLatch(1);
        Timestamp req = Timestamps.fromMillis(System.currentTimeMillis());
        api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();

        DefaultGrpcApiResponse<Timestamp> apiCall = new DefaultGrpcApiResponse<>(
                props,
                new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers, Any.pack(req)),
                settable,
                apiFactory,
                Timestamp.getDefaultInstance());

        var tmp = apiCall.thenVerifyOk(MoreExecutors.directExecutor());
        tmp.addCallback(new FutureCallback<ApiResponseEntity<Timestamp>>() {
            @Override
            public void onSuccess(ApiResponseEntity<Timestamp> result) {

            }
            @Override
            public void onFailure(Throwable t) {
                latch.countDown();
            }
        });

        //
        // ~ complete in ASYNC manner
        //
        new Thread(new Runnable() {
            @Override
            public void run() {
                settable.setException(new IllegalArgumentException());
            }
        }).start();

        latch.await();
        try {
            tmp.get();

            Assertions.assertFalse(tmp.isCancelled());
            Assertions.assertTrue(tmp.isDone());

            Assertions.fail();
        } catch (InterruptedException err) {
            Assertions.fail();
        } catch (ExecutionException err) {
            Assertions.assertEquals(err.getCause().getClass(), IllegalArgumentException.class);
        }
    }
    @Test
    public void whenCompleteWithoutErrorHappyPath() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ApiFactory apiFactory = new MockApiFactory(props, mapper);

        SettableFuture<Timestamp> settable = SettableFuture.create();
        CountDownLatch latch = new CountDownLatch(1);
        Timestamp req = Timestamps.fromMillis(System.currentTimeMillis());
        api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();

        DefaultGrpcApiResponse<Timestamp> apiCall = new DefaultGrpcApiResponse<Timestamp>(
                props,
                new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers, Any.pack(req)),
                settable,
                apiFactory,
                Timestamp.getDefaultInstance());
        var tmp = apiCall.thenVerifyOkAndAccept(new CheckedConsumer<Timestamp>() {
            @Override
            public void accept(Timestamp t) {
                latch.countDown();
            }
        });

        //
        // ~ complete in ASYNC manner
        //
        new Thread(new Runnable() {
            @Override
            public void run() {
                settable.set(Timestamp.newBuilder(req).build());
            }
        }).start();

        tmp.get();

        Assertions.assertFalse(tmp.isCancelled());
        Assertions.assertTrue(tmp.isDone());

        latch.await();
        tmp.get();
    }
    @Test
    public void exceptional() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        ApiFactory apiFactory = new MockApiFactory(props, mapper);

        SettableFuture<Timestamp> settable = SettableFuture.create();
        CountDownLatch latch = new CountDownLatch(1);
        Timestamp req = Timestamps.fromMillis(System.currentTimeMillis());
        api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();

        DefaultGrpcApiResponse<Timestamp> apiCall = new DefaultGrpcApiResponse<>(
                props,
                new DefaultRequestWrapperFacade(apiFactory.eventTemplate(), headers, Any.pack(req)),
                settable,
                apiFactory,
                Timestamp.getDefaultInstance());
        apiCall.addCallback(new FutureCallback<ApiResponseEntity<Timestamp>>() {
            @Override
            public void onSuccess(ApiResponseEntity<Timestamp> result) {
                latch.countDown();
            }
            @Override
            public void onFailure(Throwable t) {

            }
        });

        com.google.rpc.Status status = com.google.rpc.Status.newBuilder()
                .setCode(com.google.rpc.Code.UNAVAILABLE_VALUE)
                .setMessage("IO exception")
                .build();

        //
        // ~ complete in ASYNC manner
        //
        new Thread(new Runnable() {
            @Override
            public void run() {
                settable.setException(StatusProto.toStatusRuntimeException(status));
            }
        }).start();

        latch.await();
        ApiResponseEntity<Timestamp> apiResponse = apiCall.get();
        Assertions.assertTrue(apiResponse.status().isTimeout());
    }
}
