package com.turbospaces.grpc;

import com.google.common.collect.Range;
import com.turbospaces.api.v1.AdminApiGrpc.AdminApiImplBase;
import com.turbospaces.api.v1.GetApplicationVersionRequest;
import com.turbospaces.api.v1.GetApplicationVersionResponse;
import com.turbospaces.api.v1.GetExternalIpRequest;
import com.turbospaces.api.v1.GetExternalIpResponse;
import com.turbospaces.api.v1.GetIntsRequest;
import com.turbospaces.api.v1.GetIntsResponse;
import com.turbospaces.api.v1.HealthCheckRequest;
import com.turbospaces.api.v1.HealthCheckResponse;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class AdminServiceApi extends AdminApiImplBase {
    private final ApplicationProperties props;

    @Override
    public void getVersion(GetApplicationVersionRequest request, StreamObserver<GetApplicationVersionResponse> responseObserver) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                String version = PlatformUtil.version(props.CLOUD_APP_NAME);
                responseObserver.onNext(GetApplicationVersionResponse.newBuilder().setVersion(version).build());
                responseObserver.onCompleted();
            }
        }).start();
    }
    @Override
    public void getExternalIp(GetExternalIpRequest request, StreamObserver<GetExternalIpResponse> responseObserver) {
        String externalIp = props.externalIp();
        responseObserver.onNext(GetExternalIpResponse.newBuilder().setIp(externalIp).build());
        responseObserver.onCompleted();
    }
    @Override
    public void healthCheck(HealthCheckRequest request, StreamObserver<HealthCheckResponse> responseObserver) {
        boolean isHealthy = true;
        responseObserver.onNext(HealthCheckResponse.newBuilder().setHealthy(isHealthy).build());
        responseObserver.onCompleted();
    }
    @Override
    public void getInts(GetIntsRequest request, StreamObserver<GetIntsResponse> responseObserver) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Range<Integer> range = Range.closed(request.getFrom(), request.getTo());
                for (int i = range.lowerEndpoint(); i <= range.upperEndpoint(); i++) {
                    responseObserver.onNext(GetIntsResponse.newBuilder().setValue(i).build());
                }
                responseObserver.onCompleted();
            }
        }).start();
    }
}
