package com.turbospaces.grpc;

import java.util.Objects;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import com.turbospaces.cfg.ApplicationProperties;

import io.grpc.Server;
import io.grpc.netty.NettyServerBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.grpc.MetricCollectingServerInterceptor;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.vavr.CheckedConsumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class GrpcChannel implements InitializingBean, DisposableBean, CheckedConsumer<NettyServerBuilder> {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final int port;

    private Server server;

    @Override
    public void afterPropertiesSet() throws Exception {
        NettyServerBuilder forPort = NettyServerBuilder.forPort(port);
        accept(forPort);
        this.server = forPort.build().start();

        log.info("grpc server is up and running on={} port", port);
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(server)) {
            server.shutdown();
        }
    }
    @Override
    public void accept(NettyServerBuilder builder) {
        builder
                .channelType(NioServerSocketChannel.class)
                .bossEventLoopGroup(new NioEventLoopGroup(props.NETTY_ACCEPTOR_POOL_SIZE.get()))
                .workerEventLoopGroup(new NioEventLoopGroup(props.NETTY_WORKER_POOL_SIZE.get()))
                .directExecutor() // ~ application to handle it properly by default (serve in parallel)
                .withOption(ChannelOption.SO_BACKLOG, props.TCP_SOCKET_BACKLOG.get())
                .withOption(ChannelOption.SO_REUSEADDR, props.isDevMode())
                .withChildOption(ChannelOption.SO_KEEPALIVE, props.TCP_KEEP_ALIVE.get())
                .withChildOption(ChannelOption.TCP_NODELAY, props.TCP_NO_DELAY.get())
                .intercept(new MetricCollectingServerInterceptor(meterRegistry));
    }
}
