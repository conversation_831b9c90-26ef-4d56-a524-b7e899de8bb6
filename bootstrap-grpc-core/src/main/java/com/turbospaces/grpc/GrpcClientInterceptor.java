package com.turbospaces.grpc;

import java.util.Objects;
import java.util.UUID;

import com.turbospaces.cfg.ApplicationProperties;

import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.Context;
import io.grpc.MethodDescriptor;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.grpc.MetricCollectingClientInterceptor;
import io.netty.util.AsciiString;

public class GrpcClientInterceptor implements ClientInterceptor {
    private final ApplicationProperties props;
    private final MetricCollectingClientInterceptor interceptor;

    public GrpcClientInterceptor(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.props = Objects.requireNonNull(props);
        this.interceptor = new MetricCollectingClientInterceptor(meterRegistry);
    }
    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
            MethodDescriptor<ReqT, RespT> method,
            CallOptions callOptions,
            Channel next) {
        ClientCall<ReqT, RespT> interceptCall = interceptor.interceptCall(method, callOptions, next);
        return new ContextPreservingClientCall<ReqT, RespT>(props, interceptCall, method);
    }
    public static Context setContext(UUID messageId, AsciiString routingKey) {
        Context current = Context.current();
        current = current.withValue(GrpcHeaders.CONTEXT_MESSAGE_ID, messageId.toString());

        if (Objects.nonNull(routingKey)) {
            current = current.withValue(GrpcHeaders.CONTEXT_ROUTING_KEY, routingKey.toString());
        }
        return current.attach();
    }
}
