package com.turbospaces.grpc;

import io.grpc.Context;
import io.grpc.Contexts;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCall.Listener;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;

public class GrpcServer<PERSON>nterpector implements ServerInterceptor {
    @Override
    public <ReqT, RespT> Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call,
            Metadata meta,
            ServerCallHandler<ReqT, RespT> next) {
        String messageId = meta.get(GrpcHeaders.X_MESSAGE_ID);
        String topic = meta.get(GrpcHeaders.X_TOPIC);
        long timestamp = Long.parseLong(meta.get(GrpcHeaders.X_TIMESTAMP));

        Context context = Context.current()
                .withValue(GrpcHeaders.CONTEXT_MESSAGE_ID, messageId)
                .withValue(GrpcHeaders.CONTEXT_TOPIC, topic)
                .withValue(GrpcHeaders.CONTEXT_TIMESTAMP, timestamp);

        if (meta.containsKey(GrpcHeaders.X_TRACE_ID)) {
            String traceId = meta.get(GrpcHeaders.X_TRACE_ID);
            context = context.withValue(GrpcHeaders.CONTEXT_TRACE_ID, traceId);
        }
        if (meta.containsKey(GrpcHeaders.X_ROUTING_KEY)) {
            String routingKey = meta.get(GrpcHeaders.X_ROUTING_KEY);
            context = context.withValue(GrpcHeaders.CONTEXT_ROUTING_KEY, routingKey);
        }

        return Contexts.interceptCall(context, call, meta, next);
    }
}
