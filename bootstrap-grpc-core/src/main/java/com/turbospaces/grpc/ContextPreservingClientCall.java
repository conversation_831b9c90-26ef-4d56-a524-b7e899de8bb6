package com.turbospaces.grpc;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;

import io.grpc.ClientCall;
import io.grpc.Context;
import io.grpc.ForwardingClientCall.SimpleForwardingClientCall;
import io.grpc.ForwardingClientCallListener.SimpleForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ContextPreservingClientCall<ReqT, RespT> extends SimpleForwardingClientCall<ReqT, RespT> {
    private final ApplicationProperties props;
    private final MethodDescriptor<ReqT, RespT> method;

    public ContextPreservingClientCall(ApplicationProperties props, ClientCall<ReqT, RespT> delegate, MethodDescriptor<ReqT, RespT> method) {
        super(delegate);
        this.props = Objects.requireNonNull(props);
        this.method = Objects.requireNonNull(method);
    }
    @Override
    public void start(Listener<RespT> responseListener, Metadata meta) {
        Context current = Context.current();
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        String messageId = Objects.requireNonNull(GrpcHeaders.CONTEXT_MESSAGE_ID.get(), "Context is not injected");
        putMetaAndDetachContext(current, meta);

        log.debug("about to make grpc call (m-{}):({})", messageId, method.getFullMethodName());
        String operation = PlatformUtil.toLowerUnderscore(method.getFullMethodName());
        StopWatch stopWatch = StopWatch.createStarted();

        super.start(new SimpleForwardingClientCallListener<>(responseListener) {
            @Override
            public void onMessage(RespT message) {
                try {
                    MdcUtil.propagate(mdc);
                    try {
                        super.onMessage(message);
                    } finally {
                        log.debug("completed grpc call (m-{}):({}) in {}, body: {}", messageId, operation, stopWatch, message);
                    }
                } finally {
                    MDC.clear();
                }
            }
        }, meta);
    }
    private void putMetaAndDetachContext(Context current, Metadata meta) {
        String timestamp = Long.toString(System.currentTimeMillis());
        String messageId = GrpcHeaders.CONTEXT_MESSAGE_ID.get();
        String traceId = Objects.isNull(MDC.get(MdcTags.MDC_TRACE_ID)) ? messageId : MDC.get(MdcTags.MDC_TRACE_ID);
        String topic = props.CLOUD_APP_ID.get();
        String routingKey = GrpcHeaders.CONTEXT_ROUTING_KEY.get();

        meta.put(GrpcHeaders.X_MESSAGE_ID, messageId);
        meta.put(GrpcHeaders.X_TRACE_ID, traceId);
        meta.put(GrpcHeaders.X_TIMESTAMP, timestamp);
        meta.put(GrpcHeaders.X_TOPIC, topic);

        //
        // ~ optional
        //
        if (StringUtils.isNotEmpty(routingKey)) {
            meta.put(GrpcHeaders.X_ROUTING_KEY, routingKey.toString());
        }

        current.detach(Context.ROOT);
    }
}
