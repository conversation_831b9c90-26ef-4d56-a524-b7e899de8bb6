package com.turbospaces.grpc;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.collect.ImmutableList;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;
import com.turbospaces.executor.AbstractContextWorkerFactory;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.executor.LogQueueFullCallerRunsPolicy;
import com.turbospaces.executor.PlatformThread;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.executor.WorkUnit;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;

public class GrpcContextWorkerFactory extends AbstractContextWorkerFactory<String, WorkUnit> {
    public GrpcContextWorkerFactory(ApplicationProperties props, MeterRegistry meterRegistry, RateLimiterRegistry rateLimiterRegistry) {
        super(props, meterRegistry);
        workers = CacheBuilder.newBuilder().build(new CacheLoader<String, ContextWorker>() {
            @Override
            public ContextWorker load(String key) {
                var tfb = new ThreadFactoryBuilder();
                tfb.setDaemon(false);
                tfb.setNameFormat("grpc-worker-" + key + "-%d");
                tfb.setThreadFactory(new ThreadFactory() {
                    @Override
                    public Thread newThread(Runnable r) {
                        return new PlatformThread(props, r);
                    }
                });

                var min = props.GRPC_MIN_WORKERS.get();
                var max = props.GRPC_MAX_WORKERS.get();
                var ttl = props.APP_PLATFORM_MAX_IDLE.get();
                var limiter = new TriggerOnceAfterNOccurrencesRateLimiter(key, min, timer);

                var pool = new ThreadPoolExecutor(
                        min,
                        max,
                        ttl.toSeconds(),
                        TimeUnit.SECONDS,
                        new SynchronousQueue<>(),
                        tfb.build(),
                        new LogQueueFullCallerRunsPolicy(limiter));
                var metrics = new ExecutorServiceMetrics(pool, "grpc-worker", ImmutableList.of(Tag.of("topic", key)));
                metrics.bindTo(meterRegistry);

                var worker = new ThreadPoolContextWorker(props, meterRegistry, pool, true);
                worker.afterPropertiesSet();
                worker.setBeanName(beanName + key);
                return worker;
            }
        });
    }
    @Override
    public ContextWorker worker(WorkUnit unit) {
        return workers.getUnchecked(unit.topic());
    }
    @Override
    public void destroy() throws Exception {
        super.destroy();

        //
        // ~ gracefully terminate
        //
        for (ContextWorker pool : workers.asMap().values()) {
            pool.destroy();
        }

        // ~ cleanUp
        workers.invalidateAll();
    }
    @Override
    public String toString() {
        return workers.asMap().toString();
    }
}
