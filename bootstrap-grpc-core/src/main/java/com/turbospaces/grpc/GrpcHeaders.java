package com.turbospaces.grpc;

import io.grpc.Context;
import io.grpc.Metadata;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GrpcHeaders {
    public static final Metadata.Key<String> X_MESSAGE_ID = Metadata.Key.of("X-Message-Id", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_TRACE_ID = Metadata.Key.of("X-Trace-Id", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_TOPIC = Metadata.Key.of("X-Topic", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_BRAND_NAME = Metadata.Key.of("X-Brand-Name", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_TIMESTAMP = Metadata.Key.of("X-Timestamp", Metadata.ASCII_STRING_MARSHALLER);
    public static final Metadata.Key<String> X_ROUTING_KEY = Metadata.Key.of("X-Routing-Key", Metadata.ASCII_STRING_MARSHALLER);

    public static final Context.Key<String> CONTEXT_MESSAGE_ID = Context.key(GrpcHeaders.X_MESSAGE_ID.name());
    public static final Context.Key<String> CONTEXT_TRACE_ID = Context.key(GrpcHeaders.X_TRACE_ID.name());
    public static final Context.Key<String> CONTEXT_BRAND_NAME = Context.key(GrpcHeaders.X_BRAND_NAME.name());
    public static final Context.Key<String> CONTEXT_TOPIC = Context.key(GrpcHeaders.X_TOPIC.name());
    public static final Context.Key<Long> CONTEXT_TIMESTAMP = Context.key(GrpcHeaders.X_TIMESTAMP.name());
    public static final Context.Key<String> CONTEXT_ROUTING_KEY = Context.key(GrpcHeaders.X_ROUTING_KEY.name());
}
