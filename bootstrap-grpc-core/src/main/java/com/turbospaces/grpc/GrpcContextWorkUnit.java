package com.turbospaces.grpc;

import org.apache.commons.lang3.StringUtils;

import com.google.common.io.ByteSource;
import com.turbospaces.executor.WorkUnit;

import lombok.ToString;

@ToString
public class GrpcContextWorkUnit implements WorkUnit {
    //
    // ~ preserve context at the moment of creation
    //
    private final String topic = GrpcHeaders.CONTEXT_TOPIC.get();
    private final long timestamp = GrpcHeaders.CONTEXT_TIMESTAMP.get();
    private final String routingKey = GrpcHeaders.CONTEXT_ROUTING_KEY.get();

    @Override
    public String topic() {
        return topic;
    }
    @Override
    public long timestamp() {
        return timestamp;
    }
    @Override
    public byte[] key() {
        if (StringUtils.isNotEmpty(routingKey)) {
            return routingKey.getBytes();
        }
        return null;
    }
    @Override
    public ByteSource value() {
        return ByteSource.empty();
    }
    public static GrpcContextWorkUnit fromCurrentContext() {
        return new GrpcContextWorkUnit();
    }
}
