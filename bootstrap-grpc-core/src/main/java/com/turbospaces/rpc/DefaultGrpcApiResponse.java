package com.turbospaces.rpc;

import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import api.v1.ReplyUtil;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import io.vavr.CheckedConsumer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultGrpcApiResponse<RESP extends Message> extends AbstractApiResponse<RESP> {
    private final Thread origin;
    private final ApplicationProperties props;
    private final FluentFuture<ApiResponseEntity<RESP>> subject;
    private final RESP prototype;

    public DefaultGrpcApiResponse(
            ApplicationProperties props,
            RequestWrapperFacade reqw,
            ListenableFuture<RESP> post,
            ApiFactory apiFactory,
            RESP prototype) {
        super(apiFactory, reqw);
        this.props = Objects.requireNonNull(props);
        this.prototype = Objects.requireNonNull(prototype);
        this.origin = Thread.currentThread();

        var responseMapper = apiFactory.responseMapper();
        var completable = SettableFuture.<ApiResponseEntity<RESP>> create();
        subject = FluentFuture.from(completable);

        FluentFuture.from(post).addCallback(new FutureCallback<RESP>() {
            @Override
            @SuppressWarnings("unchecked")
            public void onSuccess(RESP result) {
                ResponseWrapperFacade reply = responseMapper.toReply(reqw, result, api.v1.CacheControl.getDefaultInstance());
                Class<RESP> clazz = (Class<RESP>) result.getClass();
                completable.set(new DefaultApiResponseEntity<>(reply, clazz));
            }
            @Override
            @SuppressWarnings("unchecked")
            public void onFailure(Throwable t) {
                log.error(t.getMessage(), t);

                if (t instanceof StatusRuntimeException) {
                    com.google.rpc.Status orig = StatusProto.fromThrowable(t);
                    com.google.rpc.Code origCode = com.google.rpc.Code.forNumber(orig.getCode());

                    String msg = String.format(ReplyUtil.ERROR_FORMAT, reqw.headers().getMessageId());
                    Class<RESP> clazz = (Class<RESP>) prototype.getClass();

                    switch (origCode) {
                        case OK: {
                            var resp = responseMapper.toReply(reqw, prototype, api.v1.CacheControl.getDefaultInstance());
                            completable.set(new DefaultApiResponseEntity<>(resp, clazz));
                            break;
                        }
                        case NOT_FOUND: {
                            var status = apiFactory.toExceptionalNotFoundReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            completable.set(new DefaultApiResponseEntity<>(respw, clazz));
                            break;
                        }
                        case PERMISSION_DENIED:
                        case UNAUTHENTICATED: {
                            var status = apiFactory.toExceptionalAuthReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            completable.set(new DefaultApiResponseEntity<>(respw, clazz));
                            break;
                        }
                        case FAILED_PRECONDITION:
                        case OUT_OF_RANGE:
                        case INVALID_ARGUMENT: {
                            var status = apiFactory.toExceptionalBadRequestReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            completable.set(new DefaultApiResponseEntity<>(respw, clazz));
                            break;
                        }
                        case UNAVAILABLE:
                        case DEADLINE_EXCEEDED: {
                            var status = apiFactory.toExceptionalTimeoutReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            completable.set(new DefaultApiResponseEntity<>(respw, clazz));
                            break;
                        }
                        case INTERNAL:
                        case CANCELLED:
                        case ABORTED:
                        case ALREADY_EXISTS:
                        case DATA_LOSS:
                        case RESOURCE_EXHAUSTED:
                        case UNIMPLEMENTED:
                        case UNKNOWN:
                        case UNRECOGNIZED:
                        default: {
                            var status = apiFactory.toExceptionalSystemReply(msg);
                            var respw = responseMapper.toReply(reqw, prototype, status);
                            completable.set(new DefaultApiResponseEntity<>(respw, clazz));
                            break;
                        }
                    }
                } else {
                    completable.setException(t);
                }
            }
        }, MoreExecutors.directExecutor());
    }
    private DefaultGrpcApiResponse(
            ApplicationProperties props,
            ApiFactory apiFactory,
            RequestWrapperFacade req,
            FluentFuture<ApiResponseEntity<RESP>> subject,
            RESP prototype,
            Thread origin) {
        super(apiFactory, req);
        this.props = Objects.requireNonNull(props);
        this.subject = Objects.requireNonNull(subject);
        this.prototype = Objects.requireNonNull(prototype);
        this.origin = Objects.requireNonNull(origin);
    }
    @Override
    public ApiResponseEntity<RESP> get() throws InterruptedException, ExecutionException {
        return subject.get();
    }
    @Override
    public ApiResponseEntity<RESP> get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return subject.get(timeout, unit);
    }
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return subject.cancel(mayInterruptIfRunning);
    }
    @Override
    public boolean isCancelled() {
        return subject.isCancelled();
    }
    @Override
    public boolean isDone() {
        return subject.isDone();
    }
    @Override
    public void addListener(Runnable listener, Executor executor) {
        subject.addListener(listener, executor);
    }
    @Override
    public void addCallback(FutureCallback<ApiResponseEntity<RESP>> callback, Executor executor) {
        var wrapper = new FutureCallbackWrapper<>(props, callback, origin);
        subject.addCallback(isDone() ? callback : wrapper, executor);
    }
    @Override
    public ApiResponse<RESP> thenVerifyOkAndAccept(CheckedConsumer<RESP> callback, Executor executor) {
        SettableFuture<ApiResponseEntity<RESP>> toReturn = SettableFuture.create();
        subject.addCallback(new FutureCallback<ApiResponseEntity<RESP>>() {
            @Override
            @SuppressWarnings("unchecked")
            public void onSuccess(ApiResponseEntity<RESP> result) {
                boolean isOK = true;

                try {
                    result.verifyOk();
                } catch (Exception err) {
                    toReturn.setException(err);
                    isOK = false;
                }

                if (isOK) {
                    try {
                        RESP body = (RESP) result.any().unpack(prototype.getClass());
                        callback.accept(body);
                        toReturn.set(result);
                    } catch (Throwable err) {
                        toReturn.setException(err);
                    }
                }
            }
            @Override
            public void onFailure(Throwable t) {
                toReturn.setException(t);
            }
        }, executor);

        //
        // ~ new API response by contract
        //
        return new DefaultGrpcApiResponse<>(props, apiFactory, requestWrapper, FluentFuture.from(toReturn), prototype, origin);
    }
}
