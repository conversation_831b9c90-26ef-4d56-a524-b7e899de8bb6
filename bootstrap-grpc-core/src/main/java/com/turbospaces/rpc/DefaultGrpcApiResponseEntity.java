package com.turbospaces.rpc;

import java.io.IOException;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.ResponseStatusFacade;

import api.v1.CacheControl;
import api.v1.Headers;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultGrpcApiResponseEntity<T extends Message> implements ApiResponseEntity<T> {
    private final Headers headers;
    private final ResponseStatusFacade status;
    private final api.v1.CacheControl cacheControl;
    private final Any body;
    private final Class<T> type;

    @Override
    public Headers headers() {
        return headers;
    }
    @Override
    public ResponseStatusFacade status() {
        return status;
    }
    @Override
    public Any any() {
        return body;
    }
    @Override
    public CacheControl cacheControl() {
        return cacheControl;
    }
    @Override
    public T unpack() throws IOException {
        return any().unpack(type);
    }
}
