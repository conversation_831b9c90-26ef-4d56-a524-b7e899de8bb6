package com.turbospaces.ebean;

import java.util.Collection;
import java.util.Map;

import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;

import com.google.common.util.concurrent.ListeningScheduledExecutorService;

import io.ebean.cache.ServerCacheNotification;
import io.ebean.cache.ServerCacheType;
import reactor.core.publisher.Sinks;

public interface BroadcastChannel extends DisposableBean, ApplicationListener<ApplicationReadyEvent> {
    void setBackgroundExecutor(ListeningScheduledExecutorService executor);

    Sinks.One<StopWatch> broadcastPutAsync(String cacheKey, ServerCacheType type, String key, Object obj) throws Throwable;
    Sinks.One<StopWatch> broadcastPutAllAsync(String cacheKey, ServerCacheType type, Map<String, Object> map) throws Throwable;
    Sinks.One<StopWatch> broadcastRemoveAsync(String cacheKey, String key) throws Throwable;
    Sinks.One<StopWatch> broadcastRemoveAllAsync(String cacheKey, Collection<String> keys) throws Throwable;
    Sinks.One<StopWatch> broadcastClearAllAsync(String cacheKey) throws Throwable;
    Sinks.One<StopWatch> broadcastClearAllAsync(boolean localOnly) throws Throwable;
    Sinks.One<StopWatch> broadcastAsync(ServerCacheNotification notification, String data) throws Throwable;
}
