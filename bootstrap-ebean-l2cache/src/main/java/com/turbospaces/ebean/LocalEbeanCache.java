package com.turbospaces.ebean;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.common.collect.ImmutableSet;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.cache.ServerCacheConfig;
import io.ebean.config.CurrentTenantProvider;

public class LocalEbeanCache extends AbstractEbeanCache implements LocalCache {
    private final BroadcastChannel dispatcher;

    public LocalEbeanCache(
            ApplicationProperties props,
            String cacheKey,
            CurrentTenantProvider tenantProvider,
            ServerCacheConfig config,
            BroadcastChannel dispatcher) {
        super(props, cacheKey, tenantProvider, config);
        this.dispatcher = Objects.requireNonNull(dispatcher);
    }
    @Override
    public void put(Object id, Object obj) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            //
            // ~ has to be string because we replicate by STRING to remote nodes
            //
            String key = key(id);
            super.put(key, obj);

            try {
                if (props.CACHE_LOCAL_REPLICATE_BY_INVALIDATION.get()) {
                    dispatcher.broadcastRemoveAsync(cacheKey(), key);
                }
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void putAll(Map<Object, Object> keyValues) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            ImmutableSet.Builder<String> transformed = ImmutableSet.builder();
            for (Entry<Object, Object> entry : keyValues.entrySet()) {
                String key = key(entry.getKey());
                Object value = entry.getValue();

                //
                // ~ has to be string because we replicate by STRING to remote nodes
                //
                transformed.add(key);
                super.put(key, value);
            }

            try {
                if (props.CACHE_LOCAL_REPLICATE_BY_INVALIDATION.get()) {
                    dispatcher.broadcastRemoveAllAsync(cacheKey(), transformed.build());
                }
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void remove(Object id) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            //
            // ~ has to be string because we replicate by STRING to remote nodes
            //
            String key = key(id);
            super.remove(key);

            try {
                if (props.CACHE_LOCAL_REPLICATE_BY_INVALIDATION.get()) {
                    dispatcher.broadcastRemoveAsync(cacheKey(), key);
                }
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void removeAll(Set<Object> keys) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            ImmutableSet.Builder<String> transformed = ImmutableSet.builder();
            for (Object it : keys) {
                String key = key(it);

                //
                // ~ has to be string because we replicate by STRING to remote nodes
                //
                transformed.add(key);
                super.remove(key);
            }

            try {
                if (props.CACHE_LOCAL_REPLICATE_BY_INVALIDATION.get()) {
                    dispatcher.broadcastRemoveAllAsync(cacheKey(), transformed.build());
                }
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void clear() {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            super.clear();
            try {
                dispatcher.broadcastClearAllAsync(cacheKey());
            } catch (Throwable err) {
                log.error(err.getMessage(), err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
}
