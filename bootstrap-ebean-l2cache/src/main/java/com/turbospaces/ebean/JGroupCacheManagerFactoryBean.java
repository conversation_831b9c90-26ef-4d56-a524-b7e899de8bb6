package com.turbospaces.ebean;

import java.util.Objects;

import org.jgroups.JChannel;
import org.jgroups.blocks.RpcDispatcher;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

public class JGroupCacheManagerFactoryBean extends AbstractFactoryBean<CacheManager> {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final JChannel jchannel;
    private final RpcDispatcher dispatcher = new RpcDispatcher();

    public JGroupCacheManagerFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, RateLimiterRegistry rateLimiterRegistry, JChannel jchannel) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.rateLimiterRegistry = Objects.requireNonNull(rateLimiterRegistry);
        this.jchannel = Objects.requireNonNull(jchannel);
    }
    @Override
    public Class<?> getObjectType() {
        return CacheManager.class;
    }
    @Override
    protected CacheManager createInstance() throws Exception {
        JGroupsCacheManager manager = new JGroupsCacheManager(props, meterRegistry, rateLimiterRegistry, dispatcher);

        dispatcher.setAsynDispatching(true);
        dispatcher.setChannel(jchannel);
        dispatcher.setServerObject(manager);
        dispatcher.setMethodLookup(manager);
        dispatcher.start();

        return manager;
    }
    @Override
    protected void destroyInstance(CacheManager instance) throws Exception {
        try {
            instance.destroy();
        } finally {
            dispatcher.close();
        }
    }
}
