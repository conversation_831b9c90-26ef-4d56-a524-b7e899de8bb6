package com.turbospaces.ebean;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.Objects;

import org.jgroups.blocks.MethodCall;
import org.jgroups.util.Streamable;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Accessors(fluent = true)
@Getter
@ToString
public final class MethodCallWithInstanceInfo implements Streamable {
    private String slot;
    private MethodCall call;

    public MethodCallWithInstanceInfo(String slot, MethodCall call) {
        this.slot = Objects.requireNonNull(slot);
        this.call = Objects.requireNonNull(call);
    }
    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeUTF(slot);
        call.writeTo(out);
    }
    @Override
    public void readFrom(DataInput in) throws IOException, ClassNotFoundException {
        slot = in.readUTF();

        call = new MethodCall();
        call.readFrom(in);
    }
}
