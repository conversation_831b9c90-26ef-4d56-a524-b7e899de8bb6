package com.turbospaces.ebean;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

import org.jgroups.blocks.MethodCall;
import org.jgroups.blocks.RequestOptions;
import org.jgroups.blocks.RpcDispatcher;
import org.jgroups.util.RspList;

import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

public class JGroupsCacheManager extends AbstractCacheManager {
    public JGroupsCacheManager(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            RpcDispatcher rpcDispatcher) {
        super(props, meterRegistry, new DefaultBroadcastChannel(props, meterRegistry, rateLimiterRegistry, new Broadcaster() {
            @Override
            public int parallelism() {
                return Runtime.getRuntime().availableProcessors();
            }
            @Override
            public boolean isOpen() {
                return rpcDispatcher.getChannel().isOpen();
            }
            @Override
            public void callRemoteMethods(MethodCall method_call, RequestOptions opts, BiConsumer<Object, Throwable> whenComplete) {
                try {
                    CompletableFuture<RspList<Object>> future = rpcDispatcher.callRemoteMethodsWithFuture(null, method_call, opts);
                    if (Objects.nonNull(future)) {
                        future.whenComplete(whenComplete);
                    } else {
                        whenComplete.accept(new Object(), null);
                    }
                } catch (Exception err) {
                    whenComplete.accept(null, err);
                }
            }
        }));
    }
}
