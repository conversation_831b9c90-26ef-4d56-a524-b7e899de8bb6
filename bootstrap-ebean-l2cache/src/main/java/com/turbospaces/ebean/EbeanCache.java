package com.turbospaces.ebean;

import java.util.EnumSet;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalCause;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.turbospaces.cache.BlockhoundCacheWrapper;

import io.ebean.cache.ServerCache;
import io.ebean.cache.ServerCacheType;
import io.ebeaninternal.server.cache.DefaultServerCacheConfig;

public interface EbeanCache extends ServerCache, ClearableCache, RemovableCache {
    String cacheKey();
    ServerCacheType type();

    static Cache<String, Object> cache(String cacheKey, DefaultServerCacheConfig cfg) {
        Logger logger = LoggerFactory.getLogger(EbeanCache.class);
        return new BlockhoundCacheWrapper<String, Object>(CacheBuilder.newBuilder()
                .maximumSize(cfg.getMaxSize())
                .expireAfterAccess(cfg.getMaxIdleSecs(), TimeUnit.SECONDS)
                .expireAfterWrite(cfg.getMaxSecsToLive(), TimeUnit.SECONDS)
                .recordStats()
                .removalListener(new RemovalListener<Object, Object>() {
                    private final EnumSet<RemovalCause> reasons = EnumSet.of(RemovalCause.EXPIRED, RemovalCause.SIZE);

                    @Override
                    public void onRemoval(RemovalNotification<Object, Object> notification) {
                        if (Objects.nonNull(notification.getCause())) {
                            if (reasons.contains(notification.getCause())) {
                                if (logger.isTraceEnabled()) {
                                    String type = notification.getCause().name().toLowerCase().intern();
                                    logger.trace("onRemoval({}): {} {}", cacheKey, type, notification);
                                }
                            }
                        }
                    }
                }).build());
    }
}
