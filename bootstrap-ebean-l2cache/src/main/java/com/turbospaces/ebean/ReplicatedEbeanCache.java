package com.turbospaces.ebean;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.cache.ServerCacheConfig;
import io.ebean.config.CurrentTenantProvider;

public class ReplicatedEbeanCache extends AbstractEbeanCache implements ReplicatedCache {
    private final BroadcastChannel dispatcher;

    public ReplicatedEbeanCache(
            ApplicationProperties props,
            String cacheKey,
            CurrentTenantProvider tenantProvider,
            ServerCacheConfig config,
            BroadcastChannel dispatcher) {
        super(props, cacheKey, tenantProvider, config);
        this.dispatcher = Objects.requireNonNull(dispatcher);
    }
    @Override
    public void put(Object id, Object obj) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            //
            // ~ has to be string because we replicate by STRING to remote nodes
            //
            String key = key(id);
            super.put(key, obj);

            try {
                dispatcher.broadcastPutAsync(cacheKey(), type(), key, obj);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void putAll(Map<Object, Object> keyValues) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            ImmutableMap.Builder<String, Object> transformed = ImmutableMap.builder();
            for (Entry<Object, Object> entry : keyValues.entrySet()) {
                String key = key(entry.getKey());
                Object value = entry.getValue();

                //
                // ~ has to be string because we replicate by STRING to remote nodes
                //
                transformed.put(key, value);
                super.put(key, value);
            }

            try {
                dispatcher.broadcastPutAllAsync(cacheKey(), type(), transformed.build());
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void remove(Object id) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            //
            // ~ has to be string because we replicate by STRING to remote nodes
            //
            String key = key(id);
            super.remove(key);

            try {
                dispatcher.broadcastRemoveAsync(cacheKey(), key);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void removeAll(Set<Object> keys) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            ImmutableSet.Builder<String> transformed = ImmutableSet.builder();
            for (Object it : keys) {
                String key = key(it);

                //
                // ~ has to be string because we replicate by STRING to remote nodes
                //
                transformed.add(key);
                super.remove(key);
            }

            try {
                dispatcher.broadcastRemoveAllAsync(cacheKey(), transformed.build());
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void clear() {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);

            super.clear();
            try {
                dispatcher.broadcastClearAllAsync(cacheKey());
            } catch (Throwable err) {
                log.error(err.getMessage(), err);
            }
        } finally {
            nonBlocking.set(toReset);
        }
    }
}
