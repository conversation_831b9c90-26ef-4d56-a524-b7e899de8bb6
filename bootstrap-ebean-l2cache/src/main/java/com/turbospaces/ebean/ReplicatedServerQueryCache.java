package com.turbospaces.ebean;

import java.util.Map;
import java.util.Objects;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebeaninternal.server.cache.DefaultServerCacheConfig;
import io.ebeaninternal.server.cache.DefaultServerQueryCache;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
public class ReplicatedServerQueryCache extends DefaultServerQueryCache implements ReplicatedCache {
    private final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    private final ApplicationProperties props;
    private final BroadcastChannel dispatcher;

    public ReplicatedServerQueryCache(ApplicationProperties props, DefaultServerCacheConfig config, BroadcastChannel dispatcher) {
        super(config);
        this.props = Objects.requireNonNull(props);
        this.dispatcher = Objects.requireNonNull(dispatcher);
    }
    @Override
    public void putAll(Map<Object, Object> keyValues) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            super.putAll(keyValues);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void put(Object key, Object value) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            super.put(key, value);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public Object get(Object key) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return super.get(key);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void remove(Object key) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            super.remove(key);
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public void clear() {
        onClear();
        try {
            dispatcher.broadcastClearAllAsync(name);
        } catch (Throwable err) {
            log.error(err.getMessage(), err);
        }
    }
    @Override
    public int onClear() {
        String idx = props.CLOUD_APP_INSTANCE_INDEX.get();
        int size = size();
        log.info("onClear {}, items: {} on ({})", name, size, idx);
        super.clear();
        return size();
    }
}
