package com.turbospaces.ebean;

import java.util.Objects;

import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedisCacheManagerFactoryBean extends AbstractFactoryBean<CacheManager> {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final RedissonClient redisson;

    public RedisCacheManagerFactoryBean(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            RedissonClient redisson) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.rateLimiterRegistry = Objects.requireNonNull(rateLimiterRegistry);
        this.redisson = Objects.requireNonNull(redisson);
    }
    @Override
    public Class<?> getObjectType() {
        return CacheManager.class;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        super.destroy();
    }
    @Override
    protected CacheManager createInstance() throws Exception {
        return new RedisCacheManager(props, meterRegistry, rateLimiterRegistry, redisson);
    }
    @Override
    protected void destroyInstance(CacheManager instance) throws Exception {
        instance.destroy();
    }
}
