package com.turbospaces.ebean;

import java.lang.reflect.Method;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.jgroups.blocks.MethodCall;
import org.jgroups.util.ByteArrayDataInputStream;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.MessageListener;
import org.redisson.client.codec.ByteArrayCodec;

import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

public class RedisCacheManager extends AbstractCacheManager {
    private final RTopic topic;
    private final int listener;

    public RedisCacheManager(ApplicationProperties props, MeterRegistry meterRegistry, RateLimiterRegistry rateLimiterRegistry, RedissonClient redisson) {
        this(
                props,
                "l2cache:" + (StringUtils.isEmpty(props.APP_DNS_QUERY.get()) ? props.CLOUD_APP_ID.get() : props.APP_DNS_QUERY.get()),
                meterRegistry,
                rateLimiterRegistry,
                redisson);
    }
    public RedisCacheManager(
            ApplicationProperties props,
            String channel,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            RedissonClient redisson) {
        super(props, meterRegistry, new DefaultBroadcastChannel(props, meterRegistry, rateLimiterRegistry, new RedissonBroadcaster(props, redisson, channel)));

        //
        // ~ create corresponding subscription (it actually non-blocking with regards to current thread)
        //
        ByteArrayCodec codec = new ByteArrayCodec();
        topic = redisson.getTopic(channel, codec);
        listener = topic.addListener(byte[].class, new MessageListener<byte[]>() {
            @Override
            public void onMessage(CharSequence name, byte[] msg) {
                try (ByteArrayDataInputStream stream = new ByteArrayDataInputStream(msg)) {
                    MethodCallWithInstanceInfo info = new MethodCallWithInstanceInfo();
                    info.readFrom(stream);

                    if (props.CLOUD_APP_INSTANCE_INDEX.get().equals(info.slot())) {
                        //
                        // ~ discard own messages
                        //
                    } else {
                        MethodCall methodCall = info.call();
                        Method method = findMethod(methodCall.getMethodId());

                        //
                        // ~ there must not be such a cases when lookup return NULL
                        // ~ only when the new method has been introduced and cluster nodes have different versions
                        // ~ so we simply ignore it and safely checking NOT-NULL constraint here
                        //
                        if (Objects.nonNull(method)) {
                            log.trace("about to invoke {} using args: {}, channel: {}", method.getName(), methodCall.getArgs(), channel);

                            RedisCacheManager target = RedisCacheManager.this;
                            method.invoke(target, methodCall.getArgs()); // ~ basically reflection call similar to what 'jgroups' does
                        }
                    }
                } catch (Throwable err) {
                    log.error(err.getMessage(), err);
                }
            }
        });
    }
    @Override
    public void destroy() throws Exception {
        try {
            topic.removeListener(listener);
        } finally {
            super.destroy();
        }
    }
}
