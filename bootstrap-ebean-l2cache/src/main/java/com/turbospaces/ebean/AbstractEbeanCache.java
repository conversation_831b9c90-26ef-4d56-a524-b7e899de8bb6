package com.turbospaces.ebean;

import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

import com.google.common.collect.Lists;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.cache.ServerCacheConfig;
import io.ebean.cache.ServerCacheType;
import io.ebean.cache.TenantAwareKey;
import io.ebean.config.CurrentTenantProvider;
import io.ebeaninternal.server.cache.CachedBeanData;
import io.ebeaninternal.server.cache.CachedManyIds;
import io.ebeaninternal.server.cache.DefaultServerCache;
import io.ebeaninternal.server.cache.DefaultServerCacheConfig;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class AbstractEbeanCache extends DefaultServerCache implements EbeanCache, PutableCache, CleanableCache {
    protected final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final String cacheKey;
    protected final TenantAwareKey tenantAwareKey;
    protected final ServerCacheConfig config;
    protected Level level;

    protected AbstractEbeanCache(
            ApplicationProperties props,
            String cacheKey,
            CurrentTenantProvider tenantProvider,
            ServerCacheConfig config) {
        super(new DefaultServerCacheConfig(config));
        this.props = Objects.requireNonNull(props);
        this.cacheKey = Objects.requireNonNull(cacheKey);
        this.tenantAwareKey = new TenantAwareKey(tenantProvider);
        this.config = Objects.requireNonNull(config);
        this.level = Level.valueOf(props.CACHE_REMOTE_LOG_LEVEL.get());

        //
        // ~ subscribe for change w/o restart
        //
        props.CACHE_REMOTE_LOG_LEVEL.subscribe(new Consumer<String>() {
            @Override
            public void accept(String text) {
                if (StringUtils.isNotEmpty(text)) {
                    level = Level.valueOf(text);
                }
            }
        });
    }
    @Override
    public void cleanUp() {
        super.runEviction();
    }
    @Override
    public ServerCacheType type() {
        return config.getType();
    }
    @Override
    public String cacheKey() {
        return cacheKey;
    }
    @Override
    public void onRemove(byte[] data) {
        String key = SerializationUtils.deserialize(data);
        super.remove(key);

        log.atLevel(level).log("onRemove {} by key: {}", cacheKey, key);
    }
    @Override
    public void onRemoveAll(byte[] data) throws Throwable {
        List<String> keys = Lists.newArrayList();
        try (ByteArrayInputStream stream = new ByteArrayInputStream(data)) {
            try (ObjectInputStream in = new ObjectInputStream(stream)) {
                int size = in.readInt();
                for (int i = 0; i < size; i++) {
                    String key = in.readUTF();
                    keys.add(key);
                    super.remove(key);
                }
            }
        }

        log.atLevel(level).log("onRemoveAll {} by keys: {}", cacheKey, keys);
    }
    @Override
    public int onClear() {
        String idx = props.CLOUD_APP_INSTANCE_INDEX.get();
        int size = size();
        log.debug("onClear {} items: {} on ({})", cacheKey, size, idx);
        super.clear();
        return size;
    }
    @Override
    public void onPut(byte[] kbytes, byte[] vbytes) throws Throwable {
        String key = SerializationUtils.deserialize(kbytes);

        //
        // ~ fast and reusable input stream
        //
        switch (type()) {
            case NATURAL_KEY: {
                Object read = SerializationUtils.deserialize(vbytes);
                super.put(key, read);
                break;
            }
            case BEAN: {
                try (ByteArrayInputStream in = new ByteArrayInputStream(vbytes)) {
                    try (ObjectInputStream ois = new ObjectInputStream(in)) {
                        CachedBeanData read = new CachedBeanData();
                        read.readExternal(ois);
                        onPut(key, read);
                    }
                }
                break;
            }
            case COLLECTION_IDS: {
                try (ByteArrayInputStream in = new ByteArrayInputStream(vbytes)) {
                    try (ObjectInputStream ois = new ObjectInputStream(in)) {
                        CachedManyIds read = new CachedManyIds();
                        read.readExternal(ois);
                        onPut(key, read);
                    }
                    break;
                }
            }
            default: {
                throw new IllegalArgumentException("unexpected cache type: " + type());
            }
        }
    }
    @Override
    public void onPutAll(byte[] data) throws Throwable {
        try (ByteArrayInputStream stream = new ByteArrayInputStream(data)) {
            try (ObjectInputStream in = new ObjectInputStream(stream)) {
                int size = in.readInt();
                for (int i = 0; i < size; i++) {
                    String key = in.readUTF();
                    switch (type()) {
                        case NATURAL_KEY: {
                            Object read = in.readObject();
                            super.put(key, read);
                            break;
                        }
                        case BEAN: {
                            CachedBeanData read = new CachedBeanData();
                            read.readExternal(in);
                            onPut(key, read);
                            break;
                        }
                        case COLLECTION_IDS: {
                            CachedManyIds read = new CachedManyIds();
                            read.readExternal(in);
                            onPut(key, read);
                            break;
                        }
                        default: {
                            throw new IllegalArgumentException("unexpected cache type: " + type());
                        }
                    }
                }
            }
        }
    }
    @Override
    public Object get(Object key) {
        boolean toReset = nonBlocking.get();
        try {
            nonBlocking.set(false);
            return super.get(key(key));
        } finally {
            nonBlocking.set(toReset);
        }
    }
    @Override
    public String toString() {
        return cacheKey();
    }
    @Override
    public int hashCode() {
        return Objects.hash(cacheKey);
    }
    @Override
    public boolean equals(Object obj) {
        return Objects.equals(cacheKey, ((AbstractEbeanCache) obj).cacheKey);
    }
    protected String key(Object key) {
        return tenantAwareKey.key(key).toString();
    }
    protected void onPut(String key, CachedBeanData read) {
        //
        // ~ hack under strict concurrency (do not override the data which can be stale on other nodes)
        // ~ we will push the data back shortly
        //
        boolean toPut = true;
        CachedBeanData current = (CachedBeanData) super.get(key);
        if (Objects.nonNull(current)) {
            if (read.getVersion() > 0) {
                toPut = read.getVersion() > current.getVersion(); // ~ READ committed
            }
        }

        if (toPut) {
            log.atLevel(level).log("onPut {} by key: {} bean value: {}", cacheKey, key, read);
            super.put(key, read);
        } else {
            log.atLevel(level).log("onPut {} by key: {} bean value: {} skipped, current version: {}",
                    cacheKey, key, read, current.getVersion());
        }
    }
    protected void onPut(String key, CachedManyIds read) {
        log.atLevel(level).log("onPut {} by key: {} ids value: {}", cacheKey, key, read);
        super.put(key, read);
    }
}
