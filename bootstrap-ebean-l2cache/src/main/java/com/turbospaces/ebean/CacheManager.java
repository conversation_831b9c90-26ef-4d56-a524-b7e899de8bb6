package com.turbospaces.ebean;

import org.springframework.beans.factory.DisposableBean;

import com.turbospaces.cache.ReplicatedCacheManager;

import io.ebean.BackgroundExecutor;
import io.ebean.cache.ServerCache;
import io.ebean.cache.ServerCacheFactory;
import io.ebean.cache.ServerCachePlugin;

public interface CacheManager extends ServerCachePlugin, ServerCacheFactory, ReplicatedCacheManager, DisposableBean {
    void setBackgroundExecutor(BackgroundExecutor executor);
    ServerCache getCache(String string);
}
