package com.turbospaces.ebean;

import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;

import org.apache.commons.io.output.UnsynchronizedByteArrayOutputStream;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jgroups.blocks.MethodCall;
import org.jgroups.blocks.RequestOptions;
import org.springframework.boot.context.event.ApplicationReadyEvent;

import com.google.common.base.Preconditions;
import com.google.common.cache.CacheBuilder;
import com.google.common.util.concurrent.ListenableScheduledFuture;
import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.cache.BlockhoundCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;

import io.ebean.cache.ServerCacheNotification;
import io.ebean.cache.ServerCacheType;
import io.ebeaninternal.server.cache.CachedBeanData;
import io.ebeaninternal.server.cache.CachedManyIds;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.vavr.CheckedRunnable;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;

@Slf4j
public class DefaultBroadcastChannel implements BroadcastChannel {
    private final AtomicBoolean ready = new AtomicBoolean();
    private final AtomicBoolean isActive = new AtomicBoolean(true);
    private final ApplicationProperties props;
    private final LinkedBlockingQueue<MethodCallEvent> queue;
    private final ExecutorService poller;
    private final Semaphore throttler;
    private final BlockhoundCacheWrapper<String, TriggerOnceAfterNOccurrencesRateLimiter> triggers;
    private ListeningScheduledExecutorService backgroundExecutor;
    private ListenableScheduledFuture<?> cleanUpTask;
    private Future<?> task;

    public DefaultBroadcastChannel(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            Broadcaster dispatcher) {
        this.props = Objects.requireNonNull(props);

        int cacheMaxSize = props.CACHE_DEFAULT_MAX_SIZE.get();
        Duration cacheMaxIdle = props.APP_TIMER_INTERVAL.get();
        Duration cacheMaxTtl = props.CACHE_DEFAULT_MAX_IDLE.get();

        triggers = new BlockhoundCacheWrapper<>(
                CacheBuilder.newBuilder()
                        .maximumSize(cacheMaxSize)
                        .expireAfterAccess(cacheMaxIdle)
                        .expireAfterWrite(cacheMaxTtl).build());

        throttler = new Semaphore(dispatcher.parallelism());
        queue = new LinkedBlockingQueue<>(props.CACHE_RING_BUFFER_SIZE.get());

        ThreadFactoryBuilder factory = new ThreadFactoryBuilder();
        factory.setDaemon(true);
        factory.setNameFormat("l2cache-rpc-dispatcher-%d");
        factory.setPriority(Thread.MAX_PRIORITY);

        //
        // ~ simple thread executor must be sufficient actually since we are broadcasting anyway in ASYNC manner
        //
        poller = Executors.newSingleThreadExecutor(factory.build());
        task = poller.submit(new Runnable() {
            @Override
            public void run() {
                while (isActive.get()) {
                    try {
                        MethodCallEvent event = queue.take();
                        //
                        // ~ broadcast over network
                        //
                        var outer = event.stopWatch;
                        var call = event.call;
                        var operation = event.operation;
                        var promise = event.promise;

                        var inner = StopWatch.createStarted();
                        throttler.acquire();
                        if (dispatcher.isOpen()) {
                            dispatcher.callRemoteMethods(call, RequestOptions.ASYNC(), new BiConsumer<Object, Throwable>() {
                                @Override
                                public void accept(Object result, Throwable failure) {
                                    try {
                                        outer.stop();
                                        var took = outer.getTime(TimeUnit.MILLISECONDS);
                                        var metric = meterRegistry.timer("l2-cache-broadcaster", List.of(Tag.of("operation", operation)));
                                        metric.record(took, TimeUnit.MILLISECONDS);

                                        //
                                        // ~ we want to report to sentry so that it is not ignored but not too much
                                        //
                                        if (ready.get()) {
                                            if (took > props.BROADCAST_TOO_LONG_WARNING_PERIOD.get().toMillis()) {
                                                try {
                                                    var reporter = triggers.get(operation, new Callable<>() {
                                                        @Override
                                                        public TriggerOnceAfterNOccurrencesRateLimiter call() throws Exception {
                                                            return new TriggerOnceAfterNOccurrencesRateLimiter(operation, 1, backgroundExecutor);
                                                        }
                                                    });
                                                    if (reporter.acquirePermission()) {
                                                        log.atError().log("{} took too long: {} own: {}", operation, outer, inner);
                                                    }
                                                } catch (ExecutionException err) {
                                                    log.error(err.getMessage(), err.getCause());
                                                }
                                            }
                                        }

                                        //
                                        // ~ well since we have a huge ring buffer by default, we need to help GC to cleanUp
                                        //
                                        if (Objects.nonNull(result)) {
                                            EmitResult emitResult = promise.tryEmitValue(outer);
                                            Preconditions.checkArgument(emitResult.isSuccess());
                                        } else {
                                            promise.tryEmitError(failure);
                                        }
                                    } catch (Exception err) {
                                        log.error(err.getMessage(), err);
                                    } finally {
                                        throttler.release();
                                    }
                                }
                            });
                            inner.stop();
                        }
                    } catch (InterruptedException err) {
                        Thread.currentThread().interrupt();
                    } catch (Exception err) {
                        log.error(err.getMessage(), err);
                    }
                }
            }
        });
    }
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        ready.set(true);
    }
    @Override
    public void destroy() throws Exception {
        isActive.set(false);
        try {
            if (Objects.nonNull(cleanUpTask)) {
                cleanUpTask.cancel(true);
            }
            if (Objects.nonNull(task)) {
                task.cancel(true);
            }
        } finally {
            PlatformUtil.shutdownExecutor(poller, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
        }
    }
    @Override
    public void setBackgroundExecutor(ListeningScheduledExecutorService executor) {
        this.backgroundExecutor = Objects.requireNonNull(executor);

        Duration cleanupInterval = props.APP_TIMER_INTERVAL.get();
        cleanUpTask = executor.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                long size = triggers.size();
                if (size > 0) {
                    log.debug("about to cleanUp l2 error reporters of {} items ...", size);
                }
                triggers.cleanUp();
            }
        }, cleanupInterval.toSeconds(), cleanupInterval.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public Sinks.One<StopWatch> broadcastPutAsync(String cacheKey, ServerCacheType type, String key, Object obj) throws Throwable {
        //
        // ~ based on cache type
        //
        byte[] data = switch (type) {
            case NATURAL_KEY -> SerializationUtils.serialize(Serializable.class.cast(obj));
            case BEAN -> PlatformUtil.serialize(CachedBeanData.class.cast(obj));
            case COLLECTION_IDS -> PlatformUtil.serialize(CachedManyIds.class.cast(obj));
            default -> throw new IllegalArgumentException("Unexpected cache type: " + type);
        };

        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_CACHE_PUT, cacheKey, SerializationUtils.serialize(key), data);
                event.stopWatch = StopWatch.createStarted();
                event.operation = "put(%s)".formatted(cacheKey);
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }
    @Override
    public Sinks.One<StopWatch> broadcastPutAllAsync(String cacheKey, ServerCacheType type, Map<String, Object> map) throws Throwable {
        //
        // ~ fast output stream
        //
        UnsynchronizedByteArrayOutputStream stream = UnsynchronizedByteArrayOutputStream.builder().get();
        try (ObjectOutputStream out = new ObjectOutputStream(stream)) {
            out.writeInt(map.size());
            for (Entry<String, Object> entry : map.entrySet()) {
                out.writeUTF(entry.getKey());
                switch (type) {
                    case NATURAL_KEY: {
                        out.writeObject(entry.getValue());
                        break;
                    }
                    case BEAN: {
                        CachedBeanData.class.cast(entry.getValue()).writeExternal(out);
                        break;
                    }
                    case COLLECTION_IDS: {
                        CachedManyIds.class.cast(entry.getValue()).writeExternal(out);
                        break;
                    }
                    default: {
                        throw new IllegalArgumentException("Unexpected cache type: " + type);
                    }
                }
            }
            out.flush();
        }

        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_CHANGE_PUT_ALL, cacheKey, stream.toByteArray());
                event.stopWatch = StopWatch.createStarted();
                event.operation = "putAll(%s)".formatted(cacheKey);
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }
    @Override
    public Sinks.One<StopWatch> broadcastRemoveAsync(String cacheKey, String key) throws Throwable {
        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_CHANGE_REMOVE, cacheKey, SerializationUtils.serialize(key));
                event.stopWatch = StopWatch.createStarted();
                event.operation = "remove(%s)".formatted(cacheKey);
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }
    @Override
    public Sinks.One<StopWatch> broadcastRemoveAllAsync(String cacheKey, Collection<String> keys) throws Throwable {
        //
        // ~ fast output stream
        //
        UnsynchronizedByteArrayOutputStream stream = UnsynchronizedByteArrayOutputStream.builder().get();
        try (ObjectOutputStream out = new ObjectOutputStream(stream)) {
            out.writeInt(keys.size());
            for (String it : keys) {
                out.writeUTF(it);
            }
            out.flush();
        }

        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_CHANGE_REMOVE_ALL, cacheKey, stream.toByteArray());
                event.stopWatch = StopWatch.createStarted();
                event.operation = "removeAll(%s)".formatted(cacheKey);
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }
    @Override
    public Sinks.One<StopWatch> broadcastClearAllAsync(String cacheKey) throws Throwable {
        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_CACHE_CLEAR, cacheKey);
                event.stopWatch = StopWatch.createStarted();
                event.operation = "clear(%s)".formatted(cacheKey);
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }
    @Override
    public Sinks.One<StopWatch> broadcastClearAllAsync(boolean localOnly) throws Throwable {
        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_CACHE_CLEAR_ALL, localOnly);
                event.stopWatch = StopWatch.createStarted();
                event.operation = "clearAll";
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }
    @Override
    public Sinks.One<StopWatch> broadcastAsync(ServerCacheNotification notification, String data) throws Throwable {
        //
        // ~ dispatch event to ring buffer
        //
        Sinks.One<StopWatch> signal = Sinks.one();
        DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
            @Override
            public void run() throws Throwable {
                MethodCallEvent event = new MethodCallEvent();
                event.call = new MethodCall(AbstractCacheManager.METHOD_ON_MODIFIED, data);
                event.stopWatch = StopWatch.createStarted();
                event.operation = "notify(%s)".formatted(data);
                event.promise = signal;

                queue.put(event);
            }
        });
        return signal;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    private static class MethodCallEvent {
        private MethodCall call;
        private StopWatch stopWatch;
        private String operation;
        private Sinks.One<StopWatch> promise;
    }
}
