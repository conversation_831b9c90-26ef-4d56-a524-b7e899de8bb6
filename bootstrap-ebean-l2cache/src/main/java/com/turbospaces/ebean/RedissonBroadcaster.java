package com.turbospaces.ebean;

import java.io.IOException;
import java.util.Objects;
import java.util.function.BiConsumer;

import org.jgroups.blocks.MethodCall;
import org.jgroups.blocks.RequestOptions;
import org.jgroups.blocks.ResponseMode;
import org.jgroups.util.ByteArrayDataOutputStream;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.ByteArrayCodec;

import com.turbospaces.cfg.ApplicationProperties;

public class RedissonBroadcaster implements Broadcaster {
    private final ApplicationProperties props;
    private final ByteArrayCodec codec = new ByteArrayCodec();
    private final RTopic topic;
    private final RedissonClient redisson;

    public RedissonBroadcaster(ApplicationProperties props, RedissonClient redisson, String channel) {
        this.props = Objects.requireNonNull(props);
        this.topic = redisson.getTopic(channel, codec);
        this.redisson = Objects.requireNonNull(redisson);
    }
    @Override
    public int parallelism() {
        return redisson.getConfig().getNettyThreads();
    }
    @Override
    public boolean isOpen() {
        return true;
    }
    @Override
    public void callRemoteMethods(MethodCall call, RequestOptions opts, BiConsumer<Object, Throwable> whenComplete) {
        ByteArrayDataOutputStream stream = new ByteArrayDataOutputStream();
        try {
            MethodCallWithInstanceInfo info = new MethodCallWithInstanceInfo(props.CLOUD_APP_INSTANCE_INDEX.get(), call);
            info.writeTo(stream);
        } catch (IOException err) {
            whenComplete.accept(null, err);
        }

        if (ResponseMode.GET_NONE.equals(opts.mode())) {
            topic.publishAsync(stream.buffer()).whenComplete(whenComplete);
        } else {
            long result = topic.publish(stream.buffer());
            whenComplete.accept(result, null);
        }
    }
}
