package com.turbospaces.ebean;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jctools.maps.NonBlockingHashMap;
import org.jgroups.blocks.MethodLookup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.util.concurrent.ListenableScheduledFuture;
import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.netflix.archaius.api.Config;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.metrics.MetricTags;

import io.ebean.BackgroundExecutor;
import io.ebean.DatabaseBuilder;
import io.ebean.DatabaseBuilder.Settings;
import io.ebean.cache.QueryCacheEntryValidate;
import io.ebean.cache.ServerCache;
import io.ebean.cache.ServerCacheConfig;
import io.ebean.cache.ServerCacheFactory;
import io.ebean.cache.ServerCacheNotification;
import io.ebean.cache.ServerCacheNotify;
import io.ebean.cache.ServerCacheOptions;
import io.ebean.cache.ServerCacheStatistics;
import io.ebean.cache.ServerCacheType;
import io.ebean.config.CurrentTenantProvider;
import io.ebeaninternal.server.cache.DefaultServerCacheConfig;
import io.ebeaninternal.server.cache.DefaultServerQueryCache;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;

public abstract class AbstractCacheManager implements CacheManager, ServerCacheNotify, MethodLookup, ApplicationListener<ApplicationReadyEvent> {
    public static short METHOD_ON_CACHE_PUT = 1;
    public static short METHOD_ON_CHANGE_REMOVE = 2;
    public static short METHOD_ON_CACHE_CLEAR = 3;
    public static short METHOD_ON_MODIFIED = 4;
    public static short METHOD_ON_CACHE_CLEAR_ALL = 5;
    public static short METHOD_ON_CHANGE_PUT_ALL = 6;
    public static short METHOD_ON_CHANGE_REMOVE_ALL = 7;
    public static ImmutableMap<Short, Method> METHODS;

    static {
        try {
            ImmutableMap.Builder<Short, Method> b = ImmutableMap.builder();
            b.put(METHOD_ON_MODIFIED, JGroupsCacheManager.class.getMethod("onTablesModify", String.class));
            b.put(METHOD_ON_CACHE_PUT, JGroupsCacheManager.class.getMethod("onCachePut", String.class, byte[].class, byte[].class));
            b.put(METHOD_ON_CHANGE_REMOVE, JGroupsCacheManager.class.getMethod("onCacheRemove", String.class, byte[].class));
            b.put(METHOD_ON_CACHE_CLEAR, JGroupsCacheManager.class.getMethod("onCacheClear", String.class));
            b.put(METHOD_ON_CACHE_CLEAR_ALL, JGroupsCacheManager.class.getMethod("onCacheClearAll", boolean.class));
            b.put(METHOD_ON_CHANGE_PUT_ALL, JGroupsCacheManager.class.getMethod("onCachePutAll", String.class, byte[].class));
            b.put(METHOD_ON_CHANGE_REMOVE_ALL, JGroupsCacheManager.class.getMethod("onCacheRemoveAll", String.class, byte[].class));

            METHODS = b.build();
        } catch (NoSuchMethodException err) {
            throw new Error(err);
        }
    }

    protected final Logger log = LoggerFactory.getLogger(getClass());
    private final ApplicationProperties props;
    private final BroadcastChannel broadcast;
    private final ConcurrentMap<String, ServerCache> caches;
    private final ListeningScheduledExecutorService timer;
    private BackgroundExecutor executor;
    private ServerCacheNotify notify;
    private final Map<String, Number> gauges = new ConcurrentHashMap<>();
    private ListenableScheduledFuture<?> task;

    public AbstractCacheManager(ApplicationProperties props, MeterRegistry meterRegistry, BroadcastChannel broadcast) {
        this.props = Objects.requireNonNull(props);
        this.broadcast = Objects.requireNonNull(broadcast);
        this.caches = new NonBlockingHashMap<>();

        //
        // ~ just one thread
        //
        this.timer = MoreExecutors.listeningDecorator(Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        }));
        broadcast.setBackgroundExecutor(timer);

        long intervalMillis = props.APP_METRICS_REPORT_INTERVAL.get().toMillis();
        meterRegistry.gaugeMapSize("cache.gauge.size", List.of(), gauges);
        task = timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                caches.forEach(new BiConsumer<String, ServerCache>() {
                    @Override
                    public void accept(String key, ServerCache value) {
                        String cacheType = "unknown";
                        if (key.endsWith(ServerCacheType.QUERY.code())) {
                            cacheType = "query";
                        } else if (key.endsWith(ServerCacheType.NATURAL_KEY.code())) {
                            cacheType = "natural_key";
                        } else if (key.endsWith(ServerCacheType.COLLECTION_IDS.code())) {
                            cacheType = "collection_ids";
                        } else if (key.endsWith(ServerCacheType.BEAN.code())) {
                            cacheType = "bean";
                        }

                        var tags = List.of(Tag.of(MetricTags.NAME, key), Tag.of(MetricTags.TYPE, cacheType));

                        ServerCacheStatistics stats = value.statistics(true);

                        meterRegistry.counter("ebean_cache.hits", tags).increment(stats.getHitCount());
                        meterRegistry.counter("ebean_cache.puts", tags).increment(stats.getPutCount());
                        meterRegistry.counter("ebean_cache.evicts", tags).increment(stats.getEvictCount());

                        registerGauge("ebean_cache.size", tags, stats.getSize(), meterRegistry);
                        registerGauge("ebean_cache.max_size", tags, stats.getMaxSize(), meterRegistry);
                        registerGauge("ebean_cache.hit_ratio", tags, stats.getHitRatio(), meterRegistry);

                        meterRegistry.counter("ebean_cache.miss_count", tags).increment(stats.getMissCount());
                        meterRegistry.counter("ebean_cache.ttl_count", tags).increment(stats.getTtlCount());
                        meterRegistry.counter("ebean_cache.remove_count", tags).increment(stats.getRemoveCount());
                        meterRegistry.counter("ebean_cache.clear_count", tags).increment(stats.getClearCount());
                        meterRegistry.counter("ebean_cache.idle_count", tags).increment(stats.getIdleCount());
                    }
                });
            }
        }, intervalMillis, intervalMillis, TimeUnit.MILLISECONDS);
    }
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        broadcast.onApplicationEvent(event);
    }
    @Override
    public void setBackgroundExecutor(BackgroundExecutor executor) {
        this.executor = Objects.requireNonNull(executor);
    }
    @Override
    public ServerCacheFactory create(DatabaseBuilder databaseBuilder, BackgroundExecutor backgroundExecutor) {
        Settings settings = databaseBuilder.settings();
        log.info("settings: {}", ToStringBuilder.reflectionToString(settings, ToStringStyle.SHORT_PREFIX_STYLE));
        setBackgroundExecutor(backgroundExecutor);
        return this;
    }
    @Override
    public ServerCacheNotify createCacheNotify(ServerCacheNotify cacheNotify) {
        this.notify = Objects.requireNonNull(cacheNotify);
        return this;
    }
    @Override
    public void notify(ServerCacheNotification notification) {
        if (CollectionUtils.isNotEmpty(notification.getDependentTables())) {
            Set<String> dependentTables = notification.getDependentTables();
            String line = Joiner.on(',').join(dependentTables);
            log.debug("Publish TableMods - {}", line);
            try {
                broadcast.broadcastAsync(notification, line);
            } catch (Throwable err) {
                log.error(err.getMessage(), err);
            }
        }
    }
    @Override
    public Method findMethod(short id) {
        return METHODS.get(id);
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(task)) {
            task.cancel(true);
        }
        try {
            broadcast.destroy();
            caches.clear();
        } finally {
            PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
        }
    }
    @Override
    public void onTablesModify(String line) {
        Iterable<String> it = Splitter.on(',').omitEmptyStrings().split(line);
        ImmutableSet<String> tables = ImmutableSet.copyOf(it);
        ServerCacheNotification notification = new ServerCacheNotification(tables);
        if (Objects.nonNull(notify)) {
            notify.notify(notification);
        }
    }
    @Override
    public void onCachePut(String cacheKey, byte[] key, byte[] value) throws Throwable {
        AbstractEbeanCache cache = (AbstractEbeanCache) caches.get(cacheKey);
        if (Objects.nonNull(cache)) {
            cache.onPut(key, value);
        }
    }
    @Override
    public void onCacheRemove(String cacheKey, byte[] key) throws Throwable {
        AbstractEbeanCache cache = (AbstractEbeanCache) caches.get(cacheKey);
        if (Objects.nonNull(cache)) {
            cache.onRemove(key);
        }
    }
    @Override
    public void onCachePutAll(String cacheKey, byte[] data) throws Throwable {
        AbstractEbeanCache cache = (AbstractEbeanCache) caches.get(cacheKey);
        if (Objects.nonNull(cache)) {
            cache.onPutAll(data);
        }
    }
    @Override
    public void onCacheRemoveAll(String cacheKey, byte[] data) throws Throwable {
        AbstractEbeanCache cache = (AbstractEbeanCache) caches.get(cacheKey);
        if (Objects.nonNull(cache)) {
            cache.onRemoveAll(data);
        }
    }
    @Override
    public void onCacheClear(String cacheKey) throws Throwable {
        ServerCache cache = caches.get(cacheKey);
        if (Objects.nonNull(cache)) {
            if (cache instanceof ClearableCache) {
                ((ClearableCache) cache).onClear();
            }
        }
    }
    @Override
    public void onCacheClearAll(boolean localOnly) throws Throwable {
        for (Entry<String, ServerCache> entry : caches.entrySet()) {
            String cacheKey = entry.getKey();
            ServerCache cache = entry.getValue();
            if (cache instanceof ClearableCache clearable) {
                if (cache instanceof LocalCache) {
                    log.atDebug().log("about to clear local cache: {}", cacheKey);
                    clearable.onClear();
                } else if (cache instanceof ReplicatedCache) {
                    //
                    // ~ we do not want to clear remote cache all the time since it is not necessary for replicated cache
                    //
                    boolean toApply = BooleanUtils.isFalse(localOnly);
                    log.atDebug().log("about to clear remote cache: {}, toApply: {}", cacheKey, toApply);
                    if (toApply) {
                        clearable.onClear();
                    }
                } else {
                    log.atDebug().log("about to clear cache: {}", cacheKey);
                    clearable.onClear();
                }
            }
        }
    }
    @Override
    public ServerCache getCache(String cacheKey) {
        return caches.get(cacheKey);
    }
    @Override
    public void clearAll(boolean localOnly) throws Throwable {
        try {
            onCacheClearAll(localOnly);
        } catch (Throwable err) {
            log.error(err.getMessage(), err);
        } finally {
            broadcast.broadcastClearAllAsync(localOnly);
        }
    }
    @Override
    public ServerCache createCache(ServerCacheConfig config) {
        String cacheKey = config.getCacheKey();
        String shortName = config.getShortName();

        ServerCacheOptions cacheOptions = config.getCacheOptions();
        ServerCacheType cacheType = config.getType();
        CurrentTenantProvider tenantProvider = config.getTenantProvider();
        QueryCacheEntryValidate queryCacheValidate = config.getQueryCacheEntryValidate();

        ServerCache cache = caches.get(cacheKey);
        if (Objects.isNull(cache)) {
            Config prefixedView = props.cfg().getPrefixedView(cacheKey);
            Map<String, Object> configMap = new HashMap<>();
            for (String key : prefixedView.keys()) {
                Object rawProperty = prefixedView.getRawProperty(key);
                configMap.put(key, rawProperty);
            }

            //
            // ~ optional cache settings
            //
            int maxTtl = prefixedView.getInteger(EbeanCacheConfigurer.MAX_TTL, cacheOptions.getMaxSecsToLive());
            int maxIdle = prefixedView.getInteger(EbeanCacheConfigurer.MAX_IDLE, cacheOptions.getMaxIdleSecs());
            int maxSize = prefixedView.getInteger(EbeanCacheConfigurer.MAX_SIZE, cacheOptions.getMaxSize());

            int trimFrequency = (int) props.APP_TIMER_INTERVAL.get().toSeconds();

            ServerCacheOptions options = new ServerCacheOptions();
            options.setMaxSecsToLive(maxTtl);
            options.setMaxIdleSecs(maxIdle);
            options.setMaxSize(maxSize);
            options.setTrimFrequency(trimFrequency);

            ServerCacheConfig scc = new ServerCacheConfig(
                    cacheType,
                    cacheKey,
                    shortName,
                    options,
                    tenantProvider,
                    queryCacheValidate //
            );
            if (config.isQueryCache()) {
                cache = new ReplicatedServerQueryCache(props, new DefaultServerCacheConfig(scc), broadcast);
                ServerCache prev = caches.putIfAbsent(cacheKey, cache);
                if (Objects.nonNull(prev)) {
                    cache = prev;
                } else {
                    log.debug("created query cache: {} using cfg {}",
                            cacheKey,
                            ToStringBuilder.reflectionToString(options, ToStringStyle.SHORT_PREFIX_STYLE));
                }
            } else {
                //
                // ~ fail fast in development mode and raise sentry error in production mode otherwise
                //
                if (BooleanUtils.isFalse(prefixedView.containsKey(EbeanCacheConfigurer.CACHE_MODE_LOCAL))) {
                    if (props.APP_DEV_MODE.get()) {
                        throw new IllegalStateException("cache %s is not configured".formatted(cacheKey));
                    }
                    log.error("cache {} is not configured", cacheKey);
                }

                boolean localMode = prefixedView.getBoolean(EbeanCacheConfigurer.CACHE_MODE_LOCAL, false);

                ToStringStyle nameStyle = ToStringStyle.NO_CLASS_NAME_STYLE;

                if (localMode) {
                    cache = new LocalEbeanCache(props, cacheKey, tenantProvider, scc, broadcast);
                    ServerCache prev = caches.putIfAbsent(cacheKey, cache);
                    if (Objects.nonNull(prev)) {
                        cache = prev;
                    } else {
                        log.debug("created local cache: {} using cfg {}", cacheKey, ToStringBuilder.reflectionToString(options, nameStyle));
                    }
                } else {
                    cache = new ReplicatedEbeanCache(props, cacheKey, tenantProvider, scc, broadcast);
                    ServerCache prev = caches.putIfAbsent(cacheKey, cache);
                    if (Objects.nonNull(prev)) {
                        cache = prev;
                    } else {
                        log.debug("created replicated cache: {} using cfg {}", cacheKey, ToStringBuilder.reflectionToString(options, nameStyle));
                    }
                }
            }

            //
            // ~ cleanUp
            //
            ServerCache tmp = Objects.requireNonNull(cache);

            //
            // ~ might be provided by framework directly, if not wrap into own
            //
            BackgroundExecutor toApply = executor;
            if (Objects.isNull(toApply)) {
                toApply = new BackgroundExecutor() {
                    @Override
                    public Future<?> submit(Runnable runnable) {
                        return timer.submit(runnable);
                    }
                    @Override
                    public <T> Future<T> submit(Callable<T> runnable) {
                        return timer.submit(runnable);
                    }
                    @Override
                    public ScheduledFuture<?> scheduleWithFixedDelay(Runnable runnable, long initialDelay, long delay, TimeUnit unit) {
                        return timer.scheduleWithFixedDelay(runnable, initialDelay, delay, unit);
                    }
                    @Override
                    public ScheduledFuture<?> scheduleAtFixedRate(Runnable runnable, long initialDelay, long period, TimeUnit unit) {
                        return timer.scheduleAtFixedRate(runnable, initialDelay, period, unit);
                    }
                    @Override
                    public <V> ScheduledFuture<V> schedule(Callable<V> runnable, long delay, TimeUnit unit) {
                        return timer.schedule(runnable, delay, unit);
                    }
                    @Override
                    public ScheduledFuture<?> schedule(Runnable runnable, long delay, TimeUnit unit) {
                        return timer.schedule(runnable, delay, unit);
                    }
                    @Override
                    public void execute(Runnable runnable) {
                        timer.execute(runnable);
                    }
                };
            }

            toApply.scheduleWithFixedDelay(new Runnable() {
                @Override
                public void run() {
                    if (tmp instanceof LocalCache lc) {
                        log.info("running cache cleanUp: {}", cacheKey);
                        lc.cleanUp();
                    } else if (tmp instanceof DefaultServerQueryCache qcache) {
                        log.info("running query cache cleanUp: {}", cacheKey);
                        qcache.runEviction();
                    }
                }
            }, trimFrequency, trimFrequency, TimeUnit.SECONDS);
        }

        return cache;
    }
    private void registerGauge(String name, List<Tag> tags, int stats, MeterRegistry meterRegistry) {
        gauges.put(getGaugeKey(name, tags), stats);
        PlatformUtil.registerGauge(meterRegistry, name, tags, gauges, map -> map.get(getGaugeKey(name, tags)).doubleValue());
    }
    private static String getGaugeKey(String key, List<Tag> tags) {
        return key + tags.stream().map(Tag::getValue).collect(Collectors.joining());
    }
}
