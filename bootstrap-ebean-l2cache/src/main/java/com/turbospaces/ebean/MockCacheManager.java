package com.turbospaces.ebean;

import java.util.Collection;
import java.util.Map;

import org.apache.commons.lang3.time.StopWatch;
import org.springframework.boot.context.event.ApplicationReadyEvent;

import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.cache.ServerCacheNotification;
import io.ebean.cache.ServerCacheType;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.One;

@Slf4j
public class MockCacheManager extends AbstractCacheManager {
    public MockCacheManager(ApplicationProperties props, MeterRegistry meterRegistry) {
        super(props, meterRegistry, new BroadcastChannel() {
            @Override
            public void setBackgroundExecutor(ListeningScheduledExecutorService executor) {

            }
            @Override
            public Sinks.One<StopWatch> broadcastPutAsync(String cacheName, ServerCacheType type, String key, Object obj) {
                return one();
            }
            @Override
            public Sinks.One<StopWatch> broadcastPutAllAsync(String cacheName, ServerCacheType type, Map<String, Object> map) {
                return one();
            }
            @Override
            public Sinks.One<StopWatch> broadcastRemoveAsync(String cacheName, String key) {
                return one();
            }
            @Override
            public Sinks.One<StopWatch> broadcastRemoveAllAsync(String cacheName, Collection<String> keys) {
                return one();
            }
            @Override
            public Sinks.One<StopWatch> broadcastClearAllAsync(String cacheName) {
                return one();
            }
            @Override
            public Sinks.One<StopWatch> broadcastClearAllAsync(boolean onlyLocal) {
                return one();
            }
            @Override
            public Sinks.One<StopWatch> broadcastAsync(ServerCacheNotification notification, String data) {
                return one();
            }
            @Override
            public void destroy() throws Exception {

            }
            private One<StopWatch> one() {
                StopWatch stopWatch = StopWatch.createStarted();
                Sinks.One<StopWatch> signal = Sinks.one();
                stopWatch.stop();
                signal.tryEmitValue(stopWatch);
                return signal;
            }
            @Override
            public void onApplicationEvent(ApplicationReadyEvent event) {

            }
        });
    }
}
