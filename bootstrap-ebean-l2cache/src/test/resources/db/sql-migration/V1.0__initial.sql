create table core.account (
  id                            bigint auto_increment not null,
  username                      varchar(255),
  first_name                    varchar(255),
  last_name                     varchar(255),
  age                           integer not null,
  birth_date                    date,
  first_gameplay                date,
  last_gameplay                 date,
  details                       clob,
  fraud                         clob,
  created_at                    timestamp not null,
  modified_at                   timestamp not null,
  version                       integer not null,
  constraint uq_account_username unique (username),
  constraint pk_account primary key (id)
);

create table core.utm_templates (
  id                            bigint auto_increment not null primary key,
  campaign                      varchar(255) not null,
  account_id                    bigint not null,
  version                       integer not null,
  created_at                    timestamp not null,
  modified_at                   timestamp not null,
  constraint uq_utm_templates_account_campaign unique (account_id,campaign)
);

create table core.account_balances (
  account_id                    bigint not null,
  currency                      varchar(255) not null,
  amount                        decimal(38),
  crypto                        boolean default false not null,
  version                       integer not null,
  created_at                    timestamp not null,
  modified_at                   timestamp not null,
  constraint pk_account_balances primary key (account_id,currency)
);

create table core.account_balance_snapshots (
  account_id                    bigint not null,
  currency                      varchar(255) not null,
  at                            date not null,
  amount                        decimal(38),
  version                       integer not null,
  created_at                    timestamp not null,
  modified_at                   timestamp not null,
  constraint pk_account_balance_snapshots primary key (account_id,currency, at)
);

create sequence core.accounts_id_seq;
