package com.turbospaces.ebean;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.platform.h2.H2Platform;
import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class EbeanFactoryBean extends AbstractEbeanFactoryBean<JpaManager> {
    public EbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config, CacheManager cache) {
        super(props, meterRegistry, tracer, config);
        config.setDatabasePlatform(new H2Platform());
        config.setServerCachePlugin(cache);
    }
    @Override
    public Class<?> getObjectType() {
        return JpaManager.class;
    }
    @Override
    protected JpaManager createEbean(SpiEbeanServer db) {
        return new EbeanJpaManager(props, meterRegistry, tracer, db, timer);
    }
}
