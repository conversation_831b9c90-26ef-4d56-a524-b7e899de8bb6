package com.turbospaces.ebean;

import java.time.LocalDate;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

@Embeddable
public class AccountBalanceSnapshotId {
    @Column(nullable = false)
    private Long accountId;

    @Column(nullable = false)
    private String currency;

    @Column(nullable = false)
    private LocalDate at;

    public AccountBalanceSnapshotId() {}
    public AccountBalanceSnapshotId(Account account, String currency, LocalDate at) {
        this.accountId = account.getId();
        this.currency = currency;
        this.at = at;
    }
    public Long getAccountId() {
        return accountId;
    }
    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }
    public String getCurrency() {
        return currency;
    }
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    public LocalDate getAt() {
        return at;
    }
    public void setAt(LocalDate at) {
        this.at = at;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getAccountId()).append(getCurrency()).append(getAt()).toHashCode();
    }
    @Override
    public boolean equals(Object obj) {
        AccountBalanceSnapshotId other = (AccountBalanceSnapshotId) obj;
        return new EqualsBuilder().append(getAccountId(), other.getAccountId()).append(getCurrency(), other.getCurrency()).append(getAt(), other.getAt())
                .isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("account", getAccountId()).append("currency", getCurrency()).append("at", getAt()).build();
    }
}
