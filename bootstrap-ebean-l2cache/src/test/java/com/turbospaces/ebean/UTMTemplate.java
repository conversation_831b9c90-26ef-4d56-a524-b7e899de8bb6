package com.turbospaces.ebean;

import java.util.Date;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import io.ebean.annotation.Cache;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;

@Entity
@Table(
        name = "utm_templates",
        schema = "CORE",
        uniqueConstraints = { //
                @UniqueConstraint(columnNames = { "account_id", "campaign" }) //
        })
@Cache(naturalKey = { "account", "campaign" })
public class UTMTemplate {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @ManyToOne(optional = false)
    private Account account;

    @Column(nullable = false)
    private String campaign;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public UTMTemplate() {}
    public UTMTemplate(Account account, String campaign) {
        this.account = account;
        this.campaign = campaign;
    }
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getCampaign() {
        return campaign;
    }
    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }
    public Account getAccount() {
        return account;
    }
    public void setAccount(Account account) {
        this.account = account;
    }
    public int getVersion() {
        return version;
    }
    public Date getCreatedAt() {
        return createdAt;
    }
    public Date getModifiedAt() {
        return modifiedAt;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getAccount()).append(getCampaign()).build();
    }
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        UTMTemplate other = (UTMTemplate) obj;
        return new EqualsBuilder().append(getAccount(), other.getAccount()).append(getCampaign(), other.getCampaign()).isEquals();
    }
}
