package com.turbospaces.ebean;

import static com.turbospaces.ebean.MetricsAssertions.checkEbeanCacheCounterMetric;
import static com.turbospaces.ebean.MetricsAssertions.checkEbeanCacheGaugeMetric;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.utility.DockerImageName;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.query.QAccount;
import com.turbospaces.ebean.query.QAccountBalance;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.redis.RedisClientFactoryBean;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.ebean.Database;
import io.ebean.Transaction;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class RedisCompositeKeyTest {
    private final Integer DELAY_AND_PERIOD_OF_CACHE_EXECUTIONS_MS = 10;

    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        EbeanCacheConfigurer mngr = new DefaultEbeanCacheConfigurer(props);

        mngr.setLocal(UTMTemplate.class);
        mngr.setLocal(Account.class);
        mngr.setLocal(AccountBalance.class);
        mngr.setLocal(AccountBalanceSnapshot.class);
        mngr.setLocal(Account.class, QAccount.alias().balances);
        mngr.setLocal(Account.class, QAccount.alias().utmTemplates);
        mngr.setLocal(AccountBalance.class, QAccountBalance.alias().snapshots);

        //
        // ~ local cache mode
        //
        mngr.setMaxSize(Account.class, 1);
        mngr.setMaxSize(Account.class, QAccount.alias().balances, 1);

        //
        // ~ replicated cache mode
        //
        mngr.setMaxSize(UTMTemplate.class, Byte.SIZE);
        mngr.setMaxSize(Account.class, QAccount.alias().utmTemplates, Integer.MAX_VALUE);

        //
        // ~ query cache mode
        //
        mngr.setMaxSizeQuery(Account.class, 1);

        try (var redis = new GenericContainer<>(DockerImageName.parse("redis:7"))) {
            redis.withNetworkAliases("redis");
            redis.addExposedPort(6379);
            redis.start();
            int port = redis.getMappedPort(6379);
            cfg.setDefaultProperty("redis.port", port);

            SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            try {
                bootstrap.withH2(true, bootstrap.spaceName());
                cfg.setDefaultProperty(props.APP_METRICS_REPORT_INTERVAL.getKey(), Duration.ofMillis(DELAY_AND_PERIOD_OF_CACHE_EXECUTIONS_MS));

                ServiceInfo ownerUps = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.H2_OWNER);

                bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, ownerUps, List.of(),"CORE"));
                ConfigurableApplicationContext applicationContext = bootstrap.run();

                Database ebean = applicationContext.getBean(Database.class);

                Account account = new Account();
                account.setId(ApiFactory.RANDOM.nextLong());
                account.setUsername("username_" + account.getId());
                account.setFirstName("f_" + account.getId());
                account.setLastName("l_" + account.getId());
                account.setAge(18);
                account.setBirthDate(LocalDate.now(ZoneOffset.UTC).minusYears(account.getAge()));
                ebean.save(account);

                AccountBalance balance = new AccountBalance(account, "USD");
                balance.setAmount(BigDecimal.ONE);

                AccountBalanceSnapshot snapshot1 = new AccountBalanceSnapshot(balance, LocalDate.now(ZoneOffset.UTC));
                AccountBalanceSnapshot snapshot2 = new AccountBalanceSnapshot(balance, LocalDate.now(ZoneOffset.UTC).plusDays(1));

                balance.getSnapshots().add(snapshot1);
                balance.getSnapshots().add(snapshot2);
                ebean.save(balance);

                QAccountBalance q = new QAccountBalance(ebean);
                q.setId(balance.getPk());
                q.findList();

                try (Transaction tx1 = ebean.beginTransaction()) {
                    var query = new QAccountBalance(ebean).usingTransaction(tx1).setUseQueryCache(true);
                    query.amount.ge(BigDecimal.ONE);
                    query.findList();
                }

                try (Transaction tx2 = ebean.beginTransaction()) {
                    var query = new QAccountBalance(ebean).usingTransaction(tx2).setUseQueryCache(true);
                    query.amount.ge(BigDecimal.ONE);
                    query.findList();
                }

                try (Transaction tx3 = ebean.beginTransaction()) {
                    var query = new QAccountBalance(ebean).usingTransaction(tx3).setUseQueryCache(true);
                    query.amount.ge(BigDecimal.ONE);
                    query.findList();
                }

                checkEbeanCacheGaugeMetric(bootstrap, "ebean_cache.size", "com.turbospaces.ebean.AccountBalance_B", "bean", 1);
                Awaitility.await().until(() ->
                        checkEbeanCacheCounterMetric(bootstrap, "ebean_cache.hits", "com.turbospaces.ebean.AccountBalance_Q", "query", 2)
                                && checkEbeanCacheCounterMetric(bootstrap, "ebean_cache.puts", "com.turbospaces.ebean.AccountBalance_Q", "query", 1)
                                && checkEbeanCacheCounterMetric(bootstrap, "ebean_cache.evicts", "com.turbospaces.ebean.AccountBalance_Q", "query", 0)
                );

                account.setAge(21);
                ebean.save(account);

                Thread.sleep(10 * 000);
            } finally {
                bootstrap.shutdown();
            }
        }
    }


    @Configurable
    public static class AppConfig {
        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, MeterRegistry meterRegistry, DynamicCloud cloud) {
            ServiceInfo appUps = UPSs.findRequiredServiceInfoByName(cloud, UPSs.H2_APP);
            return new HikariDataSourceFactoryBean(props, meterRegistry, appUps);
        }

        @Bean
        public RedisClientFactoryBean redissonFactory(ApplicationProperties props, MeterRegistry meterRegistry) {
            var si = new RedisServiceInfo(UPSs.REDIS, "redis://localhost:" + props.cfg().getInteger("redis.port"));
            return new RedisClientFactoryBean(props, meterRegistry, si);
        }

        @Bean
        public RedisCacheManagerFactoryBean redis(ApplicationProperties props, MeterRegistry meterRegistry, RateLimiterRegistry rateLimiterRegistry, RedissonClient redissonClient) {
            return new RedisCacheManagerFactoryBean(
                    props,
                    meterRegistry,
                    rateLimiterRegistry,
                    redissonClient);
        }

        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.addClass(Account.class);
            cfg.addClass(GameplayInfo.class);
            cfg.addClass(FraudJson.class);
            cfg.addClass(UTMTemplate.class);
            cfg.addClass(AccountBalance.class);
            cfg.addClass(AccountBalanceId.class);
            cfg.addClass(AccountBalanceSnapshot.class);
            cfg.addClass(AccountBalanceSnapshotId.class);

            cfg.setLocal(UTMTemplate.class);
            cfg.setLocal(Account.class);
            cfg.setLocal(AccountBalance.class);
            cfg.setLocal(AccountBalanceSnapshot.class);
            cfg.setLocal(Account.class, QAccount.alias().balances);
            cfg.setLocal(Account.class, QAccount.alias().utmTemplates);
            cfg.setLocal(AccountBalance.class, QAccountBalance.alias().snapshots);

            return cfg;
        }
        @Bean
        public EbeanFactoryBean ebean(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                Tracer tracer,
                EbeanDatabaseConfig config,
                RedisCacheManagerFactoryBean cache) throws Exception {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config, cache.getObject());
        }
    }
}
