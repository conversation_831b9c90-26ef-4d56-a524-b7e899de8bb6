package com.turbospaces.ebean;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jgroups.JChannel;
import org.jgroups.blocks.MethodCall;
import org.jgroups.blocks.RequestOptions;
import org.jgroups.blocks.RpcDispatcher;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import api.v1.ApiFactory;
import io.ebean.cache.QueryCacheEntry;
import io.ebean.cache.QueryCacheEntryValidate;
import io.ebean.cache.ServerCacheConfig;
import io.ebean.cache.ServerCacheOptions;
import io.ebean.cache.ServerCacheType;
import io.ebean.config.CurrentTenantProvider;
import io.ebeaninternal.server.cache.CachedBeanData;
import io.ebeaninternal.server.cache.CachedManyIds;
import io.github.resilience4j.ratelimiter.internal.InMemoryRateLimiterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

@ExtendWith(MockitoExtension.class)
class JGroupsBroadcastChannelTest {
    private ApplicationProperties props;
    private DefaultBroadcastChannel channel;
    private final int iterations = 1_000;
    private final String cacheKey = PlatformUtil.randomUUID().toString();
    private final CurrentTenantProvider tenant = new CurrentTenantProvider() {
        @Override
        public Object currentId() {
            return null;
        }
    };
    @Captor
    private ArgumentCaptor<MethodCall> captor;
    private final QueryCacheEntryValidate cacheEntryValidate = new QueryCacheEntryValidate() {
        @Override
        public boolean isValid(QueryCacheEntry queryCacheEntry) {
            return true;
        }
    };
    @Mock
    private JChannel jchannel;
    @Mock
    private RpcDispatcher dispatcher;
    ReplicatedEbeanCache replicated;

    @BeforeEach
    void beforeEach() throws Exception {
        Mockito.when(jchannel.isOpen()).thenReturn(true);
        Mockito.when(dispatcher.getChannel()).thenReturn(jchannel);

        ApplicationConfig cfg = ApplicationConfig.mock();
        props = new ApplicationProperties(cfg.factory());
        channel = new DefaultBroadcastChannel(props, new SimpleMeterRegistry(), new InMemoryRateLimiterRegistry(), new Broadcaster() {
            @Override
            public boolean isOpen() {
                return dispatcher.getChannel().isOpen();
            }
            @Override
            public void callRemoteMethods(MethodCall method_call, RequestOptions opts, BiConsumer<Object, Throwable> whenComplete) {
                try {
                    dispatcher.callRemoteMethods(null, method_call, opts);
                    whenComplete.accept(method_call, null);
                } catch (Exception err) {
                    ExceptionUtils.wrapAndThrow(err);
                }
            }
            @Override
            public int parallelism() {
                return Runtime.getRuntime().availableProcessors();
            }
        });
    }

    @AfterEach
    void afterEach() throws Exception {
        channel.destroy();
    }

    @Test
    void putByNatural() throws Throwable {
        List<String> keys = Lists.newArrayListWithExpectedSize(iterations);
        Long now = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            String key = PlatformUtil.randomAlphabetic(getClass().getName().length());
            keys.add(key);
        }

        replicated = new ReplicatedEbeanCache(
                props,
                cacheKey,
                tenant,
                new ServerCacheConfig(ServerCacheType.NATURAL_KEY, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                channel);

        for (int i = 0; i < iterations; i++) {
            String key = keys.get(i);
            channel.broadcastPutAsync(cacheKey, ServerCacheType.NATURAL_KEY, key, now).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            MethodCall methodCall = captor.getValue();
            Iterator<Object> args = Arrays.asList(methodCall.getArgs()).iterator();

            Assertions.assertEquals(cacheKey, args.next());
            replicated.onPut((byte[]) args.next(), (byte[]) args.next());
            Assertions.assertNotNull(replicated.get(key));

            Mockito.clearInvocations(dispatcher);

            channel.broadcastRemoveAsync(cacheKey, key).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            methodCall = captor.getValue();
            args = Arrays.asList(methodCall.getArgs()).iterator();
            Assertions.assertEquals(cacheKey, args.next());
            replicated.onRemove((byte[]) args.next());
            Assertions.assertNull(replicated.get(key));

            Mockito.clearInvocations(dispatcher);
        }
    }

    @Test
    void putByCacheBeanData() throws Throwable {
        List<String> keys = Lists.newArrayListWithExpectedSize(iterations);
        ImmutableMap<String, Object> fields = ImmutableMap.of("k1", 1, "k2", 2);
        CachedBeanData data = new CachedBeanData(new Object(), "disc", fields, System.currentTimeMillis());
        for (int i = 0; i < iterations; i++) {
            String key = PlatformUtil.randomAlphabetic(getClass().getName().length());
            keys.add(key);
        }

        replicated = new ReplicatedEbeanCache(
                props,
                cacheKey,
                tenant,
                new ServerCacheConfig(ServerCacheType.BEAN, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                channel);

        for (int i = 0; i < iterations; i++) {
            String key = keys.get(i);
            channel.broadcastPutAsync(cacheKey, ServerCacheType.BEAN, key, data).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            MethodCall methodCall = captor.getValue();
            Iterator<Object> args = Arrays.asList(methodCall.getArgs()).iterator();

            Assertions.assertEquals(cacheKey, args.next());
            replicated.onPut((byte[]) args.next(), (byte[]) args.next());
            Assertions.assertNotNull(replicated.get(key));
            Assertions.assertEquals(fields, ((CachedBeanData) replicated.get(key)).getData());

            Mockito.clearInvocations(dispatcher);

            channel.broadcastRemoveAsync(cacheKey, key).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            methodCall = captor.getValue();
            args = Arrays.asList(methodCall.getArgs()).iterator();
            Assertions.assertEquals(cacheKey, args.next());
            replicated.onRemove((byte[]) args.next());
            Assertions.assertNull(replicated.get(key));

            Mockito.clearInvocations(dispatcher);
        }
    }

    @Test
    void putByCollectionIds() throws Throwable {
        List<String> keys = Lists.newArrayListWithExpectedSize(iterations);
        CachedManyIds data = new CachedManyIds(ImmutableList.of(1, 2, 3));
        for (int i = 0; i < iterations; i++) {
            String key = PlatformUtil.randomAlphabetic(getClass().getName().length());
            keys.add(key);
        }

        replicated = new ReplicatedEbeanCache(
                props,
                cacheKey,
                tenant,
                new ServerCacheConfig(ServerCacheType.COLLECTION_IDS, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                channel);

        for (int i = 0; i < iterations; i++) {
            String key = keys.get(i);
            channel.broadcastPutAsync(cacheKey, ServerCacheType.COLLECTION_IDS, key, data).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            MethodCall methodCall = captor.getValue();
            Iterator<Object> args = Arrays.asList(methodCall.getArgs()).iterator();

            Assertions.assertEquals(cacheKey, args.next());
            replicated.onPut((byte[]) args.next(), (byte[]) args.next());
            Assertions.assertNotNull(replicated.get(key));
            Assertions.assertEquals(data.getIdList(), ((CachedManyIds) replicated.get(key)).getIdList());

            Mockito.clearInvocations(dispatcher);

            channel.broadcastRemoveAsync(cacheKey, key).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            methodCall = captor.getValue();
            args = Arrays.asList(methodCall.getArgs()).iterator();
            Assertions.assertEquals(cacheKey, args.next());
            replicated.onRemove((byte[]) args.next());
            Assertions.assertNull(replicated.get(key));

            Mockito.clearInvocations(dispatcher);
        }
    }

    @Test
    void putByNaturalMany() throws Throwable {
        replicated = new ReplicatedEbeanCache(
                props,
                cacheKey,
                tenant,
                new ServerCacheConfig(ServerCacheType.NATURAL_KEY, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                channel);

        for (int i = 0; i < iterations; i++) {
            Map<String, Object> data = ImmutableMap.of("k1", ApiFactory.RANDOM.nextInt(), "k2", ApiFactory.RANDOM.nextInt());

            channel.broadcastPutAllAsync(cacheKey, ServerCacheType.NATURAL_KEY, data).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            MethodCall methodCall = captor.getValue();
            Iterator<Object> args = Arrays.asList(methodCall.getArgs()).iterator();

            Assertions.assertEquals(cacheKey, args.next());
            replicated.onPutAll((byte[]) args.next());
            Assertions.assertEquals(data.get("k1"), replicated.get("k1"));
            Assertions.assertEquals(data.get("k2"), replicated.get("k2"));

            Mockito.clearInvocations(dispatcher);

            channel.broadcastRemoveAllAsync(cacheKey, data.keySet()).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            methodCall = captor.getValue();
            args = Arrays.asList(methodCall.getArgs()).iterator();
            Assertions.assertEquals(cacheKey, args.next());
            replicated.onRemoveAll((byte[]) args.next());
            Assertions.assertNull(replicated.get("k1"));
            Assertions.assertNull(replicated.get("k2"));

            Mockito.clearInvocations(dispatcher);
        }
    }

    @Test
    void putByCachedBeanMany() throws Throwable {
        replicated = new ReplicatedEbeanCache(
                props,
                cacheKey,
                tenant,
                new ServerCacheConfig(ServerCacheType.BEAN, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                channel);

        for (int i = 0; i < iterations; i++) {
            ImmutableMap<String, Object> fields1 = ImmutableMap.of("f1", 1, "f2", 2);
            ImmutableMap<String, Object> fields2 = ImmutableMap.of("f3", 2, "f4", 1);
            CachedBeanData data1 = new CachedBeanData(new Object(), "disc1", fields1, System.currentTimeMillis());
            CachedBeanData data2 = new CachedBeanData(new Object(), "disc2", fields2, System.currentTimeMillis());

            Map<String, Object> data = ImmutableMap.of("k1", data1, "k2", data2);

            channel.broadcastPutAllAsync(cacheKey, ServerCacheType.BEAN, data).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            MethodCall methodCall = captor.getValue();
            Iterator<Object> args = Arrays.asList(methodCall.getArgs()).iterator();

            Assertions.assertEquals(cacheKey, args.next());
            replicated.onPutAll((byte[]) args.next());
            Assertions.assertEquals(((CachedBeanData) data.get("k1")).getData(), ((CachedBeanData) replicated.get("k1")).getData());
            Assertions.assertEquals(((CachedBeanData) data.get("k2")).getData(), ((CachedBeanData) replicated.get("k2")).getData());

            Mockito.clearInvocations(dispatcher);

            channel.broadcastRemoveAllAsync(cacheKey, data.keySet()).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            methodCall = captor.getValue();
            args = Arrays.asList(methodCall.getArgs()).iterator();
            Assertions.assertEquals(cacheKey, args.next());
            replicated.onRemoveAll((byte[]) args.next());
            Assertions.assertNull(replicated.get("k1"));
            Assertions.assertNull(replicated.get("k2"));

            Mockito.clearInvocations(dispatcher);
        }
    }

    @Test
    void putByCachedIdsMany() throws Throwable {
        replicated = new ReplicatedEbeanCache(
                props,
                cacheKey,
                tenant,
                new ServerCacheConfig(ServerCacheType.COLLECTION_IDS, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                channel);

        for (int i = 0; i < iterations; i++) {
            CachedManyIds data1 = new CachedManyIds(ImmutableList.of(1, 2, 3));
            CachedManyIds data2 = new CachedManyIds(ImmutableList.of(4, 5, 6));

            Map<String, Object> data = ImmutableMap.of("k1", data1, "k2", data2);

            channel.broadcastPutAllAsync(cacheKey, ServerCacheType.COLLECTION_IDS, data).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            MethodCall methodCall = captor.getValue();
            Iterator<Object> args = Arrays.asList(methodCall.getArgs()).iterator();

            Assertions.assertEquals(cacheKey, args.next());
            replicated.onPutAll((byte[]) args.next());
            Assertions.assertEquals(((CachedManyIds) data.get("k1")).getIdList(), ((CachedManyIds) replicated.get("k1")).getIdList());
            Assertions.assertEquals(((CachedManyIds) data.get("k2")).getIdList(), ((CachedManyIds) replicated.get("k2")).getIdList());

            Mockito.clearInvocations(dispatcher);

            channel.broadcastRemoveAllAsync(cacheKey, data.keySet()).asMono().block();
            Mockito.verify(dispatcher).callRemoteMethods(ArgumentMatchers.isNull(), captor.capture(), ArgumentMatchers.any(RequestOptions.class));
            methodCall = captor.getValue();
            args = Arrays.asList(methodCall.getArgs()).iterator();
            Assertions.assertEquals(cacheKey, args.next());
            replicated.onRemoveAll((byte[]) args.next());
            Assertions.assertNull(replicated.get("k1"));
            Assertions.assertNull(replicated.get("k2"));

            Mockito.clearInvocations(dispatcher);
        }
    }
}
