package com.turbospaces.ebean;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.ebean.annotation.Cache;
import io.ebean.annotation.DbJsonB;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity
@Table(name = "account", schema = "CORE")
@Cache(naturalKey = { "username" }, enableQueryCache = true)
public class Account {
    @Id
    private long id;

    @Column(unique = true)
    private String username;

    private String firstName;
    private String lastName;
    private int age;
    private LocalDate birthDate;

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL)
    private List<AccountBalance> balances;

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL)
    private List<UTMTemplate> utmTemplates;

    @DbJsonB
    private Map<String, Object> details;

    @DbJsonB
    private FraudJson fraud;

    @Embedded
    private GameplayInfo gameplayInfo;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Version
    private int version;

    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    public LocalDate getBirthDate() {
        return birthDate;
    }
    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }
    public int getAge() {
        return age;
    }
    public void setAge(int age) {
        this.age = age;
    }
    public List<AccountBalance> getBalances() {
        return balances;
    }
    public void setBalances(List<AccountBalance> balances) {
        this.balances = balances;
    }
    public List<UTMTemplate> getUtmTemplates() {
        return utmTemplates;
    }
    public void setUtmTemplates(List<UTMTemplate> utmTemplates) {
        this.utmTemplates = utmTemplates;
    }
    public Map<String, Object> getDetails() {
        return details;
    }
    public void setDetails(Map<String, Object> details) {
        this.details = details;
    }
    public FraudJson getFraud() {
        return fraud;
    }
    public void setFraud(FraudJson fraud) {
        this.fraud = fraud;
    }
    public GameplayInfo getGameplayInfo() {
        return gameplayInfo;
    }
    public void setGameplayInfo(GameplayInfo gameplayInfo) {
        this.gameplayInfo = gameplayInfo;
    }
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
    public Date getCreatedAt() {
        return createdAt;
    }
    public Date getModifiedAt() {
        return modifiedAt;
    }
    public int getVersion() {
        return version;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getUsername()).build();
    }
    @Override
    public boolean equals(Object obj) {
        Account other = (Account) obj;
        return new EqualsBuilder().append(getUsername(), other.getUsername()).isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("id", getId()).append("username", getUsername()).append("version", getVersion()).build();
    }
}
