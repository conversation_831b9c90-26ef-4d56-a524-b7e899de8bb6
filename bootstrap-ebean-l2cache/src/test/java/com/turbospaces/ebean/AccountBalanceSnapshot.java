package com.turbospaces.ebean;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.ebean.annotation.Cache;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity
@Table(name = "account_balance_snapshots", schema = "CORE")
@Cache(enableQueryCache = true)
public class AccountBalanceSnapshot {
    @Id
    private AccountBalanceSnapshotId pk;

    @ManyToOne(optional = false)
    @JoinColumns({ //
            @JoinColumn(name = "account_id", referencedColumnName = "account_id", nullable = false, insertable = false, updatable = false), //
            @JoinColumn(name = "currency", referencedColumnName = "currency", nullable = false, insertable = false, updatable = false) })
    private AccountBalance balance;

    @Column(nullable = false)
    private BigDecimal amount;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountBalanceSnapshot() {}
    public AccountBalanceSnapshot(AccountBalance balance, LocalDate at) {
        AccountBalanceSnapshotId k = new AccountBalanceSnapshotId(balance.getAccount(), balance.getPk().getCurrency(), at);
        setPk(k);
        setBalance(balance);
        setAmount(BigDecimal.ZERO);
    }
    public AccountBalanceSnapshotId getPk() {
        return pk;
    }
    public void setPk(AccountBalanceSnapshotId pk) {
        this.pk = pk;
    }
    public AccountBalance getBalance() {
        return balance;
    }
    public void setBalance(AccountBalance balance) {
        this.balance = balance;
    }
    public BigDecimal getAmount() {
        return amount;
    }
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    public int getVersion() {
        return version;
    }
    public void setVersion(int version) {
        this.version = version;
    }
    public Date getCreatedAt() {
        return createdAt;
    }
    public Date getModifiedAt() {
        return modifiedAt;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getPk()).build();
    }
    @Override
    public boolean equals(Object obj) {
        AccountBalanceSnapshot other = (AccountBalanceSnapshot) obj;
        return new EqualsBuilder().append(getPk(), other.getPk()).isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("account", getPk().getAccountId())
                .append("currency", getPk().getCurrency())
                .append("at", getPk().getAt())
                .append("amount", getAmount())
                .append("version", getVersion())
                .build();
    }
}
