package com.turbospaces.ebean;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.ebean.annotation.Cache;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity
@Table(name = "account_balances", schema = "CORE")
@Cache(enableQueryCache = true)
public class AccountBalance {
    @EmbeddedId
    private AccountBalanceId pk;

    @Column
    private BigDecimal amount;

    @Column
    private boolean crypto;

    @ManyToOne(optional = false)
    private Account account;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumns({ //
            @JoinColumn(name = "account_id", referencedColumnName = "account_id", nullable = false, insertable = false, updatable = false), //
            @JoinColumn(name = "currency", referencedColumnName = "currency", nullable = false, insertable = false, updatable = false) })
    private List<AccountBalanceSnapshot> snapshots;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountBalance() {}
    public AccountBalance(Account player, String currency) {
        setPk(new AccountBalanceId(player, currency));
        setAccount(player);
        setAmount(BigDecimal.ZERO);
    }
    public AccountBalanceId getPk() {
        return pk;
    }
    public void setPk(AccountBalanceId pk) {
        this.pk = pk;
    }
    public Account getAccount() {
        return account;
    }
    public void setAccount(Account player) {
        this.account = player;
    }
    public BigDecimal getAmount() {
        return amount;
    }
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    public boolean isCrypto() {
        return crypto;
    }
    public void setCrypto(boolean crypto) {
        this.crypto = crypto;
    }
    public List<AccountBalanceSnapshot> getSnapshots() {
        return snapshots;
    }
    public void setSnapshots(List<AccountBalanceSnapshot> snapshots) {
        this.snapshots = snapshots;
    }
    public BigDecimal add(BigDecimal delta) {
        BigDecimal newAmount = getAmount().add(delta);
        setAmount(newAmount);
        return newAmount;
    }
    public BigDecimal subtract(BigDecimal delta) {
        BigDecimal newAmount = getAmount().subtract(delta);
        setAmount(newAmount);
        return newAmount;
    }
    public int getVersion() {
        return version;
    }
    public void setVersion(int version) {
        this.version = version;
    }
    public Date getCreatedAt() {
        return createdAt;
    }
    public Date getModifiedAt() {
        return modifiedAt;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getPk()).build();
    }
    @Override
    public boolean equals(Object obj) {
        AccountBalance other = (AccountBalance) obj;
        return new EqualsBuilder().append(getPk(), other.getPk()).isEquals();
    }
    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("currency", getPk().getCurrency()).append("amount", getAmount()).append("version", getVersion()).build();
    }
}
