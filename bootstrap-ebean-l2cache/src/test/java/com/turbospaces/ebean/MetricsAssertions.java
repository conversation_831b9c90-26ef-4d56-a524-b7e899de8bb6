package com.turbospaces.ebean;

import com.turbospaces.boot.SimpleBootstrap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MetricsAssertions {
    public static boolean checkEbeanCacheCounterMetric(
            SimpleBootstrap bootstrap,
            String queryName,
            String name,
            String type,
            double expectedCount) {
        try {
            double actual = bootstrap.meterRegistry()
                    .get(queryName)
                    .tag("name", name)
                    .tag("type", type)
                    .counter()
                    .count();
            log.info("asserting: actual: {} for {} {} expected: {} ", actual, queryName, name, expectedCount);
            return actual == expectedCount;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean checkEbeanCacheGaugeMetric(
            SimpleBootstrap bootstrap,
            String queryName,
            String name,
            String type,
            double expectedCount) {
        try {
            double actual = bootstrap.meterRegistry()
                    .get(queryName)
                    .tag("name", name)
                    .tag("type", type)
                    .gauge()
                    .value();
            log.info("asserting: actual: {} for {} {} expected: {} ", actual, queryName, name, expectedCount);
            return actual == expectedCount;
        } catch (Exception e) {
            return false;
        }
    }
}
