package com.turbospaces.ebean;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import org.apache.commons.lang3.time.StopWatch;
import org.jgroups.util.ByteArrayDataInputStream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.redisson.client.codec.ByteArrayCodec;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.utility.DockerImageName;

import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.redis.RedisClientFactoryBean;
import com.turbospaces.ups.UPSs;

import io.ebean.cache.QueryCacheEntry;
import io.ebean.cache.QueryCacheEntryValidate;
import io.ebean.cache.ServerCacheConfig;
import io.ebean.cache.ServerCacheOptions;
import io.ebean.cache.ServerCacheType;
import io.ebean.config.CurrentTenantProvider;
import io.github.resilience4j.ratelimiter.internal.InMemoryRateLimiterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MicroBenchmarkTest {
    public static final int REDIS_PORT = 6379;
    private final int iterations = 1_000;
    private final int threads = 16;
    private final String cacheKey = PlatformUtil.randomUUID().toString();
    private final CurrentTenantProvider tenant = new CurrentTenantProvider() {
        @Override
        public Object currentId() {
            return null;
        }
    };
    private final QueryCacheEntryValidate cacheEntryValidate = new QueryCacheEntryValidate() {
        @Override
        public boolean isValid(QueryCacheEntry queryCacheEntry) {
            return true;
        }
    };

    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();

        try (var container = new GenericContainer<>(DockerImageName.parse("redis:7"))) {
            container.withNetworkAliases("redis");
            container.addExposedPort(REDIS_PORT);
            container.start();
            RedisServiceInfo si = new RedisServiceInfo(UPSs.REDIS, "localhost", container.getMappedPort(REDIS_PORT), null);

            RedisClientFactoryBean factoryBean = new RedisClientFactoryBean(props, meterRegistry, si);
            factoryBean.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
            factoryBean.afterPropertiesSet();

            RedissonBroadcaster broadcaster = new RedissonBroadcaster(props, factoryBean.getObject(), cacheKey);
            DefaultBroadcastChannel channel = new DefaultBroadcastChannel(props, meterRegistry, new InMemoryRateLimiterRegistry(), broadcaster);
            channel.setBackgroundExecutor(MoreExecutors.listeningDecorator(Executors.newSingleThreadScheduledExecutor()));
            ReplicatedEbeanCache cache = new ReplicatedEbeanCache(
                    props,
                    cacheKey,
                    tenant,
                    new ServerCacheConfig(ServerCacheType.NATURAL_KEY, cacheKey, cacheKey, new ServerCacheOptions(), tenant, cacheEntryValidate),
                    channel);

            int total = iterations * threads;
            CountDownLatch latchReceived = new CountDownLatch(total);
            RTopic topic = factoryBean.getObject().getTopic(cacheKey, new ByteArrayCodec());
            topic.addListener(byte[].class, new MessageListener<byte[]>() {
                @Override
                public void onMessage(CharSequence name, byte[] msg) {
                    try (ByteArrayDataInputStream stream = new ByteArrayDataInputStream(msg)) {
                        MethodCallWithInstanceInfo info = new MethodCallWithInstanceInfo();
                        info.readFrom(stream);

                        if (props.CLOUD_APP_INSTANCE_INDEX.get().equals(info.slot())) {

                        }
                    } catch (Throwable err) {
                        log.error(err.getMessage(), err);
                    } finally {
                        latchReceived.countDown();
                        log.trace("current received: {}", latchReceived.getCount());
                    }
                }
            });

            CountDownLatch latchSent = new CountDownLatch(total);
            try (ExecutorService executor = Executors.newFixedThreadPool(threads)) {
                for (int i = 0; i < total; i++) {
                    executor.execute(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Long now = System.currentTimeMillis();
                                String key = PlatformUtil.randomAlphabetic(getClass().getName().length());
                                channel.broadcastPutAsync(cacheKey, ServerCacheType.NATURAL_KEY, key, now).asMono().subscribe(new Consumer<>() {
                                    @Override
                                    public void accept(StopWatch stopWatch) {
                                        latchSent.countDown();
                                        log.trace("current sent: {}", latchSent.getCount());
                                    }
                                });
                            } catch (Throwable err) {
                                log.error(err.getMessage(), err);
                            }
                        }
                    });
                }

                Assertions.assertTrue(latchSent.await(1, TimeUnit.MINUTES));
                Assertions.assertTrue(latchReceived.await(1, TimeUnit.MINUTES));
            } finally {
                cache.clear();
                factoryBean.destroy();
            }
        }
    }
}
