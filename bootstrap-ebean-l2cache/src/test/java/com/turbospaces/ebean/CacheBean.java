package com.turbospaces.ebean;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collections;
import java.util.Set;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.awaitility.Awaitility;
import org.awaitility.core.ThrowingRunnable;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.InitializingBean;

import com.turbospaces.ebean.query.QAccount;

import io.ebean.Transaction;
import io.ebean.cache.ServerCache;
import io.ebean.cache.ServerCacheType;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CacheBean implements InitializingBean {
    private final JpaManager ebean;
    private final CacheManager manager;

    @Inject
    public CacheBean(JpaManager ebean, CacheManager manager) {
        this.ebean = ebean;
        this.manager = manager;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        String aname = Account.class.getName();
        String abname = AccountBalance.class.getName();

        long id = Math.abs(hashCode());
        Account account;
        try (Transaction tx = ebean.newTransaction()) {
            account = new Account();

            account.setId(id);
            account.setFraud(new FraudJson(Collections.emptyMap()));
            account.setUsername("username_" + account.getId());
            account.setFirstName("f_" + account.getId());
            account.setLastName("l_" + account.getId());
            account.setDetails(Collections.emptyMap());

            AccountBalance balance1 = new AccountBalance(account, "USD");
            balance1.setAmount(BigDecimal.TEN);
            account.getBalances().add(balance1);

            AccountBalance balance2 = new AccountBalance(account, "EUR");
            balance2.setAmount(BigDecimal.ONE);
            account.getBalances().add(balance2);

            AccountBalance balance3 = new AccountBalance(account, "UAH");
            balance3.setAmount(BigDecimal.ONE);
            account.getBalances().add(balance3);

            ebean.save(account);
            tx.commit();
        } catch (Throwable e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        try (Transaction tx = ebean.newTransaction()) {
            for (Account it : ebean.find(Account.class).findList()) {
                it.setAge(it.getAge() + 1);
                it.setGameplayInfo(new GameplayInfo());
                ebean.save(it, tx);
            }

            tx.commit();
        } catch (Throwable e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        for (int i = 0; i < 10; i++) {
            log.debug("it ::: {}", i);
            try (Transaction tx = ebean.newReadOnlyTransaction()) {
                tx.setReadOnly(true);
                account = ebean.find(Account.class, id);
                log.debug("balances count: {}", account.getBalances().size()); // id cache
                for (AccountBalance balance : account.getBalances()) {
                    log.trace(balance.getAmount().toString()); // balance cache
                }
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }

        for (int i = 0; i < 10; i++) {
            try (Transaction tx = ebean.newReadOnlyTransaction()) {
                tx.setReadOnly(true);
                Set<Account> accounts = ebean.createQuery(Account.class).setUseQueryCache(true).findSet();
                log.debug("accounts count: {}", accounts.size());
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
        // Assert.assertEquals( 1, qCache.size() );

        try (Transaction tx = ebean.newTransaction()) {
            account = ebean.find(Account.class, id);
            account.setAge(32);
            ebean.save(account);
            tx.commit();
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(new ThrowingRunnable() {
            @Override
            public void run() throws Throwable {
                // Assert.assertEquals( 0, qCache.size() );
            }
        });

        // Cache<Object, Object> qCache = channel.getCache( aname + ServerCacheType.QUERY.code() );
        ServerCache aCache = manager.getCache(aname + ServerCacheType.BEAN.code());
        ServerCache bCache = manager.getCache(abname + ServerCacheType.BEAN.code());
        ServerCache cCache = manager.getCache(aname + "." + QAccount.alias().balances.toString() + ServerCacheType.COLLECTION_IDS.code());

        Assertions.assertEquals(1, aCache.size());
        // DefaultServerCache.getTrimSize ebean trims not to max size, ideally expected 2
        // debug DefaultServerCache.runEviction for more details
        Assertions.assertEquals(1, bCache.size());
        Assertions.assertEquals(1, cCache.size());

        ebean.cacheManager().beanCache(Account.class).clear();
        Assertions.assertEquals(0, aCache.size());
        // DefaultServerCache.getTrimSize ebean trims not to max size, ideally expected 2
        // debug DefaultServerCache.runEviction for more details
        Assertions.assertEquals(1, bCache.size());
        Assertions.assertEquals(1, cCache.size());

        // clear bean cache
        ebean.cacheManager().beanCache(AccountBalance.class).clear();
        Assertions.assertEquals(0, bCache.size());

        ebean.cacheManager().clear(Account.class);
        Assertions.assertEquals(0, cCache.size());
    }
}
