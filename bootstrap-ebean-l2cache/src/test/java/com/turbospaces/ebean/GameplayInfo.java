package com.turbospaces.ebean;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

@Embeddable
public class GameplayInfo {
    @Column
    private Date firstGameplay;

    @Column
    private Date lastGameplay;

    public Date getFirstGameplay() {
        return firstGameplay;
    }
    public void setFirstGameplay(Date firstGameplay) {
        this.firstGameplay = firstGameplay;
    }
    public Date getLastGameplay() {
        return lastGameplay;
    }
    public void setLastGameplay(Date lastGameplay) {
        this.lastGameplay = lastGameplay;
    }
}
