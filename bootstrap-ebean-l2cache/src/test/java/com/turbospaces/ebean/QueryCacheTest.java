package com.turbospaces.ebean;

import static com.turbospaces.ebean.query.QAccount.Alias.username;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.query.QAccount;
import com.turbospaces.ebean.query.QAccountBalance;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.jgroups.JGroupsFactoryBean;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.ebean.Transaction;
import io.ebean.common.BeanList;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class QueryCacheTest {

    @Test
    void testCacheReturnsSameInstanceOnHit() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        try {
            bootstrap.withH2(true, bootstrap.spaceName());
            ServiceInfo ownerUps = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.H2_OWNER);
            bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, ownerUps, List.of(),"CORE"));
            ConfigurableApplicationContext applicationContext = bootstrap.run();
            JpaManager ebean = applicationContext.getBean(JpaManager.class);

            Account account = new Account();
            account.setId(ApiFactory.RANDOM.nextLong());
            String userName = "username_" + account.getId();
            account.setUsername(userName);
            account.setFirstName("f_" + account.getId());
            account.setLastName("l_" + account.getId());
            account.setAge(18);
            account.setBirthDate(LocalDate.now(ZoneOffset.UTC).minusYears(account.getAge()));
            ebean.save(account);

            saveBalance(ebean, account, BigDecimal.ONE, "USD");

            List<Account> accountsList = accountsQueryCache(ebean);
            assertEquals(1, ebean.cacheManager().queryCache(Account.class).size());

            // ~ same account bean list
            assertEquals(BeanList.class, accountsList.getClass());
            assertSame(accountsList, accountsQueryCache(ebean));

            // ~ same account
            account = accountsList.getFirst();
            assertEquals(Account.class, account.getClass());
            assertSame(account, accountsQueryCache(ebean).getFirst());

            // ~ same balance bean list
            List<AccountBalance> balances = account.getBalances();
            assertEquals(BeanList.class, balances.getClass());
            assertSame(balances, accountsQueryCache(ebean).getFirst().getBalances());

            // ~ same balance
            assertSame(balances.getFirst(), accountsQueryCache(ebean).getFirst().getBalances().getFirst());
        } finally {
            bootstrap.shutdown();
        }
    }

    private static void saveBalance(JpaManager ebean, Account account, BigDecimal amount, String currency) {
        AccountBalance balance = new AccountBalance(account, currency);
        balance.setAmount(amount);
        ebean.save(balance);
    }

    private static List<Account> accountsQueryCache(JpaManager ebean) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            var query = new QAccount(ebean).usingTransaction(tx).setUseQueryCache(true);
            query.username.eq(username);
            return query.findList();
        }
    }

    @Configurable
    public static class AppConfig {
        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, MeterRegistry meterRegistry, DynamicCloud cloud) {
            ServiceInfo appUps = UPSs.findRequiredServiceInfoByName(cloud, UPSs.H2_APP);
            return new HikariDataSourceFactoryBean(props, meterRegistry, appUps);
        }

        @Bean
        public JGroupsFactoryBean jgroups(ApplicationProperties props) {
            return new JGroupsFactoryBean(props);
        }

        @Bean
        public JGroupCacheManagerFactoryBean cacheManager(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                JGroupsFactoryBean factory) throws Exception {
            return new JGroupCacheManagerFactoryBean(props, meterRegistry, rateLimiterRegistry, factory.getObject());
        }

        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.addClass(Account.class);
            cfg.addClass(GameplayInfo.class);
            cfg.addClass(FraudJson.class);
            cfg.addClass(UTMTemplate.class);
            cfg.addClass(AccountBalance.class);
            cfg.addClass(AccountBalanceId.class);
            cfg.addClass(AccountBalanceSnapshot.class);
            cfg.addClass(AccountBalanceSnapshotId.class);

            cfg.setLocal(UTMTemplate.class);
            cfg.setLocal(Account.class);
            cfg.setLocal(AccountBalance.class);
            cfg.setLocal(AccountBalanceSnapshot.class);
            cfg.setLocal(Account.class, QAccount.alias().balances);
            cfg.setLocal(Account.class, QAccount.alias().utmTemplates);
            cfg.setLocal(AccountBalance.class, QAccountBalance.alias().snapshots);

            return cfg;
        }

        @Bean
        public EbeanFactoryBean ebean(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                Tracer tracer,
                EbeanDatabaseConfig config,
                JGroupCacheManagerFactoryBean cache) throws Exception {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config, cache.getObject());
        }
    }
}
