package com.turbospaces.ebean;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import com.google.common.collect.Sets;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.query.QAccount;
import com.turbospaces.ebean.query.QAccountBalance;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.jgroups.JGroupsFactoryBean;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.ups.H2ServiceInfo;
import com.turbospaces.ups.UPSs;

import io.ebean.Transaction;
import io.ebean.config.dbplatform.PlatformIdGenerator;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class EbeanInfinispanPluginTest {
    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
        bootstrap.withH2(true, bootstrap.spaceName());
        H2ServiceInfo ownerUps = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.H2_OWNER);

        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, ownerUps, List.of(),"CORE"));
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            EbeanJpaManager ebean = (EbeanJpaManager) applicationContext.getBean(JpaManager.class);
            Set<Long> set = Sets.newHashSet();
            long now = System.currentTimeMillis();

            try (Transaction tx = ebean.newTransaction()) {
                for (int i = 0; i < 16 * 1024; i++) {
                    PlatformIdGenerator generator = ebean.idGenerator(AccountSeq.class);
                    Account account = new Account();

                    long nextId = (long) generator.nextId(tx);
                    Assertions.assertTrue(set.add(nextId));

                    account.setId(now + nextId);
                    account.setFraud(new FraudJson(Collections.emptyMap()));
                    account.setUsername("username_" + account.getId());
                    account.setFirstName("f_" + account.getId());
                    account.setLastName("l_" + account.getId());
                    account.setDetails(Collections.emptyMap());

                    ebean.save(account, tx);

                }

                tx.commit();
            }

            long min = set.stream().min(Long::compare).get();
            long max = set.stream().max(Long::compare).get();

            Assertions.assertEquals(min, 1);
            Assertions.assertEquals(max, 16 * 1024);
            Assertions.assertEquals(ebean.idGenerators().size(), 1);

        } finally {
            bootstrap.shutdown();
        }
    }

    @Configurable
    public static class AppConfig {
        @Bean
        public HikariDataSourceFactoryBean ds(ApplicationProperties props, MeterRegistry meterRegistry, DynamicCloud cloud) {
            ServiceInfo appUps = UPSs.findRequiredServiceInfoByName(cloud, UPSs.H2_APP);
            return new HikariDataSourceFactoryBean(props, meterRegistry, appUps);
        }
        @Bean
        public JGroupsFactoryBean jgroups(ApplicationProperties props) {
            return new JGroupsFactoryBean(props);
        }
        @Bean
        public JGroupCacheManagerFactoryBean cacheManager(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                JGroupsFactoryBean factory)
                throws Exception {
            return new JGroupCacheManagerFactoryBean(props, meterRegistry, rateLimiterRegistry, factory.getObject());
        }
        @Bean
        public EbeanDatabaseConfig ebeanConfig(ApplicationProperties props, HikariDataSourceFactoryBean factory) throws Exception {
            EbeanDatabaseConfig cfg = new EbeanDatabaseConfig(factory.getObject(), props);

            cfg.setLocal(UTMTemplate.class);
            cfg.setLocal(Account.class);
            cfg.setLocal(AccountBalance.class);
            cfg.setLocal(AccountBalanceSnapshot.class);
            cfg.setLocal(Account.class, QAccount.alias().balances);
            cfg.setLocal(Account.class, QAccount.alias().utmTemplates);
            cfg.setLocal(AccountBalance.class, QAccountBalance.alias().snapshots);

            cfg.addClass(AccountSeq.class);
            cfg.addClass(Account.class);
            cfg.addClass(GameplayInfo.class);
            cfg.addClass(FraudJson.class);
            cfg.addClass(AccountBalance.class);
            cfg.addClass(AccountBalanceId.class);
            cfg.addClass(AccountBalanceSnapshot.class);
            cfg.addClass(AccountBalanceSnapshotId.class);
            cfg.addClass(UTMTemplate.class);

            cfg.setMaxSize(Account.class, 1);
            cfg.setMaxSizeQuery(Account.class, 1);

            cfg.setMaxSize(Account.class, QAccount.alias().balances, 1);
            cfg.setMaxSize(UTMTemplate.class, Byte.SIZE);
            cfg.setMaxSize(AccountBalance.class, 2);
            cfg.setMaxSize(AccountBalanceSnapshot.class, 4);
            cfg.setMaxSize(Account.class, QAccount.alias().utmTemplates, Integer.MAX_VALUE);

            return cfg;
        }
        @Bean
        public EbeanFactoryBean ebean(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                Tracer tracer,
                EbeanDatabaseConfig config,
                JGroupCacheManagerFactoryBean cache) throws Exception {
            return new EbeanFactoryBean(props, meterRegistry, tracer, config, cache.getObject());
        }
        @Bean
        public RollbackBean rollbackBean(EbeanFactoryBean ebean, CacheManager manager) throws Exception {
            return new RollbackBean(ebean.getObject(), manager);
        }
        @Bean
        public CacheBean cacheBean(EbeanFactoryBean ebean, CacheManager manager) throws Exception {
            return new CacheBean(ebean.getObject(), manager);
        }
    }
}
