package com.turbospaces.ebean;

import io.ebean.annotation.Identity;
import io.ebean.annotation.IdentityType;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "accounts_seq", schema = "CORE")
public class AccountSeq extends AbstractSeq {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    @Identity(type = IdentityType.SEQUENCE, sequenceName = "core.accounts_id_seq")
    private long id;

    @Override
    public long getId() {
        return id;
    }
    @Override
    public void setId(long id) {
        this.id = id;
    }
}
