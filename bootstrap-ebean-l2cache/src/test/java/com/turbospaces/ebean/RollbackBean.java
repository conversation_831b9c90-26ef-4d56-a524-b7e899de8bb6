package com.turbospaces.ebean;

import java.util.Collections;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.InitializingBean;

import com.turbospaces.ebean.query.QUTMTemplate;

import io.ebean.DuplicateKeyException;
import io.ebean.Transaction;
import io.ebean.cache.ServerCache;
import io.ebean.cache.ServerCacheType;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RollbackBean implements InitializingBean {
    private final JpaManager ebean;
    private final CacheManager manager;

    @Inject
    public RollbackBean(JpaManager ebean, CacheManager manager) {
        this.ebean = ebean;
        this.manager = manager;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        String tname = UTMTemplate.class.getName();
        long id = Math.abs(hashCode());

        Account account;
        try (Transaction tx = ebean.newTransaction()) {
            account = new Account();

            account.setId(id);
            account.setFraud(new FraudJson(Collections.emptyMap()));
            account.setUsername("username_" + account.getId());
            account.setFirstName("f_" + account.getId());
            account.setLastName("l_" + account.getId());
            account.setDetails(Collections.emptyMap());

            ebean.save(account);
            tx.flush();

            UTMTemplate template1 = new UTMTemplate(account, "utm-1");
            ebean.save(template1, tx);

            UTMTemplate template2 = new UTMTemplate(account, "utm-2");
            ebean.save(template2, tx);

            new QUTMTemplate(ebean).usingTransaction(tx).account.eq(account).campaign.eq(template1.getCampaign()).findOne();
            new QUTMTemplate(ebean).usingTransaction(tx).account.eq(account).campaign.eq(template2.getCampaign()).findOne();

            UTMTemplate template3 = new UTMTemplate(account, "utm-2");
            ebean.save(template3, tx);

            tx.rollback();
        } catch (DuplicateKeyException err) {
            log.warn(err.getMessage(), err);
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }

        ServerCache tCache = manager.getCache(tname + ServerCacheType.BEAN.code());

        Assertions.assertEquals(2, tCache.size());
    }
}