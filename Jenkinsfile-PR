def SONAR_TOKEN = ""
withFolderProperties{
  SONAR_TOKEN = "${env.SONAR_TOKEN}"
}

pipeline {
  agent {
    node { label 'patrianna-dev' }
  }

  environment {
    REGISTRY_ADDRESS = 'https://europe-docker.pkg.dev'
	  DOCKER_BUILDKIT = 1
  }
  
  options {
    disableConcurrentBuilds()
    timeout(time: 1, unit: 'HOURS')
  }

  tools { 
    maven 'mvn3'
    jdk 'jdk22'
  }

  stages {
	stage ('mvn package') {
      when {
        not { changelog '.*\\[maven-release-plugin\\].*' }
      }
      steps {
        withCredentials([file(credentialsId: 'patrianna-docker-registry-auth', variable: 'AUTH')]) {
          withCredentials([usernamePassword(credentialsId: 'nexus-oss-registry-auth', usernameVariable: 'NEXUS_OSS_USER', passwordVariable: 'NEXUS_OSS_CREDENTIALS')]) {
            sh "cat $AUTH | docker login -u _json_key --password-stdin ${env.REGISTRY_ADDRESS}"
              script{
                sh "mvn -s settings.xml clean package -DfailIfNoTests=false -U -T 1C"
              }
          }
        }
      }
	}

	stage ('mvn verify sonar') {
      when {
        not { changelog '.*\\[maven-release-plugin\\].*' }
      }
      steps {
        withCredentials([file(credentialsId: 'patrianna-docker-registry-auth', variable: 'AUTH')]) {
            sh "cat $AUTH | docker login -u _json_key --password-stdin ${env.REGISTRY_ADDRESS}"
              withSonarQubeEnv(credentialsId: 'sonarcloud', installationName: 'SonarCloud') { // You can override the credential to be used
                  script{
                      if (env.CHANGE_ID) {
                          sh "mvn verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.pullrequest.branch=${env.BRANCH_NAME} -Dsonar.pullrequest.base=${env.CHANGE_TARGET} -Dsonar.pullrequest.key=${env.CHANGE_ID} -Dsonar.organization=patrianna -Dsonar.projectKey=turbospaces_crm"
                      } else {
                          sh "mvn verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.branch.name=${env.BRANCH_NAME} -Dsonar.organization=patrianna -Dsonar.projectKey=turbospaces_crm"
                      }
                  }
            }
        }
      }
	}
  }

  post {
    failure {
      slackSend (
        channel: "${env.SLACK_CHANNEL}",
        color: "danger",
        message: "${env.JOB_NAME} failed: <${env.BUILD_URL}|Check build>"
      )
    }
    always {
      deleteDir()
    }
  }
}
