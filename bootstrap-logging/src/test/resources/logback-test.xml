<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <appender name="ELASTIC" class="com.turbospaces.logging.ElasticSearchAppender">
        <space>local-dev</space>
        <host>localhost</host>
        <service>demo-service</service>
        <slot>1</slot>
        <release>0.1</release>
        <threads>8</threads>
        <rate>5</rate>
        <queueSize>4096</queueSize>
        <neverBlock>false</neverBlock>
        <trace>true</trace>
        <template>logging-template.json</template>
        <properties>
            <property>
                <name>message</name>
                <value>%msg</value>
            </property>
            <property>
                <name>level</name>
                <value>%level</value>
            </property>
            <property>
                <name>thread</name>
                <value>%thread</value>
            </property>
            <property>
                <name>stacktrace</name>
                <value>%ex</value>
            </property>
            <property>
                <name>logger</name>
                <value>%logger</value>
            </property>
        </properties>
    </appender>
    <appender name="SENTRY" class="com.turbospaces.logging.SentryAppender">
        <space>local-dev</space>
        <host>localhost</host>
        <service>demo-service</service>
        <slot>1</slot>
        <release>0.1</release>
        <trace>true</trace>
        <properties>
            <property>
                <name>thread</name>
                <value>%thread</value>
            </property>
        </properties>
    </appender>

    <root>
        <level value="DEBUG" />
        <appender-ref ref="ELASTIC" />
        <appender-ref ref="SENTRY" />
    </root>
</configuration>