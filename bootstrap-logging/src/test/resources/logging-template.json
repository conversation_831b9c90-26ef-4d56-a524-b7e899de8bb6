{"index_patterns": ["logs-demo-service-*"], "settings": {"number_of_shards": 1, "index": {"sort.field": ["@timestamp", "@sequence"], "sort.order": ["asc", "asc"]}, "analysis": {"filter": {"edge_ngram_filter": {"type": "edge_ngram", "min_gram": 3, "max_gram": 10}}, "analyzer": {"edge_ngram_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "edge_ngram_filter"]}}}}, "mappings": {"properties": {"@timestamp": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}, "@sequence": {"type": "long"}, "space": {"type": "keyword"}, "host": {"type": "keyword"}, "service": {"type": "keyword"}, "slot": {"type": "keyword"}, "release": {"type": "keyword"}, "level": {"type": "keyword"}, "thread": {"type": "keyword"}, "logger": {"type": "keyword"}, "message": {"type": "text", "fields": {"ngrams": {"type": "text", "analyzer": "edge_ngram_analyzer"}}}, "stacktrace": {"type": "object", "enabled": false}}}}