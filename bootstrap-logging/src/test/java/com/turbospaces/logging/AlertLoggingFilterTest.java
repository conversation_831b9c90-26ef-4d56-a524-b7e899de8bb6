package com.turbospaces.logging;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import com.netflix.archaius.api.Property;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;

public class AlertLoggingFilterTest {
    private static final Throwable exception = new RuntimeException(new IllegalArgumentException("ex message"));

    public static Stream<Arguments> testLoggingFilterData() {
        NullPointerException NPE = new NullPointerException();
        return Stream.of(
                // Error without message
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList(),
                        Level.ERROR,
                        null,
                        new Object[] { null, NPE },
                        null,
                        NPE),
                // basic exception flow exception in params
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList(),
                        Level.ERROR,
                        "test {}",
                        new Object[] { "res", exception },
                        null,
                        exception),
                // basic exception flow
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList("ignore"),
                        Level.ERROR,
                        "test {}",
                        new Object[] { "res" },
                        exception,
                        exception),
                // basic exception flow wrong filters
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList(),
                        Level.ERROR,
                        "test {}",
                        new Object[] { "res", exception },
                        null,
                        exception),
                // level is filtered
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList(),
                        Level.INFO,
                        "test {}",
                        new Object[] { "res", exception },
                        null,
                        null),
                // ignore by log message
                Arguments.of(
                        Arrays.asList("target: CONFIG_API"),
                        Arrays.asList(),
                        Level.ERROR,
                        "target: {}; address: {}",
                        new Object[] { "CONFIG_API", "***********", exception },
                        null,
                        null),
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList(),
                        Level.WARN,
                        "target: {}; address: {}",
                        new Object[] { "CONFIG_API", "***********", exception },
                        null,
                        null),
                // filter by logger
                Arguments.of(
                        Arrays.asList(),
                        Arrays.asList("test"),
                        Level.ERROR,
                        "target: {}; address: {}",
                        new Object[] { "CONFIG_API", "***********", exception },
                        null,
                        null),
                // ignore ex message
                Arguments.of(
                        Arrays.asList("ex message"),
                        Arrays.asList(),
                        Level.ERROR,
                        "test {}",
                        new Object[] { "res", exception },
                        null,
                        null));
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @ParameterizedTest
    @MethodSource("testLoggingFilterData")
    public void testLoggingFilter(
            List<String> logMessagesToIgnore,
            List<String> loggersToIgnore,
            Level level,
            String format,
            Object[] params,
            Throwable error,
            Throwable expected) {
        Property logMessagesToIgnoreProp = mock(Property.class);
        when(logMessagesToIgnoreProp.get()).thenReturn(logMessagesToIgnore);
        Property loggersToIgnoreProp = mock(Property.class);
        when(loggersToIgnoreProp.get()).thenReturn(loggersToIgnore);
        AlertLoggingFilter filter = new AlertLoggingFilter(
                logMessagesToIgnoreProp,
                loggersToIgnoreProp);
        Logger logger = new LoggerContext().getLogger("test");
        assertEquals(
                expected,
                filter.test(logger.getName(), level, format, params, error).orElse(null));
    }
}
