package com.turbospaces.logging;

import java.util.concurrent.CountDownLatch;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DemoRunner {
    public static void main(String[] args) throws InterruptedException {
        long now = System.currentTimeMillis();

        int threads = 1;
        Logger logger = LoggerFactory.getLogger(DemoRunner.class);
        logger.error("validation_error", new RuntimeException("Validation Error"));
        CountDownLatch latch = new CountDownLatch(threads);
        for (int t = 0; t < threads; t++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        for (int i = 0; i < 10000; i++) {
                            logger.info("iteration = {}{} some text, and another {} {}",
                                    Thread.currentThread().getName(),
                                    i,
                                    System.currentTimeMillis(),
                                    getClass().getName());
                            try {
                                Thread.sleep(10);
                            } catch (Exception err) {}
                        }
                    } finally {
                        latch.countDown();
                    }
                }
            }).start();
        }
        latch.await();

        System.err.println("DONE : " + (System.currentTimeMillis() - now));

        Thread.sleep(100000);
    }
}
