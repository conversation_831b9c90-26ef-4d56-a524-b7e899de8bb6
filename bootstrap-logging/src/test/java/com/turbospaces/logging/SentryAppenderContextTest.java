package com.turbospaces.logging;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Predicate;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import io.github.resilience4j.ratelimiter.internal.InMemoryRateLimiterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.sentry.SentryClient;
import io.sentry.event.EventBuilder;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;

public class SentryAppenderContextTest {
    private final Logger logger = (Logger) LoggerFactory.getLogger(SentryAppenderContextTest.class);

    private InMemoryRateLimiterRegistry rateLimiterRegistry;
    private Sinks.Many<ILoggingEvent> processor;
    private SentryAppender appender;
    private final SentryClient sentryClient = Mockito.mock(SentryClient.class);

    @BeforeEach
    public void setUp() {
        rateLimiterRegistry = new InMemoryRateLimiterRegistry();
        processor = Sinks.unsafe().many().multicast().directBestEffort();

        appender = new SentryAppender() {
            @Override
            protected void sendBulk(List<ILoggingEvent> list) {
                super.sendBulk(list);
                for (var event : list) {
                    EmitResult emitResult = processor.tryEmitNext(event);
                    Assertions.assertTrue(emitResult.isSuccess());
                }
            }
        };
        appender.setName("sentry-appender");
        appender.setSpace("dev");
        appender.setHost("localhost");
        appender.setService("house-keeping");
        appender.setSlot(Long.toString(System.currentTimeMillis()));
        appender.setRelease("1.0");
        appender.setMeterRegistry(new SimpleMeterRegistry());
        appender.setRateLimiterRegistry(rateLimiterRegistry);
        appender.setSentry(sentryClient);
        appender.setContext((LoggerContext) LoggerFactory.getILoggerFactory());

        appender.start();

        logger.addAppender(appender);
    }
    @AfterEach
    public void tearDown() {
        logger.detachAppender(appender);
    }
    @Test
    void baseScenarioCheckRateLimiter_success() throws Throwable {
        int amountOfErrors = SentryAppender.COUNT * 3;
        String errorMessage = "n errors call: {}";
        CountDownLatch latch = new CountDownLatch(amountOfErrors);

        processor.asFlux().filter(new Predicate<>() {
            @Override
            public boolean test(ILoggingEvent event) {
                return event.getMDCPropertyMap().containsKey("counter1");
            }
        }).subscribe(new Consumer<>() {
            @Override
            public void accept(ILoggingEvent t) {
                latch.countDown();
            }
        });

        for (int i = 0; i < amountOfErrors; i++) {
            MDC.put("counter1", Integer.toString(i));
            try {
                logger.error(errorMessage, i);
            } finally {
                MDC.clear();
            }
        }

        Assertions.assertTrue(latch.await(30, TimeUnit.SECONDS));
        verify(sentryClient, times(SentryAppender.COUNT)).sendEvent(any(EventBuilder.class));
    }
    @Test
    void baseScenarioCheckRateLimiterAndMetricsWithExceptionInLogger_success() throws Throwable {

        int amountOfErrors = SentryAppender.COUNT * 3;
        String errorMessage = "n errors call: {}";
        CountDownLatch latch = new CountDownLatch(amountOfErrors);

        processor.asFlux().filter(new Predicate<>() {
            @Override
            public boolean test(ILoggingEvent event) {
                return event.getMDCPropertyMap().containsKey("counter2");
            }
        }).subscribe(new Consumer<>() {
            @Override
            public void accept(ILoggingEvent t) {
                latch.countDown();
            }
        });

        for (int i = 0; i < amountOfErrors; i++) {
            MDC.put("counter2", Integer.toString(i));
            try {
                logger.error(errorMessage, new IllegalAccessException());
            } finally {
                MDC.clear();
            }
        }

        Assertions.assertTrue(latch.await(30, TimeUnit.SECONDS));
        verify(sentryClient, times(SentryAppender.COUNT)).sendEvent(any(EventBuilder.class));
    }
}
