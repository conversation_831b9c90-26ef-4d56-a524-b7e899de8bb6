package com.turbospaces.logging;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;

public class DropSentryFilter extends Filter<ILoggingEvent> {
    @Override
    public FilterReply decide(ILoggingEvent event) {
        String loggerName = event.getLoggerName();
        if (loggerName != null && loggerName.startsWith("io.sentry")) {
            return FilterReply.DENY;
        }
        return FilterReply.NEUTRAL;
    }
}
