package com.turbospaces.logging;

import java.io.InputStream;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.service.UriBasedServiceInfo;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.joran.JoranConfigurator;
import ch.qos.logback.core.ConsoleAppender;
import ch.qos.logback.core.joran.spi.ConfigurationWatchList;
import ch.qos.logback.core.joran.spi.JoranException;
import ch.qos.logback.core.joran.util.ConfigurationWatchListUtil;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.sentry.SentryClient;

public interface Logback {
    //
    // ~ meta
    //
    String SPACE = "space";
    String HOST = "host";
    String SLOT = "slot";
    String SERVICE = "service";
    String RELEASE = "release";

    String LOGGING_REPORT_TO_SENTRY = "logging-report-to-sentry";
    String LOGGING_DRY_RUN = "logging-dryRun";
    String ALERTS_DRY_RUN = "alerts-dryRun";

    // ~ metrics registry
    String METER_REGISTRY = "meter-registry";
    String RATE_LIMITER_REGISTRY = "rate-limiter-registry";
    String SENTRY_LOGGING_FILTER = "sentry-logging-filter";

    String SENTRY_CLIENT = "sentry-client";
    String UPS_SENTRY = "sentry";
    String UPS_ELASTIC_SEARCH = "elastic-search";

    //
    // ~ template fields
    //
    String TIMESTAMP = "@timestamp";
    String SEQUENCE = "@sequence";

    static void configureFrom(
            LoggerContext loggerContext,
            AlertLoggingFilter alertLoggingFilter,
            InputStream io,
            Map<String, String> options,
            SentryClient sentry,
            RateLimiterRegistry rateLimiterRegistry,
            MeterRegistry meterRegistry,
            Supplier<Boolean> loggingDryRun,
            Supplier<Boolean> alertsDryRun,
            Supplier<Boolean> loggingReportToSentry,
            UriBasedServiceInfo sentryUps,
            UriBasedServiceInfo elasticUps,
            Map<String, String> substitute) throws JoranException {
        loggerContext.reset();

        for (Entry<String, String> entry : options.entrySet()) {
            loggerContext.putProperty(entry.getKey(), entry.getValue());
        }
        putObjectToLoggerContext(loggerContext, Logback.SENTRY_CLIENT, sentry);
        putObjectToLoggerContext(loggerContext, Logback.UPS_SENTRY, sentryUps);
        putObjectToLoggerContext(loggerContext, Logback.UPS_ELASTIC_SEARCH, elasticUps);
        putObjectToLoggerContext(loggerContext, Logback.SENTRY_LOGGING_FILTER, alertLoggingFilter);
        putObjectToLoggerContext(loggerContext, Logback.RATE_LIMITER_REGISTRY, rateLimiterRegistry);
        putObjectToLoggerContext(loggerContext, Logback.LOGGING_DRY_RUN, loggingDryRun);
        putObjectToLoggerContext(loggerContext, Logback.ALERTS_DRY_RUN, alertsDryRun);
        putObjectToLoggerContext(loggerContext, Logback.LOGGING_REPORT_TO_SENTRY, loggingReportToSentry);
        putObjectToLoggerContext(loggerContext, Logback.METER_REGISTRY, meterRegistry);
        ConfigurationWatchListUtil.registerConfigurationWatchList(loggerContext, new ConfigurationWatchList());

        //
        // ~ well, for some loggers defined in context we want to setup level from GIT directly
        //
        substitute.entrySet().forEach(new Consumer<>() {
            @Override
            public void accept(Entry<String, String> entry) {
                loggerContext.addSubstitutionProperty(entry.getKey(), entry.getValue());
            }
        });

        configure(loggerContext, io);
    }
    static void putObjectToLoggerContext(LoggerContext loggerContext, String key, Object value) {
        if (Objects.nonNull(loggerContext) && Objects.nonNull(key) && Objects.nonNull(value)) {
            loggerContext.putObject(key, value);
        }
    }
    static void configure(LoggerContext loggerContext, InputStream io) throws JoranException {
        synchronized (Logback.class) {
            JoranConfigurator configurator = new JoranConfigurator();
            configurator.setContext(loggerContext);

            configurator.addInfo(String.format("starting configuration of logback ctx(%s) using props", loggerContext.getName()));
            for (Entry<String, String> entry : loggerContext.getCopyOfPropertyMap().entrySet()) {
                configurator.addInfo(entry.getKey() + "=" + entry.getValue());
            }

            configurator.doConfigure(io);
            configurator.addInfo(String.format("configured logback ctx(%s) ...", loggerContext.getName()));
        }
    }
    @SuppressWarnings({ "rawtypes", "unchecked" })
    static void resetToLevel(ch.qos.logback.classic.Level level) {
        synchronized (Logback.class) {
            LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();
            lc.reset();

            PatternLayoutEncoder enc = new PatternLayoutEncoder();
            enc.setContext(lc);
            enc.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} %msg%n");
            enc.start();

            ConsoleAppender console = new ConsoleAppender<>();
            console.setContext(lc);
            console.setName("console");
            console.setEncoder(enc);
            console.start();

            ch.qos.logback.classic.Logger root = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
            root.setLevel(level);
            root.addAppender(console);
        }
    }
    static void resetToInfo() {
        resetToLevel(ch.qos.logback.classic.Level.INFO);
    }
    static void resetToDebug() {
        resetToLevel(ch.qos.logback.classic.Level.DEBUG);
    }
}
