package com.turbospaces.logging;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.netflix.archaius.api.Property;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.EventArgUtil;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.ThrowableProxy;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;

/**
 * Filter for logging events with dynamic properties support.
 */
public class AlertLoggingFilter extends Filter<ILoggingEvent> {
    private volatile List<String> logMessagesToIgnore;
    private volatile List<String> loggersToIgnore;
    private volatile LoggingEventFilter loggingFilter;

    public AlertLoggingFilter(Property<List<String>> logMessagesToIgnoreProperty, Property<List<String>> loggersToIgnore) {
        this.logMessagesToIgnore = logMessagesToIgnoreProperty.get();
        this.loggersToIgnore = loggersToIgnore.get();
        this.loggingFilter = new LoggingEventFilter(this.logMessagesToIgnore, this.loggersToIgnore);
        subscribeToProperties(logMessagesToIgnoreProperty, loggersToIgnore);
    }
    @Override
    public FilterReply decide(ILoggingEvent event) {
        Throwable exception = null;
        if (event.getThrowableProxy() instanceof ThrowableProxy) {
            exception = ((ThrowableProxy) event.getThrowableProxy()).getThrowable();
        }

        return test(
                event.getLoggerName(),
                event.getLevel(),
                event.getMessage(),
                event.getArgumentArray(),
                exception).isPresent() ? FilterReply.NEUTRAL : FilterReply.DENY;
    }
    private void subscribeToProperties(
            Property<List<String>> logMessagesToIgnoreProperty,
            Property<List<String>> loggersToIgnoreProperty) {
        /*
         * on change for list is fired each time config is read cause list checked by reference
         * check DefaultPropertyContainer.subscribe
         */
        logMessagesToIgnoreProperty.subscribe(value -> {
            if (logMessagesToIgnore.equals(value)) {
                return;
            }
            logMessagesToIgnore = value;
            loggingFilter = new LoggingEventFilter(
                    logMessagesToIgnore,
                    loggersToIgnore);
        });
        loggersToIgnoreProperty.subscribe(value -> {
            if (loggersToIgnore.equals(value)) {
                return;
            }
            loggersToIgnore = value;
            loggingFilter = new LoggingEventFilter(logMessagesToIgnore, loggersToIgnore);
        });
    }
    public Optional<Throwable> test(String loggerName, Level level, String format, Object[] params, Throwable t) {
        Throwable target = t;
        if (Objects.isNull(target)) {
            target = EventArgUtil.extractThrowable(params);
        }

        //
        // ~ exception may be null if log.error occurred without any error logged
        //
        if (loggingFilter.test(loggerName, level, format, params, target)) {
            return Optional.of(Objects.isNull(target) ? new Exception() : target);
        }

        return Optional.empty();
    }
}
