package com.turbospaces.logging;

import ch.qos.logback.classic.PatternLayout;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Context;
import ch.qos.logback.core.pattern.PatternLayoutBase;

public class DocumentProperty {
    private PatternLayoutBase<ILoggingEvent> layout;
    private String name;
    private String value;

    public DocumentProperty() {}
    public DocumentProperty(String name, String value) {
        this.name = name;
        this.value = value;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getValue() {
        return value;
    }
    public void setValue(String value) {
        this.value = value;
    }
    public void init(Context context) {
        this.layout = new PatternLayout();
        this.layout.setContext(context);
        this.layout.setPattern(getValue());
        this.layout.setPostCompileProcessor(null);
        this.layout.start();
    }
    public String format(ILoggingEvent event) {
        return layout.doLayout(event);
    }
}
