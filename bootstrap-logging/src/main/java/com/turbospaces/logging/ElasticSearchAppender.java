package com.turbospaces.logging;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Random;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.google.common.base.Joiner;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;

import ch.qos.logback.classic.spi.ILoggingEvent;
import io.sentry.event.Event.Level;
import io.sentry.event.EventBuilder;
import io.sentry.event.interfaces.MessageInterface;
import lombok.Setter;
import okhttp3.ConnectionPool;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import okio.BufferedSink;

public class ElasticSearchAppender extends AbstractAppender {
    public static final MediaType X_JSON = MediaType.get("application/json");
    public static final MediaType X_NDJSON = MediaType.get("application/x-ndjson");

    public static final String TIMESTAMP = "@timestamp";
    public static final String SEQUENCE = "@sequence";

    public static DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static DateTimeFormatter YYYY_MM_DD_HH = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");

    private final JsonFactory jf = new JsonFactory(); // ~ JSON low-level factory
    private final Timer timer = new Timer(true); // ~ remove old indexes in background
    private final Cache<Response, List<String>> corr = CacheBuilder.newBuilder().expireAfterWrite(Duration.ofMinutes(1)).build();

    private OkHttpClient httpClient;

    // ~ options
    @Setter
    private String template; // ~ for automatic template registration
    @Setter
    private boolean hourly; // ~ create hourly index or daily instead
    @Setter
    private int retention = 7; // ~ for how long to keep logs (3 days by default)
    @Setter
    private String designator = "logs"; // ~ main logging prefix (designator)
    @Setter
    private int keepAliveTimeout = 30; // ~ how long to keep-alive HTTP connections
    @Setter
    private boolean retryOnConnectionFailure = true; // ~ should we automatically retry on client side

    @Override
    public void start() {
        super.start();

        addInfo(String.format("starting with settings hourly: %s, template: %s, retention: %s, designator: %s", hourly, template, retention, designator));

        OkHttpClient.Builder ok = new OkHttpClient.Builder();
        ok.connectTimeout(socketTimeout, TimeUnit.SECONDS);
        ok.connectionPool(new ConnectionPool(threads, keepAliveTimeout, TimeUnit.SECONDS));
        ok.retryOnConnectionFailure(retryOnConnectionFailure);
        if (trace) {
            //
            // ~ this might be buggy code, please do not enable it w/o real need, only if you want to really see network wire traffic
            //
            ok.addInterceptor(new Interceptor() {
                @Override
                public Response intercept(Chain chain) throws IOException {
                    //
                    // ~ line by line output (not 1 line actually)
                    //
                    List<String> l = Lists.newLinkedList();
                    HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(new HttpLoggingInterceptor.Logger() {
                        @Override
                        public void log(String message) {
                            l.add(message);
                        }
                    });
                    interceptor.setLevel(okhttp3.logging.HttpLoggingInterceptor.Level.BODY);
                    Response response = interceptor.intercept(chain);
                    corr.put(response, l);
                    return response;
                }
            });
        }

        httpClient = ok.build();

        if (Objects.nonNull(getElasticEndpoint())) {
            //
            // ~ try to register template automatically
            //
            if (StringUtils.isNotEmpty(template)) {
                registerTemplate();
            }

            // ~ mark started
            started = true;

            //
            // ~ effectively start
            //
            for (int i = 0; i < threads; i++) {
                WorkerThread worker = workers[i];
                worker.start();
            }

            //
            // ~ schedule cleanup job randomly (not to bombard all at once)
            // ~ auto house-keeping
            //
            Random rnd = new SecureRandom();

            int intervalRandom = 1 + rnd.nextInt((int) TimeUnit.DAYS.toHours(1));
            int interval = (int) TimeUnit.HOURS.toMillis(intervalRandom);

            int delayRandom = 1 + rnd.nextInt((int) TimeUnit.HOURS.toMinutes(1));
            int delay = (int) TimeUnit.MINUTES.toMillis(delayRandom);

            addInfo(String.format("log removal task with run hourly at rate: %s hour(s) and initial delay: %s min(s)", intervalRandom, delayRandom));

            timer.scheduleAtFixedRate(new TimerTask() {
                @Override
                public void run() {
                    if (retention > 0) {
                        String op = "delete_old_indexes_by_retention";
                        LocalDate from = LocalDate.now(ZoneOffset.UTC).minusMonths(1);
                        LocalDate to = LocalDate.now(ZoneOffset.UTC).minusDays(retention);

                        addInfo(String.format("running log retention task from: %s, to: %s", from, to));

                        //
                        // ~ irrespectively if this it is hourly or not, we still want to drop daily + hourly indexes
                        // ~ we want to cover case when the index was hourly but then down shifted to daily or vice versa
                        //
                        for (int d = 0; d < ChronoUnit.DAYS.between(from, to); d++) {
                            LocalDate date = from.plusDays(d);
                            Collection<String> indexes = Lists.newArrayList();

                            //
                            // ~ append daily and hourly all together (try finally just for formatting purpose)
                            //
                            try {
                                indexes.add(Joiner.on("-").join(designator, getService(), YYYY_MM_DD.format(date)));
                            } finally {
                                for (int i = 0; i < TimeUnit.DAYS.toHours(1); i++) {
                                    LocalDateTime hours = date.atStartOfDay().plusHours(i);
                                    indexes.add(Joiner.on("-").join(designator, getService(), YYYY_MM_DD_HH.format(hours)));
                                }
                            }

                            List<String> success = new LinkedList<>();
                            for (String index : indexes) {
                                Request.Builder reqb = new Request.Builder();
                                reqb.url(baseUrl().addPathSegment(index).build());
                                reqb.delete();
                                addAuthInfo(reqb);
                                Request req = reqb.build();

                                try (Response response = httpClient.newCall(req).execute()) {
                                    try {
                                        if (response.isSuccessful()) {
                                            addInfo(String.format("deleted index at: %s", index));
                                            success.add(index);
                                        }
                                    } finally {
                                        corr.invalidate(response);
                                    }
                                } catch (Exception err) {
                                    if (reportToSentry()) {
                                        sendSentryAlert(op, err);
                                    }
                                    addError(op, err);
                                }
                            }

                            if (success.isEmpty()) {
                                // ~ maybe add warning
                            } else {
                                addInfo("successfully deleted: " + success);
                            }
                        }
                    }
                }
            }, delay, interval);
        }
    }
    @Override
    public void stop() {
        super.stop();

        //
        // ~ cancel timer (remove auto-housekeeping thread gracefully)
        //
        timer.cancel();

        //
        // ~ close OK client (not necessary, but nice to have)
        //
        if (Objects.nonNull(httpClient)) {
            try (ExecutorService executorService = httpClient.dispatcher().executorService()) {
                httpClient.connectionPool().evictAll();
                if (Objects.nonNull(httpClient.cache())) {
                    try (okhttp3.Cache cache = httpClient.cache()) {

                    } catch (IOException err) {
                        addError("unable to close OK cache", err);
                    }
                }
            }
        }
    }
    @Override
    protected boolean dryRun() {
        return loggingDryRun();
    }
    @Override
    protected void sendBulk(List<ILoggingEvent> list) {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        String pattern = DateTimeFormatter.ofPattern(hourly ? "yyyy-MM-dd-HH" : "yyyy-MM-dd").format(now);
        String op = "send_bulk_logs";
        int size = list.size();

        try {
            Request.Builder reqb = new Request.Builder();
            reqb.url(baseUrl().addPathSegment(String.format("%s-%s-%s", designator, getService(), pattern)).addPathSegment("_bulk").build());
            reqb.post(new RequestBody() {
                @Override
                public MediaType contentType() {
                    return X_NDJSON;
                }
                @Override
                public void writeTo(BufferedSink sink) throws IOException {
                    //
                    // ~ write directly to sink's output stream (non-trace)
                    //
                    try (OutputStreamWriter out = new OutputStreamWriter(sink.outputStream())) {
                        try (BufferedWriter writer = new BufferedWriter(out)) {
                            try (JsonGenerator json = jf.createGenerator(writer)) {
                                json.setRootValueSeparator(null); // ~ no spaces in between

                                //
                                // ~ write the document and immediately evict it from list (old school iterator loop)
                                //
                                for (Iterator<ILoggingEvent> it = list.iterator(); it.hasNext();) {
                                    var next = it.next();
                                    try {
                                        json.writeStartObject();
                                        json.writeFieldName("index");

                                        //
                                        // ~ generate unique ID
                                        //
                                        Hasher hasher = Hashing.farmHashFingerprint64().newHasher();
                                        hasher.putLong(next.getTimeStamp());
                                        hasher.putLong(next.getSequenceNumber());
                                        hasher.putInt(next.hashCode());
                                        hasher.putString(getSlot(), StandardCharsets.UTF_8);
                                        if (Objects.nonNull(next.getLevel())) {
                                            hasher.putString(next.getLevel().toString(), StandardCharsets.UTF_8);
                                        }
                                        if (StringUtils.isNotEmpty(next.getThreadName())) {
                                            hasher.putString(next.getThreadName(), StandardCharsets.UTF_8);
                                        }
                                        if (StringUtils.isNotEmpty(next.getLoggerName())) {
                                            hasher.putString(next.getLoggerName(), StandardCharsets.UTF_8);
                                        }
                                        HashCode hashCode = hasher.hash();

                                        //
                                        // ~ write unique ID
                                        //
                                        json.writeStartObject();
                                        json.writeStringField("_id", hashCode.toString());
                                        json.writeEndObject();

                                        json.writeEndObject();
                                        json.flush();
                                    } finally {
                                        writer.newLine();
                                    }

                                    //
                                    // ~ and actual body
                                    //
                                    try {
                                        writeBody(json, next);
                                    } finally {
                                        writer.newLine();
                                    }

                                    it.remove(); // ~ GC
                                }
                            }
                        }
                    } catch (IOException err) {
                        addError(err.getMessage(), err);
                        throw err;
                    } catch (Exception err) {
                        addError(err.getMessage(), err);
                        throw new IOException(err);
                    }
                }
            });
            addAuthInfo(reqb);
            Request req = reqb.build();

            try (Response response = httpClient.newCall(req).execute()) {
                try {
                    if (response.isSuccessful()) {
                        //
                        // ~ NO-OP
                        //
                    } else {
                        sendUnexpectedHttpStatus(op, req, response, new Consumer<EventBuilder>() {
                            @Override
                            public void accept(EventBuilder event) {
                                event.withTag("batch-size", Integer.toString(size));
                            }
                        });
                    }
                } finally {
                    corr.invalidate(response);
                }
            }
        } catch (InterruptedIOException err) {
            addInfo("got interrupted while sending data", err);
        } catch (Exception err) {
            if (reportToSentry()) {
                sendSentryAlert(op, err);
            }
            addError(op, err);
        } finally {
            corr.cleanUp();
        }
    }
    private void registerTemplate() {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        String op = "register_logging_template";
        URL resource = classLoader.getResource(template);

        if (Objects.isNull(resource)) {
            resource = getClass().getClassLoader().getResource(template);
        }
        if (Objects.isNull(resource)) {
            resource = ClassLoader.getSystemResource(template);
        }

        //
        // ~ should not happen but just in case
        //
        if (Objects.nonNull(resource)) {
            try (InputStream io = resource.openStream()) {
                String raw = IOUtils.toString(io, StandardCharsets.UTF_8);

                StringSubstitutor str = new StringSubstitutor();
                str.setEnableUndefinedVariableException(true); // ~ early prevention of undefined variables
                String body = str.replace(raw);

                Request.Builder reqb = new Request.Builder();
                reqb.url(baseUrl().addPathSegment("_template").addPathSegment("logging-" + getService()).build());
                reqb.put(RequestBody.create(body, X_JSON));
                addAuthInfo(reqb);
                Request req = reqb.build();

                try (Response response = httpClient.newCall(req).execute()) {
                    try {
                        if (response.isSuccessful()) {
                            addInfo(String.format("successfully registered logging template from %s", resource.toExternalForm()));
                        } else {
                            sendUnexpectedHttpStatus(op, req, response, new Consumer<EventBuilder>() {
                                @Override
                                public void accept(EventBuilder event) {
                                    event.withTag("template", template);
                                }
                            });
                        }
                    } finally {
                        corr.invalidate(response);
                    }
                }
            } catch (Exception err) {
                if (reportToSentry()) {
                    sendSentryAlert(op, err);
                }
                addError(op, err);
            }
        } else {
            addError("unable to resolve template: " + template);
        }
    }
    private void writeBody(JsonGenerator json, ILoggingEvent data) throws IOException {
        String timestamp = DateTimeFormatter.ISO_INSTANT.format(Instant.ofEpochMilli(data.getTimeStamp()).atZone(ZoneOffset.UTC));

        try {
            json.writeStartObject();

            //
            // ~ system
            //
            json.writeStringField(TIMESTAMP, timestamp);
            json.writeNumberField(SEQUENCE, data.getSequenceNumber());
            json.writeStringField(Logback.HOST, getHost());
            json.writeStringField(Logback.SLOT, getSlot());
            json.writeStringField(Logback.RELEASE, getRelease());

            //
            // ~ write all formatted values
            //
            for (DocumentProperty field : properties.getProperties()) {
                String formatted = field.format(data);
                if (StringUtils.isNotEmpty(formatted)) {
                    json.writeStringField(field.getName(), formatted);
                }
            }

            //
            // ~ write MDC values
            //
            Map<String, String> mdc = data.getMDCPropertyMap();
            if (Objects.nonNull(mdc)) {
                for (Entry<String, String> entry : mdc.entrySet()) {
                    boolean toInclude = true;
                    if (BooleanUtils.isFalse(mdcNames.isEmpty())) {
                        toInclude = mdcNames.contains(entry.getKey());
                    }
                    if (toInclude) {
                        if (StringUtils.isNotEmpty(entry.getValue())) {
                            json.writeStringField(entry.getKey(), entry.getValue());
                        }
                    }
                }
            }

            json.writeEndObject();
        } finally {
            json.flush();
        }
    }
    private void sendUnexpectedHttpStatus(String operation, Request req, Response response, Consumer<EventBuilder> customizer) {
        long now = System.currentTimeMillis();
        long prev = lastSentryAlert.get();
        long period = now - prev;

        if (BooleanUtils.isFalse(alertsDryRun())) {
            if (Objects.nonNull(getSentry())) {
                if (period >= TimeUnit.MINUTES.toMillis(sentryRateLimit)) {
                    EventBuilder builder = new EventBuilder();
                    builder.withTimestamp(new Date());
                    builder.withMessage(operation);
                    builder.withLogger(getClass().getName());
                    builder.withLevel(Level.ERROR);

                    try {
                        if (Objects.nonNull(response.body())) {
                            MessageInterface intf = new MessageInterface(response.body().string());
                            builder.withSentryInterface(intf);
                            builder.withTag("content-type", req.body().contentType().toString());
                            builder.withTag("method", req.method());
                            builder.withTag("url", req.url().toString());
                            builder.withTag("status", Integer.toString(response.code()));
                            customizer.accept(builder);
                        }
                    } catch (IOException err) {
                        addError(err.getMessage(), err);
                    } finally {
                        lastSentryAlert.compareAndSet(prev, now);
                    }

                    getSentry().sendEvent(builder);

                    List<String> debug = corr.asMap().remove(response);
                    if (Objects.nonNull(debug)) {
                        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
                            try (PrintWriter writer = new PrintWriter(stream)) {
                                for (String it : debug) {
                                    writer.println(it);
                                }
                            }
                            stream.flush();
                            addError(stream.toString(StandardCharsets.UTF_8));
                        } catch (IOException err) {
                            addError(err.getMessage(), err);
                        }
                    }
                }
            }
        }
    }
    private void addAuthInfo(Request.Builder req) {
        String userInfo = getElasticEndpoint().getUserInfo();
        if (userInfo != null && !userInfo.isEmpty()) {
            String auth = Base64.getEncoder().encodeToString(userInfo.getBytes());
            req.addHeader("Authorization", "Basic " + auth);
        }
    }
    private HttpUrl.Builder baseUrl() {
        HttpUrl.Builder url = new HttpUrl.Builder();
        url.scheme(getElasticEndpoint().getProtocol());
        url.host(getElasticEndpoint().getHost());
        url.port(getElasticEndpoint().getPort());
        if (StringUtils.isNotEmpty(getElasticEndpoint().getPath())) {
            url.addPathSegment(getElasticEndpoint().getPath());
        }
        return url;
    }
}
