package com.turbospaces.logging;

import java.util.Collection;
import java.util.Optional;

import org.slf4j.helpers.MessageFormatter;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.EventArgUtil;

public class LoggingEventFilter {
    private final Collection<String> ignoreMessages;
    private final Collection<String> ignoreLoggers;

    public LoggingEventFilter(Collection<String> ignoreMessages, Collection<String> ignoreLoggers) {
        this.ignoreMessages = ignoreMessages;
        this.ignoreLoggers = ignoreLoggers;
    }
    public boolean test(String loggerName, Level level, String format, Object[] params, Throwable error) {
        return level.toInt() == Level.ERROR_INT
                && !ignoreByExceptionMessage(params, error)
                && !ignoreByLogMessage(format, params)
                && !ignoreByLogger(loggerName);
    }

    private boolean ignoreByLogger(String loggerName) {
        return Optional.ofNullable(ignoreLoggers).map(l -> l.contains(loggerName)).orElse(false);
    }

    private boolean ignoreByLogMessage(String format, Object[] params) {
        if (format == null) {
            return false;
        }
        if (ignoreMessages == null || ignoreMessages.isEmpty()) {
            return false;
        }
        String formattedMessage = MessageFormatter.arrayFormat(format, params).getMessage();
        for (String logMessageToIgnore : ignoreMessages) {
            if (formattedMessage.contains(logMessageToIgnore)) {
                return true;
            }
        }
        return false;
    }

    private boolean ignoreByExceptionMessage(Object[] params, Throwable t) {
        Throwable throwable = t;
        if (throwable == null) {
            throwable = EventArgUtil.extractThrowable(params);
            if (throwable == null) {
                return false;
            }
        }
        if (ignoreMessages.isEmpty()) {
            return false;
        }
        return containsMessage(throwable);
    }

    private boolean containsMessage(Throwable exc) {
        return (exc != null && containsMessage(exc.getMessage())) || (exc != null && containsMessage(exc.getCause()));
    }

    private boolean containsMessage(String message) {
        if (message == null) {
            return false;
        }
        return ignoreMessages.stream().anyMatch(message::contains);
    }
}
