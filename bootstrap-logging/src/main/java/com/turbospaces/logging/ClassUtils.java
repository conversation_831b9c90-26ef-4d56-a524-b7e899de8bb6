package com.turbospaces.logging;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
@SuppressWarnings({ "unchecked", "rawtypes" })
public class ClassUtils {
    public static Optional<Class<? extends Throwable>> parseThrowableClass(String classStr) {
        try {
            Class clazz = Class.forName(classStr.trim());
            if (Throwable.class.isAssignableFrom(clazz)) {
                return Optional.of((Class<? extends Throwable>) clazz);
            }
            log.error("Class is not Throwable: {}", classStr);
        } catch (Exception ex) {
            log.error("Class can't be parsed: {}", classStr, ex);
        }
        return Optional.empty();
    }
    public static List<Class<? extends Throwable>> parseThrowableClasses(String classesStr, String separator) {
        List<Class<? extends Throwable>> exceptions = new ArrayList<>();
        if (!classesStr.trim().isEmpty()) {
            for (String exClass : classesStr.split(separator)) {
                ClassUtils.parseThrowableClass(exClass).ifPresent(exceptions::add);
            }
        }
        return exceptions;
    }
}
