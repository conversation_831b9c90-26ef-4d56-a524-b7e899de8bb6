package com.turbospaces.spanner;

import java.util.function.Supplier;

import org.springframework.context.annotation.Bean;

import com.google.cloud.spanner.DatabaseClient;
import com.google.cloud.spanner.DatabaseId;
import com.google.cloud.spanner.Spanner;
import com.google.cloud.spanner.SpannerOptions;
import com.google.cloud.spring.data.spanner.core.SpannerMutationFactory;
import com.google.cloud.spring.data.spanner.core.SpannerMutationFactoryImpl;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.cloud.spring.data.spanner.core.admin.SpannerSchemaUtils;
import com.google.cloud.spring.data.spanner.core.convert.ConverterAwareMappingSpannerEntityProcessor;
import com.google.cloud.spring.data.spanner.core.convert.SpannerEntityProcessor;
import com.google.cloud.spring.data.spanner.core.mapping.SpannerMappingContext;
import com.turbospaces.cfg.ApplicationProperties;

public abstract class AbstractSpannerDiModule {
    @Bean
    public DatabaseClient spannerClient(ApplicationProperties props, SpannerClientFactoryBean factoryBean) throws Exception {
        Spanner spanner = factoryBean.getObject();
        SpannerOptions options = spanner.getOptions();
        DatabaseId db = DatabaseId.of(
                options.getProjectId(),
                props.SPANNER_INSTANCE_NAME.get(),
                props.SPANNER_DATABASE_NAME.get());
        return spanner.getDatabaseClient(db);
    }
    @Bean
    public SpannerMappingContext spannerMappingContext() {
        return new SpannerMappingContext();
    }
    @Bean
    public SpannerSchemaUtils spannerSchemaUtils(SpannerMappingContext mappingContext, SpannerEntityProcessor entityProcessor) {
        return new SpannerSchemaUtils(mappingContext, entityProcessor, true);
    }
    @Bean
    public SpannerEntityProcessor spannerEntityProcessor(SpannerMappingContext mappingContext) {
        return new ConverterAwareMappingSpannerEntityProcessor(mappingContext);
    }
    @Bean
    public SpannerMutationFactory spannerMutationFactory(
            SpannerMappingContext mappingContext,
            SpannerEntityProcessor entityProcessor,
            SpannerSchemaUtils schemaUtils) {
        return new SpannerMutationFactoryImpl(entityProcessor, mappingContext, schemaUtils);
    }
    @Bean
    public SpannerTemplate spannerTemplate(
            DatabaseClient databaseClient,
            SpannerClientFactoryBean clientFactoryBean,
            SpannerMappingContext mappingContext,
            SpannerEntityProcessor entityProcessor,
            SpannerMutationFactory mutationFactory,
            SpannerSchemaUtils schemaUtils) {
        return new SpannerTemplate(new Supplier<DatabaseClient>() {
            @Override
            public DatabaseClient get() {
                return databaseClient;
            }
        }, mappingContext, entityProcessor, mutationFactory, schemaUtils);
    }
}
