package com.turbospaces.spanner;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;

@Configuration
public class SpannerDiModule extends AbstractSpannerDiModule {
    @Bean
    public SpannerClientFactoryBean spannerClientFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry) {
        return new SpannerClientFactoryBean(props, meterRegistry);
    }
}
