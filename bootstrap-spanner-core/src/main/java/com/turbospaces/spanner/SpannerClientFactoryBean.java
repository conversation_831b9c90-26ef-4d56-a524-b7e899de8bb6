package com.turbospaces.spanner;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;
import org.threeten.bp.Duration;

import com.google.api.core.ApiFunction;
import com.google.cloud.NoCredentials;
import com.google.cloud.spanner.SessionPoolOptions;
import com.google.cloud.spanner.Spanner;
import com.google.cloud.spanner.SpannerOptions;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;

import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.NettyChannelBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SpannerClientFactoryBean extends AbstractFactoryBean<Spanner> implements BeanNameAware {
    protected final ApplicationProperties props;
    protected final PlatformExecutorService executor;

    public SpannerClientFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.props = props;
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }
    @Override
    public Class<?> getObjectType() {
        return Spanner.class;
    }
    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
        super.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
        } finally {
            executor.destroy();
        }
    }
    @Override
    protected Spanner createInstance() throws Exception {
        var options = defaultOptions();

        if (props.isDevMode()) {
            if (StringUtils.isNotEmpty(props.SPANNER_PROJECT_ID.get())) {
                options.setProjectId(props.SPANNER_PROJECT_ID.get());
            }
            if (props.SPANNER_DATABASE_PORT.get() != null) {
                options.setCredentials(NoCredentials.getInstance());
                options.setEmulatorHost("localhost:" + props.SPANNER_DATABASE_PORT.get());
            }
        }

        return options.build().getService();
    }
    @Override
    protected void destroyInstance(Spanner instance) throws Exception {
        if (Objects.nonNull(instance)) {
            StopWatch stopWatch = StopWatch.createStarted();
            log.info("about to close spanner({}) now ...", instance.getOptions().getApplicationName());
            instance.close();
            stopWatch.stop();
            log.info("closed spanner({}) in {}", instance.getOptions().getApplicationName(), stopWatch);
        }
    }
    protected SpannerOptions.Builder defaultOptions() {
        var options = SpannerOptions.newBuilder();

        //
        // ~ well unstable functionality in PROD it seems, but we keep it locally to enforce dependency rules
        //
        if (props.isDevMode()) {
            var cores = Runtime.getRuntime().availableProcessors();
            var pool = SessionPoolOptions.newBuilder().setMinSessions(cores).setRemoveInactiveSessionAfter(Duration.ofMinutes(1)).build();
            options = options
                    .enableGrpcGcpExtension()
                    .setSessionPoolOption(pool)
                    .setChannelConfigurator(new ApiFunction<>() {
                        @Override
                        @SuppressWarnings("rawtypes")
                        public NettyChannelBuilder apply(ManagedChannelBuilder input) {
                            return (NettyChannelBuilder) input.executor(executor);
                        }
                    });
        }

        return options;
    }
}
