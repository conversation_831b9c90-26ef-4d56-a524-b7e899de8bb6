package com.turbospaces.plugins;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.Location;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.flywaydb.core.api.output.MigrateResult;
import org.springframework.boot.BootstrapContextClosedEvent;
import org.springframework.boot.BootstrapRegistry;
import org.springframework.boot.BootstrapRegistry.InstanceSupplier;
import org.springframework.boot.BootstrapRegistryInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;

import com.google.cloud.spanner.jdbc.JdbcDataSource;
import com.turbospaces.cfg.ApplicationProperties;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FlywaySpannerBootstrapInitializer implements BootstrapRegistryInitializer, Ordered {
    private final ApplicationProperties props;
    private final String jdbcUrl;

    public FlywaySpannerBootstrapInitializer(ApplicationProperties props, String jdbcUrl) {
        this.props = Objects.requireNonNull(props);
        this.jdbcUrl = Objects.requireNonNull(jdbcUrl);
    }
    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE;
    }
    @Override
    public void initialize(BootstrapRegistry registry) {
        if (props.APP_DB_MIGRATION_ENABLED.get()) {
            MigrateResult result = migrate();
            registry.register(MigrateResult.class, InstanceSupplier.of(result));
            registry.addCloseListener(new ApplicationListener<BootstrapContextClosedEvent>() {
                @Override
                public void onApplicationEvent(BootstrapContextClosedEvent event) {
                    ConfigurableApplicationContext context = event.getApplicationContext();
                    context.getBeanFactory().registerSingleton("flyway-migration-result", result);
                }
            });
        }
    }
    protected void configureMigration(FluentConfiguration config) {
        Location location = new Location("classpath" + ":" + props.APP_DB_MIGRATION_PATH.get());
        config.locations(location);
    }
    public MigrateResult migrate() {
        boolean baseline = props.APP_DB_MIGRATION_BASELINE.get();
        String baselineVersion = props.APP_DB_MIGRATION_BASELINE_VERSION.get();

        JdbcDataSource ds = new JdbcDataSource();
        ds.setUrl(jdbcUrl);

        FluentConfiguration fluent = Flyway.configure();
        fluent.outOfOrder(true);
        fluent.dataSource(ds);
        fluent.baselineOnMigrate(baseline);
        if (StringUtils.isNotEmpty(baselineVersion)) {
            fluent.baselineVersion(baselineVersion);
        }

        configureMigration(fluent);

        Flyway flyway = fluent.load();
        MigrateResult result = flyway.migrate();
        log.debug("successfully applied {} migrations ...", ReflectionToStringBuilder.toString(result, ToStringStyle.SHORT_PREFIX_STYLE));

        return result;
    }
}
