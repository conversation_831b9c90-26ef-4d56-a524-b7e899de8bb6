package api.v1;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.URI;
import java.util.Arrays;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.RepeatedTest;

import io.cloudevents.CloudEventData;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.netty.util.AsciiString;

class CloudEventWithRoutingKeyTest {

    @RepeatedTest(1)
    public void works() throws IOException, ClassNotFoundException {
        long now = System.currentTimeMillis();
        byte[] data = new byte[Short.MAX_VALUE];
        ApiFactory.RANDOM.nextBytes(data);

        var routingKey = AsciiString.cached(getClass().getSimpleName());
        var messageId = ApiFactory.UUID.toString();
        var type = "undefined";
        var eventTemplate = CloudEventBuilder.v1().withSource(URI.create("http://my.domain"));
        var event = eventTemplate.newBuilder()
                .withId(messageId)
                .withType(type)
                .withData(new CloudEventData() {
                    @Override
                    public byte[] toBytes() {
                        return data;
                    }
                });

        CloudEventWithRoutingKey withRoutingKey = new CloudEventWithRoutingKey(now, routingKey, event.build());
        byte[] serialized;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            try (ObjectOutputStream oos = new ObjectOutputStream(out)) {
                withRoutingKey.writeExternal(oos);
                oos.flush();
                serialized = out.toByteArray();
            }
        }

        try (ByteArrayInputStream io = new ByteArrayInputStream(serialized)) {
            try (ObjectInputStream inputStream = new ObjectInputStream(io)) {
                CloudEventWithRoutingKey clone = new CloudEventWithRoutingKey();
                clone.readExternal(inputStream);

                Assertions.assertEquals(routingKey.toString(), clone.getKey().toString());
                Assertions.assertEquals(now, clone.getTimestamp());
                Assertions.assertEquals(messageId, clone.getEvent().getId());
                Assertions.assertEquals(type, clone.getEvent().getType());
                Assertions.assertEquals(data.length, clone.getEvent().getData().toBytes().length);

                Assertions.assertTrue(Arrays.equals(data, clone.getEvent().getData().toBytes()));
            }
        }
    }
}
