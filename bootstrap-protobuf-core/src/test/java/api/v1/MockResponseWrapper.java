package api.v1;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.google.protobuf.Any;

import api.v1.MockResponseStatusFacade.Code;
import api.v1.MockResponseStatusFacade.Reason;
import io.cloudevents.CloudEventData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
public class MockResponseWrapper implements CloudEventData {
    private api.v1.Headers headers;
    private Any body;
    private Code errorCode = Code.ERR_OK;
    private Reason errorReason = Reason.UNSPECIFIED;
    private String errorText = StringUtils.EMPTY;
    private api.v1.CacheControl cacheControl = api.v1.CacheControl.getDefaultInstance();

    public MockResponseWrapper(Headers headers, Any body) {
        this.headers = Objects.requireNonNull(headers);
        this.body = Objects.requireNonNull(body);
    }
    @Override
    public byte[] toBytes() {
        try (ByteArrayOutputStream io = new ByteArrayOutputStream()) {
            // headers
            byte[] bytes = headers.toByteArray();
            io.write(bytes.length);
            io.write(bytes);

            // body
            bytes = body.toByteArray();
            io.write(bytes.length);
            io.write(bytes);

            // code
            io.write(errorCode.getNumber());

            // reason
            io.write(errorReason.getNumber());

            // text
            bytes = errorText.getBytes();
            io.write(bytes.length);
            io.write(bytes);

            // body
            bytes = cacheControl.toByteArray();
            io.write(bytes.length);
            io.write(bytes);

            io.flush();
            return io.toByteArray();
        } catch (IOException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
    public static MockResponseWrapper mergeFrom(InputStream io) throws IOException {
        MockResponseWrapper wrapper = new MockResponseWrapper();

        // headers
        byte[] bytes = new byte[io.read()];
        io.read(bytes);
        wrapper.headers = api.v1.Headers.newBuilder().mergeFrom(bytes).build();

        // body
        bytes = new byte[io.read()];
        io.read(bytes);
        wrapper.body = Any.newBuilder().mergeFrom(bytes).build();

        // code
        wrapper.errorCode = Code.forNumber(io.read());

        // reason
        wrapper.errorReason = Reason.forNumber(io.read());

        // text
        bytes = new byte[io.read()];
        io.read(bytes);
        wrapper.errorText = new String(bytes);

        //
        bytes = new byte[io.read()];
        io.read(bytes);
        wrapper.cacheControl = api.v1.CacheControl.newBuilder().mergeFrom(bytes).build();
        return wrapper;
    }
}
