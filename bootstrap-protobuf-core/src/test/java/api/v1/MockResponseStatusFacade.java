package api.v1;

import java.util.Map;
import java.util.Objects;

import com.google.protobuf.Internal.EnumLite;
import com.turbospaces.api.facade.ResponseStatusFacade;

public class MockResponseStatusFacade implements ResponseStatusFacade {
    private final Code code;
    private final Reason reason;
    private String text;

    public MockResponseStatusFacade(Code code, Reason reason, String text) {
        this.code = Objects.requireNonNull(code);
        this.reason = Objects.requireNonNull(reason);
        this.text = Objects.requireNonNull(text);
    }
    public MockResponseStatusFacade(Code code, String text) {
        this.code = Objects.requireNonNull(code);
        this.reason = Reason.UNSPECIFIED;
        this.text = Objects.requireNonNull(text);
    }
    public MockResponseStatusFacade(Code code) {
        this.code = Objects.requireNonNull(code);
        this.reason = Reason.UNSPECIFIED;
    }
    @Override
    public boolean isOK() {
        return code.equals(Code.ERR_OK);
    }
    @Override
    public boolean isAuth() {
        return code.equals(Code.ERR_AUTH);
    }
    @Override
    public boolean isSystem() {
        return code.equals(Code.ERR_SYSTEM);
    }
    @Override
    public boolean isTimeout() {
        return code.equals(Code.ERR_TIMEOUT);
    }
    @Override
    public boolean isDuplicate() {
        return code.equals(Code.ERR_DUPLICATE);
    }
    @Override
    public boolean isNotFound() {
        return code.equals(Code.ERR_NOT_FOUND);
    }
    @Override
    public boolean isBadRequest() {
        return code.equals(Code.ERR_BAD_REQUEST);
    }
    @Override
    public boolean isDenied() {
        return code.equals(Code.ERR_DENIED);
    }
    @Override
    public boolean isInsufficientFunds() {
        return code.equals(Code.ERR_INSUFFICIENT_FUNDS);
    }
    @Override
    public boolean isFrozen() {
        return code.equals(Code.ERR_FROZEN);
    }
    @Override
    public boolean isNoContent() {
        return code.equals(Code.ERR_NOT_CONTENT);
    }
    @Override
    public String errorText() {
        return text;
    }
    @Override
    public EnumLite errorCode() {
        return code;
    }
    @Override
    public EnumLite errorReason() {
        return reason;
    }
    @Override
    public Map<String, String> errorDetails() {
        return Map.of();
    }
    @Override
    public ApplicationException toException() {
        return EnhancedApplicationException.of(errorText(), errorCode(), errorReason());
    }

    public enum Code implements EnumLite {
        ERR_OK,
        ERR_AUTH,
        ERR_SYSTEM,
        ERR_TIMEOUT,
        ERR_DUPLICATE,
        ERR_NOT_FOUND,
        ERR_BAD_REQUEST,
        ERR_DENIED,
        ERR_INSUFFICIENT_FUNDS,
        ERR_FROZEN,
        ERR_NOT_CONTENT,
        ERR_OTP_REQUIRED;

        @Override
        public int getNumber() {
            return ordinal();
        }
        public static Code forNumber(int number) {
            return Code.values()[number];
        }
    }

    public enum Reason implements EnumLite {
        UNSPECIFIED,
        SPECIFIED;

        @Override
        public int getNumber() {
            return ordinal();
        }
        public static Reason forNumber(int number) {
            return Reason.values()[number];
        }
    }
}
