package api.v1;

import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.TimeoutException;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.MockResponseWrapperFacade;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.api.mappers.DefaultRequestFacadeMapper;
import com.turbospaces.api.mappers.RequestFacadeMapper;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.WorkUnit;

import api.v1.MockResponseStatusFacade.Code;
import api.v1.MockResponseStatusFacade.Reason;

public class MockApiFactory extends AbstractApiFactory {
    public MockApiFactory(ApplicationProperties props, ObjectMapper mapper) {
        super(props, mapper);
    }
    @Override
    public RequestFacadeMapper requestMapper() {
        return new DefaultRequestFacadeMapper(eventTemplate());
    }
    @Override
    public ResponseFacadeMapper responseMapper() {
        return new ResponseFacadeMapper() {
            @Override
            public ResponseWrapperFacade unpack(WorkUnit workUnit) throws Exception {
                try (InputStream io = workUnit.value().openStream()) {
                    MockResponseWrapper rw = MockResponseWrapper.mergeFrom(io);
                    return new MockResponseWrapperFacade(eventTemplate(), rw);
                }
            }
            @Override
            public ResponseWrapperFacade toReply(RequestWrapperFacade reqw, Message message, api.v1.CacheControl cacheControl) {
                MockResponseWrapper wrapperb = new MockResponseWrapper();
                wrapperb.setHeaders(reqw.headers());
                wrapperb.setBody(Any.pack(message));
                wrapperb.setCacheControl(cacheControl);
                return new MockResponseWrapperFacade(eventTemplate(), wrapperb);
            }
            @Override
            public ResponseWrapperFacade toReply(RequestWrapperFacade reqw, Message message, ResponseStatusFacade status) {
                MockResponseWrapper wrapperb = new MockResponseWrapper();
                wrapperb.setHeaders(reqw.headers());
                wrapperb.setBody(Any.pack(message));
                wrapperb.setErrorCode(MockResponseStatusFacade.Code.forNumber(status.errorCode().getNumber()));
                wrapperb.setErrorReason(MockResponseStatusFacade.Reason.forNumber(status.errorReason().getNumber()));
                wrapperb.setErrorText(status.errorText());
                return new MockResponseWrapperFacade(eventTemplate(), wrapperb);
            }
        };
    }
    @Override
    public ResponseStatusFacade status(String code, int reason, String error, Map<String, String> details) {
        return new MockResponseStatusFacade(Code.valueOf(code.toUpperCase()), Reason.forNumber(reason), error);
    }
    @Override
    public ResponseStatusFacade toExceptional(String messageId, Throwable cause) {
        Code errorCode = Code.ERR_SYSTEM;
        Reason errorReason = Reason.UNSPECIFIED;
        String errorText = String.format(ReplyUtil.ERROR_FORMAT, ApiFactory.UUID.generate());

        if (cause instanceof ApplicationException err) {
            errorCode = MockResponseStatusFacade.Code.forNumber(err.getCode().getNumber());
            if (StringUtils.isNotEmpty(cause.getMessage())) {
                errorText = cause.getMessage();
            }
            if (err instanceof EnhancedApplicationException enhancedApp) {
                errorReason = Reason.values()[enhancedApp.getReason().getNumber()];
            }
        } else if (cause instanceof TimeoutException) {
            errorCode = Code.ERR_TIMEOUT;
        } else {
            errorText = String.format(ReplyUtil.ERROR_FORMAT, messageId);
        }

        return new MockResponseStatusFacade(errorCode, errorReason, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalAuthReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_AUTH, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalNotFoundReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_NOT_FOUND, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalBadRequestReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_BAD_REQUEST, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalTimeoutReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_TIMEOUT, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalSystemReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_SYSTEM, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalDeniedReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_DENIED, errorText);
    }
    @Override
    public ResponseStatusFacade toExceptionalOtpRequiredReply(String errorText) {
        return new MockResponseStatusFacade(Code.ERR_OTP_REQUIRED, errorText);
    }
}
