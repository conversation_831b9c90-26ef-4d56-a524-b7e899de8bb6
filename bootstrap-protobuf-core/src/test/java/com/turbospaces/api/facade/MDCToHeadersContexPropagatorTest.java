package com.turbospaces.api.facade;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import com.turbospaces.mdc.MdcTags;

import api.v1.ApiFactory;

class MDCToHeadersContexPropagatorTest {
    @Test
    void works() {
        MDCToHeadersContexPropagator propagator = new MDCToHeadersContexPropagator();
        String traceId = ApiFactory.UUID.generate().toString();
        String messageId = ApiFactory.UUID.generate().toString();

        api.v1.Headers.Builder headers = api.v1.Headers.newBuilder();

        try {
            MDC.put(MdcTags.MDC_TRACE_ID, traceId);
            MDC.put(MdcTags.MDC_MESSAGE_ID, messageId);
            MDC.put(MdcTags.MDC_PATH, "/v1");

            propagator.injectContext(headers);
            Assertions.assertEquals(traceId, headers.getTraceId());
            Assertions.assertTrue(StringUtils.isEmpty(headers.getMessageId()));
        } finally {
            MDC.clear();
        }
    }
}
