package com.turbospaces.rpc;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import io.vavr.CheckedConsumer;

public interface ApiResponse<RESP extends Message> extends ListenableFuture<ApiResponseEntity<RESP>> {
    @Override
    ApiResponseEntity<RESP> get() throws InterruptedException, ExecutionException;
    @Override
    ApiResponseEntity<RESP> get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException;
    @Override
    void addListener(Runnable listener, Executor executor);

    api.v1.Headers headers();
    Any request();
    RequestWrapperFacade wrapper();

    ResponseWrapperFacade toReply(Message message, ResponseStatusFacade status);
    ResponseWrapperFacade toExceptionalReply(Message message, Exception exception);

    void addCallback(FutureCallback<ApiResponseEntity<RESP>> callback, Executor executor);
    ApiResponse<RESP> thenVerifyOk(Executor executor);
    ApiResponse<RESP> thenVerifyOkAndAccept(CheckedConsumer<RESP> callback, Executor executor);

    default void addListener(Runnable listener) {
        addListener(listener, MoreExecutors.directExecutor());
    }
    default void addCallback(FutureCallback<ApiResponseEntity<RESP>> callback) {
        addCallback(callback, MoreExecutors.directExecutor());
    }
    default ApiResponse<RESP> thenVerifyOk() {
        return thenVerifyOk(MoreExecutors.directExecutor());
    }
    default ApiResponse<RESP> thenVerifyOkAndAccept(CheckedConsumer<RESP> callback) {
        return thenVerifyOkAndAccept(callback, MoreExecutors.directExecutor());
    }
}
