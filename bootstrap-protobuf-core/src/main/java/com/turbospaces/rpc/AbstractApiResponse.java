package com.turbospaces.rpc;

import java.util.Objects;
import java.util.concurrent.Executor;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import api.v1.ApiFactory;
import io.vavr.CheckedConsumer;

public abstract class AbstractApiResponse<RESP extends Message> implements ApiResponse<RESP> {
    protected final ApiFactory apiFactory;
    protected final RequestWrapperFacade requestWrapper;

    protected AbstractApiResponse(ApiFactory apiFactory, RequestWrapperFacade requestWrapper) {
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.requestWrapper = Objects.requireNonNull(requestWrapper);
    }
    @Override
    public Any request() {
        return requestWrapper.body();
    }
    @Override
    public api.v1.Headers headers() {
        return requestWrapper.headers();
    }
    @Override
    public RequestWrapperFacade wrapper() {
        return requestWrapper;
    }
    @Override
    public ResponseWrapperFacade toReply(Message message, ResponseStatusFacade status) {
        return apiFactory.responseMapper().toReply(requestWrapper, message, status);
    }
    @Override
    public ResponseWrapperFacade toExceptionalReply(Message message, Exception exception) {
        var fault = apiFactory.toExceptional(requestWrapper.headers().getMessageId(), exception);
        return apiFactory.responseMapper().toReply(requestWrapper, message, fault);
    }
    @Override
    public ApiResponse<RESP> thenVerifyOk(Executor executor) {
        return thenVerifyOkAndAccept(new CheckedConsumer<RESP>() {
            @Override
            public void accept(RESP t) {

            }
        }, executor);
    }
}
