package com.turbospaces.rpc;

import java.io.IOException;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.ResponseStatusFacade;

import api.v1.ReplyUtil;

public interface ApiResponseEntity<T extends Message> {
    api.v1.Headers headers();
    ResponseStatusFacade status();
    Any any();
    T unpack() throws IOException;
    api.v1.CacheControl cacheControl();

    default void verifyOk() throws Exception {
        ReplyUtil.verifyOk(status());
    }
    default T unpackAndVerifyOk() throws Exception {
        verifyOk();
        return unpack();
    }
}
