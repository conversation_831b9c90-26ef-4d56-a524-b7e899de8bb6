package com.turbospaces.rpc;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

public interface WrappedQueuePost extends ListenableFuture<ResponseWrapperFacade> {
    RequestWrapperFacade requestWrapper();

    @Override
    ResponseWrapperFacade get() throws InterruptedException, ExecutionException;
    @Override
    ResponseWrapperFacade get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException;
    @Override
    void addListener(Runnable listener, Executor executor);

    void addCallback(FutureCallback<ResponseWrapperFacade> callback, Executor executor);
}
