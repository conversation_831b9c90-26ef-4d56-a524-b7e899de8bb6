package com.turbospaces.dispatch;

import java.util.Objects;

import com.google.common.util.concurrent.ListenableFuture;
import com.turbospaces.executor.WorkUnit;

import lombok.experimental.Delegate;

public class WorkerCompletableTask implements ListenableFuture<TransactionalRequestOutcome> {
    @Delegate
    private final ListenableFuture<TransactionalRequestOutcome> delegate;

    public final String messageId;
    public final String topic;
    public final long timestamp;

    public WorkerCompletableTask(
            ListenableFuture<TransactionalRequestOutcome> delegate,
            WorkUnit unit,
            String messageId) {
        this.delegate = Objects.requireNonNull(delegate);
        this.messageId = Objects.requireNonNull(messageId);
        this.topic = unit.topic();
        this.timestamp = unit.timestamp();
    }
    @Override
    public String toString() {
        long took = isDone() ? (System.currentTimeMillis() - timestamp) : -1;
        return String.format("Task(topic: %s, timestamp: %d, messageId: %s, took: %d since injection %s)",
                topic,
                timestamp,
                messageId,
                took,
                super.toString());
    }
}
