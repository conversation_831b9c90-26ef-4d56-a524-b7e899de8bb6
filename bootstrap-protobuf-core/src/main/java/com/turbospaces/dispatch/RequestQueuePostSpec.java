package com.turbospaces.dispatch;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.MutableReplyTopic;
import com.turbospaces.api.Topic;

import api.v1.ApiFactory;
import io.netty.util.AsciiString;

public interface RequestQueuePostSpec extends UntypedPostSpec {
    Optional<MutableReplyTopic> replyTo();
    UUID messageId();
    Optional<Duration> timeout();
    Any pack();

    static RequestBuilder newBuilder(Message request) {
        return new RequestBuilder(request);
    }
    static RequestBuilder newBuilder(Message request, Duration duration) {
        return new RequestBuilder(request, duration);
    }

    public static class RequestBuilder extends UntypedPostSpec.UntypedBuilder {
        private final Message request;
        private UUID messageId;
        private Optional<Duration> timeout = Optional.empty();
        private Optional<MutableReplyTopic> replyTo = Optional.empty();

        private RequestBuilder(Message request) {
            this.messageId = ApiFactory.UUID.generate();
            this.request = Objects.requireNonNull(request);
        }
        private RequestBuilder(Message request, Duration timeout) {
            this.request = Objects.requireNonNull(request);
            this.messageId = ApiFactory.UUID.generate();
            this.timeout = Optional.of(timeout);
        }
        @Override
        public RequestBuilder setTopic(Topic topic) {
            this.topic = Objects.requireNonNull(topic);
            return this;
        }
        @Override
        public RequestBuilder setRoutingKey(AsciiString routingKey) {
            this.routingKey = Objects.requireNonNull(routingKey);
            return this;
        }
        public RequestBuilder setReplyTo(MutableReplyTopic replyTo) {
            this.replyTo = Optional.of(replyTo);
            return this;
        }
        public RequestBuilder setMessageId(UUID messageId) {
            this.messageId = Objects.requireNonNull(messageId);
            return this;
        }
        @Override
        public RequestQueuePostSpec build() {
            UntypedPostSpec post = super.build();
            return new RequestQueuePostSpec() {
                @Override
                public Topic topic() {
                    return post.topic();
                }
                @Override
                public Optional<AsciiString> routingKey() {
                    return post.routingKey();
                }
                @Override
                public Optional<Duration> timeout() {
                    return timeout;
                }
                @Override
                public Optional<MutableReplyTopic> replyTo() {
                    return replyTo;
                }
                @Override
                public UUID messageId() {
                    return Objects.requireNonNull(messageId);
                }
                @Override
                public Any pack() {
                    return Any.pack(request);
                }
            };
        }
    }
}
