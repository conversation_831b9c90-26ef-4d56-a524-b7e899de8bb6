package com.turbospaces.dispatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.rpc.ApiResponse;

public class WorkerCompletableTasksBatch extends StopWatch {
    private final Map<String, ListenableFuture<?>> map;
    private final CountDownLatch latch;

    public WorkerCompletableTasksBatch(ImmutableList.Builder<ApiResponse<?>> tasks) {
        this(toMap(tasks));
    }
    public WorkerCompletableTasksBatch(ImmutableMap.Builder<String, ListenableFuture<?>> tasks) {
        this(tasks.build());
    }
    public WorkerCompletableTasksBatch(Map<String, ListenableFuture<?>> tasks) {
        this.map = Objects.requireNonNull(tasks);
        this.latch = new CountDownLatch(this.map.size());

        super.start();

        for (ListenableFuture<?> value : this.map.values()) {
            value.addListener(new Runnable() {
                @Override
                public void run() {
                    try {

                    } finally {
                        latch.countDown();
                    }
                }
            }, MoreExecutors.directExecutor());
        }
    }
    public boolean awaitAndJustLogIncomplete(Logger logger, Supplier<Integer> prop, TimeUnit unit) throws InterruptedException {
        boolean await = latch.await(prop.get(), unit);

        if (await) {
            stop();
            logger.debug("completed batch operation in: {}", super.toString());
        } else {
            List<String> ids = new ArrayList<>(map.size());

            for (Map.Entry<String, ListenableFuture<?>> it : map.entrySet()) {
                if (it.getValue().isDone()) {
                    logger.trace("operation: {} completed", it.getKey());
                } else {
                    ids.add(it.getKey());
                }
            }

            logger.error("unable to complete batch operation in: {} sec(s), incomplete: {}", prop.get(), ids);
        }

        return await;
    }
    public void awaitAndRejectExceptional(Logger logger, Supplier<Integer> prop, TimeUnit unit) throws InterruptedException, TimeoutException {
        boolean await = latch.await(prop.get(), unit);

        if (await) {
            stop();
            logger.debug("completed batch operation in: {}", super.toString());
        } else {
            List<String> exceptionally = new ArrayList<>(map.size());
            List<String> ids = new ArrayList<>(map.size());

            for (Map.Entry<String, ListenableFuture<?>> it : map.entrySet()) {
                try {
                    //
                    // ~ get and catch exception (meaning exceptional completion)
                    //
                    it.getValue().get(prop.get(), TimeUnit.SECONDS);

                    if (it.getValue().isDone()) {
                        logger.trace("operation: {} completed", it.getKey());
                    } else {
                        ids.add(it.getKey());
                    }
                } catch (ExecutionException err) {
                    logger.trace("operation: {} completed exceptionally", it.getKey());
                    exceptionally.add(it.getKey());
                }
            }

            if (Objects.nonNull(exceptionally) && BooleanUtils.isFalse(exceptionally.isEmpty())) {
                throw new TimeoutException(String.format(
                        "unable to complete batch operation in: %s sec(s), incomplete: %s, exceptionally: %s",
                        prop.get(),
                        ids,
                        exceptionally) //
                );
            }
        }
    }
    private static Map<String, ListenableFuture<?>> toMap(ImmutableList.Builder<ApiResponse<?>> c) {
        ImmutableMap.Builder<String, ListenableFuture<?>> m = ImmutableMap.builder();
        for (ApiResponse<?> it : c.build()) {
            m.put(it.headers().getMessageId(), it);
        }
        return m.build();
    }
}
