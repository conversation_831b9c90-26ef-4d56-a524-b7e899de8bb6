package com.turbospaces.dispatch;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import com.google.protobuf.Any;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.NotificationWrapperFacade;

import api.v1.ApiFactory;
import io.cloudevents.CloudEventData;
import io.cloudevents.SpecVersion;
import io.netty.util.AsciiString;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

public interface NotifyQueuePostSpec extends UntypedPostSpec, NotificationWrapperFacade {
    @Override
    UUID messageId();

    static NotificationBuilder newBuilder(NotificationWrapperFacade facade) {
        return new NotificationBuilder(facade);
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class NotificationBuilder extends UntypedPostSpec.UntypedBuilder {
        private final UUID messageId = ApiFactory.UUID.generate();
        private final NotificationWrapperFacade facade;

        @Override
        public NotificationBuilder setTopic(Topic topic) {
            super.setTopic(topic);
            return this;
        }
        @Override
        public NotificationBuilder setRoutingKey(AsciiString routingKey) {
            super.setRoutingKey(routingKey);
            return this;
        }
        @Override
        public NotifyQueuePostSpec build() {
            UntypedPostSpec post = super.build();
            return new NotifyQueuePostSpec() {
                @Override
                public Topic topic() {
                    return post.topic();
                }
                @Override
                public Optional<AsciiString> routingKey() {
                    return post.routingKey();
                }
                @Override
                public UUID messageId() {
                    return messageId;
                }
                @Override
                public Any body() {
                    return facade.body();
                }
                @Override
                public CloudEventData getData() {
                    return facade.getData();
                }
                @Override
                public SpecVersion getSpecVersion() {
                    return facade.getSpecVersion();
                }
                @Override
                public String getId() {
                    return facade.getId();
                }
                @Override
                public String getType() {
                    return facade.getType();
                }
                @Override
                public URI getSource() {
                    return facade.getSource();
                }
                @Override
                public String getDataContentType() {
                    return facade.getDataContentType();
                }
                @Override
                public URI getDataSchema() {
                    return facade.getDataSchema();
                }
                @Override
                public String getSubject() {
                    return facade.getSubject();
                }
                @Override
                public OffsetDateTime getTime() {
                    return facade.getTime();
                }
                @Override
                public Object getAttribute(String attributeName) throws IllegalArgumentException {
                    return facade.getAttribute(attributeName);
                }
                @Override
                public Object getExtension(String extensionName) {
                    return facade.getExtension(extensionName);
                }
                @Override
                public Set<String> getExtensionNames() {
                    return facade.getExtensionNames();
                }
            };
        }
    }
}
