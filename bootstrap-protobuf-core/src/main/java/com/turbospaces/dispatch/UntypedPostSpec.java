package com.turbospaces.dispatch;

import java.util.Objects;
import java.util.Optional;

import com.turbospaces.api.Topic;

import io.netty.util.AsciiString;

public interface UntypedPostSpec {
    Topic topic();
    Optional<AsciiString> routingKey();

    public abstract static class UntypedBuilder {
        protected Topic topic;
        protected AsciiString routingKey;

        protected UntypedBuilder() {
        }
        public UntypedBuilder setTopic(Topic topic) {
            this.topic = Objects.requireNonNull(topic);
            return this;
        }
        public UntypedBuilder setRoutingKey(AsciiString routingKey) {
            this.routingKey = Objects.requireNonNull(routingKey);
            return this;
        }
        public UntypedPostSpec build() {
            Objects.requireNonNull(topic, "request topic is missing");

            return new UntypedPostSpec() {
                @Override
                public Topic topic() {
                    return topic;
                }
                @Override
                public Optional<AsciiString> routingKey() {
                    return Optional.ofNullable(routingKey);
                }
            };
        }
    }
}
