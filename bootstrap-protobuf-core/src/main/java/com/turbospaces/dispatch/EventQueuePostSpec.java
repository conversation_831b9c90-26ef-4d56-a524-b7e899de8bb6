package com.turbospaces.dispatch;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.Topic;

import api.v1.ApiFactory;
import io.netty.util.AsciiString;

public interface EventQueuePostSpec extends UntypedPostSpec {
    UUID messageId();
    Any pack();

    static EventBuilder newBuilder(Message event) {
        return new EventBuilder(event);
    }
    static EventBuilder newBuilder(Any any) {
        return new EventBuilder(any);
    }

    public static class EventBuilder extends UntypedPostSpec.UntypedBuilder {
        private final UUID messageId = ApiFactory.UUID.generate();
        private Message event;
        private Any any;

        public EventBuilder(Message request) {
            this.event = Objects.requireNonNull(request);
        }
        public EventBuilder(Any any) {
            this.any = Objects.requireNonNull(any);
        }
        @Override
        public EventBuilder setTopic(Topic topic) {
            super.setTopic(topic);
            return this;
        }
        @Override
        public EventBuilder setRoutingKey(AsciiString routingKey) {
            super.setRoutingKey(routingKey);
            return this;
        }
        @Override
        public EventQueuePostSpec build() {
            UntypedPostSpec post = super.build();
            Any toReturn = Objects.nonNull(any) ? any : Any.pack(event);
            return new EventQueuePostSpec() {
                @Override
                public Topic topic() {
                    return post.topic();
                }
                @Override
                public Optional<AsciiString> routingKey() {
                    return post.routingKey();
                }
                @Override
                public UUID messageId() {
                    return messageId;
                }
                @Override
                public Any pack() {
                    return toReturn;
                }
            };
        }
    }
}
