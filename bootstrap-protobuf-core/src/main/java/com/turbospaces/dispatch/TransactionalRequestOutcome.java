package com.turbospaces.dispatch;

import java.util.LinkedList;
import java.util.List;

import com.google.common.collect.Lists;
import com.google.protobuf.Any;
import com.google.protobuf.TextFormat;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import io.netty.util.AsciiString;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class TransactionalRequestOutcome {
    private final AsciiString key;

    private final ResponseWrapperFacade reply;

    @Builder.Default
    private final List<NotificationWrapperFacade> notifications = Lists.newLinkedList();

    @Builder.Default
    private final List<Any> eventStream = Lists.newLinkedList();

    @Builder.Default
    private final List<RequestQueuePostSpec> generic = new LinkedList<>();

    @Override
    public String toString() {
        return TextFormat.printer().shortDebugString(getReply().body());
    }
}
