package com.turbospaces.executor;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;

import com.google.common.io.ByteSource;

import api.v1.CloudEventWithRoutingKey;
import io.cloudevents.CloudEventData;
import io.cloudevents.SpecVersion;
import io.cloudevents.core.data.BytesCloudEventData;
import lombok.ToString;

@ToString
public class CloudEventWithRoutingKeyWorkUnit implements CloudNativeWorkUnit {
    private final String topic;
    private final CloudEventWithRoutingKey record;
    private final CloudEventData data;

    public CloudEventWithRoutingKeyWorkUnit(String topic, CloudEventWithRoutingKey record) {
        this.topic = Objects.requireNonNull(topic);
        this.record = Objects.requireNonNull(record);

        //
        // ~ pre-cache bytes for repeated usage if necessary
        //
        if (record.getEvent().getData() instanceof BytesCloudEventData) {
            data = record.getEvent().getData();
        } else {
            var asBytes = record.getEvent().getData().toBytes();
            this.data = new CloudEventData() {
                @Override
                public byte[] toBytes() {
                    return asBytes;
                }
            };
        }
    }
    @Override
    public long timestamp() {
        return record.getTimestamp();
    }
    @Override
    public String topic() {
        return topic;
    }
    @Override
    public byte[] key() {
        if (Objects.nonNull(record.getKey())) {
            return record.getKey().toByteArray();
        }
        return null;
    }
    @Override
    public ByteSource value() {
        return ByteSource.wrap(data.toBytes());
    }
    @Override
    public Optional<String> lastHeader(String key) {
        Object extension = record.getEvent().getExtension(key);
        return Optional.ofNullable(extension).or(new Supplier<>() {
            @Override
            public Optional<Object> get() {
                //
                // ~ throws IllegalArgumentException if the provided attribute name it's not a valid attribute for this specification
                //
                try {
                    return Optional.ofNullable(record.getEvent().getAttribute(key));
                } catch (IllegalArgumentException err) {
                    return Optional.empty();
                }
            }
        }).map(Object::toString);
    }
    @Override
    public CloudEventData getData() {
        return data;
    }
    @Override
    public SpecVersion getSpecVersion() {
        return record.getEvent().getSpecVersion();
    }
    @Override
    public String getId() {
        return record.getEvent().getId();
    }
    @Override
    public String getType() {
        return record.getEvent().getType();
    }
    @Override
    public URI getSource() {
        return record.getEvent().getSource();
    }
    @Override
    public String getDataContentType() {
        return record.getEvent().getDataContentType();
    }
    @Override
    public URI getDataSchema() {
        return record.getEvent().getDataSchema();
    }
    @Override
    public String getSubject() {
        return record.getEvent().getSubject();
    }
    @Override
    public OffsetDateTime getTime() {
        return record.getEvent().getTime();
    }
    @Override
    public Object getAttribute(String attributeName) throws IllegalArgumentException {
        return record.getEvent().getAttribute(attributeName);
    }
    @Override
    public Object getExtension(String extensionName) {
        return record.getEvent().getExtension(extensionName);
    }
    @Override
    public Set<String> getExtensionNames() {
        return record.getEvent().getExtensionNames();
    }
}
