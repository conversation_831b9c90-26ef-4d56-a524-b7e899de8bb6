package com.turbospaces.api.facade;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Supplier;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.google.common.base.Suppliers;
import com.google.protobuf.Any;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Message;

import api.v1.ApiFactory;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import io.cloudevents.SpecVersion;
import io.cloudevents.core.builder.CloudEventBuilder;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class DefaultRequestWrapperFacade implements RequestWrapperFacade {
    private final Supplier<CloudEvent> event;

    @EqualsAndHashCode.Include
    private final api.v1.Headers headers;

    @EqualsAndHashCode.Include
    private final Any body;

    private final boolean nativeFormat;

    public DefaultRequestWrapperFacade(CloudEventBuilder eventTemplate, api.v1.Headers headers, Any body) {
        this.headers = Objects.requireNonNull(headers);
        this.body = Objects.requireNonNull(body);
        this.nativeFormat = true;
        this.event = Suppliers.memoize(new com.google.common.base.Supplier<>() {
            @Override
            public CloudEvent get() {
                CloudEventBuilder builder = eventTemplate
                        .withId(headers.getMessageId())
                        .withType(body().getTypeUrl())
                        .withExtension(ApiFactory.CLOUD_EVENT_NATIVE_FORMAT, true)
                        .withData(new CloudEventData() {
                            @Override
                            public byte[] toBytes() {
                                return body.toByteArray();
                            }
                        });

                //
                // ~ write all headers as plain headers (any MessageMQ provider)
                //
                headers.getAllFields().forEach(new BiConsumer<>() {
                    @Override
                    public void accept(FieldDescriptor field, Object value) {
                        if (Objects.nonNull(value)) {
                            builder.withContextAttribute(field.getJsonName().toLowerCase().intern(), ApiFactory.toString(field, value));
                        }
                    }
                });

                return builder.build();
            }
        });
    }
    @Override
    public api.v1.Headers headers() {
        return headers;
    }
    @Override
    public Any body() {
        return body;
    }
    @Override
    public RequestWrapperFacade repack(Message req) {
        return new DefaultRequestWrapperFacade(CloudEventBuilder.from(event.get()), headers, Any.pack(req));
    }
    @Override
    public boolean isNative() {
        return nativeFormat;
    }
    @Override
    public CloudEventData getData() {
        return event.get().getData();
    }
    @Override
    public Object getExtension(String extensionName) {
        return event.get().getExtension(extensionName);
    }
    @Override
    public Set<String> getExtensionNames() {
        return event.get().getExtensionNames();
    }
    @Override
    public SpecVersion getSpecVersion() {
        return event.get().getSpecVersion();
    }
    @Override
    public String getId() {
        return event.get().getId();
    }
    @Override
    public String getType() {
        return event.get().getType();
    }
    @Override
    public URI getSource() {
        return event.get().getSource();
    }
    @Override
    public String getDataContentType() {
        return event.get().getDataContentType();
    }
    @Override
    public URI getDataSchema() {
        return event.get().getDataSchema();
    }
    @Override
    public String getSubject() {
        return event.get().getSubject();
    }
    @Override
    public OffsetDateTime getTime() {
        return event.get().getTime();
    }
    @Override
    public Object getAttribute(String attributeName) {
        return event.get().getAttribute(attributeName);
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE).append("headers", headers()).append("body", body()).build();
    }
}
