package com.turbospaces.api.facade;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Supplier;

import com.google.common.base.Suppliers;
import com.google.protobuf.Any;

import api.v1.ApiFactory;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import io.cloudevents.SpecVersion;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.protobuf.ProtoCloudEventData;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class DefaultNotificationWrapperFacade implements NotificationWrapperFacade {
    private final Supplier<CloudEvent> event;

    @EqualsAndHashCode.Include
    private UUID messageId;

    @EqualsAndHashCode.Include
    private final Any body;

    public DefaultNotificationWrapperFacade(CloudEventBuilder eventTemplate, UUID messageId, Any body) {
        this.messageId = Objects.requireNonNull(messageId);
        this.body = Objects.requireNonNull(body);
        this.event = Suppliers.memoize(new com.google.common.base.Supplier<>() {
            @Override
            public CloudEvent get() {
                return eventTemplate
                        .withId(messageId.toString())
                        .withType(body().getTypeUrl())
                        .withExtension(ApiFactory.CLOUD_EVENT_NATIVE_FORMAT, true)
                        .withData(ProtoCloudEventData.wrap(body))
                        .build();
            }
        });
    }
    @Override
    public UUID messageId() {
        return messageId;
    }
    @Override
    public Any body() {
        return body;
    }
    @Override
    public CloudEventData getData() {
        return event.get().getData();
    }
    @Override
    public Object getExtension(String extensionName) {
        return event.get().getExtension(extensionName);
    }
    @Override
    public Set<String> getExtensionNames() {
        return event.get().getExtensionNames();
    }
    @Override
    public SpecVersion getSpecVersion() {
        return event.get().getSpecVersion();
    }
    @Override
    public String getId() {
        return event.get().getId();
    }
    @Override
    public String getType() {
        return event.get().getType();
    }
    @Override
    public URI getSource() {
        return event.get().getSource();
    }
    @Override
    public String getDataContentType() {
        return event.get().getDataContentType();
    }
    @Override
    public URI getDataSchema() {
        return event.get().getDataSchema();
    }
    @Override
    public String getSubject() {
        return event.get().getSubject();
    }
    @Override
    public OffsetDateTime getTime() {
        return event.get().getTime();
    }
    @Override
    public Object getAttribute(String attributeName) {
        return event.get().getAttribute(attributeName);
    }
    @Override
    public String toString() {
        return body().toString();
    }
}
