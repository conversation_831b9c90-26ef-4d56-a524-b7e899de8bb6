package com.turbospaces.api.facade;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import io.cloudevents.CloudEvent;

import java.io.IOException;

public interface ResponseWrapperFacade extends CloudEvent {
    api.v1.Headers headers();
    ResponseStatusFacade status();
    api.v1.CacheControl cacheControl();
    Any body();

    default <T extends Message> T unpack(Class<T> type) throws IOException {
        return body().unpack(type);
    }

    default <T extends Message> boolean is(Class<T> type) {
        return body().is(type);
    }
}
