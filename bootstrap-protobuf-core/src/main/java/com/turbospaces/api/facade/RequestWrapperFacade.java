package com.turbospaces.api.facade;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;

import io.cloudevents.CloudEvent;

public interface RequestWrapperFacade extends CloudEvent {
    api.v1.Headers headers();
    Any body();
    RequestWrapperFacade repack(Message message);
    boolean isNative();

    default <T extends Message> T unpack(Class<T> type) throws InvalidProtocolBufferException {
        return body().unpack(type);
    }
}
