package com.turbospaces.api.facade;

import java.util.Map;

import com.google.protobuf.Internal.EnumLite;

import api.v1.ApplicationException;

public interface ResponseStatusFacade {
    boolean isOK();
    boolean isAuth();
    boolean isSystem();
    boolean isTimeout();
    boolean isDuplicate();
    boolean isNotFound();
    boolean isBadRequest();
    boolean isDenied();
    boolean isFrozen();
    boolean isInsufficientFunds();
    boolean isNoContent();

    ApplicationException toException();

    EnumLite errorCode();
    String errorText();
    EnumLite errorReason();
    Map<String, String> errorDetails();
}
