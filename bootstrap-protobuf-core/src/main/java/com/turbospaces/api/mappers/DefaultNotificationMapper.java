package com.turbospaces.api.mappers;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.DefaultNotificationWrapperFacade;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.executor.CloudNativeWorkUnit;
import com.turbospaces.executor.WorkUnit;

import api.v1.ApiFactory;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.core.v1.CloudEventV1;
import io.vavr.CheckedFunction3;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public final class DefaultNotificationMapper implements NotificationFacadeMapper, CheckedFunction3<WorkUnit, byte[], String, NotificationWrapperFacade> {
    private final CloudEventBuilder eventTemplate;

    @Override
    public NotificationWrapperFacade unpack(WorkUnit workUnit) throws Exception {
        if (workUnit instanceof CloudNativeWorkUnit cnwu) {
            return apply(workUnit, cnwu.getData().toBytes(), StringUtils.EMPTY);
        }

        for (String alias : new String[] { "-", "_" }) {
            var prefix = "ce" + alias;
            var isNative = workUnit.lastHeader(prefix + ApiFactory.CLOUD_EVENT_NATIVE_FORMAT).map(Boolean::parseBoolean).orElse(false);
            if (isNative) {
                return apply(workUnit, workUnit.value().read(), prefix);
            }
        }

        throw new IllegalArgumentException("message is not coded in cloud events native format: " + workUnit);
    }
    @Override
    public NotificationWrapperFacade pack(Message.Builder message) {
        UUID messageId = ApiFactory.UUID.generate();
        return new DefaultNotificationWrapperFacade(eventTemplate, messageId, Any.pack(message.build()));
    }
    @Override
    public NotificationWrapperFacade apply(WorkUnit workUnit, byte[] data, String prefix) throws Exception {
        var messageId = workUnit.lastHeader(prefix + CloudEventV1.ID).map(UUID::fromString).get();
        var body = Any.newBuilder().mergeFrom(data);
        return new DefaultNotificationWrapperFacade(eventTemplate, messageId, body.build());
    }
}
