package com.turbospaces.api.mappers;

import java.time.Duration;

import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.ContextPropagator;

public interface RequestFacadeMapper {
    RequestWrapperFacade pack(RequestQueuePostSpec spec, ContextPropagator<api.v1.Headers.Builder> propagator, Duration defaultTimeout);
    RequestWrapperFacade unpack(WorkUnit workUnit) throws Exception;
}
