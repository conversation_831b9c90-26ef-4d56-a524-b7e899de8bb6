package com.turbospaces.api.mappers;

import java.io.InputStream;
import java.time.Duration;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;

import com.google.protobuf.Any;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.ContextPropagator;

import api.v1.ApiFactory;
import api.v1.Headers;
import io.cloudevents.core.builder.CloudEventBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public final class DefaultRequestFacadeMapper implements RequestFacadeMapper {
    private final CloudEventBuilder eventTemplate;

    @Override
    public RequestWrapperFacade unpack(WorkUnit workUnit) throws Exception {
        try (InputStream io = workUnit.value().openStream()) {
            for (String alias : new String[] { "-", "_" }) {
                var prefix = "ce" + alias;
                var isNative = workUnit.lastHeader(prefix + ApiFactory.CLOUD_EVENT_NATIVE_FORMAT).map(Boolean::parseBoolean).orElse(false);
                if (isNative) {
                    var headers = api.v1.Headers.newBuilder();
                    var body = Any.newBuilder().mergeFrom(io);

                    //
                    // ~ read all headers as plain headers (any MessageMQ provider)
                    //
                    api.v1.Headers.getDescriptor().getFields().forEach(new Consumer<>() {
                        @Override
                        public void accept(FieldDescriptor field) {
                            workUnit.lastHeader(prefix + field.getJsonName().toLowerCase().intern()).ifPresent(new Consumer<>() {
                                @Override
                                public void accept(String value) {
                                    headers.setField(field, ApiFactory.parse(field, value));
                                }
                            });
                        }
                    });

                    return new DefaultRequestWrapperFacade(eventTemplate, headers.build(), body.build());
                }
            }

            throw new IllegalArgumentException("message is not coded in cloud events native format: " + workUnit);
        }
    }
    @Override
    public RequestWrapperFacade pack(
            RequestQueuePostSpec spec,
            ContextPropagator<api.v1.Headers.Builder> propagator,
            Duration defaultTimeout) {
        UUID messageId = spec.messageId();
        Duration operationTimeout = spec.timeout().orElse(defaultTimeout);

        Headers.Builder headers = Headers.newBuilder();
        headers.setMessageId(messageId.toString());
        headers.setTimeout((int) operationTimeout.toSeconds());
        if (spec.replyTo().isPresent()) {
            Topic topic = spec.replyTo().get();
            headers.setReplyTo(topic.name().toString());
        }

        //
        // ~ try to inject bound MDC variables into headers
        //
        if (Objects.nonNull(propagator)) {
            propagator.injectContext(headers);
        }

        Any any = spec.pack();

        return new DefaultRequestWrapperFacade(eventTemplate, headers.build(), any);
    }
}
