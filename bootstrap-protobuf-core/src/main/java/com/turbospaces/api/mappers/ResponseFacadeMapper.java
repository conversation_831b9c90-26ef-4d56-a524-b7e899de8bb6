package com.turbospaces.api.mappers;

import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.executor.WorkUnit;

public interface ResponseFacadeMapper {
    ResponseWrapperFacade unpack(WorkUnit workUnit) throws Exception;
    ResponseWrapperFacade toReply(RequestWrapperFacade reqw, Message message, ResponseStatusFacade status);
    ResponseWrapperFacade toReply(RequestWrapperFacade reqw, Message message, api.v1.CacheControl cacheControl);
}
