package api.v1;

import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Map;
import java.util.Random;

import org.apache.commons.rng.core.source64.SplitMix64;
import org.slf4j.MDC;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.uuid.Generators;
import com.fasterxml.uuid.impl.RandomBasedGenerator;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Internal.EnumLite;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.mappers.NotificationFacadeMapper;
import com.turbospaces.api.mappers.RequestFacadeMapper;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;

import io.cloudevents.core.builder.CloudEventBuilder;
import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.ws.rs.core.Response;

public interface ApiFactory {
    String CLOUD_EVENT_NATIVE_FORMAT = "nativeformat"; // ~ cloud event native format shortcut

    ThreadLocal<SplitMix64> RNG = new ThreadLocal<>() {
        @Override
        protected SplitMix64 initialValue() {
            Hasher hasher = Hashing.murmur3_128().newHasher();

            //
            // ~ make initial seed as random as possible
            //
            hasher.putInt(Runtime.getRuntime().availableProcessors());
            hasher.putString(Thread.currentThread().getName(), StandardCharsets.UTF_8);
            hasher.putLong(System.nanoTime());
            hasher.putString(ApiFactory.class.getName(), StandardCharsets.UTF_8);
            hasher.putLong(Runtime.getRuntime().freeMemory());

            long seed = Math.abs(hasher.hash().asLong());
            return new SplitMix64(seed);
        }
    };

    Random RANDOM = new Random() {
        @Override
        protected int next(int bits) {
            return RNG.get().nextInt() >>> 32 - bits;
        }
        @Override
        public int nextInt() {
            return RNG.get().nextInt();
        }
        @Override
        public long nextLong() {
            return RNG.get().nextLong();
        }
        @Override
        public boolean nextBoolean() {
            return RNG.get().nextBoolean();
        }
        @Override
        public float nextFloat() {
            return RNG.get().nextFloat();
        }
        @Override
        public double nextDouble() {
            return RNG.get().nextDouble();
        }
    };

    RandomBasedGenerator UUID = Generators.randomBasedGenerator(RANDOM);

    Topic notifyTopic();
    Topic eventsTopic();
    ObjectMapper objectMapper();
    CloudEventBuilder eventTemplate();
    RequestFacadeMapper requestMapper();
    ResponseFacadeMapper responseMapper();
    NotificationFacadeMapper notificationMapper();

    ResponseStatusFacade status(String code, int reason, String error, Map<String, String> details);
    ResponseStatusFacade toExceptional(String messageId, Throwable cause);
    ResponseStatusFacade toExceptionalSystemReply(String errorText);
    ResponseStatusFacade toExceptionalTimeoutReply(String errorText);
    ResponseStatusFacade toExceptionalBadRequestReply(String errorText);
    ResponseStatusFacade toExceptionalNotFoundReply(String errorText);
    ResponseStatusFacade toExceptionalAuthReply(String errorText);
    ResponseStatusFacade toExceptionalDeniedReply(String errorText);
    ResponseStatusFacade toExceptionalOtpRequiredReply(String errorText);

    default Response.ResponseBuilder translateFault(ResponseStatusFacade status, Object error) {
        if (status.isOK()) {
            return Response.ok();
        } else if (status.isBadRequest()) {
            return Response.status(HttpResponseStatus.BAD_REQUEST.code()).entity(error);
        } else if (status.isNoContent()) {
            return Response.status(HttpResponseStatus.NO_CONTENT.code()).entity(error);
        } else if (status.isAuth()) {
            return Response.status(HttpResponseStatus.UNAUTHORIZED.code()).entity(error);
        } else if (status.isDenied()) {
            return Response.status(HttpResponseStatus.FORBIDDEN.code()).entity(error);
        } else if (status.isInsufficientFunds()) {
            return Response.status(HttpResponseStatus.PAYMENT_REQUIRED.code()).entity(error);
        } else if (status.isNotFound()) {
            return Response.status(HttpResponseStatus.NOT_FOUND.code()).entity(error);
        } else if (status.isDuplicate()) {
            return Response.status(HttpResponseStatus.CONFLICT.code()).entity(error);
        } else if (status.isFrozen()) {
            return Response.status(HttpResponseStatus.LOCKED.code()).entity(error);
        } else if (status.isTimeout()) {
            return Response.status(HttpResponseStatus.GATEWAY_TIMEOUT.code()).entity(error);
        } else if (status.isSystem()) {
            return Response.status(HttpResponseStatus.INTERNAL_SERVER_ERROR.code()).entity(status.errorText());
        }

        return Response.status(HttpResponseStatus.SERVICE_UNAVAILABLE.code()).entity(status.errorText());
    }
    static Object parse(FieldDescriptor field, String text) {
        return switch (field.getJavaType()) {
            case BOOLEAN: yield Boolean.parseBoolean(text);
            case DOUBLE: yield Double.parseDouble(text);
            case FLOAT: yield Float.parseFloat(text);
            case INT: yield Integer.parseInt(text);
            case LONG: yield Long.parseLong(text);
            case STRING: yield text;
            default:
                throw new IllegalArgumentException("uknown java type: " + field.getJavaType());
        };
    }
    static String toString(FieldDescriptor field, Object value) {
        return switch (field.getJavaType()) {
            case BOOLEAN: yield Boolean.class.cast(value).toString();
            case DOUBLE: yield Double.class.cast(value).toString();
            case FLOAT: yield Float.class.cast(value).toString();
            case INT: yield Integer.class.cast(value).toString();
            case LONG: yield Long.class.cast(value).toString();
            case STRING: yield String.class.cast(value);
            case ENUM: yield Integer.toString(EnumLite.class.cast(value).getNumber());
            case MESSAGE: {
                if (field.isMapField()) {
                    yield Map.class.cast(value).toString();
                } else if (field.isRepeated()) {
                    yield Collection.class.cast(value).toString();
                }
                throw new UnsupportedOperationException(field.toProto().toString());
            }
            default: throw new IllegalArgumentException("uknown java type: " + field.getJavaType());
        };
    }
    static void setMdc(WorkUnit record, String operation, api.v1.Headers headers) {
        var messageId = headers.getMessageId();
        var traceId = headers.getTraceId();

        MDC.put(MdcTags.MDC_MESSAGE_ID, messageId);
        MdcUtil.setMdc(record, operation, traceId);
    }
}
