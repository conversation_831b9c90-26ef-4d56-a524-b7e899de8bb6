package api.v1;

import java.net.URI;
import java.util.Objects;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Iterables;
import com.turbospaces.api.CommonTopics;
import com.turbospaces.api.Topic;
import com.turbospaces.api.mappers.DefaultNotificationMapper;
import com.turbospaces.api.mappers.NotificationFacadeMapper;
import com.turbospaces.cfg.ApplicationProperties;

import io.cloudevents.core.builder.CloudEventBuilder;

public abstract class AbstractApiFactory implements ApiFactory {
    protected URI uri = URI.create(ApiFactory.UUID.generate().toString());
    protected ObjectMapper mapper;
    protected Topic events;
    protected Supplier<Topic> notify;

    public AbstractApiFactory(ApplicationProperties props, ObjectMapper mapper) {
        this.uri = URI.create(props.CLOUD_APP_ID.get());
        this.mapper = Objects.requireNonNull(mapper);
        this.events = CommonTopics.asEvents(props);
        this.notify = Suppliers.memoize(new Supplier<>() {
            @Override
            public Topic get() {
                return Iterables.getOnlyElement(CommonTopics.asNotifies(props));
            }
        });
    }
    @Override
    public NotificationFacadeMapper notificationMapper() {
        return new DefaultNotificationMapper(eventTemplate());
    }
    @Override
    public Topic notifyTopic() {
        return notify.get();
    }
    @Override
    public Topic eventsTopic() {
        return events;
    }
    @Override
    public CloudEventBuilder eventTemplate() {
        return CloudEventBuilder.v1().withSource(uri);
    }
    @Override
    public ObjectMapper objectMapper() {
        return mapper;
    }
}
