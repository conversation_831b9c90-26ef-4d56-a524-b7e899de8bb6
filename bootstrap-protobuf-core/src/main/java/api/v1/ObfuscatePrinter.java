package api.v1;

import java.util.Map.Entry;

import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Message.Builder;
import com.google.protobuf.TextFormat;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class ObfuscatePrinter {
    public static String shortDebugString(Builder message) {
        return TextFormat.printer().shortDebugString(obfuscate(message));
    }
    private static Builder obfuscate(Builder builder) {
        for (Entry<FieldDescriptor, Object> entry : builder.getAllFields().entrySet()) {
            FieldDescriptor field = entry.getKey();
            if (field.isRepeated()) {
                if (field.getJavaType() == FieldDescriptor.JavaType.MESSAGE) {
                    if (field.isMapField()) {

                    } else {
                        for (int i = 0; i < builder.getRepeatedFieldCount(field); ++i) {
                            obfuscate(builder.getRepeatedFieldBuilder(field, i));
                        }
                    }
                } else if (shouldObfuscate(field)) {
                    if (field.isMapField()) {

                    } else {
                        for (int i = 0; i < builder.getRepeatedFieldCount(field); ++i) {
                            var f = builder.getRepeatedField(field, i);
                            builder.setRepeatedField(field, i, getReplacement(f.toString()));
                        }
                    }
                }
            } else {
                if (field.getJavaType() == FieldDescriptor.JavaType.MESSAGE) {
                    obfuscate(builder.getFieldBuilder(field));
                } else if (shouldObfuscate(field)) {
                    var f = builder.getField(field);
                    builder.setField(field, getReplacement(f.toString()));
                }
            }
        }
        return builder;
    }
    private static String getReplacement(String value) {
        return "*".repeat(value.length());
    }
    private static boolean shouldObfuscate(FieldDescriptor field) {
        if (field.getJavaType() != FieldDescriptor.JavaType.STRING) {
            return false;
        }
        SensitiveMode obfuscationMode = field.getOptions().getExtension(Obfuscation.sensitiveMode);
        if (obfuscationMode == null) {
            return false;
        }
        return obfuscationMode.getSensitive();
    }
}
