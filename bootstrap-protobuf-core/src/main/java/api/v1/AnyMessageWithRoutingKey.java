package api.v1;

import java.io.Externalizable;
import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;
import java.util.Objects;

import org.apache.commons.io.IOUtils;

import com.google.protobuf.Any;

import io.netty.util.AsciiString;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Getter
public class AnyMessageWithRoutingKey implements Externalizable {
    private static final short VERSION = 1;

    private final long timestamp;
    private AsciiString key;
    private Any event;

    @ToString.Exclude
    private transient byte[] data;

    public AnyMessageWithRoutingKey(long timestamp, AsciiString key, Any event) {
        this.timestamp = timestamp;
        this.key = key;
        this.event = Objects.requireNonNull(event);
    }
    @Override
    public void writeExternal(ObjectOutput out) throws IOException {
        out.writeShort(VERSION);
        out.writeBoolean(Objects.nonNull(key));
        out.writeUTF(key.toString());
        data = event.toByteArray();
        out.writeInt(data.length);
        out.write(data);
    }
    @Override
    public void readExternal(ObjectInput in) throws IOException, ClassNotFoundException {
        short version = in.readShort();
        switch (version) {
            case VERSION: {
                if (in.readBoolean()) {
                    key = AsciiString.cached(in.readUTF());
                }
                data = new byte[in.readInt()];
                IOUtils.readFully(ObjectInputStream.class.cast(in), data); // ~ do not read directly into data, it doesn't work with large byte array
                event = Any.newBuilder().mergeFrom(data).build();
                break;
            }
            default:
                throw new IOException("Unsupported version: " + version);
        }
    }
}
