package api.v1;

import java.util.Objects;
import java.util.function.Supplier;

import org.apache.commons.lang3.ArrayUtils;

import com.google.protobuf.Internal.EnumLite;
import com.google.protobuf.ProtocolMessageEnum;

import lombok.Getter;

@Getter
public class ApplicationException extends Exception {
    private final EnumLite code;

    ApplicationException(String message, EnumLite code) {
        super(message);
        this.code = Objects.requireNonNull(code);
    }
    ApplicationException(String message, Throwable cause, EnumLite code) {
        super(message, cause);
        this.code = Objects.requireNonNull(code);
    }
    ApplicationException(Throwable cause, EnumLite code) {
        super(cause);
        this.code = Objects.requireNonNull(code);
    }
    public static ApplicationException of(String message, EnumLite code, Object... args) {
        if (Objects.isNull(args)) {
            return new ApplicationException(message, code);
        } else if (ArrayUtils.isEmpty(args)) {
            return new ApplicationException(message, code);
        }
        return new ApplicationException(String.format(message, args), code);
    }
    public static ApplicationException rethrowing(Throwable cause, EnumLite code) {
        return new ApplicationException(cause.getMessage(), cause, code);
    }
    public static ApplicationException rethrowing(String message, Throwable cause, EnumLite code) {
        return new ApplicationException(message, cause, code);
    }
    public static Supplier<ApplicationException> orElseThrow(String message, EnumLite code) {
        return new Supplier<>() {
            @Override
            public ApplicationException get() {
                return new ApplicationException(message, code);
            }
        };
    }
    public static Supplier<ApplicationException> orElseThrow(String message, EnumLite code, Object... args) {
        return new Supplier<>() {
            @Override
            public ApplicationException get() {
                if (Objects.isNull(args)) {
                    return new ApplicationException(message, code);
                } else if (ArrayUtils.isEmpty(args)) {
                    return new ApplicationException(message, code);
                }
                return new ApplicationException(String.format(message, args), code);
            }
        };
    }
    public static ApplicationException of(String message, ProtocolMessageEnum code, Object... args) {
        return of(message, (EnumLite) code, args);
    }
    public static ApplicationException rethrowing(Throwable cause, ProtocolMessageEnum code) {
        return new ApplicationException(cause.getMessage(), cause, code);
    }
    public static ApplicationException rethrowing(String message, Throwable cause, ProtocolMessageEnum code) {
        return new ApplicationException(message, cause, code);
    }
    public static Supplier<ApplicationException> orElseThrow(String message, ProtocolMessageEnum code) {
        return orElseThrow(message, (EnumLite) code);
    }
    public static Supplier<ApplicationException> orElseThrow(String message, ProtocolMessageEnum code, Object... args) {
        return orElseThrow(message, (EnumLite) code, args);
    }
}
