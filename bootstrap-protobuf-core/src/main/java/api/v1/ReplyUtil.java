package api.v1;

import org.apache.commons.lang3.BooleanUtils;

import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class ReplyUtil {
    public static final String ERROR_FORMAT = "The server encountered an internal error or misconfiguration and was unable to complete your request. Reference: %s";

    public static ResponseWrapperFacade verifyOk(ResponseWrapperFacade resp) throws Exception {
        ReplyUtil.verifyOk(resp.status());
        return resp;
    }
    public static void verifyOk(ResponseStatusFacade status) throws Exception {
        if (BooleanUtils.isFalse(status.isOK())) {
            throw status.toException();
        }
    }
}
