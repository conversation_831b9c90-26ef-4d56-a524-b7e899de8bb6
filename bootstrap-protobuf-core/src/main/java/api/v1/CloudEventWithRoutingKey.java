package api.v1;

import java.io.Externalizable;
import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;
import java.util.Objects;

import org.apache.commons.io.IOUtils;

import io.cloudevents.CloudEvent;
import io.cloudevents.protobuf.ProtobufFormat;
import io.netty.util.AsciiString;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@NoArgsConstructor
@Getter
public class CloudEventWithRoutingKey implements Externalizable {
    private static final short VERSION = 1;
    private static final ProtobufFormat FORMAT = new ProtobufFormat();

    private long timestamp;
    private AsciiString key;
    private CloudEvent event;

    @ToString.Exclude
    private transient byte[] data;

    public CloudEventWithRoutingKey(long timestamp, AsciiString key, CloudEvent event) {
        this.timestamp = timestamp;
        this.key = key;
        this.event = Objects.requireNonNull(event);
    }
    @Override
    public void writeExternal(ObjectOutput out) throws IOException {
        out.writeShort(VERSION);
        out.writeLong(timestamp);
        out.writeBoolean(Objects.nonNull(key));
        if (Objects.nonNull(key)) {
            out.writeUTF(key.toString());
        }
        data = FORMAT.serialize(event);
        out.writeInt(data.length);
        out.write(data);
    }
    @Override
    public void readExternal(ObjectInput in) throws IOException, ClassNotFoundException {
        short version = in.readShort();
        switch (version) {
            case VERSION: {
                timestamp = in.readLong();
                if (in.readBoolean()) {
                    key = AsciiString.cached(in.readUTF());
                }
                data = new byte[in.readInt()];
                IOUtils.readFully(ObjectInputStream.class.cast(in), data); // ~ do not read directly into data, it doesn't work with large byte array
                event = FORMAT.deserialize(data);
                break;
            }
            default:
                throw new IOException("Unsupported version: " + version);
        }
    }
}
