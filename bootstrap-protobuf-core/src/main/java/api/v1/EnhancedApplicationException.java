package api.v1;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

import com.google.protobuf.Internal.EnumLite;
import com.google.protobuf.Message;

import lombok.Getter;

@Getter
public final class EnhancedApplicationException extends ApplicationException {
    private final EnumLite reason;
    private final List<Message> errorDetails;

    private EnhancedApplicationException(String message, EnumLite code, EnumLite reason) {
        this(message, code, reason, Collections.emptyList());
    }

    private EnhancedApplicationException(String message, Throwable cause, EnumLite code, EnumLite reason) {
        this(message, cause, code, reason, Collections.emptyList());
    }

    private EnhancedApplicationException(String message, EnumLite code, EnumLite reason, List<Message> errorDetails) {
        super(message, code);
        this.reason = reason;
        this.errorDetails = Collections.unmodifiableList(errorDetails);
    }

    private EnhancedApplicationException(String message, Throwable cause, EnumLite code, EnumLite reason, List<Message> errorDetails) {
        super(message, cause, code);
        this.reason = reason;
        this.errorDetails = Collections.unmodifiableList(errorDetails);
    }

    public EnhancedApplicationException errorDetail(Message errorDetail) {
        return errorDetails(List.of(errorDetail));
    }

    public EnhancedApplicationException errorDetails(List<Message> details) {
        var mergedErrorDetails = new ArrayList<>(this.errorDetails);
        mergedErrorDetails.addAll(details);
        return new EnhancedApplicationException(getMessage(), getCause(), getCode(), reason, mergedErrorDetails);
    }

    public static EnhancedApplicationException internal(Throwable cause, EnumLite code, EnumLite reason) {
        return new EnhancedApplicationException("Something went wrong. Server encountered an internal error", cause, code, reason);
    }

    public static EnhancedApplicationException of(String message, EnumLite code, EnumLite reason) {
        return new EnhancedApplicationException(message, code, reason);
    }

    public static EnhancedApplicationException rethrowing(String message, Throwable cause, EnumLite code, EnumLite reason) {
        return new EnhancedApplicationException(message, cause, code, reason);
    }

    public static Supplier<EnhancedApplicationException> orElseThrow(String message, EnumLite code, EnumLite reason, List<Message> errorDetails) {
        return () -> new EnhancedApplicationException(message, code, reason, errorDetails);
    }

    public static Supplier<EnhancedApplicationException> orElseThrow(String message, EnumLite code, EnumLite reason) {
        return orElseThrow(message, code, reason, Collections.emptyList());
    }
}
