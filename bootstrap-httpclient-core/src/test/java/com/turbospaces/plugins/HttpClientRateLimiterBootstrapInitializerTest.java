package com.turbospaces.plugins;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Objects;

import com.turbospaces.executor.DefaultPlatformExecutorService;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.sun.net.httpserver.HttpServer;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.CloseableHttpClientFactoryBean;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.netty.ProxyHttpChannel;
import com.turbospaces.resteasy.JaxrsClientFactoryBean;
import com.turbospaces.resteasy.RateLimiterApiClientFilter;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.internal.InMemoryRateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.micrometer.core.instrument.search.RequiredSearch;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HttpClientRateLimiterBootstrapInitializerTest {
    @Test
    public void testClassLevel() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        var limitingClass = GoogleApi.class;
        cfg.setLocalProperty("GoogleApi.rate-limiter.enable", true);
        runSelectedRequestsWithDirectLimit(cfg, props, limitingClass);
        cfg.setLocalProperty(props.API_RATE_LIMIT_OUTGOING_DEFAULT_PERIOD.getKey(), Duration.ofMillis(1));
        fireSeveralRequests(20, 20, props, new InMemoryRateLimiterRegistry(), limitingClass);
    }

    @Test
    public void testMethodLevel() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        var limitingClass = GoogleApi.class;
        cfg.setLocalProperty("GoogleApi.search.rate-limiter.enable", true);
        runSelectedRequestsWithDirectLimit(cfg, props, limitingClass);
        cfg.setLocalProperty(props.API_RATE_LIMIT_OUTGOING_DEFAULT_PERIOD.getKey(), Duration.ofMillis(1));
        fireSeveralRequests(20, 20, props, new InMemoryRateLimiterRegistry(), limitingClass);
    }

    @Test
    public void runCounterWithOverridePropsOnBarrierKey() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        var limitingClass = GoogleApi.class;
        cfg.setLocalProperty("methodLevelLimiter.rate-limiter.enable", true);
        String limiterKey = "methodLevelLimiter";
        runSelectedReqeustsWithProperties(cfg, props, limitingClass, limiterKey);
    }

    private void runSelectedRequestsWithDirectLimit(ApplicationConfig cfg, ApplicationProperties props, Class<? extends SearchParent> limitingClass)
            throws Throwable {
        applyRateLimitDirectly(30, 1, cfg, props);
        fireSeveralRequests(5, 1, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(30, 2, cfg, props);
        fireSeveralRequests(5, 2, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(30, 5, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(30, 10, cfg, props);
        fireSeveralRequests(20, 10, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(1, 500, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
    }

    private void runSelectedReqeustsWithProperties(
            ApplicationConfig cfg,
            ApplicationProperties props, Class<? extends SearchParent> limitingClass,
            String limiterKey) throws Throwable {
        setPropertiesByLimitingKey(limiterKey, 30, 1, 0, cfg);
        fireSeveralRequests(5, 1, props, new InMemoryRateLimiterRegistry(), limitingClass);
        setPropertiesByLimitingKey(limiterKey, 30, 2, 0, cfg);
        fireSeveralRequests(5, 2, props, new InMemoryRateLimiterRegistry(), limitingClass);
        setPropertiesByLimitingKey(limiterKey, 30, 5, 0, cfg);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
        setPropertiesByLimitingKey(limiterKey, 30, 10, 0, cfg);
        fireSeveralRequests(20, 10, props, new InMemoryRateLimiterRegistry(), limitingClass);
        setPropertiesByLimitingKey(limiterKey, 1, 500, 1, cfg);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
    }

    @Test
    public void runCounterWithOverridePropsOnClass() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        var limitingClass = GoogleApiClassLevel.class;
        cfg.setLocalProperty("ClassLevelLimiter.rate-limiter.enable", true);
        String limiterKey = "ClassLevelLimiter";
        runSelectedReqeustsWithProperties(cfg, props, limitingClass, limiterKey);
    }

    @Test
    public void runCounterWithNoEnabledInProps() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        var limitingClass = GoogleApi.class;
        applyRateLimitDirectly(30, 1, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(30, 2, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(30, 5, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(30, 10, cfg, props);
        fireSeveralRequests(20, 20, props, new InMemoryRateLimiterRegistry(), limitingClass);
        applyRateLimitDirectly(1, 500, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
    }

    @Test
    public void runAnnotatedButNoKeySpecifiedClass() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        var limitingClass = NoRateKeySpecified.class;
        applyRateLimitDirectly(30, 1, cfg, props);
        fireSeveralRequests(5, 5, props, new InMemoryRateLimiterRegistry(), limitingClass);
    }

    public void fireSeveralRequests(
            int toRun,
            int succeedRunsExpected,
            ApplicationProperties props,
            RateLimiterRegistry rateLimiterRegistry,
            Class<? extends SearchParent> limitingClass) throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, HttpClientRateLimiterBootstrapInitializerTest.RootContext.class);
        ConfigurableApplicationContext context = bootstrap.run();
        RateLimiterApiClientFilter rateLimiterApiClientFilter = new RateLimiterApiClientFilter(props, rateLimiterRegistry);
        int timerCounterReal = 0;
        int counterLimiter = 0;
        HttpServer httpServer = mockHttpServerLocal(port);
        try {
            ResteasyWebTarget webTarget = setupHttpClient(port, props, bootstrap.rateLimiterRegistry(), context, rateLimiterApiClientFilter, bootstrap.meterRegistry());
            var googleApi = webTarget.proxy(limitingClass);

            for (int i = 0; i < toRun; i++) {
                try (Response reply = googleApi.search()) {
                    if (reply.getStatus() == 200) {
                        log.debug(IOUtils.toString((InputStream) reply.getEntity(), StandardCharsets.UTF_8));
                    } else if (reply.getStatus() == 429) {
                        counterLimiter++;
                    }
                    RequiredSearch search = bootstrap.meterRegistry().get("httpcomponents.httpclient.request").tag("uri", "search");
                    Timer timer = search.timer();
                    Assertions.assertNotNull(timer);
                    timerCounterReal = (int) timer.count();
                }
            }
        } finally {
            Assertions.assertEquals(succeedRunsExpected, timerCounterReal);
            Assertions.assertEquals(toRun - succeedRunsExpected, counterLimiter);

            bootstrap.shutdown();
            httpServer.stop(0);
        }
    }

    private static ResteasyWebTarget setupHttpClient(
            int port,
            ApplicationProperties props,
            RateLimiterRegistry rateLimiterRegistry,
            ConfigurableApplicationContext context,
            RateLimiterApiClientFilter rateLimiterApiClientFilter, MeterRegistry meterRegistry) throws Exception {
        CloseableHttpClient httpClient = context.getBean(CloseableHttpClient.class);
        var executor = new DefaultPlatformExecutorService(props, meterRegistry);
        JaxrsClientFactoryBean jaxrs = new JaxrsClientFactoryBean(props, rateLimiterRegistry, httpClient, executor);
        ResteasyClient client = Objects.requireNonNull(jaxrs.getObject()).register(rateLimiterApiClientFilter).build();
        return client.target(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).build());
    }

    private static void applyRateLimitDirectly(int periodP, int countP, ApplicationConfig cfg, ApplicationProperties props) {
        cfg.setLocalProperty(props.API_RATE_LIMIT_OUTGOING_DEFAULT_PERIOD.getKey(), Duration.ofMinutes(periodP));
        cfg.setLocalProperty(props.API_RATE_LIMIT_OUTGOING_DEFAULT_COUNT.getKey(), countP);
        cfg.setLocalProperty(props.API_RATE_LIMIT_OUTGOING_DEFAULT_TIMEOUT.getKey(), Duration.ofMinutes(0));
    }

    private static void setPropertiesByLimitingKey(String limiterKey, int period, int count, int timeout, ApplicationConfig cfg) {
        cfg.setLocalProperty("rate-limiter." + limiterKey + ".period", Duration.ofMinutes(period));
        cfg.setLocalProperty("rate-limiter." + limiterKey + ".count", count);
        cfg.setLocalProperty("rate-limiter." + limiterKey + ".timeout", Duration.ofMinutes(timeout));
    }

    private static HttpServer mockHttpServerLocal(int port) throws IOException {
        HttpServer httpServer = HttpServer.create(new InetSocketAddress(port),  PlatformUtil.findAvailableTcpPort()); // or use InetSocketAddress(0) for ephemeral port
        httpServer.createContext("/search", exchange -> {
            byte[] response = "{\"success\": true}".getBytes();
            exchange.sendResponseHeaders(HttpURLConnection.HTTP_OK, response.length);
            exchange.getResponseBody().write(response);
            exchange.close();
        });
        httpServer.start();
        return httpServer;
    }

    @Configuration
    public static class RootContext {
        @Bean
        public CommonObjectMapper mapper() {
            return new CommonObjectMapper();
        }
        @Bean
        public CloseableHttpClientFactoryBean httpClient(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new CloseableHttpClientFactoryBean(props, meterRegistry);
        }
        @Bean
        public ProxyHttpChannel proxyChannel(
                ApplicationProperties props,
                CommonObjectMapper mapper,
                CompositeMeterRegistry meterRegistry,
                HealthCheckRegistry healthCheckRegistry) {
            return new ProxyHttpChannel(props, mapper, meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get());
        }
    }

    @ApiEndpoint(rateBarrierKey = "ClassLevelLimiter")
    public interface GoogleApi extends SearchParent {
        @Override @Path("/search")
        @GET
        @ApiEndpoint(
                metricsUri = "search",
                rateBarrierKey = "methodLevelLimiter")
        Response search();
    }

    @ApiEndpoint(rateBarrierKey = "ClassLevelLimiter")
    public interface GoogleApiClassLevel extends SearchParent {
        @Override @Path("/search")
        @GET
        @ApiEndpoint(metricsUri = "search")
        Response search();
    }

    @ApiEndpoint
    public interface NoRateKeySpecified extends SearchParent {
        @Override @Path("/search")
        @GET
        @ApiEndpoint(metricsUri = "search")
        Response search();
    }

    public interface SearchParent {
        Response search();
    }
}
