package com.turbospaces.plugins;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

import com.turbospaces.executor.DefaultPlatformExecutorService;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.github.robtimus.obfuscation.Obfuscated;
import com.github.robtimus.obfuscation.Obfuscator;
import com.github.robtimus.obfuscation.annotation.ObfuscateAll;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.CloseableHttpClientFactoryBean;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.netty.ProxyHttpChannel;
import com.turbospaces.resteasy.HttpClientBodyPrinter;
import com.turbospaces.resteasy.JaxrsClientFactoryBean;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.micrometer.core.instrument.search.RequiredSearch;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Form;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import nl.altindag.log.LogCaptor;

class HttpClientBootstrapInitializerTest {
    Logger logger = LoggerFactory.getLogger(getClass());

    @Test
    void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        cfg.setLocalProperty(props.HTTP_PROXY.getKey(), "http://localhost:" + port);
        cfg.setLocalProperty(props.HTTP_REQUEST_TO_PROXY_PATTERNS.getKey(), ".*google.*");

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, RootContext.class);
        ConfigurableApplicationContext context = bootstrap.run();

        try {
            CloseableHttpClient httpClient = context.getBean(CloseableHttpClient.class);

            var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
            JaxrsClientFactoryBean jaxrs = new JaxrsClientFactoryBean(props, bootstrap.rateLimiterRegistry(), httpClient, executor);
            ResteasyClient client = jaxrs.getObject().build();

            ResteasyWebTarget webTarget = client.target(new URIBuilder().setScheme("https").setHost("www.google.com").build());
            GoogleApi googleApi = webTarget.proxy(GoogleApi.class);

            String header1 = PlatformUtil.randomUUID().toString();
            String header2 = PlatformUtil.randomUUID().toString();
            try (Response reply = googleApi.search(header1, header2, "turbospaces", "passw0rd", "passw0rd2")) {
                Assertions.assertTrue(Arrays.asList(HttpStatus.SC_OK, HttpStatus.SC_MOVED_TEMPORARILY).contains(reply.getStatus()));

                logger.debug(IOUtils.toString((InputStream) reply.getEntity(), StandardCharsets.UTF_8));
                logger.debug("buffer: {}", IOUtils.toString((InputStream) reply.getEntity(), StandardCharsets.UTF_8).length());

                //
                // ~ actual validation (proxy adds netty header)
                //
                Assertions.assertNotNull(reply.getHeaderString(props.NETTY_VERSION_HTTP_HEADER_NAME.get()));

                RequiredSearch search = bootstrap.meterRegistry().get("httpcomponents.httpclient.request").tag("uri", "google-search");
                Timer timer = search.timer();
                Assertions.assertNotNull(timer);
                Assertions.assertNotNull(timer.getId().getTag("status"));
                Assertions.assertEquals("GET", timer.getId().getTag("method"));
                Assertions.assertEquals("tagChild", timer.getId().getTag("custom"));
                Assertions.assertEquals(1, timer.count());
            }
            try (Response reply = googleApi.test()) {
                reply.bufferEntity();
                RequiredSearch search = bootstrap.meterRegistry().get("httpcomponents.httpclient.request").tag("uri", "/test");
                Timer timer = search.timer();
                Assertions.assertNotNull(timer);
            }
        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void testObfuscation() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, RootContext.class);
        ConfigurableApplicationContext context = bootstrap.run();

        try {
            CloseableHttpClient httpClient = context.getBean(CloseableHttpClient.class);
            var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
            JaxrsClientFactoryBean jaxrs = new JaxrsClientFactoryBean(props, bootstrap.rateLimiterRegistry(), httpClient, executor);
            ResteasyClient client = jaxrs.getObject().build();

            ResteasyWebTarget webTarget = client.target(new URIBuilder().setScheme("https").setHost("www.google.com").build());
            GoogleApi googleApi = webTarget.proxy(GoogleApi.class);

            try (var logCaptor = LogCaptor.forName(HttpClientBodyPrinter.class.getPackage().getName() + "." + "ClientLoggingFilter")) {
                GoogleApi.Request req = new GoogleApi.Request();
                req.hidden = Obfuscator.all().obfuscateObject("hidden");
                req.not_hidden = "not_hidden";
                try (Response reply = googleApi.search(req)) {
                    // hidden not shown
                }
                Assertions.assertTrue(
                        logCaptor.getDebugLogs().contains("""
                            Request:
                            POST: https://www.google.com/searchHidden
                            Header: Accept = application/json
                            Header: Content-Type = application/json
                            Body: {"hidden":"******","not_hidden":"not_hidden"}"""));
            }

        } finally {
            bootstrap.shutdown();
        }
    }

    @Test
    void testHtmlFormData() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, RootContext.class);
        ConfigurableApplicationContext context = bootstrap.run();

        try {
            CloseableHttpClient httpClient = context.getBean(CloseableHttpClient.class);
            var executor = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
            JaxrsClientFactoryBean jaxrs = new JaxrsClientFactoryBean(props, bootstrap.rateLimiterRegistry(), httpClient, executor);
            ResteasyClient client = jaxrs.getObject().build();

            ResteasyWebTarget webTarget = client.target(new URIBuilder().setScheme("https").setHost("www.google.com").build());
            GoogleApi googleApi = webTarget.proxy(GoogleApi.class);

            try (var logCaptor = LogCaptor.forName(HttpClientBodyPrinter.class.getPackage().getName() + "." + "ClientLoggingFilter")) {
                try (Response response = googleApi
                        .searchForm(new Form().param("testParam1", "test1").param("testParam1", "test2").param("testParam2", "test1"))) {
                    // assert logged correctly
                }
                Assertions.assertTrue(
                        logCaptor.getDebugLogs().contains("""
                            Request:
                            POST: https://www.google.com/searchForm
                            Header: Accept = application/json
                            Header: Content-Type = application/x-www-form-urlencoded
                            Body: testParam1=test1,test2;testParam2=test1;"""));
            }

        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class RootContext {
        @Bean
        public CommonObjectMapper mapper() {
            return new CommonObjectMapper();
        }
        @Bean
        public CloseableHttpClientFactoryBean httpClient(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new CloseableHttpClientFactoryBean(props, meterRegistry);
        }
        @Bean
        public ProxyHttpChannel proxyChannel(
                ApplicationProperties props,
                CommonObjectMapper mapper,
                CompositeMeterRegistry meterRegistry,
                HealthCheckRegistry healthCheckRegistry) {
            return new ProxyHttpChannel(props, mapper, meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get());
        }
    }

    @ApiEndpoint(metricTags = { @Tag(key = "custom", value = "tagRoot"), @Tag(key = "customSecond", value = "tagRoot") })
    public interface GoogleApi {
        @Path("/search")
        @GET
        @ApiEndpoint(
                metricsUri = "google-search", headersToMask = "X-my-Header2", queryParamsToMask = "Password2",
                metricTags = { @Tag(key = "custom", value = "tagChild") },
                doNotPrintResponseBody = true)
        Response search(
                @HeaderParam("X-My-Header1") String header1,
                @HeaderParam("X-My-Header2") String header2,
                @QueryParam("q") String query,
                @QueryParam("password") String password,
                @QueryParam("password2") String password2);

        @Path("/test")
        @GET
        Response test();

        @Path("/searchHidden")
        @POST
        @ApiEndpoint(metricsUri = "google-search")
        @Produces(MediaType.APPLICATION_JSON)
        @Consumes(MediaType.APPLICATION_JSON)
        Response search(Request request);

        @Path("/searchForm")
        @POST
        @ApiEndpoint(metricsUri = "google-search")
        @Produces(MediaType.APPLICATION_JSON)
        @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
        Response searchForm(Form form);

        class Request {
            @ObfuscateAll
            public Obfuscated<String> hidden;

            public String not_hidden;
        }
    }
}
