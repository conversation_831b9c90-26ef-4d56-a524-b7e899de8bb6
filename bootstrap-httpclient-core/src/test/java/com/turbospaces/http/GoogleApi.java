package com.turbospaces.http;

import java.util.concurrent.CompletionStage;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

public interface GoogleApi {
    @Path("/search-x")
    @Produces(MediaType.WILDCARD)
    @Consumes(MediaType.WILDCARD)
    @GET
    @ApiEndpoint(metricTags = { @Tag(key = "domain", value = "xxx") })
   CompletionStage<Response> searchx(@QueryParam("q") String query, @HeaderParam(HttpProto.HEADER_X_TAGS) String xTags, @HeaderParam("nextTag") String brand);

    @Path("/search-y")
    @Produces(MediaType.WILDCARD)
    @Consumes(MediaType.WILDCARD)
    @GET
    @ApiEndpoint(metricTags = { @Tag(key = "domain", value = "yyy") })
    CompletionStage<Response> searchy(@QueryParam("q") String query);
}
