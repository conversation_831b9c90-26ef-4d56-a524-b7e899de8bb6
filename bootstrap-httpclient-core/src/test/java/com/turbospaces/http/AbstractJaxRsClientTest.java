package com.turbospaces.http;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.netflix.archaius.api.Property;
import com.turbospaces.resteasy.AbstractJaxRsClient;
import com.turbospaces.ups.PlainServiceInfo;

import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;

class AbstractJaxRsClientTest {
    private final Property<Integer> propertyInt10 = new Property<>() {
        @Override
        public Integer get() {
            return 10;
        }

        @Override
        public String getKey() {
            return null;
        }
    };

    private final Property<Integer> propertyInt20 = new Property<>() {
        @Override
        public Integer get() {
            return 20;
        }

        @Override
        public String getKey() {
            return null;
        }
    };

    @Test
    void testWebTargetConfigEquals() {
        EqualsVerifier.simple().forClass(AbstractJaxRsClient.WebTargetConfig.class)
                .withPrefabValues(Property.class, propertyInt10, propertyInt20)
                .suppress(Warning.NULL_FIELDS)
                .suppress(Warning.ALL_FIELDS_SHOULD_BE_USED)
                .verify();

        var wtc1 = new AbstractJaxRsClient.WebTargetConfig(new PlainServiceInfo("test", "https://a.b/c?d=e"), propertyInt10, propertyInt20);
        var wtc2 = new AbstractJaxRsClient.WebTargetConfig(new PlainServiceInfo("test", "https://a.b/c?d=e"), propertyInt10, propertyInt20);
        var wtc3 = new AbstractJaxRsClient.WebTargetConfig(new PlainServiceInfo("test", "https://a.b/c?d=e"), propertyInt20, propertyInt20);
        Assertions.assertEquals(wtc1, wtc2);
        Assertions.assertNotEquals(wtc2, wtc3);
        Assertions.assertNotEquals(wtc1, wtc3);
    }
}
