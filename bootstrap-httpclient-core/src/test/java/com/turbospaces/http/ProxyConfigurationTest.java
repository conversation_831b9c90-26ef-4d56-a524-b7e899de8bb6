package com.turbospaces.http;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

class ProxyConfigurationTest {
    public static Stream<Arguments> data() {
        return Stream.of(
                Arguments.of(
                        "https://iomeu-casino-partner.api.relaxg.com:7000/papi/1.0/casino/games/getgames",
                        "http://68.183.248.159:8881",
                        Collections.singletonList(Pattern.compile(".*relaxg.*")),
                        "http://68.183.248.159:8881/papi/1.0/casino/games/getgames?_pref=https%3A%2F%2Fiomeu-casino-partner.api.relaxg.com%3A7000"),
                Arguments.of(
                        "https://test.com/papi/1.0/casino/games/getgames?foo=bar%21",
                        "http://proxy.com",
                        Collections.singletonList(Pattern.compile(".*test.*")),
                        "http://proxy.com/papi/1.0/casino/games/getgames?foo=bar%21&_pref=https%3A%2F%2Ftest.com"),
                Arguments.of(
                        "https://test.com?foo=bar",
                        "http://proxy.com",
                        Collections.singletonList(Pattern.compile(".*test.*")),
                        "http://proxy.com?foo=bar&_pref=https%3A%2F%2Ftest.com"),
                Arguments.of(
                        "https://test.com/?foo=bar",
                        "http://proxy.com",
                        Collections.singletonList(Pattern.compile(".*test.*")),
                        "http://proxy.com/?foo=bar&_pref=https%3A%2F%2Ftest.com"),
                Arguments.of(
                        "https://NO_MATCH.com/papi/1.0/casino/games/getgames?foo=bar",
                        "http://proxy.com",
                        Collections.singletonList(Pattern.compile(".*test.*")),
                        null),
                Arguments.of(
                        "https://NO_MATCH.com/papi/1.0/casino/games/getgames?foo=bar",
                        "http://proxy.com",
                        Lists.newArrayList(),
                        null));
    }

    @ParameterizedTest
    @MethodSource("data")
    void testProxyConfig(String uri, String proxy, List<Pattern> applyTo, String expected) {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        cfg.setLocalProperty(props.HTTP_PROXY.getKey(), proxy);
        if (CollectionUtils.isNotEmpty(applyTo)) {
            cfg.setLocalProperty(props.HTTP_REQUEST_TO_PROXY_PATTERNS.getKey(), Joiner.on(',').join(applyTo));
        }

        Optional<URI> opt = UrlUtils.proxyRequest(props, uri);
        if (opt.isPresent()) {
            URI uriBuilder = opt.get();
            Assertions.assertEquals(expected, uriBuilder.toString());
        } else {
            Assertions.assertNull(expected);
        }
    }

    @ParameterizedTest
    @MethodSource("data")
    void testProxyInterceptorConfig(String uri, String proxy, List<Pattern> applyTo, String expected) {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        cfg.setLocalProperty(props.HTTP_PROXY.getKey(), proxy);
        if (CollectionUtils.isNotEmpty(applyTo)) {
            cfg.setLocalProperty(props.HTTP_REQUEST_TO_PROXY_PATTERNS.getKey(), Joiner.on(',').join(applyTo));
        }

        Optional<URI> opt = UrlUtils.proxyRequest(props, uri);
        if (opt.isPresent()) {
            URI uriBuilder = opt.get();
            Assertions.assertEquals(expected, uriBuilder.toString());
        } else {
            Assertions.assertNull(expected);
        }
    }
}
