package com.turbospaces.http;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.HttpStatusCode;
import org.mockserver.model.MediaType;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.resteasy.SimpleScopedJaxRsClient;
import com.turbospaces.ups.PlainServiceInfo;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.search.Search;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AbstractClientTest {
    @Test
    public void works() throws Throwable {
        try (ClientAndServer server = ClientAndServer.startClientAndServer(0)) {
            server.when(HttpRequest.request("/search-x"))
                    .respond(new HttpResponse()
                            .withStatusCode(HttpStatusCode.OK_200.code())
                            .withReasonPhrase(HttpStatusCode.OK_200.reasonPhrase())
                            .withContentType(MediaType.TEXT_PLAIN)
                            .withBody(PlatformUtil.randomUUID().toString()));
            server.when(HttpRequest.request("/search-y"))
                    .respond(new HttpResponse()
                            .withStatusCode(HttpStatusCode.ACCEPTED_202.code())
                            .withReasonPhrase(HttpStatusCode.ACCEPTED_202.reasonPhrase())
                            .withContentType(MediaType.TEXT_PLAIN)
                            .withBody(PlatformUtil.randomUUID().toString()));

            PlainServiceInfo si = new PlainServiceInfo("google", "http://localhost:" + server.getPort());

            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            cfg.setDefaultProperty(props.TCP_CONNECTION_TIMEOUT.getKey(), Duration.ofSeconds(5));
            cfg.setDefaultProperty(props.TCP_SOCKET_TIMEOUT.getKey(), Duration.ofSeconds(15));

            SimpleBootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.addUps(si);
            ConfigurableApplicationContext ctx = bootstrap.run();

            try {
                for (var it : new SimpleScopedJaxRsClient[] { ctx.getBean(GoogleApiClientAsync.class), ctx.getBean(GoogleApiClientSync.class) }) {
                    GoogleApi api1 = (GoogleApi) it.proxy(si);

                    AtomicReference<Throwable> failure = new AtomicReference<>();
                    CompletionStage<Void> f1 = api1.searchx("it works!", "nextTag", "tag").thenAccept(new Consumer<>() {
                        @Override
                        public void accept(Response response) {
                            response.bufferEntity();
                            try {
                                Assertions.assertEquals(HttpStatus.SC_OK, response.getStatus());
                                log.info(IOUtils.toString((InputStream) response.getEntity(), StandardCharsets.UTF_8));
                                log.info(IOUtils.toString((InputStream) response.getEntity(), StandardCharsets.UTF_8));
                            } catch (Exception err) {
                                log.error(err.getMessage(), err);
                                failure.set(err);
                            } finally {
                                response.close();
                            }
                        }
                    });

                    f1.toCompletableFuture().join();

                    Assertions.assertNull(failure.get());
                    Search search1 = bootstrap.meterRegistry().find("httpcomponents.httpclient.request");
                    Assertions.assertTrue(search1.tag("domain", "xxx").timer().count() > 0);
                    Assertions.assertTrue(search1.tag("nextTag", "tag").timer().count() > 0);

                    GoogleApi api2 = (GoogleApi) it.proxy(si);
                    CompletionStage<Void> f2 = api2.searchy("it works!").thenAccept(new Consumer<>() {
                        @Override
                        public void accept(Response response) {
                            response.bufferEntity();
                            try {
                                Assertions.assertEquals(HttpStatus.SC_ACCEPTED, response.getStatus());
                                log.info(IOUtils.toString((InputStream) response.getEntity(), StandardCharsets.UTF_8));
                                log.info(IOUtils.toString((InputStream) response.getEntity(), StandardCharsets.UTF_8));
                            } catch (Exception err) {
                                log.error(err.getMessage(), err);
                                failure.set(err);
                            } finally {
                                response.close();
                            }
                        }
                    });

                    f2.toCompletableFuture().join();

                    Assertions.assertNull(failure.get());
                    Search search2 = bootstrap.meterRegistry().find("httpcomponents.httpclient.request");
                    Assertions.assertTrue(search2.tag("domain", "yyy").timer().count() > 0);
                }
            } finally {
                bootstrap.shutdown();
            }
        }
    }

    public static class GoogleApiClientSync extends SimpleScopedJaxRsClient<GoogleApi> {
        public GoogleApiClientSync(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                CloseableHttpClient closeableHttpClient,
                CommonObjectMapper mapper) {
            super(props, meterRegistry, rateLimiterRegistry, closeableHttpClient, mapper, GoogleApi.class);
        }
    }

    public static class GoogleApiClientAsync extends SimpleScopedJaxRsClient<GoogleApi> {
        public GoogleApiClientAsync(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                CloseableHttpAsyncClient closeableHttpClient,
                CommonObjectMapper mapper) {
            super(props, meterRegistry, rateLimiterRegistry, closeableHttpClient, mapper, GoogleApi.class);
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public CloseableHttpClientFactoryBean httpClient(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new CloseableHttpClientFactoryBean(props, meterRegistry);
        }
        @Bean
        public CloseableHttpAsyncClientFactoryBean httpClientAsync(ApplicationProperties props, MeterRegistry meterRegistry) throws Exception {
            return new CloseableHttpAsyncClientFactoryBean(props, meterRegistry);
        }
        @Bean
        public GoogleApiClientSync apiSync(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                CloseableHttpClientFactoryBean httpClient) throws Exception {
            return new GoogleApiClientSync(props, meterRegistry, rateLimiterRegistry, httpClient.getObject(), new CommonObjectMapper());
        }
        @Bean
        public GoogleApiClientAsync apiAsync(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry,
                CloseableHttpAsyncClientFactoryBean httpClient) throws Exception {
            return new GoogleApiClientAsync(props, meterRegistry, rateLimiterRegistry, httpClient.getObject(), new CommonObjectMapper());
        }
    }
}
