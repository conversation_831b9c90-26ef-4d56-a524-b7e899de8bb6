package com.turbospaces.http;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Map;

import org.junit.jupiter.api.Test;

class UrlUtilsTest {
    @Test
    void testNoQuery() throws MalformedURLException, URISyntaxException {
        String urlVal = "https://www.pulsz.com/games";
        assertEquals(
                "https://www.pulsz.com/games",
                UrlUtils.addParams(urlVal, Map.of()).toString()
        );
        assertEquals(
                "https://www.pulsz.com/games?test=value",
                UrlUtils.addParams(urlVal, Map.of("test", "value")).toString()
        );
    }

    @Test
    void testCorrectlyEncodesPartiallyEncodedParam() throws MalformedURLException, URISyntaxException {
        String urlVal = "https://www.pulsz.com/games/?utm_source=googleads&utm_campaign=Pulsz%20|%20Web%20Search%20|%20Web%20Traffic";
        assertEquals(
                "https://www.pulsz.com/games/?utm_campaign=Pulsz+%7C+Web+Search+%7C+Web+Traffic&utm_source=googleads&test=value",
                UrlUtils.addParams(urlVal, Map.of("test", "value")).toString()
        );
        assertEquals(
                "https://www.pulsz.com/games/?utm_campaign=Pulsz+%7C+Web+Search+%7C+Web+Traffic&utm_source=googleads",
                UrlUtils.addParams(urlVal, Map.of()).toString()
        );
    }

    @Test
    void testDecode() {
        var res = UrlUtils.decodeQuery("fi_acc_type=PC&acc_label=************0000&fi_name=CHASE");
        assertEquals(res.get("fi_acc_type").iterator().next(), "PC");
        assertEquals(res.get("acc_label").iterator().next(), "************0000");
        assertEquals(res.get("fi_name").iterator().next(), "CHASE");
        assertTrue(UrlUtils.decodeQuery(null).isEmpty());
    }
}