package com.turbospaces.resteasy;

import java.io.IOException;
import java.lang.reflect.Method;
import java.time.Duration;

import org.apache.commons.lang3.StringUtils;
import org.jboss.resteasy.client.jaxrs.internal.proxy.ClientInvoker;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.cfg.ApplicationProperties;

import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.netty.handler.codec.http.HttpResponseStatus;
import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class RateLimiterApiClientFilter implements ClientRequestFilter, HttpClientBodyPrinter {
    private final ApplicationProperties props;
    private final RateLimiterRegistry rateLimiterRegistry;

    @Override
    public void filter(ClientRequestContext requestContext) throws IOException {
        ClientInvoker clientInvoker = clientInvoker(requestContext);
        Method method = clientInvoker.getMethod();
        String className = method.getDeclaringClass().getSimpleName();
        String fullMethodName = className + "." + method.getName();
        boolean classEnabled = props.cfg().get(Boolean.class, className + ".rate-limiter.enable", false);
        boolean methodEnabled = props.cfg().get(Boolean.class, fullMethodName + ".rate-limiter.enable", false);
        if (methodEnabled) {
            applyLimit(requestContext, fullMethodName);
            return;
        }
        if (classEnabled) {
            applyLimit(requestContext, className);
            return;
        }
        ApiEndpoint api = method.getAnnotation(ApiEndpoint.class);
        Class<?> resourceClass = clientInvoker.getDeclaring();
        ApiEndpoint apiClass = resourceClass.getAnnotation(ApiEndpoint.class);
        String declaredRateBarrierKey = null;
        if (api != null && StringUtils.isNotEmpty(api.rateBarrierKey())) {
            declaredRateBarrierKey = api.rateBarrierKey();
        } else if (apiClass != null && StringUtils.isNotEmpty(apiClass.rateBarrierKey())) {
            declaredRateBarrierKey = apiClass.rateBarrierKey();
        }
        boolean rateBarrierKeyEnabled = false;
        if (StringUtils.isNotEmpty(declaredRateBarrierKey)) {
            rateBarrierKeyEnabled = props.cfg().get(Boolean.class, declaredRateBarrierKey + ".rate-limiter.enable", false);
        }
        if (rateBarrierKeyEnabled) {
            applyLimit(requestContext, declaredRateBarrierKey);
        }
    }

    private void applyLimit(ClientRequestContext requestContext, String key) {
        String rateLimiterKey = "rate-limiter." + key;
        var cfg = rateLimiterRegistry.getConfiguration(rateLimiterKey).orElseGet(() -> createConfig(props, rateLimiterKey));
        RateLimiter rateLimiter = rateLimiterRegistry.rateLimiter(rateLimiterKey, cfg);
        if (!rateLimiter.acquirePermission()) {
            requestContext.abortWith(Response.status(HttpResponseStatus.TOO_MANY_REQUESTS.code()).build());
        }
    }

    private static RateLimiterConfig createConfig(ApplicationProperties props, String key) {
        Duration period = props.cfg().get(Duration.class, key + ".period", props.API_RATE_LIMIT_OUTGOING_DEFAULT_PERIOD.get());
        Integer count = props.cfg().get(int.class, key + ".count", props.API_RATE_LIMIT_OUTGOING_DEFAULT_COUNT.get());
        Duration timeout = props.cfg().get(Duration.class, key + ".timeout", props.API_RATE_LIMIT_OUTGOING_DEFAULT_TIMEOUT.get());
        return RateLimiterConfig.custom()
                .limitRefreshPeriod(period)
                .limitForPeriod(count)
                .timeoutDuration(timeout)
                .build();
    }
}
