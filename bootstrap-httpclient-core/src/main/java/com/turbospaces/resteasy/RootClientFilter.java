package com.turbospaces.resteasy;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.resteasy.client.jaxrs.internal.ClientResponseContextImpl;
import org.jboss.resteasy.client.jaxrs.internal.proxy.ClientInvoker;
import org.slf4j.MDC;

import com.google.common.collect.Sets;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.HttpProto;
import com.turbospaces.mdc.MdcTags;

import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.client.ClientResponseContext;
import jakarta.ws.rs.client.ClientResponseFilter;
import jakarta.ws.rs.core.MultivaluedMap;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class RootClientFilter implements ClientRequestFilter, ClientResponseFilter, HttpClientBodyPrinter {
    private final ApplicationProperties props;

    @Override
    @SuppressWarnings("deprecation")
    public void filter(ClientRequestContext requestContext) throws IOException {
        MDC.put(MdcTags.MDC_PATH, requestContext.getUri().getPath());
        MDC.put(MdcTags.MDC_METHOD, requestContext.getMethod());

        MultivaluedMap<String, Object> headers = requestContext.getHeaders();
        Set<String> additionalTags = Sets.newLinkedHashSet();

        DefaultHttpClientRequestPrinter printer = new DefaultHttpClientRequestPrinter(props);
        printer.doOutPut(requestContext);

        ClientInvoker clientInvoker = clientInvoker(requestContext);
        Method method = clientInvoker.getMethod();

        ApiEndpoint apiOnClass = method.getDeclaringClass().getAnnotation(ApiEndpoint.class);
        ApiEndpoint apiOnMethod = method.getAnnotation(ApiEndpoint.class);

        if (Objects.nonNull(apiOnMethod)) {
            if (StringUtils.isNotEmpty(apiOnMethod.metricsUri())) {
                headers.add(io.micrometer.core.instrument.binder.httpcomponents.DefaultUriMapper.URI_PATTERN_HEADER, apiOnMethod.metricsUri());
            }
        }

        //
        // ~ attach additional tags from class/method level meta data if available (order is important in array)
        //
        for (ApiEndpoint next : new ApiEndpoint[] { apiOnClass, apiOnMethod }) {
            Optional.ofNullable(next).ifPresent(new Consumer<>() {
                @Override
                public void accept(ApiEndpoint annotation) {
                    for (Tag tag : annotation.metricTags()) {
                        headers.add(tag.key(), tag.value());
                        additionalTags.add(tag.key());
                    }
                }
            });
        }

        //
        // ~ well we simple appender from method and class level additional tags as plain headers and store them in intermediate header
        //
        if (CollectionUtils.isNotEmpty(additionalTags)) {
            headers.add(HttpProto.HEADER_X_TAGS, additionalTags.stream().collect(Collectors.joining(";")));
        }
    }
    @Override
    public void filter(ClientRequestContext requestContext, ClientResponseContext responseContext) throws IOException {
        try {
            MDC.put(MdcTags.MDC_STATUS_CODE, Integer.toString(responseContext.getStatus()));

            DefaultHttpClientResponsePrinter printer = new DefaultHttpClientResponsePrinter(props);

            // buffer entity for multiple read entity calls
            if (responseContext instanceof ClientResponseContextImpl) {
                ((ClientResponseContextImpl) responseContext).getClientResponse().bufferEntity();
            }

            printer.doOutPut(requestContext, responseContext);
        } catch (Throwable err) {
            throw new IOException(err);
        } finally {
            MDC.remove(MdcTags.MDC_PATH);
            MDC.remove(MdcTags.MDC_METHOD);
            MDC.remove(MdcTags.MDC_STATUS_CODE);
        }
    }
}
