package com.turbospaces.resteasy;

import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.springframework.cloud.service.UriBasedServiceInfo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Suppliers;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.netflix.archaius.api.Property;
import com.turbospaces.cache.BlockhoundLoadingCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.json.CommonObjectMapper;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

public class SimpleScopedJaxRsClient<T> extends AbstractJaxRsClient implements Consumer<LoadingCache<AbstractJaxRsClient.WebTargetConfig, ResteasyWebTarget>> {
    private final Class<T> type;
    private LoadingCache<WebTargetConfig, T> cache;

    public SimpleScopedJaxRsClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient httpClient,
            CommonObjectMapper mapper,
            Class<T> type) {
        super(props, meterRegistry, rateLimiterRegistry, httpClient, Suppliers.ofInstance(mapper));
        this.type = Objects.requireNonNull(type);
    }
    public SimpleScopedJaxRsClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpAsyncClient httpClient,
            CommonObjectMapper mapper,
            Class<T> type) {
        super(props, meterRegistry, rateLimiterRegistry, httpClient, Suppliers.ofInstance(mapper));
        this.type = Objects.requireNonNull(type);
    }
    public SimpleScopedJaxRsClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient httpClient,
            Supplier<ObjectMapper> mapper,
            Class<T> type) {
        super(props, meterRegistry, rateLimiterRegistry, httpClient, mapper);
        this.type = Objects.requireNonNull(type);
    }
    public SimpleScopedJaxRsClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpAsyncClient httpClient,
            Supplier<ObjectMapper> mapper,
            Class<T> type) {
        super(props, meterRegistry, rateLimiterRegistry, httpClient, mapper);
        this.type = Objects.requireNonNull(type);
    }
    @Override
    public void accept(LoadingCache<WebTargetConfig, ResteasyWebTarget> value) {
        cache = new BlockhoundLoadingCacheWrapper<>(CacheBuilder.newBuilder().build(new CacheLoader<WebTargetConfig, T>() {
            @Override
            public T load(WebTargetConfig key) {
                return value.getUnchecked(key).proxy(type);
            }
        }));
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        asFlux().subscribe(this);
    }
    public T proxy(UriBasedServiceInfo ups) {
        DynamicPropertyFactory pf = props.factory();

        //
        // ~ by default reasonable convention
        //

        Property<Integer> connectionTimeout = pf.get(ups.getId() + "." + props.TCP_CONNECTION_TIMEOUT.getKey(), int.class);
        Property<Integer> socketTimeout = pf.get(ups.getId() + "." + props.TCP_SOCKET_TIMEOUT.getKey(), int.class);

        return cache.getUnchecked(new WebTargetConfig(
                ups,
                connectionTimeout.orElse((int) props.TCP_CONNECTION_TIMEOUT.get().toSeconds()),
                socketTimeout.orElse((int) props.TCP_SOCKET_TIMEOUT.get().toSeconds())) //
        );
    }
    public T proxy(UriBasedServiceInfo ups, Property<Integer> connectionTimeout, Property<Integer> socketTimeout) {
        return cache.getUnchecked(new WebTargetConfig(ups, connectionTimeout, socketTimeout));
    }
}
