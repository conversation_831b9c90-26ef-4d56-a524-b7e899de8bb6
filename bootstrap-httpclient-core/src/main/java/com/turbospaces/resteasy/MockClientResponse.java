package com.turbospaces.resteasy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

import org.apache.http.HttpStatus;
import org.jboss.resteasy.specimpl.BuiltResponse;
import org.jboss.resteasy.spi.MarshalledEntity;

import com.fasterxml.jackson.databind.ObjectMapper;

public class MockClientResponse<T> extends BuiltResponse {
    private final MarshalledEntity<T> wrapper;

    private MockClientResponse(MarshalledEntity<T> wrapper) {
        super();
        this.wrapper = Objects.requireNonNull(wrapper);
        setEntity(wrapper.getEntity());
    }
    private MockClientResponse(MarshalledEntity<T> wrapper, int status) {
        this(wrapper);
        setStatus(status);
    }
    @Override
    protected InputStream getInputStream() {
        return new ByteArrayInputStream(wrapper.getMarshalledBytes());
    }

    public static <T> MockClientResponse<T> ok(ObjectMapper mapper, T entity) throws IOException {
        return newInstance(mapper, entity, HttpStatus.SC_OK);
    }
    public static <T> MockClientResponse<T> newInstance(ObjectMapper mapper, T entity, int status) throws IOException {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            mapper.writeValue(out, entity);
            out.flush();

            return new MockClientResponse<>(new MarshalledEntity<T>() {
                @Override
                public byte[] getMarshalledBytes() {
                    return out.toByteArray();
                }
                @Override
                public T getEntity() {
                    return entity;
                }
            }, status);
        }
    }
}
