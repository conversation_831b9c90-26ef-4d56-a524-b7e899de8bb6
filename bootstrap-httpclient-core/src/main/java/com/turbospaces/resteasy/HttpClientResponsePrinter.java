package com.turbospaces.resteasy;

import java.io.IOException;

import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientResponseContext;

public interface HttpClientResponsePrinter extends HttpClientBodyPrinter {
    void writeStart(ClientRequestContext req, ClientResponseContext resp) throws IOException;
    void writeUri(ClientRequestContext context, ClientResponseContext resp) throws IOException;
    void writeStatus(ClientRequestContext req, ClientResponseContext resp) throws IOException;
    void writeHeaders(ClientRequestContext req, ClientResponseContext resp) throws IOException;
    void writeCookies(ClientRequestContext req, ClientResponseContext resp) throws IOException;
    void writeBody(ClientRequestContext req, ClientResponseContext resp) throws IOException;

    void doOutPut(ClientRequestContext req, ClientResponseContext resp);
}
