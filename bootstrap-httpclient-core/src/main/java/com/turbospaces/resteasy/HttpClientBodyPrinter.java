package com.turbospaces.resteasy;

import org.jboss.resteasy.client.jaxrs.internal.ClientInvocation;
import org.jboss.resteasy.client.jaxrs.internal.ClientRequestContextImpl;
import org.jboss.resteasy.client.jaxrs.internal.proxy.ClientInvoker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.ws.rs.client.ClientRequestContext;

public interface HttpClientBodyPrinter {
    Logger LOGGING_FILTER_LOGGER = LoggerFactory.getLogger(HttpClientBodyPrinter.class.getPackage().getName() + "." + "ClientLoggingFilter");

    default ClientInvoker clientInvoker(ClientRequestContext requestContext) {
        ClientRequestContextImpl impl = (ClientRequestContextImpl) requestContext;
        ClientInvocation invocation = impl.getInvocation();
        return invocation.getClientInvoker();
    }
}
