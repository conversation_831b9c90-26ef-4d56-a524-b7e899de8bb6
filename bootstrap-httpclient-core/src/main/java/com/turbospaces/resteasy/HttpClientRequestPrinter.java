package com.turbospaces.resteasy;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import jakarta.ws.rs.client.ClientRequestContext;

public interface HttpClientRequestPrinter extends HttpClientBodyPrinter {
    void writeStart(ClientRequestContext context) throws IOException;

    Map<String, List<String>> writeUri(ClientRequestContext context) throws IOException;

    void writeQueryParams(ClientRequestContext context, Map<String, List<String>> query) throws IOException;

    void writeHeaders(ClientRequestContext context) throws IOException;

    void writeCookies(ClientRequestContext context) throws IOException;

    void writeBody(ClientRequestContext context) throws IOException;

    void doOutPut(ClientRequestContext context);
}
