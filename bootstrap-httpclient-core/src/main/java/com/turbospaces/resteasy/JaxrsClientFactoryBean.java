package com.turbospaces.resteasy;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.function.Consumer;

import org.apache.http.HttpException;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.Configurable;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.nio.ContentEncoder;
import org.apache.http.nio.IOControl;
import org.apache.http.nio.protocol.HttpAsyncRequestProducer;
import org.apache.http.nio.protocol.HttpAsyncResponseConsumer;
import org.apache.http.protocol.HttpContext;
import org.jboss.resteasy.client.jaxrs.ClientHttpEngine;
import org.jboss.resteasy.client.jaxrs.engines.URLConnectionEngine;
import org.jboss.resteasy.client.jaxrs.internal.ClientInvocation;
import org.jboss.resteasy.client.jaxrs.internal.ResteasyClientBuilderImpl;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.jakarta.rs.json.JacksonJsonProvider;
import com.fasterxml.jackson.jakarta.rs.xml.JacksonXMLProvider;
import com.github.robtimus.obfuscation.jackson.databind.ObfuscationModule;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.ext.ContextResolver;
import jakarta.ws.rs.ext.Provider;

public class JaxrsClientFactoryBean extends AbstractFactoryBean<ResteasyClientBuilderImpl> {
    private final ApplicationProperties props;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final Object httpClient;
    private ObjectMapper mapper;
    private Consumer<RequestConfig.Builder> requestConfig;
    private final XmlMapper xmlMapper;
    private final DefaultPlatformExecutorService executor;

    public JaxrsClientFactoryBean(ApplicationProperties props, RateLimiterRegistry rateLimiterRegistry, 
                                  Object httpClient, DefaultPlatformExecutorService executor) {
        this.props = Objects.requireNonNull(props);
        this.rateLimiterRegistry = Objects.requireNonNull(rateLimiterRegistry);
        this.httpClient = Objects.requireNonNull(httpClient);
        this.executor = Objects.requireNonNull(executor);

        setSingleton(false);

        mapper = new ObjectMapper();
        mapper.setDateFormat(new StdDateFormat());
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.setSerializationInclusion(Include.NON_NULL);
        mapper.registerModule(ObfuscationModule.defaultModule());

        xmlMapper = new XmlMapper();
        xmlMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        xmlMapper.setSerializationInclusion(Include.NON_NULL);
        xmlMapper.registerModule(ObfuscationModule.defaultModule());
    }
    public void setMapper(ObjectMapper mapper) {
        this.mapper = mapper;
    }
    public void setRequestConfig(Consumer<RequestConfig.Builder> requestConfig) {
        this.requestConfig = requestConfig;
    }
    @Override
    public Class<?> getObjectType() {
        return ResteasyClientBuilderImpl.class;
    }

    @Override
    @SuppressWarnings({ "deprecation", "removal" })
    protected ResteasyClientBuilderImpl createInstance() throws Exception {
        RootClientFilter rootFilter = new RootClientFilter(props);
        RateLimiterApiClientFilter rateLimiterApiClientFilter = new RateLimiterApiClientFilter(props, rateLimiterRegistry);

        ResteasyJacksonProvider jackson = new ResteasyJacksonProvider(mapper);
        ResteasyJacksonXmlProvider jacksonXml = new ResteasyJacksonXmlProvider(xmlMapper);

        ClientHttpEngine httpEngine = new URLConnectionEngine();
        if (httpClient instanceof CloseableHttpClient sync) {
            httpEngine = new org.jboss.resteasy.client.jaxrs.engines.ApacheHttpClient43Engine(sync, false) {
                @Override
                protected void loadHttpMethod(ClientInvocation request, HttpRequestBase httpMethod) throws Exception {
                    super.loadHttpMethod(request, httpMethod);
                    if (Objects.nonNull(requestConfig)) {
                        RequestConfig defaultRequestConfig = httpMethod.getConfig();
                        if (Objects.isNull(defaultRequestConfig)) {
                            defaultRequestConfig = ((Configurable) sync).getConfig();
                        }

                        RequestConfig.Builder rcfgb = RequestConfig.copy(defaultRequestConfig);
                        rcfgb.setRedirectsEnabled(true);
                        requestConfig.accept(rcfgb);

                        httpMethod.setConfig(rcfgb.build());
                    }
                }
            };
        } else if (httpClient instanceof CloseableHttpAsyncClient async) {
            httpEngine = new org.jboss.resteasy.client.jaxrs.engines.ApacheHttpAsyncClient4Engine(new CloseableHttpAsyncClient() {
                @Override
                public <T> Future<T> execute(
                        HttpAsyncRequestProducer requestProducer,
                        HttpAsyncResponseConsumer<T> responseConsumer,
                        FutureCallback<T> callback) {
                    return this.execute(requestProducer, responseConsumer, HttpClientContext.create(), callback);
                }
                @Override
                public <T> Future<T> execute(
                        HttpAsyncRequestProducer requestProducer,
                        HttpAsyncResponseConsumer<T> responseConsumer,
                        HttpContext context,
                        FutureCallback<T> callback) {
                    return async.execute(new HttpAsyncRequestProducer() {
                        @Override
                        public HttpHost getTarget() {
                            return requestProducer.getTarget();
                        }
                        @Override
                        public HttpRequest generateRequest() throws IOException, HttpException {
                            HttpRequest toReturn = requestProducer.generateRequest();
                            if (toReturn instanceof HttpRequestBase base) {
                                if (Objects.nonNull(requestConfig)) {
                                    RequestConfig defaultRequestConfig = (RequestConfig) context.getAttribute(HttpClientContext.REQUEST_CONFIG);

                                    RequestConfig.Builder rcfgb = RequestConfig.copy(defaultRequestConfig);
                                    rcfgb.setRedirectsEnabled(true);
                                    requestConfig.accept(rcfgb);

                                    base.setConfig(rcfgb.build());
                                }
                            }
                            return toReturn;
                        }
                        @Override
                        public void produceContent(ContentEncoder encoder, IOControl ioControl) throws IOException {
                            requestProducer.produceContent(encoder, ioControl);
                        }
                        @Override
                        public void requestCompleted(HttpContext httpContext) {
                            requestProducer.requestCompleted(httpContext);
                        }
                        @Override
                        public void failed(Exception ex) {
                            requestProducer.failed(ex);
                        }
                        @Override
                        public boolean isRepeatable() {
                            return requestProducer.isRepeatable();
                        }
                        @Override
                        public void resetRequest() throws IOException {
                            requestProducer.resetRequest();
                        }
                        @Override
                        public void close() throws IOException {
                            requestProducer.close();
                        }
                    }, responseConsumer, context, callback);
                }
                @Override
                public void start() {

                }
                @Override
                public void close() throws IOException {

                }
                @Override
                public boolean isRunning() {
                    return async.isRunning();
                }
            }, false);
        }

        return (ResteasyClientBuilderImpl) new ResteasyClientBuilderImpl()
                .httpEngine(httpEngine)
                .executorService(executor)
                .register(rootFilter)
                .register(rateLimiterApiClientFilter)
                .register(jackson)
                .register(jacksonXml);
    }

    @Provider
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    private static class ResteasyJacksonProvider extends JacksonJsonProvider implements ContextResolver<ObjectMapper> {
        private final ObjectMapper mapper;

        public ResteasyJacksonProvider(ObjectMapper mapper) {
            super(mapper);
            this.mapper = Objects.requireNonNull(mapper);
        }
        @Override
        public ObjectMapper getContext(Class<?> type) {
            return mapper;
        }
    }

    @Provider
    @Produces({ MediaType.APPLICATION_XML, MediaType.TEXT_XML })
    @Consumes({ MediaType.APPLICATION_XML, MediaType.TEXT_XML })
    public static class ResteasyJacksonXmlProvider extends JacksonXMLProvider implements ContextResolver<ObjectMapper> {
        private final XmlMapper xmlMapper;

        public ResteasyJacksonXmlProvider(XmlMapper xmlMapper) {
            super(xmlMapper);
            xmlMapper.registerModule(ObfuscationModule.defaultModule());
            this.xmlMapper = Objects.requireNonNull(xmlMapper);
        }
        @Override
        public ObjectMapper getContext(Class<?> type) {
            return xmlMapper;
        }
    }
}
