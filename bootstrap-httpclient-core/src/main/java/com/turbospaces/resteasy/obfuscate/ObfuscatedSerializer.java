package com.turbospaces.resteasy.obfuscate;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

public final class ObfuscatedSerializer extends JsonSerializer<Object> {
    private final BeanProperty property;
    private final JsonSerializer<Object> serializer;

    ObfuscatedSerializer(BeanProperty property, JsonSerializer<Object> serializer) {
        this.property = property;
        this.serializer = serializer;
    }

    @Override
    public void serialize(Object object, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (object instanceof String) {
            gen.writeString(StringUtils.repeat("*", ((String) object).length()));
        } else {
            // this is not likely to happen cause all values are obfuscated already and comes as string
            JsonSerializer<Object> actualSerializer = serializer != null
                    ? serializer
                    : serializers.findTypedValueSerializer(object.getClass(), true, property);
            actualSerializer.serialize(object, gen, serializers);
        }
    }
}
