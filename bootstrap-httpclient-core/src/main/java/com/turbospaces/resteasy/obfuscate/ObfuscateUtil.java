package com.turbospaces.resteasy.obfuscate;

import java.io.IOException;
import java.util.List;
import java.util.ListIterator;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.github.robtimus.obfuscation.Obfuscated;

public class ObfuscateUtil {
    @SuppressWarnings({ "rawtypes", "serial" })
    public static SimpleModule LOGGING_OBFUSCATED_MODULE = new SimpleModule()
            // this serializer is userfull for ObjectMappers without ObfuscationModule.defaultModule() registered
            .addSerializer(Obfuscated.class, new JsonSerializer<Obfuscated>() {
                @Override
                public void serialize(Obfuscated value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                    gen.writeString(value.toString());
                }
            })
            // this setting is used for ObjectMappers with ObfuscationModule.defaultModule() registered
            .setSerializerModifier(new BeanSerializerModifier() {
                @Override
                public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc, List<BeanPropertyWriter> beanProperties) {
                    List<BeanPropertyWriter> properties = super.changeProperties(config, beanDesc, beanProperties);
                    for (ListIterator<BeanPropertyWriter> i = properties.listIterator(); i.hasNext(); ) {
                        BeanPropertyWriter property = i.next();
                        if (property.getType().getRawClass() == Obfuscated.class) {
                            property = new ObfuscatedBeanPropertyWriter(property);
                            i.set(property);
                        }
                    }
                    return properties;
                }
            });
}
