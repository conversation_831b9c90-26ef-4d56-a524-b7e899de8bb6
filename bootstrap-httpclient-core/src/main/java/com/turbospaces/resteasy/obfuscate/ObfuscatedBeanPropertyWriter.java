package com.turbospaces.resteasy.obfuscate;

import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;

public final class ObfuscatedBeanPropertyWriter extends BeanPropertyWriter {

    private static final long serialVersionUID = 1L;

    public ObfuscatedBeanPropertyWriter(BeanPropertyWriter base) {
        super(base);
        _serializer = new ObfuscatedSerializer(base, base.getSerializer());
    }
}
