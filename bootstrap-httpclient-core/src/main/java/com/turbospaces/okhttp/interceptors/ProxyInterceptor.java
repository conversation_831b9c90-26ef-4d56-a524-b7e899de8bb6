package com.turbospaces.okhttp.interceptors;

import java.io.IOException;
import java.net.URI;
import java.util.Optional;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.UrlUtils;

import lombok.RequiredArgsConstructor;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

@RequiredArgsConstructor
public class ProxyInterceptor implements Interceptor {
    private final ApplicationProperties props;

    @Override
    public Response intercept(Chain chain) throws IOException {
        var request = chain.request();
        var originalUri = request.url().uri().toString();

        Optional<URI> mockProxy = UrlUtils.proxyMockRequest(props, originalUri);
        if (mockProxy.isPresent()) {
            HttpUrl proxyUrl = HttpUrl.get(mockProxy.get());
            if (proxyUrl != null) {
                Request proxyRequest = request.newBuilder().url(proxyUrl).build();
                return chain.proceed(proxyRequest);
            }
        }

        Optional<URI> proxy = UrlUtils.proxyRequest(props, originalUri);
        if (proxy.isPresent()) {
            HttpUrl proxyUrl = HttpUrl.get(proxy.get());
            if (proxyUrl != null) {
                Request proxyRequest = request.newBuilder().url(proxyUrl).build();
                return chain.proceed(proxyRequest);
            }
        }

        return chain.proceed(request);
    }
}
