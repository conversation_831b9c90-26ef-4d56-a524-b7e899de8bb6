package com.turbospaces.okhttp.interceptors;

import okhttp3.Interceptor;
import okhttp3.Response;
import org.apache.http.protocol.HTTP;

import java.io.IOException;

/**
 * Removes {@code Content-Length} and
 * {@code Transfer-Encoding} headers from the request if present 'SOAPAction' header.
 * Necessary, because some SAAJ and other SOAP implementations set
 * these headers themselves, and HttpClient throws an exception if they have been set.
 */
public class RemoveSoapHeadersInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        var request = chain.request();
        if (request.header("SOAPAction") != null) {
            var requestBuilder = request.newBuilder();
            if (request.header(HTTP.TRANSFER_ENCODING) != null) {
                requestBuilder.removeHeader(HTTP.TRANSFER_ENCODING);
            }
            if (request.header(HTTP.CONTENT_LEN) != null) {
                requestBuilder.removeHeader(HTTP.CONTENT_LEN);
            }
            return chain.proceed(requestBuilder.build());
        }

        return chain.proceed(request);
    }
}
