package com.turbospaces.okhttp;

import java.util.List;
import java.util.Optional;

import com.turbospaces.http.UnexpectedHttpStatusException;

import lombok.Getter;
import okhttp3.Headers;
import okhttp3.Response;

@Getter
public class UnexpectedHttpClientException extends UnexpectedHttpStatusException {
    private final Headers headers;

    public UnexpectedHttpClientException(Response response, String entity) {
        super(String.format("%s %s %s", response.protocol(), response.code(), response.message()),
                entity,
                response.code());
        this.headers = response.headers();
    }
    @Override
    public List<String> getHeaderValues(String key) {
        return headers.values(key);
    }
    @Override
    public Optional<String> getFirstHeaderValue(String key) {
        // Returns the last value corresponding to the specified field, or null.
        return Optional.ofNullable(headers.get(key));
    }
}
