package com.turbospaces.okhttp;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.okhttp.interceptors.ProxyInterceptor;
import com.turbospaces.okhttp.interceptors.RemoveSoapHeadersInterceptor;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.okhttp3.OkHttpConnectionPoolMetrics;
import io.micrometer.core.instrument.binder.okhttp3.OkHttpMetricsEventListener;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import okhttp3.Request;

public class OkHttpClientFactoryBean extends AbstractFactoryBean<OkHttpClient> implements BeanNameAware {
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected String beanName;

    public OkHttpClientFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        setSingleton(true);
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = name;
    }
    @Override
    public Class<?> getObjectType() {
        return OkHttpClient.class;
    }
    @Override
    protected OkHttpClient createInstance() throws Exception {
        var builder = new OkHttpClient.Builder();
        var maxIdleConnections = props.TCP_CONNECTION_CLOSE_IDLE_IMMEDIATELY.get() ? 0 : props.KAFKA_MAX_WORKERS.get();
        var connectionPool = new ConnectionPool(
                maxIdleConnections,
                props.TCP_KEEP_ALIVE_TIMEOUT.get().toMillis(),
                TimeUnit.MILLISECONDS
        );

        var dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(props.HTTP_POOL_MAX_SIZE.get());
        dispatcher.setMaxRequestsPerHost(props.HTTP_POOL_MAX_PER_ROUTE.get());

        builder.addInterceptor(new ProxyInterceptor(props))
                .addInterceptor(new RemoveSoapHeadersInterceptor())
                .retryOnConnectionFailure(true)
                .connectionPool(connectionPool)
                .dispatcher(dispatcher)
                .readTimeout(props.TCP_SOCKET_TIMEOUT.get())
                .connectTimeout(props.TCP_CONNECTION_TIMEOUT.get())
                .eventListener(OkHttpMetricsEventListener
                        .builder(meterRegistry, "httpcomponents.httpclient.request")
                        .uriMapper(this::getUri)
                        .build());

        new OkHttpConnectionPoolMetrics(connectionPool, "okhttp.pool", Collections.emptyList(), maxIdleConnections).bindTo(meterRegistry);

        return builder.build();
    }
    @SuppressWarnings("deprecation")
    private String getUri(Request httpRequest) {
        List<Pattern> mask = props.HTTP_METRICS_OUTBOUND_PATH_MASK.get();
        String uriPattern = httpRequest.header(io.micrometer.core.instrument.binder.httpcomponents.DefaultUriMapper.URI_PATTERN_HEADER);
        if (StringUtils.isNotEmpty(uriPattern)) {
            return uriPattern;
        }

        String uri = httpRequest.url().uri().toString();
        if (CollectionUtils.isNotEmpty(mask)) {
            Optional<String> mapping = mask.stream().filter(p -> p.asPredicate().test(uri)).map(Pattern::pattern).findAny();
            if (mapping.isPresent()) {
                return mapping.get();
            }
        }

        return URI.create(uri).getPath();
    }
}
