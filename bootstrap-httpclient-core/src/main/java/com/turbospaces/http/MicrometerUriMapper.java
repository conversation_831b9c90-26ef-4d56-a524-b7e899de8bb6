package com.turbospaces.http;

import java.net.URI;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.Header;
import org.apache.http.HttpRequest;

import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.binder.httpcomponents.DefaultUriMapper;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class MicrometerUriMapper implements Function<HttpRequest, String> {
    private final ApplicationProperties props;

    @Override
    @SuppressWarnings("deprecation")
    public String apply(HttpRequest httpRequest) {
        List<Pattern> mask = props.HTTP_METRICS_OUTBOUND_PATH_MASK.get();
        Header uriPattern = httpRequest.getLastHeader(DefaultUriMapper.URI_PATTERN_HEADER);
        if (uriPattern != null && uriPattern.getValue() != null) {
            return uriPattern.getValue();
        }

        String uri = httpRequest.getRequestLine().getUri();
        if (CollectionUtils.isNotEmpty(mask)) {
            Optional<String> mapping = mask.stream().filter(p -> p.asPredicate().test(uri)).map(Pattern::pattern).findAny();
            if (mapping.isPresent()) {
                return mapping.get();
            }
        }

        return URI.create(uri).getPath();
    }
}
