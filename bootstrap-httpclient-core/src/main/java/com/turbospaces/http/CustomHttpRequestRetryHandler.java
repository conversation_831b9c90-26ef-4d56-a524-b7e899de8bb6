package com.turbospaces.http;

import java.io.IOException;

import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.protocol.HttpContext;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomHttpRequestRetryHandler extends DefaultHttpRequestRetryHandler {
    public CustomHttpRequestRetryHandler(int retryCount, boolean requestSentRetryEnabled) {
        super(retryCount, requestSentRetryEnabled);
    }
    @Override
    public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
        var retry = super.retryRequest(exception, executionCount, context);
        if (retry) {
            log.warn("About to retry http request with error", exception);
        }
        return retry;
    }
}
