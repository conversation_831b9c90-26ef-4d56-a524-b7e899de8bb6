package com.turbospaces.http;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.StatusType;
import lombok.Getter;

public class UnexpectedJaxrsException extends UnexpectedHttpStatusException {
    @Getter
    private final MultivaluedMap<String, String> headers;

    public UnexpectedJaxrsException(Response resp) {
        super(resp.getStatusInfo().toString(), resp.hasEntity() ? resp.readEntity(String.class) : StringUtils.EMPTY, resp.getStatus());
        this.headers = resp.getStringHeaders();
    }
    public UnexpectedJaxrsException(StatusType statusInfo, String entity, MultivaluedMap<String, String> headers) {
        super(statusInfo.toString(), entity, statusInfo.getStatusCode());
        this.headers = headers;
    }
    @Override
    public List<String> getHeaderValues(String key) {
        return Objects.nonNull(headers) ? headers.get(key) : Collections.emptyList();
    }
    @Override
    public Optional<String> getFirstHeaderValue(String key) {
        return Objects.nonNull(headers) ? Optional.ofNullable(headers.getFirst(key)) : Optional.empty();
    }
}
