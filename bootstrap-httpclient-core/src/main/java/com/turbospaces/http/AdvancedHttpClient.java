package com.turbospaces.http;

import java.net.URISyntaxException;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpEntity;
import org.apache.http.HttpRequest;
import org.apache.http.NameValuePair;
import org.apache.http.StatusLine;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;

import com.fasterxml.jackson.core.JsonProcessingException;

public interface AdvancedHttpClient extends HttpClient {
    HttpPost preparePost(String path, HttpEntity httpEntity) throws URISyntaxException;

    HttpPut preparePut(String path, HttpEntity httpEntity) throws URISyntaxException, JsonProcessingException;

    HttpPost preparePost(String path, Object payload) throws URISyntaxException, JsonProcessingException;

    HttpRequest addBasicAuth(HttpRequest httpRequest);

    HttpGet prepareGet(String path, NameValuePair... params) throws URISyntaxException;

    <R> R send(HttpRequestBase req, Class<R> respClass) throws Exception;

    <R> Pair<StatusLine, R> sendNoStatusCheck(HttpRequestBase req, Class<R> respClass) throws Exception;
}
