package com.turbospaces.http;

import java.util.concurrent.TimeUnit;

import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import com.turbospaces.cfg.ApplicationProperties;

import jakarta.inject.Provider;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PoolingHttpClientConnectionManagerProvider implements Provider<PoolingHttpClientConnectionManager> {
    private final ApplicationProperties props;

    @Override
    public PoolingHttpClientConnectionManager get() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setDefaultMaxPerRoute(props.HTTP_POOL_MAX_PER_ROUTE.get());
        connectionManager.setMaxTotal(props.HTTP_POOL_MAX_SIZE.get());
        connectionManager.setDefaultSocketConfig(CloseableHttpClientFactoryBean.socketConfig(props).build());

        //
        // ~ https://stackoverflow.com/questions/70175836/apache-httpclient-throws-java-net-socketexception-connection-reset-if-i-use-it
        //
        int validateAfterInactivity = (int) props.TCP_CONNECTION_VALIDATE_AFTER_INACTIVITY_TIMEOUT.get().toMillis();
        if (validateAfterInactivity > 0) {
            connectionManager.setValidateAfterInactivity(validateAfterInactivity);
        }

        if (props.TCP_CONNECTION_CLOSE_IDLE_IMMEDIATELY.get()) {
            connectionManager.closeIdleConnections(0, TimeUnit.MICROSECONDS);
        }

        return connectionManager;
    }
}
