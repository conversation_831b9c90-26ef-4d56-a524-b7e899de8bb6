package com.turbospaces.http;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.SocketConfig;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CloseableHttpClientFactoryBean extends AbstractFactoryBean<CloseableHttpClient> implements BeanNameAware {
    private final MeterRegistry meterRegistry;
    private final Provider<PoolingHttpClientConnectionManager> connectionManager;
    private final Provider<HttpClientBuilder> builder;
    private String beanName;

    public CloseableHttpClientFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry) {
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.connectionManager = new PoolingHttpClientConnectionManagerProvider(props);
        this.builder = new CloseableHttpClientBuilderProvider(props, meterRegistry, connectionManager);
        setSingleton(true);
    }
    public CloseableHttpClientFactoryBean(
            MeterRegistry meterRegistry,
            Provider<PoolingHttpClientConnectionManager> connectionManager,
            Provider<HttpClientBuilder> builder) {
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.connectionManager = Objects.requireNonNull(connectionManager);
        this.builder = Objects.requireNonNull(builder);
        setSingleton(true);
    }
    @Override
    public Class<?> getObjectType() {
        return CloseableHttpClient.class;
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = name;
    }
    @Override
    @SuppressWarnings({ "deprecation" })
    protected CloseableHttpClient createInstance() throws Exception {
        CloseableHttpClient toReturn = builder.get().build();

        if (StringUtils.isNotEmpty(beanName)) {
            new io.micrometer.core.instrument.binder.httpcomponents.PoolingHttpClientConnectionManagerMetricsBinder(connectionManager.get(), "httpclient-" + beanName).bindTo(meterRegistry);
        }

        return toReturn;
    }
    @Override
    protected void destroyInstance(CloseableHttpClient instance) throws Exception {
        if (instance != null) {
            instance.close();
        }
    }

    public static ConnectionKeepAliveStrategy keepAliveStrategy(ApplicationProperties props) {
        return new DefaultConnectionKeepAliveStrategy() {
            @Override
            public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
                long keepAliveDuration = super.getKeepAliveDuration(response, context);
                if (keepAliveDuration <= 0) {
                    keepAliveDuration = props.TCP_KEEP_ALIVE_TIMEOUT.get().toMillis();
                }
                return keepAliveDuration;
            }
        };
    }
    public static RequestConfig.Builder requestConfig(ApplicationProperties props) {
        return requestConfig(props.TCP_CONNECTION_TIMEOUT, props.TCP_SOCKET_TIMEOUT);
    }
    public static RequestConfig.Builder requestConfig(long connectTimeout, long soTimeout) {
        int connectionTimeout = (int) TimeUnit.SECONDS.toMillis(connectTimeout);
        int socketTimeout = (int) TimeUnit.SECONDS.toMillis(soTimeout);

        RequestConfig.Builder requestCfg = RequestConfig.custom();
        requestCfg.setConnectTimeout(connectionTimeout);
        requestCfg.setSocketTimeout(socketTimeout);
        requestCfg.setCookieSpec(CookieSpecs.STANDARD);

        return requestCfg;
    }
    public static RequestConfig.Builder requestConfig(Supplier<Integer> connectTimeout, Supplier<Integer> soTimeout) {
        return requestConfig(connectTimeout.get(), soTimeout.get());
    }
    public static RequestConfig.Builder requestConfig(Property<Duration> connectTimeout, Property<Duration> soTimeout) {
        int connectTimeoutSecs = (int) connectTimeout.get().toSeconds();
        int soTimeoutSecs = (int) soTimeout.get().toSeconds();
        return requestConfig(connectTimeoutSecs, soTimeoutSecs);
    }
    public static SocketConfig.Builder socketConfig(ApplicationProperties props) {
        int socketTimeout = (int) props.TCP_SOCKET_TIMEOUT.get().toMillis();

        SocketConfig.Builder socketCfg = SocketConfig.custom();
        socketCfg.setSoKeepAlive(props.TCP_KEEP_ALIVE.get());
        socketCfg.setSoReuseAddress(props.TCP_REUSE_ADDRESS.get());
        socketCfg.setTcpNoDelay(props.TCP_NO_DELAY.get());
        socketCfg.setBacklogSize(props.TCP_SOCKET_BACKLOG.get());
        socketCfg.setSoTimeout(socketTimeout);

        return socketCfg;
    }

    /**
     * Removes {@code Content-Length} and
     * {@code Transfer-Encoding} headers from the request if present 'SOAPAction' header.
     * Necessary, because some SAAJ and other SOAP implementations set
     * these headers themselves, and HttpClient throws an exception if they have been set.
     */
    public static class RemoveSoapHeadersInterceptor implements HttpRequestInterceptor {
        @Override
        public void process(HttpRequest request, HttpContext context) {
            //
            // ~ Second condition is not required, but added for double check, these headers will be removed and added by HttpClient later.
            //
            if (request instanceof HttpEntityEnclosingRequest && request.getFirstHeader("SOAPAction") != null) {
                if (request.containsHeader(HTTP.TRANSFER_ENCODING)) {
                    request.removeHeaders(HTTP.TRANSFER_ENCODING);
                }
                if (request.containsHeader(HTTP.CONTENT_LEN)) {
                    request.removeHeaders(HTTP.CONTENT_LEN);
                }
            }
        }
    }
}
