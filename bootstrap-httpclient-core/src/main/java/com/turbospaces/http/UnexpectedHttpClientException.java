package com.turbospaces.http;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.Strings;
import org.apache.http.Header;
import org.apache.http.StatusLine;

import com.google.common.collect.ImmutableList;

import lombok.Getter;

public class UnexpectedHttpClientException extends UnexpectedHttpStatusException {
    @Getter
    private final Header[] headers;

    public UnexpectedHttpClientException(StatusLine status, String entity, Header[] headers) {
        super(status.toString(), entity, status.getStatusCode());
        this.headers = headers;
    }
    @Override
    public List<String> getHeaderValues(String key) {
        ImmutableList.Builder<String> l = ImmutableList.builder();
        if (Objects.nonNull(headers)) {
            for (Header header : headers) {
                if (Strings.CI.equals(header.getName(), key)) {
                    l.add(header.getValue());
                }
            }
        }
        return l.build();
    }
    @Override
    public Optional<String> getFirstHeaderValue(String key) {
        if (Objects.nonNull(headers)) {
            for (Header header : headers) {
                if (Strings.CI.equals(header.getName(), key)) {
                    return Optional.of(header.getValue());
                }
            }
        }
        return Optional.empty();
    }
}
