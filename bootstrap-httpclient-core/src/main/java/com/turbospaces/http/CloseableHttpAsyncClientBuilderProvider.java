package com.turbospaces.http;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpException;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.HttpResponse;
import org.apache.http.HttpResponseInterceptor;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.protocol.HttpContext;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.CloseableHttpAsyncClientFactoryBean.RemoveSoapHeadersInterceptor;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.httpcomponents.MicrometerHttpClientInterceptor;
import jakarta.inject.Provider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class CloseableHttpAsyncClientBuilderProvider implements Provider<HttpAsyncClientBuilder> {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final Provider<PoolingNHttpClientConnectionManager> connectionManager;

    @Override
    @SuppressWarnings("deprecation")
    public HttpAsyncClientBuilder get() {
        HttpAsyncClientBuilder builder = HttpAsyncClientBuilder.create();
        builder.addInterceptorFirst(new RemoveSoapHeadersInterceptor());

        //
        // ~ well we temporary capture the additional tags which we parse from HTTP headers via thread local
        //
        ThreadLocal<List<Tag>> tags = new ThreadLocal<>() {
            @Override
            protected List<Tag> initialValue() {
                return Collections.emptyList();
            }
        };
        Map<HttpContext, List<Tag>> map = new ConcurrentHashMap<>();

        MicrometerHttpClientInterceptor interceptor = new MicrometerHttpClientInterceptor(meterRegistry,
                new MicrometerUriMapper(props),
                new Iterable<Tag>() {
                    @Override
                    public Iterator<Tag> iterator() {
                        return tags.get().iterator();
                    }
                },
                false);

        builder.setConnectionManager(connectionManager.get());
        builder.setKeepAliveStrategy(CloseableHttpClientFactoryBean.keepAliveStrategy(props));
        builder.setDefaultRequestConfig(CloseableHttpClientFactoryBean.requestConfig(props).build());
        builder.addInterceptorFirst(new HttpRequestInterceptor() {
            @Override
            public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
                HttpRequestInterceptor delegate = interceptor.getRequestInterceptor();
                List<Tag> list = Lists.newLinkedList();

                for (Header header : request.getHeaders(HttpProto.HEADER_X_TAGS)) {
                    if (Objects.nonNull(header) && StringUtils.isNotEmpty(header.getValue())) {
                        String value = header.getValue();
                        Splitter.on(";").splitToStream(value).forEach(it -> {
                            var last = request.getLastHeader(it);
                            if (Objects.nonNull(last) && StringUtils.isNotEmpty(last.getValue())) {
                                list.add(Tag.of(it, last.getValue()));
                            }
                        });
                    }
                }

                map.put(context, list);
                delegate.process(request, context);
            }
        });
        builder.addInterceptorLast(new HttpRequestInterceptor() {
            @Override
            public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
                Optional<URI> mockProxy = UrlUtils.proxyMockRequest(props, request.getRequestLine().getUri());
                if (mockProxy.isPresent()) {
                    try {
                        URI originalUri = URI.create(request.getRequestLine().getUri());
                        URI proxyUri = new URI(props.MOCK_HTTP_PROXY.get());
                        HttpRoute proxyRoute = new HttpRoute(new HttpHost(proxyUri.getHost(), proxyUri.getPort(), proxyUri.getScheme()));

                        if (request instanceof HttpRequestBase base) {
                            base.setHeader(HttpProto.HEADER_X_REAL_HOST, originalUri.getHost());
                            base.setURI(new URIBuilder(proxyUri).setPath(originalUri.getPath()).setQuery(originalUri.getQuery()).build());
                            context.setAttribute(HttpClientContext.HTTP_ROUTE, proxyRoute);
                        }
                    } catch (URISyntaxException err) {
                        throw new IOException(err);
                    }
                }

                Optional<URI> proxy = UrlUtils.proxyRequest(props, request.getRequestLine().getUri());
                if (proxy.isPresent()) {
                    try {
                        URI originalUri = URI.create(request.getRequestLine().getUri());
                        URI proxyUri = new URI(props.HTTP_PROXY.get());
                        HttpRoute proxyRoute = new HttpRoute(new HttpHost(proxyUri.getHost(), proxyUri.getPort(), proxyUri.getScheme()));

                        if (request instanceof HttpRequestBase base) {
                            base.setURI(new URIBuilder(proxyUri).setPath(originalUri.getPath()).setQuery(originalUri.getQuery()).build());
                            context.setAttribute(HttpClientContext.HTTP_ROUTE, proxyRoute);
                        }
                    } catch (URISyntaxException err) {
                        throw new IOException(err);
                    }
                }
            }
        });
        builder.addInterceptorLast(new HttpResponseInterceptor() {
            @Override
            public void process(HttpResponse response, HttpContext context) throws HttpException, IOException {
                HttpResponseInterceptor delegate = interceptor.getResponseInterceptor();

                List<Tag> l = map.remove(context);
                if (Objects.nonNull(l)) {
                    tags.set(l);
                }

                try {
                    delegate.process(response, context);
                } finally {
                    tags.remove();
                }
            }
        });

        return builder;
    }
}
