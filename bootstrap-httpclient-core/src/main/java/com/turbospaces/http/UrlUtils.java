package com.turbospaces.http;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Iterables;
import com.google.common.collect.Multimap;
import com.turbospaces.cfg.ApplicationProperties;

import lombok.experimental.UtilityClass;

@UtilityClass
public class UrlUtils {
    @SuppressWarnings("deprecation")
    public static URL addParams(String urlStr, Map<String, String> params) throws URISyntaxException, MalformedURLException {
        URL url = new URL(urlStr);
        String urlWithoutQuery = urlStr.split("\\?")[0];
        Multimap<String, String> queryParams = decodeQuery(url.getQuery());

        URIBuilder successUri = new URIBuilder(urlWithoutQuery);
        queryParams.forEach(successUri::addParameter);
        if (params != null) {
            params.forEach(successUri::addParameter);
        }
        return successUri.build().toURL();
    }
    public static Multimap<String, String> decodeQuery(String query) {
        Multimap<String, String> toReturn = HashMultimap.create();
        if (StringUtils.isEmpty(query)) {
            return toReturn;
        }
        List<NameValuePair> qparams = URLEncodedUtils.parse(query, StandardCharsets.UTF_8);

        qparams.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getValue()))
                .forEach(pair -> toReturn.put(pair.getName(), pair.getValue()));

        return toReturn;
    }
    public static Optional<String> getParam(String name, Multimap<String, String> params) {
        if (params.containsKey(name)) {
            Collection<String> l = params.get(name);
            if (CollectionUtils.isNotEmpty(l)) {
                return Optional.of(StringUtils.lowerCase(Iterables.getOnlyElement(l)));
            }
        }

        return Optional.empty();
    }
    public static Optional<String> getLastParam(String name, Multimap<String, String> params) {
        if (params.containsKey(name)) {
            Collection<String> l = params.get(name);
            if (CollectionUtils.isNotEmpty(l)) {
                return Optional.of(StringUtils.lowerCase(Iterables.getLast(l)));
            }
        }

        return Optional.empty();
    }
    public static String toQuery(Multimap<String, String> multimap) {
        var params = multimap.entries().stream()
                .map(e -> new BasicNameValuePair(e.getKey(), e.getValue()))
                .collect(Collectors.toUnmodifiableList());

        return toQuery(params);
    }
    public static String toQuery(List<BasicNameValuePair> params) {
        return URLEncodedUtils.format(params, StandardCharsets.UTF_8);
    }
    public static Optional<URI> proxyRequest(ApplicationProperties props, String plainUri) {
        if (StringUtils.isEmpty(props.HTTP_PROXY.get())) {
            return Optional.empty();
        }

        //
        // ~ check by pattern
        //
        List<Pattern> patterns = props.HTTP_REQUEST_TO_PROXY_PATTERNS.get();
        if (CollectionUtils.isEmpty(patterns)) {
            return Optional.empty();
        }

        boolean apply = patterns.stream().anyMatch(p -> p.asPredicate().test(plainUri));
        if (BooleanUtils.isFalse(apply)) {
            return Optional.empty();
        }

        URI uri = URI.create(plainUri);
        URI proxyUri = URI.create(props.HTTP_PROXY.get());
        String orig = new URIBuilder().setScheme(uri.getScheme()).setHost(uri.getHost()).setPort(uri.getPort()).toString();

        return Optional.of(URI.create(new URIBuilder(uri)
                .setScheme(proxyUri.getScheme())
                .setHost(proxyUri.getHost())
                .setPort(proxyUri.getPort())
                .addParameter(HttpProto.PARAM_PROXY_REF, orig)
                .toString()));
    }
    public static Optional<URI> proxyMockRequest(ApplicationProperties props, String plainUri) {
        if (StringUtils.isEmpty(props.MOCK_HTTP_PROXY.get())) {
            return Optional.empty();
        }

        //
        // ~ check by pattern
        //
        List<Pattern> patterns = props.MOCK_HTTP_REQUEST_TO_PROXY_PATTERNS.get();
        if (CollectionUtils.isEmpty(patterns)) {
            return Optional.empty();
        }

        boolean apply = patterns.stream().anyMatch(p -> p.asPredicate().test(plainUri));
        if (BooleanUtils.isFalse(apply)) {
            return Optional.empty();
        }

        URI uri = URI.create(plainUri);
        URI proxyUri = URI.create(props.MOCK_HTTP_PROXY.get());

        return Optional.of(URI.create(new URIBuilder(uri)
                .setScheme(proxyUri.getScheme())
                .setHost(proxyUri.getHost())
                .setPort(proxyUri.getPort())
                .toString()));
    }

}
