package com.turbospaces.http;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.Header;
import org.apache.http.HttpClientConnection;
import org.apache.http.HttpException;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpExecutionAware;
import org.apache.http.client.methods.HttpRequestWrapper;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.execchain.ClientExecChain;
import org.apache.http.protocol.HttpContext;
import org.apache.http.protocol.HttpRequestExecutor;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.CloseableHttpClientFactoryBean.RemoveSoapHeadersInterceptor;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.httpcomponents.MicrometerHttpRequestExecutor;
import jakarta.inject.Provider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class CloseableHttpClientBuilderProvider implements Provider<HttpClientBuilder> {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final Provider<PoolingHttpClientConnectionManager> connectionManager;

    @Override
    @SuppressWarnings("deprecation")
    public HttpClientBuilder get() {
        HttpClientBuilder builder = new HttpClientBuilder() {
            @Override
            protected ClientExecChain decorateProtocolExec(ClientExecChain protocolExec) {
                return new ClientExecChain() {
                    @Override
                    public CloseableHttpResponse execute(
                            HttpRoute route,
                            HttpRequestWrapper request,
                            HttpClientContext clientContext,
                            HttpExecutionAware execAware) throws IOException, HttpException {
                        Optional<URI> mockProxy = UrlUtils.proxyMockRequest(props, request.getRequestLine().getUri());
                        if (mockProxy.isPresent()) {
                            URI originalUri = URI.create(request.getRequestLine().getUri());
                            HttpUriRequest proxyReq = RequestBuilder.copy(request.getOriginal())
                                    .setUri(mockProxy.get())
                                    .setHeader(HttpProto.HEADER_X_REAL_HOST, originalUri.getHost())
                                    .build();
                            try {
                                URI proxyUri = new URI(props.MOCK_HTTP_PROXY.get());
                                HttpRoute proxyRoute = new HttpRoute(new HttpHost(proxyUri.getHost(), proxyUri.getPort(), proxyUri.getScheme()));
                                return protocolExec.execute(proxyRoute, HttpRequestWrapper.wrap(proxyReq), clientContext, execAware);
                            } catch (URISyntaxException e) {
                                ExceptionUtils.wrapAndThrow(e);
                            }
                        }

                        try {
                            Optional<URI> proxy = UrlUtils.proxyRequest(props, request.getRequestLine().getUri());
                            if (proxy.isPresent()) {
                                HttpUriRequest proxyReq = RequestBuilder.copy(request.getOriginal()).setUri(proxy.get()).build();
                                URI proxyUri = new URI(props.HTTP_PROXY.get());
                                HttpRoute proxyRoute = new HttpRoute(new HttpHost(proxyUri.getHost(), proxyUri.getPort(), proxyUri.getScheme()));
                                return protocolExec.execute(proxyRoute, HttpRequestWrapper.wrap(proxyReq), clientContext, execAware);
                            }
                        } catch (Exception err) {
                            log.error("Failed to proxy request", err);
                        }

                        return protocolExec.execute(route, HttpRequestWrapper.wrap(request), clientContext, execAware);
                    }
                };
            }
        };

        builder.addInterceptorFirst(new RemoveSoapHeadersInterceptor());

        long evictIdleConnection = props.TCP_CONNECTION_EVICT_IDLE_TIMEOUT.get().toMillis();
        if (evictIdleConnection > 0) {
            builder.evictExpiredConnections();
            builder.evictIdleConnections(evictIdleConnection, TimeUnit.MILLISECONDS);
        }


        //
        // ~ well we temporary capture the additional tags which we parse from HTTP headers via thread local
        //
        ThreadLocal<List<Tag>> tags = new ThreadLocal<>() {
            @Override
            protected List<Tag> initialValue() {
                return Collections.emptyList();
            }
        };
        MicrometerHttpRequestExecutor delegate = MicrometerHttpRequestExecutor.builder(meterRegistry)
                .exportTagsForRoute(false)
                .tags(new Iterable<Tag>() {
                    @Override
                    public Iterator<Tag> iterator() {
                        return tags.get().iterator();
                    }
                })
                .uriMapper(new MicrometerUriMapper(props)).build();

        builder.setConnectionManager(connectionManager.get());
        builder.setRequestExecutor(new HttpRequestExecutor() {
            @Override
            public HttpResponse execute(HttpRequest request, HttpClientConnection conn, HttpContext context) throws IOException, HttpException {
                List<Tag> list = Lists.newLinkedList();
                for (Header header : request.getHeaders(HttpProto.HEADER_X_TAGS)) {
                    if (Objects.nonNull(header) && StringUtils.isNotEmpty(header.getValue())) {
                        String value = header.getValue();
                        Splitter.on(";").splitToStream(value).forEach(it -> {
                            var last = request.getLastHeader(it);
                            if (Objects.nonNull(last) && StringUtils.isNotEmpty(last.getValue())) {
                                list.add(Tag.of(it, last.getValue()));
                            }
                        });
                    }
                }

                try {
                    tags.set(list);
                    return delegate.execute(request, conn, context);
                } finally {
                    tags.remove();
                }
            }
        });
        builder.setKeepAliveStrategy(CloseableHttpClientFactoryBean.keepAliveStrategy(props));
        builder.setDefaultRequestConfig(CloseableHttpClientFactoryBean.requestConfig(props).build());

        if (props.HTTP_RETRY_REQUEST_ENABLED.get()) {
            builder.setRetryHandler(new CustomHttpRequestRetryHandler(
                    props.HTTP_RETRY_REQUEST_COUNT.get(),
                    props.HTTP_RETRY_ALREADY_SENT_REQUEST_ENABLED.get()));
        }

        return builder;
    }
}
