package org.springframework.cloud;

import java.util.function.Consumer;

import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.spring.secretmanager.SecretManagerTemplate;

public interface UPSFilter {
    String SECRET = "secret";

    char UNDERSCORE = '_';
    char DASH = '-';

    void accept(SecretName secretName, SecretManagerTemplate template, Consumer<GCPServiceInfo> consumer);
}
