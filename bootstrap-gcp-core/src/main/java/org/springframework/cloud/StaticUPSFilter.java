package org.springframework.cloud;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.spring.secretmanager.SecretManagerTemplate;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StaticUPSFilter implements UPSFilter {
    private final Multimap<String, String> upss = HashMultimap.create();

    public StaticUPSFilter(Iterable<String> upss) {
        for (String it : upss) {
            String ups = StringUtils.lowerCase(it).intern();

            //
            // ~ reasonable convention (designator) just in case
            //

            String alias1 = Joiner.on(UNDERSCORE).join(ListUtils.union(Splitter.on(DASH).trimResults().omitEmptyStrings().splitToList(ups), List.of("secret")));
            String alias2 = Joiner.on(DASH).join(ListUtils.union(Splitter.on(UNDERSCORE).trimResults().omitEmptyStrings().splitToList(ups), List.of("secret")));

            //
            // ~ just to mitigate risk of typo
            //
            String alias3 = ups.replace(DASH, UNDERSCORE);
            String alias4 = ups.replace(UNDERSCORE, DASH);

            this.upss.put(ups, alias1);
            this.upss.put(ups, alias2);
            this.upss.put(ups, ups); // ~ direct
            this.upss.put(ups, alias3);
            this.upss.put(ups, alias4);

            log.info("registered UPS: {} under aliases: {}", ups, this.upss.get(ups));
        }
    }
    @Override
    public void accept(SecretName secretName, SecretManagerTemplate template, Consumer<GCPServiceInfo> consumer) {
        String shortName = secretName.getSecret().trim().toLowerCase().intern();
        for (String ups : upss.keySet()) {
            Collection<String> aliases = upss.get(ups);

            log.trace("checking {} against: {} for ups: {}", shortName, aliases, ups);

            for (String alias : aliases) {
                if (shortName.equals(alias)) {
                    StopWatch stopWatch = StopWatch.createStarted();
                    String payload = template.getSecretString(shortName);
                    consumer.accept(new GCPServiceInfo(ups, payload));
                    stopWatch.stop();
                    log.debug("added ups: {} from GCP {}", ups, stopWatch);

                    break;
                }
            }
        }
    }
}
