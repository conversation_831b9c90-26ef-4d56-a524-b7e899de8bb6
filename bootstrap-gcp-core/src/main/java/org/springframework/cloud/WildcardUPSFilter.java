package org.springframework.cloud;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;

import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.apache.commons.lang3.time.StopWatch;

import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.spring.secretmanager.SecretManagerTemplate;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WildcardUPSFilter extends StaticUPSFilter {
    private final Multimap<String, Pattern> wildcardUpss = HashMultimap.create();
    private final Function<String, String> sanitizer;

    public WildcardUPSFilter(Iterable<String> upss, Iterable<Pattern> wildcard) {
        this(upss, wildcard, new Function<String, String>() {
            @Override
            public String apply(String input) {
                return input.replace(UNDERSCORE, DASH);
            }
        });
    }
    public WildcardUPSFilter(Iterable<String> upss, Iterable<Pattern> wildcard, Function<String, String> sanitizer) {
        super(upss);
        this.sanitizer = Objects.requireNonNull(sanitizer);

        for (Pattern it : wildcard) {
            String ups = StringUtils.lowerCase(it.pattern()).intern();
            //
            // ~ reasonable convention (designator) just in case
            //

            String alias1 = Joiner.on(UNDERSCORE).join(ListUtils.union(Splitter.on(DASH).trimResults().omitEmptyStrings().splitToList(ups), List.of("secret")));
            String alias2 = Joiner.on(DASH).join(ListUtils.union(Splitter.on(UNDERSCORE).trimResults().omitEmptyStrings().splitToList(ups), List.of("secret")));

            //
            // ~ just to mitigate risk of typo
            //
            String alias3 = ups.replace(DASH, UNDERSCORE);
            String alias4 = ups.replace(UNDERSCORE, DASH);

            this.wildcardUpss.put(ups, Pattern.compile(alias1));
            this.wildcardUpss.put(ups, Pattern.compile(alias2));
            this.wildcardUpss.put(ups, Pattern.compile(alias3));
            this.wildcardUpss.put(ups, Pattern.compile(alias4));

            log.info("registered wildcard UPS: {} under aliases: {}", ups, this.wildcardUpss.get(ups));
        }
    }
    @Override
    public void accept(SecretName secretName, SecretManagerTemplate template, Consumer<GCPServiceInfo> consumer) {
        MutableBoolean accepted = new MutableBoolean(false);
        super.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                accepted.setTrue();
                consumer.accept(t);
            }
        });

        if (accepted.isFalse()) {
            String shortName = secretName.getSecret().trim().toLowerCase().intern();

            boolean secretAccepted = false;
            for (String ups : wildcardUpss.keySet()) {
                Collection<Pattern> aliases = wildcardUpss.get(ups);

                log.trace("checking {} against: {}", shortName, aliases);

                for (Pattern alias : aliases) {
                    if (alias.asPredicate().test(shortName)) {
                        StopWatch stopWatch = StopWatch.createStarted();
                        String payload = template.getSecretString(shortName);
                        String name = shortName;

                        //
                        // ~ remove secret (small house-keeping)
                        //
                        for (char separator : new char[] { DASH, UNDERSCORE }) {
                            List<String> l = new ArrayList<>(Splitter.on(separator).splitToList(shortName));
                            if (l.remove(SECRET)) {
                                name = Joiner.on(separator).join(l);
                                break;
                            }
                        }

                        consumer.accept(new GCPServiceInfo(sanitizer.apply(name), payload));
                        secretAccepted = true;
                        stopWatch.stop();
                        log.debug("added ups: {} from GCP {}", name, stopWatch);
                        break;
                    }
                }
                if (secretAccepted) {
                    break;
                }
            }
        }
    }
}
