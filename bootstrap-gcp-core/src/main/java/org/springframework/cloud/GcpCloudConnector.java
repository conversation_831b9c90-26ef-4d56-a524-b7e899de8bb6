package org.springframework.cloud;

import java.io.IOException;
import java.lang.reflect.UndeclaredThrowableException;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.reactivestreams.Publisher;
import org.reactivestreams.Subscriber;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import com.google.cloud.secretmanager.v1.ProjectName;
import com.google.cloud.secretmanager.v1.Secret;
import com.google.cloud.secretmanager.v1.SecretManagerServiceClient;
import com.google.cloud.secretmanager.v1.SecretManagerServiceClient.ListSecretsPagedResponse;
import com.google.cloud.secretmanager.v1.SecretManagerServiceSettings;
import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.spring.core.DefaultGcpProjectIdProvider;
import com.google.cloud.spring.secretmanager.SecretManagerTemplate;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.common.PlatformUtil;

import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
public class GcpCloudConnector extends ConfigurableCloudConnector {
    private final AtomicBoolean slowDownAccess = new AtomicBoolean(false);
    private final RateLimiter limiter = RateLimiter.create(1);
    private final ThreadPoolTaskScheduler timer = new ThreadPoolTaskScheduler();
    private final DefaultGcpProjectIdProvider project = new DefaultGcpProjectIdProvider();
    private final Duration freq;
    private final UPSFilter filter;

    public GcpCloudConnector(Collection<String> upss) throws Exception {
        this(new StaticUPSFilter(upss));
    }
    public GcpCloudConnector(UPSFilter filter) throws Exception {
        this.freq = Duration.ofMinutes(1);
        this.filter = Objects.requireNonNull(filter);

        timer.setDaemon(true);
        timer.afterPropertiesSet();
    }
    @Override
    public void dispose() {
        try {
            super.dispose();
        } finally {
            timer.destroy();
        }
    }
    @Override
    protected Flux<Map<String, ServiceInfo>> readServices(RetryRegistry retryRegistry) throws Exception {
        //
        // ~ once (static lookup, so it is fast)
        //
        Map<String, ServiceInfo> fixed = super.readServices(retryRegistry).blockFirst();

        Callable<Map<String, ServiceInfo>> task = new Callable<>() {
            @Override
            public Map<String, ServiceInfo> call() {
                var factory = new ThreadFactoryBuilder();
                factory.setDaemon(true);
                factory.setNameFormat("secretmanager-executor-%d");

                //
                // ~ we don't want to even cache the client, it is just fine to create it on demand and use on purpose since we are not fetching secrets that
                // often
                //
                var executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), factory.build());
                var transport = SecretManagerServiceSettings.defaultHttpJsonTransportProviderBuilder().setExecutor(executor).build();
                try (var client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setTransportChannelProvider(transport).build())) {
                    var template = new SecretManagerTemplate(client, project);
                    ImmutableMap.Builder<String, ServiceInfo> map = ImmutableMap.builder();
                    map.putAll(fixed);

                    ProjectName projectId = ProjectName.of(project.getProjectId());
                    ListSecretsPagedResponse pagedResponse = client.listSecrets(projectId);
                    Iterable<Secret> iterateAll = pagedResponse.iterateAll();

                    List<String> all = Lists.newLinkedList();
                    List<String> accepted = Lists.newLinkedList();
                    iterateAll.forEach(new Consumer<Secret>() {
                        @Override
                        public void accept(Secret secret) {
                            SecretName secretName = SecretName.parse(secret.getName());
                            all.add(secretName.getSecret());

                            filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
                                @Override
                                public void accept(GCPServiceInfo kv) {
                                    String ups = kv.getId();
                                    String payload = kv.getValue();

                                    addServiceInfo(ups, StringUtils.trim(payload), map);
                                    accepted.add(ups);

                                    if (slowDownAccess.get()) {
                                        limiter.acquire();
                                    }
                                }
                            });
                        }
                    });

                    log.debug("accepted {} UPSs among: {}", accepted, all.size());

                    if (slowDownAccess.compareAndSet(false, true)) {
                        log.debug("will enable artificial rate limiter ...");
                    }

                    return map.build();
                } catch (IOException err) {
                    throw new UndeclaredThrowableException(err);
                } finally {
                    PlatformUtil.shutdownExecutor(executor, freq);
                }
            }
        };

        Map<String, ServiceInfo> initial = task.call(); // ~ block on current thread

        return Flux.from(new Publisher<Map<String, ServiceInfo>>() {
            @Override
            public void subscribe(Subscriber<? super Map<String, ServiceInfo>> subsciber) {
                subsciber.onNext(initial); // ~ emit immediately initial value
                //
                // ~ schedule periodic UPS refresh with fixed delay specifically
                //
                timer.getScheduledExecutor().scheduleWithFixedDelay(new Runnable() {
                    @Override
                    public void run() {
                        //
                        // ~ retry several times (with exponential back-off) before alerting to sentry
                        //
                        Retry retry = retryRegistry.retry("upss-refresh-action");
                        Callable<Map<String, ServiceInfo>> retryableTask = Retry.decorateCallable(retry, task);

                        try {
                            StopWatch stopWatch = StopWatch.createStarted();
                            subsciber.onNext(retryableTask.call());
                            stopWatch.stop();
                            log.debug("refreshed UPSs from GCP {}", stopWatch);
                        } catch (Throwable t) {
                            log.error(t.getMessage(), t); // ~ alert to sentry
                        }
                    }
                }, freq.toMillis(), freq.toMillis(), TimeUnit.MILLISECONDS);
            }
        });
    }
}
