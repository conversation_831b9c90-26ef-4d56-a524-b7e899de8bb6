package org.springframework.cloud;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;

import org.apache.commons.lang3.mutable.MutableObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.spring.secretmanager.SecretManagerTemplate;

class WildcardUPSFilterTest {
    SecretManagerTemplate template;

    @BeforeEach
    public void before() {
        template = Mockito.mock(SecretManagerTemplate.class);
        Mockito.when(template.getSecretString(ArgumentMatchers.anyString())).then(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                return Integer.toString(Objects.hash(invocation.getArguments()));
            }
        });
    }

    @Test
    public void secretShouldBeAcceptedOnlyOnce() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("part1-part2-bluedream-secret").build();
        WildcardUPSFilter filter = new WildcardUPSFilter(Collections.emptyList(), List.of(Pattern.compile("part1-*"), Pattern.compile("part1-part2-*"), Pattern.compile("part1-part2-bluedream")));
        AtomicInteger acceptedCount = new AtomicInteger(0);
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                acceptedCount.incrementAndGet();
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("part1-part2-bluedream", val1.getValue());
        Assertions.assertEquals(1, acceptedCount.intValue());
    }
    @Test
    public void raw() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("pg").build();
        WildcardUPSFilter filter = new WildcardUPSFilter(Collections.emptyList(), Collections.singletonList(Pattern.compile("pg")));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("pg", val1.getValue());
    }
    @Test
    public void withTildaSecretWildcard() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("ipg-bluedream-secret").build();
        WildcardUPSFilter filter = new WildcardUPSFilter(Collections.emptyList(), Collections.singleton(Pattern.compile("ipg-*")));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("ipg-bluedream", val1.getValue());
    }
    @Test
    public void withUnderscoreSecretWildcard() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("ipg_bluedream_secret").build();
        WildcardUPSFilter filter = new WildcardUPSFilter(
                Collections.emptyList(),
                Collections.singleton(Pattern.compile("ipg_*")),
                new Function<String, String>() {
                    @Override
                    public String apply(String t) {
                        return t;
                    }
                });
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("ipg_bluedream", val1.getValue());
    }
    @Test
    public void withTildaWildcard() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("ipg-bluedream-secret").build();
        WildcardUPSFilter filter = new WildcardUPSFilter(
                Collections.emptyList(),
                Collections.singleton(Pattern.compile("ipg_*")),
                new Function<String, String>() {
                    @Override
                    public String apply(String t) {
                        return t.replace(UPSFilter.DASH, UPSFilter.UNDERSCORE);
                    }
                });
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("ipg_bluedream", val1.getValue());
    }
    @Test
    public void withUnderscoreWildcard() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("ipg-bluedream-secret").build();
        WildcardUPSFilter filter = new WildcardUPSFilter(Collections.emptyList(), Collections.singleton(Pattern.compile("ipg-*")));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("ipg-bluedream", val1.getValue());
    }
}
