package org.springframework.cloud;

import java.util.Collections;
import java.util.Objects;
import java.util.function.Consumer;

import org.apache.commons.lang3.mutable.MutableObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.spring.secretmanager.SecretManagerTemplate;

class StaticUPSFilterTest {
    SecretManagerTemplate template;

    @BeforeEach
    public void before() {
        template = Mockito.mock(SecretManagerTemplate.class);
        Mockito.when(template.getSecretString(ArgumentMatchers.anyString())).then(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                return Integer.toString(Objects.hash(invocation.getArguments()));
            }
        });
    }

    @Test
    public void raw() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("pg").build();
        StaticUPSFilter filter = new StaticUPSFilter(Collections.singleton("pg"));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("pg", val1.getValue());
    }
    @Test
    public void withTildaSecret() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("pg-secret").build();
        StaticUPSFilter filter = new StaticUPSFilter(Collections.singleton("pg"));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("pg", val1.getValue());
    }
    @Test
    public void withUnderscoreSecret() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("pg_secret").build();
        StaticUPSFilter filter = new StaticUPSFilter(Collections.singleton("pg"));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("pg", val1.getValue());
    }
    @Test
    public void withTilda() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("pg-owner").build();
        StaticUPSFilter filter = new StaticUPSFilter(Collections.singleton("pg_owner"));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("pg_owner", val1.getValue());
    }
    @Test
    public void withUnderscore() {
        SecretName secretName = SecretName.newBuilder().setProject("prj").setSecret("pg_owner").build();
        StaticUPSFilter filter = new StaticUPSFilter(Collections.singleton("pg-owner"));
        MutableObject<String> val1 = new MutableObject<String>();
        filter.accept(secretName, template, new Consumer<GCPServiceInfo>() {
            @Override
            public void accept(GCPServiceInfo t) {
                val1.setValue(t.getId());
            }
        });

        Assertions.assertEquals("pg-owner", val1.getValue());
    }
}
