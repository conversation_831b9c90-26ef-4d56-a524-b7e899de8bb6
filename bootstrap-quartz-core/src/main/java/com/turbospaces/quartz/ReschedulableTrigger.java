package com.turbospaces.quartz;

import com.netflix.archaius.api.Property;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Trigger;

import java.util.function.Consumer;
import java.util.function.Function;

@Slf4j
public class ReschedulableTrigger<T> implements Trigger {
    private final Function<T, Trigger> triggerBuilder;
    @Getter
    @Delegate
    private Trigger quartzTrigger;
    private Property<T> frequency;

    public ReschedulableTrigger(Function<T, Trigger> triggerBuilder, Property<T> frequency) {
        this.triggerBuilder = triggerBuilder;
        this.frequency = frequency;
        this.quartzTrigger = triggerBuilder.apply(frequency.get());
    }

    private Trigger buildTrigger() {
        this.quartzTrigger = triggerBuilder.apply(frequency.get());
        return quartzTrigger;
    }


    public void onFrequencyChange(Consumer<Trigger> callback) {
        frequency.subscribe((freq) -> callback.accept(buildTrigger()));
    }
}
