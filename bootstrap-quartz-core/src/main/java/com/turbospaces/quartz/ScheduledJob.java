package com.turbospaces.quartz;

import java.time.Duration;
import java.util.function.Supplier;

import org.apache.commons.lang3.time.StopWatch;
import org.quartz.Job;
import org.quartz.JobBuilder;

import reactor.core.publisher.Mono;

public interface ScheduledJob extends Job {
    Supplier<Boolean> isEnabled();
    JobBuilder toJob();
    void schedule() throws Exception;
    Mono<StopWatch> runOnce() throws Exception;
    default void runOnceBlocking() throws Exception {
        runOnce().block(Duration.ofSeconds(30));
    }
}
