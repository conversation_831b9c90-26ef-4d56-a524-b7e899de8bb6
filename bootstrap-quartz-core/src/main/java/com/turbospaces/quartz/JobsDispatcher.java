package com.turbospaces.quartz;

import java.util.Date;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import org.apache.commons.lang3.time.StopWatch;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobListener;
import org.quartz.JobPersistenceException;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.Trigger.TriggerState;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.base.Suppliers;
import com.netflix.archaius.api.Property;
import com.turbospaces.common.PlatformUtil;

import reactor.core.publisher.Mono;

public interface JobsDispatcher {
    default StopWatch runOnceBlock(Scheduler scheduler, JobBuilder job, TriggerKey triggerKey) {
        Mono<StopWatch> flux = runOnce(scheduler, job, triggerKey);
        return flux.block();
    }
    default Mono<StopWatch> runOnce(Scheduler scheduler, JobBuilder job, TriggerKey triggerKey) {
        JobDetail jobDetail = job.storeDurably().build();
        return Mono.fromCallable(new Callable<StopWatch>() {
            @Override
            public StopWatch call() throws Exception {
                StopWatch stopWatch = StopWatch.createStarted();

                String k = "run-once-uuid";
                String v = PlatformUtil.randomUUID().toString();

                TriggerBuilder<Trigger> now = TriggerBuilder.newTrigger();
                now.withIdentity(triggerKey);
                now.startAt(new Date()); // ~ as of now
                now.forJob(jobDetail);
                now.usingJobData(k, v);

                try {
                    CountDownLatch latch = new CountDownLatch(1);
                    AtomicReference<Exception> jobExecutionError = new AtomicReference<>(null);
                    JobListener jobListener = new JobListener() {
                        @Override
                        public String getName() {
                            return now.toString();
                        }

                        @Override
                        public void jobToBeExecuted(JobExecutionContext context) {
                        }

                        @Override
                        public void jobExecutionVetoed(JobExecutionContext context) {
                        }

                        @Override
                        public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
                            if (context.getJobDetail().equals(jobDetail)) {
                                if (v.equals(context.getTrigger().getJobDataMap().get(k))) {
                                    if (jobException  instanceof Exception exception) {
                                        jobExecutionError.set(exception);
                                    }
                                    latch.countDown();
                                }
                            }
                        }
                    };

                    scheduler.getListenerManager().addJobListener(jobListener); // ~ add listener

                    scheduler.addJob(jobDetail, true);
                    scheduler.scheduleJob(now.build());
                    latch.await(); // ~ await for completion
                    if (jobExecutionError.get() != null) {
                        throw jobExecutionError.get();
                    }
                    scheduler.getListenerManager().removeJobListener(jobListener.getName()); // ~ remove listener

                    return stopWatch;
                } catch (Exception err) {
                    throw new RuntimeException(err);
                }
            }
        });
    }
    default void scheduleEnabledJob(Scheduler instance, JobBuilder job, Trigger trigger, Supplier<Boolean> runOnStart) throws SchedulerException {
        scheduleJob(instance, job, trigger, Suppliers.ofInstance(true), runOnStart);
    }
    default void scheduleJob(
            Scheduler instance,
            JobBuilder job,
            Trigger trigger,
            Supplier<Boolean> enabled,
            Supplier<Boolean> runOnStart) throws SchedulerException {
        JobDetail jobDetail = job.storeDurably().build();
        //
        // if there is not such class anymore
        //
        try {
            instance.getJobDetail(jobDetail.getKey());
        } catch (JobPersistenceException err) {
            if (err.getCause() instanceof ClassNotFoundException) {
                instance.deleteJob(jobDetail.getKey());
            }
        }

        Logger logger = LoggerFactory.getLogger(getClass());
        Trigger quartzTrigger = trigger instanceof ReschedulableTrigger
                ? ((ReschedulableTrigger<?>) trigger).getQuartzTrigger()
                : trigger;

        if (trigger instanceof ReschedulableTrigger<?> reschedulableTrigger) {
            reschedulableTrigger.onFrequencyChange(
                    (newTrigger) -> {
                        try {
                            logger.info("detected frequency changes in trigger, re-scheduling job={} to use new trigger={} now ...", jobDetail.getKey(), newTrigger);
                            instance.rescheduleJob(newTrigger.getKey(), newTrigger);
                        } catch (SchedulerException e) {
                            logger.error("Failed to reschedule job={} with new trigger={} due to: {}", jobDetail.getKey(), newTrigger, e.getMessage(), e);
                        }
                    }
            );
        }
        if (enabled instanceof Property<Boolean> prop) {
            prop.subscribe(new Consumer<Boolean>() {
                @Override
                public void accept(Boolean value) {
                    try {
                        if (Boolean.TRUE.equals(value)) {
                            scheduleJob(instance, job, trigger, Suppliers.ofInstance(value), runOnStart);
                        } else {
                            instance.pauseJob(jobDetail.getKey());
                        }
                    } catch (SchedulerException err) {
                        logger.error(err.getMessage(), err);
                    }
                }
            });
        }

        if (enabled.get()) {
            if (runOnStart.get()) {
                if (instance.checkExists(jobDetail.getKey())) {
                    instance.triggerJob(jobDetail.getKey());
                }
            }
        }

        // replace trigger if needed
        if (instance.checkExists(jobDetail.getKey())) {
            if (enabled.get()) {
                boolean reschedule = false;
                Trigger prev = instance.getTrigger(quartzTrigger.getKey());
                if (prev != null) {
                    if (prev.getClass().equals(quartzTrigger.getClass())) {
                        if (quartzTrigger instanceof SimpleTriggerImpl newTrigger) {
                            SimpleTriggerImpl prevTrigger = (SimpleTriggerImpl) prev;
                            if (newTrigger.getRepeatInterval() == prevTrigger.getRepeatInterval()) {
                                logger.debug("simple trigger = {} has not changed since last run ...", quartzTrigger.getKey());
                            } else {
                                reschedule = true;
                            }
                        } else if (quartzTrigger instanceof CronTriggerImpl newTrigger) {
                            CronTriggerImpl prevTrigger = (CronTriggerImpl) prev;
                            if (newTrigger.getCronExpression().equals(prevTrigger.getCronExpression())) {
                                logger.debug("cron trigger = {} has not changed since last run ...", quartzTrigger.getKey());
                            } else {
                                reschedule = true;
                            }
                        }
                    }
                }

                instance.resumeJob(jobDetail.getKey());

                if (reschedule) {
                    logger.info("detected changes in trigger, re-scheduling job={} to use new trigger={} now ...", jobDetail.getKey(), quartzTrigger);
                    instance.rescheduleJob(quartzTrigger.getKey(), quartzTrigger);
                } else {
                    TriggerState state = instance.getTriggerState(quartzTrigger.getKey());
                    logger.debug("trigger {} is currently is in state: {}", quartzTrigger.getKey(), state.name().toLowerCase());
                }
            } else {
                logger.info("pausing existing job = {}", jobDetail.getKey());
                instance.pauseJob(jobDetail.getKey());
            }
        } else {
            if (enabled.get()) {
                logger.info("scheduling new job = {} now", jobDetail.getKey());
                instance.scheduleJob(jobDetail, quartzTrigger);
            }
        }
    }

    default Trigger cronTriggerWithMisfireHandlingInstructionFireAndProceed(TriggerKey triggerKey, Supplier<String> cron) {
        return triggerOrRescedulable(
                freq -> TriggerBuilder.newTrigger()
                        .withIdentity(triggerKey)
                        .withSchedule(CronScheduleBuilder.cronSchedule(cron.get())
                                .withMisfireHandlingInstructionFireAndProceed())
                        .build(),
                cron
        );
    }

    default Trigger secondlyTriggerWithMisfireNowWithExistingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatSecondlyForever(freq).withMisfireHandlingInstructionNowWithExistingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger secondlyTriggerWithMisfireNowWithRemainingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatSecondlyForever(frequency.get()).withMisfireHandlingInstructionNowWithRemainingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger secondlyTriggerWithMisfireNextWithExistingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatSecondlyForever(frequency.get()).withMisfireHandlingInstructionNextWithExistingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger secondlyTriggerWithMisfireNextWithRemainingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatSecondlyForever(frequency.get()).withMisfireHandlingInstructionNextWithRemainingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger minutelyTriggerWithMisfireNowWithExistingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatMinutelyForever(frequency.get()).withMisfireHandlingInstructionNowWithExistingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger minutelyTriggerWithMisfireNowWithRemainingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatMinutelyForever(frequency.get()).withMisfireHandlingInstructionNowWithRemainingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger minutelyTriggerWithMisfireNextWithExistingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatMinutelyForever(frequency.get()).withMisfireHandlingInstructionNextWithExistingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger minutelyTriggerWithMisfireNextWithRemainingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatMinutelyForever(frequency.get()).withMisfireHandlingInstructionNextWithRemainingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger hourlyWithMisfireNowWithExistingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatHourlyForever(frequency.get()).withMisfireHandlingInstructionNowWithExistingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger hourlyTriggerWithMisfireNowWithRemainingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatHourlyForever(frequency.get()).withMisfireHandlingInstructionNowWithRemainingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger hourlyTriggerWithMisfireNextWithExistingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatHourlyForever(frequency.get()).withMisfireHandlingInstructionNextWithExistingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    default Trigger hourlyTriggerWithMisfireNextWithRemainingCount(TriggerKey triggerKey, Supplier<Integer> frequency) {
        return triggerOrRescedulable(
                freq -> {
                    SimpleScheduleBuilder schedule = SimpleScheduleBuilder.repeatHourlyForever(frequency.get()).withMisfireHandlingInstructionNextWithRemainingCount();
                    return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                },
                frequency
        );
    }
    private static <T> Trigger triggerOrRescedulable(Function<T, Trigger> trigger, Supplier<T> frequency) {
        return frequency instanceof Property<T> prop ? new ReschedulableTrigger<>(trigger, prop) : trigger.apply(frequency.get());
    }
}
