package com.turbospaces.quartz;

import java.util.Optional;
import java.util.Properties;

import org.quartz.SchedulerFactory;
import org.quartz.impl.jdbcjobstore.JobStoreTX;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RelationalServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.H2ServiceInfo;
import com.turbospaces.ups.UPSs;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class QuartzJobStoreConfigurer implements QuartzConfigurer {
    protected final ApplicationProperties props;
    protected final DynamicCloud cloud;

    @Override
    public void configureQuartz(Properties map, SchedulerFactory factory) throws Exception {
        String cpName = props.CLOUD_APP_ID.get();

        map.put("org.quartz.jobStore.class", JobStoreTX.class.getName());
        map.put("org.quartz.jobStore.tablePrefix", props.QUARTZ_JOBSTORE_TABLE_PREFIX.get());
        map.put("org.quartz.jobStore.useProperties", props.QUARTZ_JOBSTORE_USE_PROPS.get().toString());
        map.put("org.quartz.jobStore.acquireTriggersWithinLock", props.QUARTZ_JOBSTORE_ACQUIRE_TRIGGERS_WITHIN_LOCK.get().toString());
        map.put("org.quartz.jobStore.isClustered", props.QUARTZ_JOBSTORE_IS_CLUSTERED.get().toString());
        //
        map.put("org.quartz.jobStore.dataSource", cpName);
        map.put(String.format("org.quartz.dataSource.%s.minimumIdle", cpName), props.QUARTZ_CONNECTION_POOL_MIN.get().toString());
        map.put(String.format("org.quartz.dataSource.%s.maximumPoolSize", cpName), props.QUARTZ_CONNECTION_POOL_MAX.get().toString());
        map.put(String.format("org.quartz.dataSource.%s.provider", cpName), "hikaricp");

        if (props.QUARTZ_JOBSTORE_IS_CLUSTERED.get()) {
            String appId = props.CLOUD_APP_ID.get();
            String slot = props.CLOUD_APP_INSTANCE_INDEX.get();
            map.put("org.quartz.scheduler.instanceId", String.format("%s-%s", appId, slot));
        }
    }
    protected PostgresqlServiceInfo postgresqlServiceInfo() {
        PostgresqlServiceInfo pasi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.POSTGRES_APP);
        return (PostgresqlServiceInfo) appServiceInfo(pasi);
    }
    protected H2ServiceInfo h2ServiceInfo() {
        H2ServiceInfo pasi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.H2_APP);
        return (H2ServiceInfo) appServiceInfo(pasi);
    }
    protected ServiceInfo appServiceInfo(ServiceInfo pasi) {
        Optional<ServiceInfo> opt = UPSs.findServiceInfoByName(cloud, UPSs.QUARTZ_APP);
        if (opt.isPresent()) {
            ServiceInfo qasi = opt.get();
            if (pasi instanceof RelationalServiceInfo) {
                log.debug("found quartz user {}", ((RelationalServiceInfo) qasi).getUserName());
            }
            return qasi;
        }
        if (pasi instanceof RelationalServiceInfo) {
            log.debug("not found quartz user, will use default user {}", ((RelationalServiceInfo) pasi).getUserName());
        }
        return pasi;
    }
    protected void usePostgresql(Properties map, PostgresqlServiceInfo pasi, String jdbcURL) {
        String cpName = props.CLOUD_APP_ID.get();

        map.put("org.quartz.jobStore.driverDelegateClass", org.quartz.impl.jdbcjobstore.PostgreSQLDelegate.class.getName());
        map.put(String.format("org.quartz.dataSource.%s.driver", cpName), "org.postgresql.Driver");
        map.put(String.format("org.quartz.dataSource.%s.URL", cpName), jdbcURL);
        map.put(String.format("org.quartz.dataSource.%s.user", cpName), pasi.getUserName());
        map.put(String.format("org.quartz.dataSource.%s.password", cpName), pasi.getPassword());
    }
    protected void useH2(Properties map, H2ServiceInfo si) {
        useH2(map, si.getJdbcUrl());
    }
    protected void useH2(Properties map, String jdbcURL) {
        String cpName = props.CLOUD_APP_ID.get();

        map.put(String.format("org.quartz.dataSource.%s.driver", cpName), "org.h2.Driver");
        map.put(String.format("org.quartz.dataSource.%s.URL", cpName), jdbcURL);
    }
    protected void useH2(Properties map, String jdbcURL, String username, String password) {
        String cpName = props.CLOUD_APP_ID.get();

        map.put(String.format("org.quartz.dataSource.%s.driver", cpName), "org.h2.Driver");
        map.put(String.format("org.quartz.dataSource.%s.URL", cpName), jdbcURL);
        map.put(String.format("org.quartz.dataSource.%s.user", cpName), username);
        map.put(String.format("org.quartz.dataSource.%s.password", cpName), password);
    }
}
