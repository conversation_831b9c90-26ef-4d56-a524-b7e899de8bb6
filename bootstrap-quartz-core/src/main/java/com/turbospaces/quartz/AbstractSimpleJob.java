package com.turbospaces.quartz;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.MDC;

import com.google.common.base.Joiner;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.metrics.MetricTags;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;

public abstract class AbstractSimpleJob extends AbstractJob {
    protected String operationName;

    protected AbstractSimpleJob(ApplicationProperties props, MeterRegistry meterRegistry) {
        super(props, meterRegistry);
    }
    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        this.operationName = PlatformUtil.toLowerUnderscore(getClass().getName());
        super.execute(ctx);
    }
    @Override
    protected void doBeforeExecution(JobExecutionContext ctx) throws Throwable {
        super.doBeforeExecution(ctx);

        String newName = Joiner.on('|').join(threadName, schedulerName, schedulerInstanceId, operationName);

        Thread.currentThread().setName(newName);

        MDC.put(MdcTags.MDC_OPERATION, operationName);
        MDC.put(MdcTags.MDC_TRACE_ID, fireInstanceId);

        tags.add(Tag.of(MetricTags.NAME, operationName));
    }
    @Override
    protected void doAfterExecution(JobExecutionContext ctx) throws Throwable {
        super.doAfterExecution(ctx);
    }
    @Override
    protected void doOnFailure(JobExecutionContext ctx, Throwable err) throws JobExecutionException {
        tags.add(Tag.of(MetricTags.ERROR, err.getClass().getSimpleName()));
        super.doOnFailure(ctx, err);
    }
}
