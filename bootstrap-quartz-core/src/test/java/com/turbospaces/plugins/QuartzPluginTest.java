package com.turbospaces.plugins;

import java.io.File;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerFactory;
import org.quartz.TriggerKey;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ResourceUtils;

import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.quartz.JobsDispatcher;
import com.turbospaces.quartz.QuartzFactoryBean;
import com.turbospaces.quartz.QuartzJobStoreConfigurer;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import io.ebean.ddlrunner.DdlRunner;

public class QuartzPluginTest {
    public static HikariConfig hcfg = new HikariConfig() {
        {
            setRegisterMbeans(false);
            setUsername("sa");
            setPassword("sa");
            setJdbcUrl("jdbc:h2:mem:embed;DB_CLOSE_DELAY=0;LOCK_MODE=0");
        }
    };

    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);

        URL sqlResource = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "tables_h2.sql");
        String sqlScript = FileUtils.readFileToString(new File(sqlResource.getFile()), StandardCharsets.UTF_8);

        try (HikariDataSource ds = new HikariDataSource(hcfg)) {
            try (Connection connection = ds.getConnection()) {
                try {
                    DdlRunner runner = new DdlRunner(false, PlatformUtil.randomAlphanumeric(4));
                    runner.runAll(sqlScript, connection);
                    connection.commit();
                } catch (Exception err) {
                    connection.rollback();
                    throw err;
                }
            }

            Bootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                Scheduler scheduler = applicationContext.getBean(QuartzFactoryBean.class).getObject();
                scheduler.start();

                try {
                    JobKey jobKey = new JobKey("fake", "default");
                    JobBuilder job = JobBuilder.newJob(FakeJob.class).withIdentity(jobKey);
                    TriggerKey triggerKey = new TriggerKey(PlatformUtil.randomUUID().toString(), jobKey.getGroup());

                    new JobsDispatcher() {}.runOnce(scheduler, job, triggerKey);
                } finally {
                    scheduler.shutdown();
                }
            } finally {
                bootstrap.shutdown();
            }
        }
    }

    public static class FakeJob implements Job {
        @Override
        public void execute(JobExecutionContext context) throws JobExecutionException {
            context.getMergedJobDataMap();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public QuartzJobStoreConfigurer configurer(ApplicationProperties props, DynamicCloud cloud) {
            return new QuartzJobStoreConfigurer(props, cloud) {
                @Override
                public void configureQuartz(Properties map, SchedulerFactory factory) throws Exception {
                    super.configureQuartz(map, factory);
                    useH2(map, hcfg.getJdbcUrl(), "sa", hcfg.getPassword());
                }
            };
        }
        @Bean
        public QuartzFactoryBean foo(ApplicationProperties props, QuartzJobStoreConfigurer configurer) {
            return new QuartzFactoryBean(props, configurer);
        }
    }
}
