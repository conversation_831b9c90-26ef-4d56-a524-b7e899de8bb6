package com.turbospaces.plugins;

import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.quartz.AbstractJob;
import com.turbospaces.quartz.JobsDispatcher;
import com.turbospaces.quartz.QuartzFactoryBean;
import com.turbospaces.quartz.QuartzJobStoreConfigurer;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerFactory;
import org.quartz.TriggerKey;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

public class RamQuartzPluginTest {
    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);

        Bootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        ConfigurableApplicationContext applicationContext = bootstrap.run();

        try {
            Scheduler scheduler = applicationContext.getBean(QuartzFactoryBean.class).getObject();
            scheduler.start();

            try {
                JobKey jobKey = new JobKey("fake", "default");
                JobBuilder job = JobBuilder.newJob(FakeJob.class).withIdentity(jobKey).storeDurably();
                TriggerKey triggerKey = new TriggerKey(PlatformUtil.randomUUID().toString(), jobKey.getGroup());

                new JobsDispatcher() {
                }.runOnce(scheduler, job, triggerKey);

                // run failing job
                jobKey = new JobKey("fakeFailure", "default");
                var failedJob = JobBuilder.newJob(FakeFailureJob.class).withIdentity(jobKey).storeDurably();
                TriggerKey failureTriggerKey = new TriggerKey(PlatformUtil.randomUUID().toString(), jobKey.getGroup());

                var err = Assertions.assertThrows(
                        RuntimeException.class,
                        () -> new JobsDispatcher() {
                        }.runOnceBlock(scheduler, failedJob, failureTriggerKey).getDuration()
                );
                Assertions.assertEquals("fake failure", ExceptionUtils.getRootCause(err).getMessage());
            } finally {
                scheduler.shutdown();
            }
        } finally {
            bootstrap.shutdown();
        }
    }


    public static class FakeJob implements Job {
        @Override
        public void execute(JobExecutionContext context) throws JobExecutionException {
            context.getMergedJobDataMap();
        }
    }


    public static class FakeFailureJob extends AbstractJob {
        public FakeFailureJob(ApplicationProperties props, MeterRegistry meterRegistry) {
            super(props, meterRegistry);
        }

        @Override
        protected void doExecute(JobExecutionContext ctx) throws Throwable {
            throw new RuntimeException("fake failure");
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public QuartzJobStoreConfigurer configurer(ApplicationProperties props, DynamicCloud cloud) {
            return new QuartzJobStoreConfigurer(props, cloud) {
                @Override
                public void configureQuartz(Properties map, SchedulerFactory factory) throws Exception {
                    map.put("org.quartz.jobStore.class", org.quartz.simpl.RAMJobStore.class.getName());
                }
            };
        }

        @Bean
        public CustomQuartzFactoryBean foo(ApplicationProperties props, QuartzJobStoreConfigurer configurer) {
            return new CustomQuartzFactoryBean(props, configurer);
        }
    }
}
