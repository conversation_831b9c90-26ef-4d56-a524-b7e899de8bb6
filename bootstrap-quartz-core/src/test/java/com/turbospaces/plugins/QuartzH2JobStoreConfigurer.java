package com.turbospaces.plugins;

import java.util.Properties;

import org.quartz.SchedulerFactory;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.quartz.QuartzJobStoreConfigurer;
import com.turbospaces.ups.H2ServiceInfo;

public class QuartzH2JobStoreConfigurer extends QuartzJobStoreConfigurer {
    public QuartzH2JobStoreConfigurer(ApplicationProperties props, DynamicCloud cloud) {
        super(props, cloud);
    }
    @Override
    public void configureQuartz(Properties map, SchedulerFactory factory) throws Exception {
        super.configureQuartz(map, factory);

        H2ServiceInfo si = h2ServiceInfo();
        useH2(map, si);
    }
}
