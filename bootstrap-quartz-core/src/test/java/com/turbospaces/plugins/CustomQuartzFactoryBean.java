package com.turbospaces.plugins;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.quartz.JobsDispatcher;
import com.turbospaces.quartz.QuartzConfigurer;
import com.turbospaces.quartz.QuartzFactoryBean;
import org.quartz.Job;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.impl.matchers.GroupMatcher;
import org.quartz.spi.TriggerFiredBundle;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

public class CustomQuartzFactoryBean extends QuartzFactoryBean implements JobsDispatcher, ApplicationRunner {
    public CustomQuartzFactoryBean(ApplicationProperties props, QuartzConfigurer configurer) {
        super(props, configurer);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        getObject().start();
    }

    @Override
    public Job newJob(TriggerFiredBundle bundle, Scheduler sched) throws SchedulerException {
        try {
            var jobClass = bundle.getJobDetail().getJobClass();
            AutowireCapableBeanFactory beanFactory = applicationContext.getAutowireCapableBeanFactory();
            return beanFactory.createBean(jobClass);
        } catch (Exception err) {
            throw new SchedulerException(err);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();

        Scheduler instance = getObject();
        for (JobKey it : instance.getJobKeys(GroupMatcher.anyJobGroup())) {
            log.debug("registered {} into {}({})", it, instance.getSchedulerName(), instance.getSchedulerInstanceId());
        }
    }
}
