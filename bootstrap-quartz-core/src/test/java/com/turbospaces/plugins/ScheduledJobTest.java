package com.turbospaces.plugins;

import java.io.File;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.time.Duration;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobBuilder;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerFactory;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ResourceUtils;

import com.netflix.archaius.api.Property;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.quartz.AbstractJob;
import com.turbospaces.quartz.JobsDispatcher;
import com.turbospaces.quartz.QuartzJobStoreConfigurer;
import com.turbospaces.quartz.ReschedulableTrigger;
import com.turbospaces.quartz.ScheduledJob;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import io.ebean.ddlrunner.DdlRunner;
import io.micrometer.core.instrument.MeterRegistry;
import reactor.core.publisher.Mono;

public class ScheduledJobTest {
    public static HikariConfig hcfg = new HikariConfig() {
        {
            setRegisterMbeans(false);
            setUsername("sa");
            setPassword("sa");
            setJdbcUrl("jdbc:h2:mem:embed;DB_CLOSE_DELAY=0;LOCK_MODE=0");
        }
    };

    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();
        ApplicationConfig cfg = ApplicationConfig.mock(port);
        TestProperties props = new TestProperties(cfg.factory());
        cfg.setLocalProperty(props.QUARTZ_ENFORCE_DISABLED_JOBS_ENABLED.getKey(), false);

        URL sqlResource = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "tables_h2.sql");
        String sqlScript = FileUtils.readFileToString(new File(sqlResource.getFile()), StandardCharsets.UTF_8);

        Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class, FakeScheduledJob.class);
        try (HikariDataSource ds = new HikariDataSource(hcfg)) {
            try (Connection connection = ds.getConnection()) {
                try {
                    DdlRunner runner = new DdlRunner(false, PlatformUtil.randomAlphanumeric(4));
                    runner.runAll(sqlScript, connection);
                    connection.commit();
                } catch (Exception err) {
                    connection.rollback();
                    throw err;
                }
            }
            var ctx = bootstrap.run();

            // check not invoked
            Awaitility.await()
                    .atLeast(Duration.ofMillis(50))
                    .untilAsserted(() -> {
                        Assertions.assertEquals(0, FakeScheduledJob.invocationCount.get());
                    });

            // activate the job
            cfg.setLocalProperty(props.FAKE_JOB_ENABLED.getKey(), true);
            Awaitility.await().untilAsserted(() -> Assertions.assertTrue(FakeScheduledJob.invocationCount.get() > 0));

            // change frequency
            int newRepeatInterval = 2;
            cfg.setLocalProperty(props.FAKE_JOB_FREQUENCY.getKey(), newRepeatInterval);
            var scheduler = ctx.getBean(Scheduler.class);
            Awaitility.await().untilAsserted(
                    () -> {
                        var triggers = scheduler.getTriggersOfJob(FakeScheduledJob.JOB_KEY);
                        Assertions.assertEquals(1, triggers.size());
                        var trigger = (SimpleTrigger) triggers.get(0);
                        Assertions.assertEquals(newRepeatInterval, trigger.getRepeatInterval());
                    }
            );

            // disable job
            cfg.setLocalProperty(props.FAKE_JOB_ENABLED.getKey(), false);
            Awaitility.await().untilAsserted(() -> Assertions.assertEquals(Trigger.TriggerState.PAUSED, scheduler.getTriggerState(FakeScheduledJob.TRIGGER_KEY)));
            FakeScheduledJob.invocationCount.set(0);
            Awaitility.await()
                    .atLeast(Duration.ofMillis(50))
                    .untilAsserted(() -> {
                        Assertions.assertEquals(0, FakeScheduledJob.invocationCount.get());
                    });

            // change frequency when disabled
            int newRepeatIntervalForDisabled = 1;
            cfg.setLocalProperty(props.FAKE_JOB_FREQUENCY.getKey(), newRepeatIntervalForDisabled);
            Awaitility.await().untilAsserted(
                    () -> {
                        var triggers = scheduler.getTriggersOfJob(FakeScheduledJob.JOB_KEY);
                        Assertions.assertEquals(1, triggers.size());
                        var trigger = (SimpleTrigger) triggers.get(0);
                        Assertions.assertEquals(newRepeatIntervalForDisabled, trigger.getRepeatInterval());
                    }
            );
        } finally {
            bootstrap.shutdown();
        }
    }

    @DisallowConcurrentExecution
    public static class FakeScheduledJob extends AbstractJob implements ScheduledJob, JobsDispatcher {
        public static final JobKey JOB_KEY = new JobKey("fake_scheduled_job", "fake");
        public static final TriggerKey TRIGGER_KEY = new TriggerKey("fake_scheduled_job", "fake");
        private final TestProperties props;
        private final Scheduler scheduler;
        public static final AtomicInteger invocationCount = new AtomicInteger(0);

        public FakeScheduledJob(ApplicationProperties props, Scheduler scheduler, MeterRegistry meterRegistry) {
            super(props, meterRegistry);
            this.props = (TestProperties) props;
            this.scheduler = scheduler;
        }

        @Override
        protected void doExecute(JobExecutionContext ctx) throws Throwable {
            invocationCount.incrementAndGet();
        }

        @Override
        public Supplier<Boolean> isEnabled() {
            return props.FAKE_JOB_ENABLED;
        }

        @Override
        public JobBuilder toJob() {
            return JobBuilder.newJob(getClass()).withIdentity(JOB_KEY);

        }

        @Override
        public void schedule() throws Exception {
            TriggerKey triggerKey = TRIGGER_KEY;
            Trigger trigger = new ReschedulableTrigger<Integer>(
                    freq -> {
                        SimpleScheduleBuilder schedule = SimpleScheduleBuilder
                                .simpleSchedule()
                                .withIntervalInMilliseconds(freq)
                                .repeatForever().withMisfireHandlingInstructionNowWithExistingCount();
                        return TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(schedule).build();
                    },
                    props.FAKE_JOB_FREQUENCY
            );
            scheduleJob(scheduler, toJob(), trigger, props.FAKE_JOB_ENABLED, props.FAKE_JOB_ENABLED);
        }

        @Override
        public Mono<StopWatch> runOnce() throws Exception {
            TriggerKey triggerKey = new TriggerKey("fake_scheduled_job" + "/" + PlatformUtil.randomUUID(), "fake");
            return runOnce(scheduler, toJob(), triggerKey);
        }
    }

    public static class TestProperties extends ApplicationProperties {
        public final Property<Integer> FAKE_JOB_FREQUENCY;
        public final Property<Boolean> FAKE_JOB_ENABLED;

        public TestProperties(DynamicPropertyFactory pf) {
            super(pf);

            FAKE_JOB_ENABLED = pf.get("fake.job.enabled", boolean.class).orElse(false);
            FAKE_JOB_FREQUENCY = pf.get("fake.job.frequency", int.class).orElse(1);
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public QuartzJobStoreConfigurer configurer(ApplicationProperties props, DynamicCloud cloud) {
            return new QuartzJobStoreConfigurer(props, cloud) {
                @Override
                public void configureQuartz(Properties map, SchedulerFactory factory) throws Exception {
                    super.configureQuartz(map, factory);
                    useH2(map, hcfg.getJdbcUrl(), "sa", hcfg.getPassword());
                }
            };
        }

        @Bean
        public CustomQuartzFactoryBean foo(ApplicationProperties props, QuartzJobStoreConfigurer configurer) {
            return new CustomQuartzFactoryBean(props, configurer);
        }
    }
}
