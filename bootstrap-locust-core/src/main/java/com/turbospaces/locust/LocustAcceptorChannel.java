package com.turbospaces.locust;

import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.bigsonata.swarm.Cron;
import com.bigsonata.swarm.Locust;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

public class LocustAcceptorChannel implements InitializingBean, DisposableBean {
    private final ApplicationProperties props;
    private final PlainServiceInfo si;
    private final Collection<Cron> crons;
    private Locust locust;

    public LocustAcceptorChannel(ApplicationProperties props, DynamicCloud cloud, Collection<Cron> crons) {
        this.props = Objects.requireNonNull(props);
        this.crons = Objects.requireNonNull(crons);
        this.si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.LOCUST);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        locust = Locust.Builder.newInstance()
                .setMasterHost(si.getHost())
                .setMasterPort(si.getPort())
                .setStatInterval((int) TimeUnit.SECONDS.toMillis(1))
                .setThreads(props.LOCUST_WORKERS.get())
                .setMaxRps(props.LOCUST_RATE_LIMITER.get())
                .setCrons(crons.toArray(new Cron[crons.size()])).build();
    }
    @Override
    public void destroy() throws Exception {
        locust.dispose();
    }
}
