package com.turbospaces.gcp.pubsub.producer;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.cloud.util.StandardUriInfoFactory;

import com.google.cloud.spring.pubsub.support.PublisherFactory;
import com.google.common.util.concurrent.FutureCallback;
import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.MDCToHeadersContexPropagator;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.dispatch.UntypedPostSpec;
import com.turbospaces.http.HttpProto;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.rpc.DefaultWrappedQueuePost;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.rpc.WrappedQueuePost;

import api.v1.ApiFactory;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.gpubsub.impl.PubsubWithRoutingKeyMessageWriterImpl;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.Tracer.SpanBuilder;
import io.opentracing.propagation.Format.Builtin;
import jakarta.inject.Inject;

public class PubsubPostTemplate extends DefaultPubsubPublisher implements QueuePostTemplate<String> {
    private final StandardUriInfoFactory uriFactory = new StandardUriInfoFactory();
    private final Tracer tracer;
    private final ApiFactory apiFactory;
    private final DefaultRequestReplyMapper mapper;
    private final Supplier<Duration> defaultTimeout;

    @Inject
    public PubsubPostTemplate(
            ApplicationProperties props,
            Tracer tracer,
            ApiFactory apiFactory,
            DefaultRequestReplyMapper mapper,
            PublisherFactory producerFactory,
            Supplier<Duration> timeout) {
        super(props, producerFactory);
        this.tracer = Objects.requireNonNull(tracer);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.mapper = Objects.requireNonNull(mapper);
        this.defaultTimeout = Objects.requireNonNull(timeout);
    }
    @Override
    public ApiFactory apiFactory() {
        return apiFactory;
    }
    @Override
    public WrappedQueuePost sendReq(RequestQueuePostSpec spec) {
        Duration operationTimeout = spec.timeout().orElse(defaultTimeout.get());
        long now = System.currentTimeMillis();
        String qualifier = spec.topic().name().toString();
        UUID messageId = spec.messageId();

        RequestWrapperFacade reqw = apiFactory.requestMapper().pack(spec, new MDCToHeadersContexPropagator(), defaultTimeout.get());
        String typeUrl = reqw.body().getTypeUrl();

        var record = PubsubMessage.newBuilder();
        if (spec.routingKey().isPresent()) {
            record.setOrderingKey(spec.routingKey().get().toString());
        }
        record.putAttributes(HttpProto.HEADER_X_TIMESTAMP, String.valueOf(now));
        var post = span(spec, record);
        var messageWriter = new PubsubWithRoutingKeyMessageWriterImpl(record);
        var future = mapper.acquire(messageId, operationTimeout);
        var callback = new LoggingCallback(typeUrl, messageId, spec.topic(), now) {
            @Override
            public void onSuccess(String result) {
                super.onSuccess(result);
                post.finish();
            }
            @Override
            public void onFailure(Throwable ex) {
                super.onFailure(ex);
                future.setException(ex);
                post.finish();
            }
        };

        publish(qualifier, messageWriter.writeBinary(reqw), new FutureCallback<>() {
            @Override
            public void onSuccess(String result) {
                callback.onSuccess(result);
            }
            @Override
            public void onFailure(Throwable ex) {
                callback.onFailure(ex);
            }
        });

        return new DefaultWrappedQueuePost(future, reqw);
    }
    @Override
    public CompletableFuture<String> sendNotify(NotifyQueuePostSpec spec) {
        long now = System.currentTimeMillis();
        String qualifier = spec.topic().name().toString();
        String typeUrl = spec.body().getTypeUrl();
        UUID messageId = spec.messageId();

        var record = PubsubMessage.newBuilder();
        if (spec.routingKey().isPresent()) {
            record.setOrderingKey(spec.routingKey().get().toString());
        }

        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        if (Objects.nonNull(traceId)) {
            record.putAttributes(MdcTags.MDC_TRACE_ID, traceId);
        }

        var post = span(spec, record);
        var messageWriter = new PubsubWithRoutingKeyMessageWriterImpl(record);
        var callback = new LoggingCallback(typeUrl, messageId, spec.topic(), now) {
            @Override
            public void onSuccess(String result) {
                super.onSuccess(result);
                post.finish();
            }
            @Override
            public void onFailure(Throwable ex) {
                super.onFailure(ex);
                post.finish();
            }
        };

        return publish(qualifier, messageWriter.writeBinary(spec), new FutureCallback<>() {
            @Override
            public void onSuccess(String result) {
                callback.onSuccess(result);
            }
            @Override
            public void onFailure(Throwable ex) {
                callback.onFailure(ex);

            }
        });
    }
    @Override
    public CompletableFuture<String> sendEvent(EventQueuePostSpec spec) {
        Any any = spec.pack();
        long now = System.currentTimeMillis();
        String qualifier = spec.topic().name().toString();
        String typeUrl = any.getTypeUrl();
        UUID messageId = spec.messageId();

        var record = PubsubMessage.newBuilder();
        record.setData(any.toByteString());
        if (spec.routingKey().isPresent()) {
            record.setOrderingKey(spec.routingKey().get().toString());
        }

        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        if (Objects.nonNull(traceId)) {
            record.putAttributes(MdcTags.MDC_TRACE_ID, traceId);
        }

        var eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
        var event = eventTemplate.newBuilder()
                .withId(messageId.toString())
                .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                .withType(typeUrl);

        var post = span(spec, record);
        var messageWriter = new PubsubWithRoutingKeyMessageWriterImpl(record);
        var callback = new LoggingCallback(typeUrl, messageId, spec.topic(), now) {
            @Override
            public void onSuccess(String result) {
                super.onSuccess(result);
                post.finish();
            }
            @Override
            public void onFailure(Throwable ex) {
                super.onFailure(ex);
                post.finish();
            }
        };

        return publish(qualifier, messageWriter.writeBinary(event.build()), new FutureCallback<>() {
            @Override
            public void onSuccess(String result) {
                callback.onSuccess(result);
            }
            @Override
            public void onFailure(Throwable ex) {
                callback.onFailure(ex);
            }
        });
    }
    @Override
    public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
        ResponseWrapperFacade respw = outcome.getReply();
        String replyTo = MutableNonPersistentReplyTopic.asPersistentReplyTo(respw.headers().getReplyTo(), uriFactory);
        String typeUrl = respw.body().getTypeUrl();
        long now = System.currentTimeMillis();
        String messageId = respw.headers().getMessageId();
        String status = StringUtils.lowerCase(respw.status().errorCode().toString()).intern();

        if (StringUtils.isNotEmpty(replyTo)) {
            log.trace("about to post {} to {} ...", typeUrl, replyTo);

            var record = PubsubMessage.newBuilder();
            record.setData(ByteString.copyFrom(respw.getData().toBytes()));
            if (Objects.nonNull(outcome.getKey())) {
                record.setOrderingKey(outcome.getKey().toString());
            }
            record.putAttributes(HttpProto.HEADER_X_TIMESTAMP, String.valueOf(now));

            CloudEventBuilder eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
            CloudEventBuilder event = eventTemplate.newBuilder()
                    .withId(messageId)
                    .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                    .withType(typeUrl);

            var messageWriter = new PubsubWithRoutingKeyMessageWriterImpl(record);

            return publish(replyTo, messageWriter.writeBinary(event.build()), new FutureCallback<>() {
                @Override
                public void onSuccess(String result) {
                    log.debug("OUT ::: ({}:{}:s-{}) has been accepted by pubsub (topic={}:messageId={},took={})",
                            typeUrl,
                            messageId,
                            status,
                            replyTo,
                            result,
                            System.currentTimeMillis() - now);
                }
                @Override
                public void onFailure(Throwable ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        }
        return CompletableFuture.completedStage(new Object());
    }
    @Override
    public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
        List<NotificationWrapperFacade> notifications = outcome.getNotifications();
        if (log.isTraceEnabled()) {
            List<String> types = notifications.stream().map(NotificationWrapperFacade::body).map(Any::getTypeUrl).collect(Collectors.toList());
            log.trace("about to post {} to {} ...", types, topic.name());
        }

        CompletableFuture<?>[] list = new CompletableFuture<?>[notifications.size()];
        for (int i = 0; i < notifications.size(); i++) {
            NotificationWrapperFacade it = notifications.get(i);
            String typeUrl = it.body().getTypeUrl();
            long now = System.currentTimeMillis();

            var record = PubsubMessage.newBuilder();
            record.setData(ByteString.copyFrom(it.getData().toBytes()));
            if (Objects.nonNull(outcome.getKey())) {
                record.setOrderingKey(outcome.getKey().toString());
            }

            CloudEventBuilder eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
            CloudEventBuilder event = eventTemplate.newBuilder()
                    .withId(PlatformUtil.randomUUID().toString())
                    .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                    .withType(typeUrl);

            var messageWriter = new PubsubWithRoutingKeyMessageWriterImpl(record);

            list[i] = publish(topic.name().toString(), messageWriter.writeBinary(event.build()), new FutureCallback<>() {
                @Override
                public void onSuccess(String result) {
                    log.debug("OUT ::: ({}) has been accepted by pubsub (topic={}:messageId={},took={})",
                            typeUrl,
                            topic.name(),
                            result,
                            System.currentTimeMillis() - now);
                }
                @Override
                public void onFailure(Throwable ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        }
        return CompletableFuture.allOf(list);
    }
    @Override
    public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
        List<Any> eventStream = outcome.getEventStream();
        if (log.isTraceEnabled()) {
            var types = eventStream.stream().map(Any::getTypeUrl).collect(Collectors.toList());
            log.trace("about to post {} to {} ...", types, topic.name());
        }

        CompletableFuture<?>[] list = new CompletableFuture<?>[eventStream.size()];
        for (int i = 0; i < eventStream.size(); i++) {
            Any wrapper = eventStream.get(i);
            String typeUrl = wrapper.getTypeUrl();
            long now = System.currentTimeMillis();

            var record = PubsubMessage.newBuilder();
            record.setData(wrapper.toByteString());
            if (Objects.nonNull(outcome.getKey())) {
                record.setOrderingKey(outcome.getKey().toString());
            }

            CloudEventBuilder eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
            CloudEventBuilder event = eventTemplate.newBuilder()
                    .withId(PlatformUtil.randomUUID().toString())
                    .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                    .withType(typeUrl);

            var messageWriter = new PubsubWithRoutingKeyMessageWriterImpl(record);

            list[i] = publish(topic.name().toString(), messageWriter.writeBinary(event.build()), new FutureCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    log.debug("OUT ::: ({}) has been accepted by pubsub (topic={}:messageId={},took={})",
                            typeUrl,
                            topic.name(),
                            result,
                            System.currentTimeMillis() - now);
                }
                @Override
                public void onFailure(Throwable ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        }
        return CompletableFuture.allOf(list);
    }
    private Span span(UntypedPostSpec spec, PubsubMessage.Builder recordBuilder) {
        Span span = tracer.activeSpan();
        SpanBuilder post = tracer.buildSpan("pubsub-post");

        if (Objects.nonNull(span)) {
            tracer.inject(span.context(), Builtin.TEXT_MAP, new PubsubSpanContextWriter(recordBuilder));
            return post.asChildOf(span).start();
        }

        return post.start();
    }
}
