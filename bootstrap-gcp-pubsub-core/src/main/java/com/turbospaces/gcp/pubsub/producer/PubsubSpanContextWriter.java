package com.turbospaces.gcp.pubsub.producer;

import java.util.Iterator;
import java.util.Map;

import com.google.pubsub.v1.PubsubMessage;

import io.opentracing.propagation.TextMap;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PubsubSpanContextWriter implements TextMap {
    private final PubsubMessage.Builder record;

    @Override
    public void put(String k, String v) {
        record.putAttributes(k, v);
    }
    @Override
    public Iterator<Map.Entry<String, String>> iterator() {
        throw new UnsupportedOperationException();
    }
}
