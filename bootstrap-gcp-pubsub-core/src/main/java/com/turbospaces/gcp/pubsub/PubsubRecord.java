package com.turbospaces.gcp.pubsub;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.google.cloud.spring.pubsub.support.BasicAcknowledgeablePubsubMessage;
import com.google.cloud.spring.pubsub.support.GcpPubSubHeaders;
import com.google.common.io.ByteSource;
import com.google.protobuf.util.Timestamps;
import com.turbospaces.api.Topic;

import io.netty.util.AsciiString;

public class PubsubRecord implements PubsubWorkUnit {
    private final Topic topic;
    private final BasicAcknowledgeablePubsubMessage message;

    public PubsubRecord(Topic topic, org.springframework.messaging.Message<?> message) {
        this.topic = Objects.requireNonNull(topic);
        this.message = (BasicAcknowledgeablePubsubMessage) message.getHeaders().get(GcpPubSubHeaders.ORIGINAL_MESSAGE);
    }
    public PubsubRecord(Topic topic, BasicAcknowledgeablePubsubMessage message) {
        this.topic = Objects.requireNonNull(topic);
        this.message = Objects.requireNonNull(message);
    }
    @Override
    public String topic() {
        return topic.name().toString();
    }
    @Override
    public long timestamp() {
        return Timestamps.toMillis(message.getPubsubMessage().getPublishTime());
    }
    @Override
    public byte[] key() {
        String orderingKey = message.getPubsubMessage().getOrderingKey();
        if (StringUtils.isNotEmpty(orderingKey)) {
            return orderingKey.getBytes();
        }
        return null;
    }
    @Override
    public ByteSource value() {
        return new ByteSource() {
            @Override
            public InputStream openStream() throws IOException {
                return message.getPubsubMessage().getData().newInput();
            }
        };
    }
    @Override
    public CompletableFuture<Void> ack() {
        return message.ack();
    }
    @Override
    public CompletableFuture<Void> nack() {
        return message.nack();
    }
    @Override
    public Map<String, String> attributes() {
        return message.getPubsubMessage().getAttributesMap();
    }
    @Override
    public Optional<String> lastHeader(String key) {
        return Optional.ofNullable(attributes().get(key));
    }
    @Override
    public String messageId() {
        return message.getPubsubMessage().getMessageId();
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE)
                .append("topic", topic())
                .append("timestamp", timestamp())
                .append("messageId", message.getPubsubMessage().getMessageId())
                .append("key", Objects.isNull(key()) ? key() : new AsciiString(key()))
                .append("attributes", message.getPubsubMessage().getAttributesMap())
                .build();
    }
}
