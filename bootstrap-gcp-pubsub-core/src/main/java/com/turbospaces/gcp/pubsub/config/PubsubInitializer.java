package com.turbospaces.gcp.pubsub.config;

import java.util.Objects;
import java.util.function.Supplier;

import com.google.api.gax.rpc.ApiException;
import com.google.cloud.pubsub.v1.SubscriptionAdminClient;
import com.google.cloud.spring.pubsub.PubSubAdmin;
import com.google.protobuf.Duration;
import com.google.protobuf.FieldMask;
import com.google.pubsub.v1.ExpirationPolicy;
import com.google.pubsub.v1.Subscription;
import com.google.pubsub.v1.UpdateSubscriptionRequest;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class PubsubInitializer {
    private static final String DLQ_POSTFIX = "dlq";

    public static void getOrCreateTopicAndSubscription(
            ApplicationProperties props,
            PubSubAdmin admin,
            SubscriptionAdminClient subscriptionAdmin,
            Topic topic,
            String subscriptionName,
            Supplier<Subscription.Builder> supplier) {
        String topicName = topic.name().toString();
        if (Objects.isNull(admin.getTopic(topicName))) {
            log.info("created topic: {}", admin.createTopic(topicName).getName());
        }

        Subscription subscription = admin.getSubscription(subscriptionName);
        if (Objects.isNull(subscription)) {
            log.info("created subscription: {}", admin.createSubscription(supplier.get()));
        } else {
            updateSubscriptionSettings(props, subscriptionAdmin, subscription);
        }
    }
    public static com.google.pubsub.v1.Topic getOrCreateDLQ(ApplicationProperties props, String topicName, PubSubAdmin admin) {
        String appId = props.CLOUD_APP_ID.get();
        String topic = String.format("%s-%s", topicName, DLQ_POSTFIX);
        String subscription = String.format("%s-%s-%s", topicName, appId, DLQ_POSTFIX);
        com.google.pubsub.v1.Topic dlq = Objects.isNull(admin.getTopic(topic)) ? admin.createTopic(topic) : admin.getTopic(topic);

        if (Objects.isNull(admin.getSubscription(subscription))) {
            admin.createSubscription(Subscription.newBuilder()
                    .setName(subscription)
                    .setTopic(topic)
                    .setExpirationPolicy(ExpirationPolicy.newBuilder().build()));
        }

        return dlq;
    }
    public static void updateSubscriptionSettings(ApplicationProperties props, SubscriptionAdminClient adminClient, Subscription subscription) {
        var subscriptionBuilder = Subscription.newBuilder(subscription);
        var fieldMask = FieldMask.newBuilder();
        boolean toApply = false;

        var exactlyOnceDelivery = props.PUBSUB_SUBSCRIPTION_EXACTLY_ONCE_DELIVERY_ENABLED.get();
        if (subscription.getEnableExactlyOnceDelivery() != exactlyOnceDelivery) {
            subscriptionBuilder.setEnableExactlyOnceDelivery(exactlyOnceDelivery);
            fieldMask.addPaths("enable_exactly_once_delivery");
            toApply = true;
            log.debug("{}: enable_exactly_once_delivery {} => {}",
                    subscription.getName(),
                    subscription.getEnableExactlyOnceDelivery(),
                    exactlyOnceDelivery);
        }

        boolean retainAckedMessages = props.PUBSUB_SUBSCRIPTION_RETAIN_ACKED_MESSAGES.get();
        if (subscription.getRetainAckedMessages() != retainAckedMessages) {
            subscriptionBuilder.setRetainAckedMessages(retainAckedMessages);
            fieldMask.addPaths("retain_acked_messages");
            toApply = true;
            log.debug("{}: retain_acked_messages {} => {}",
                    subscription.getName(),
                    subscription.getRetainAckedMessages(),
                    retainAckedMessages);
        }

        long messageRetentionSeconds = props.PUBSUB_SUBSCRIPTION_MESSAGE_RETENTION_DURATION.get().getSeconds();
        if (subscription.getMessageRetentionDuration().getSeconds() != messageRetentionSeconds) {
            Duration newMessageRetention = Duration.newBuilder().setSeconds(messageRetentionSeconds).build();
            subscriptionBuilder.setMessageRetentionDuration(newMessageRetention);
            fieldMask.addPaths("message_retention_duration");
            toApply = true;
            log.debug("{}: message_retention_duration {} => {}",
                    subscription.getName(),
                    subscription.getMessageRetentionDuration().getSeconds(),
                    messageRetentionSeconds);
        }

        if (toApply) {
            try {
                var request = UpdateSubscriptionRequest.newBuilder().setSubscription(subscriptionBuilder).setUpdateMask(fieldMask);
                Subscription updatedSubscription = adminClient.updateSubscription(request.build());
                log.info("updated subscription: {}", updatedSubscription);
            } catch (ApiException e) {
                log.error("Failed to apply subscription updates", e);
            }
        }
    }
}
