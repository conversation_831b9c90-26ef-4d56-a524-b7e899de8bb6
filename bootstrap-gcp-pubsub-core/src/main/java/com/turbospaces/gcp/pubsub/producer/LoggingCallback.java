package com.turbospaces.gcp.pubsub.producer;

import java.util.Objects;
import java.util.UUID;

import com.google.common.util.concurrent.FutureCallback;
import com.turbospaces.api.Topic;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LoggingCallback implements FutureCallback<String> {
    private final String namespace;
    private final UUID messageId;
    private final Topic topic;
    private final long now;

    public LoggingCallback(String namespace, UUID messageId, Topic topic, long now) {
        this.namespace = Objects.requireNonNull(namespace);
        this.messageId = Objects.requireNonNull(messageId);
        this.topic = Objects.requireNonNull(topic);
        this.now = now;
    }
    @Override
    public void onSuccess(String result) {
        log.debug("({}:{}) has been accepted by PubSub (topic={}, guid={}) in {}ms",
                namespace,
                messageId,
                topic.name(),
                result,
                System.currentTimeMillis() - now);
    }
    @Override
    public void onFailure(Throwable ex) {
        log.error(ex.getMessage(), ex);
    }
}
