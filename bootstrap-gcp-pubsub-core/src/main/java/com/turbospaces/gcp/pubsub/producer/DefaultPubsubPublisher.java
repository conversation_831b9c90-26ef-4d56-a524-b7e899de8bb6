package com.turbospaces.gcp.pubsub.producer;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.google.cloud.spring.pubsub.core.publisher.PubSubPublisherTemplate;
import com.google.cloud.spring.pubsub.support.PublisherFactory;
import com.google.common.util.concurrent.FutureCallback;
import com.google.pubsub.v1.PubsubMessage;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcUtil;

import io.vavr.Function0;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class DefaultPubsubPublisher extends PubSubPublisherTemplate implements PubsubPublisher {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final ThreadLocal<Boolean> flag = DefaultBlockHoundIntegration.FLAG;

    public DefaultPubsubPublisher(ApplicationProperties props, PublisherFactory producerFactory) {
        super(producerFactory);
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public CompletableFuture<String> publish(String topic, PubsubMessage record, FutureCallback<String> callback) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC

        //
        // ~ temporary allow blocking call even though it is not blocking by API specification
        //
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public CompletableFuture<String> apply() {
                CompletableFuture<String> future = publish(topic, record);
                return future.whenComplete((result, ex) -> {
                    MdcUtil.propagate(mdc);
                    try {
                        if (Objects.nonNull(result)) {
                            callback.onSuccess(result);
                        } else {
                            callback.onFailure(ex);
                        }
                    } finally {
                        if (Objects.nonNull(mdc)) {
                            MDC.clear();
                        }
                    }
                });
            }
        });
    }
    @Override
    public CompletableFuture<String> publish(String topic, PubsubMessage pubsubMessage) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.publish(topic, pubsubMessage);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public <T> CompletableFuture<String> publish(String topic, T payload, Map<String, String> headers) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.publish(topic, payload, headers);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public <T> CompletableFuture<String> publish(String topic, T payload) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.publish(topic, payload);
        } finally {
            flag.set(toReset);
        }
    }
}
