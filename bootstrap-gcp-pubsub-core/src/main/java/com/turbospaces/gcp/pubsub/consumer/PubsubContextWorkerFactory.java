package com.turbospaces.gcp.pubsub.consumer;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.collect.ImmutableList;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;
import com.turbospaces.executor.AbstractContextWorkerFactory;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.executor.LogQueueFullCallerRunsPolicy;
import com.turbospaces.executor.PlatformThread;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.gcp.pubsub.PubsubWorkUnit;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;

public class PubsubContextWorkerFactory extends AbstractContextWorkerFactory<String, PubsubWorkUnit> {
    public PubsubContextWorkerFactory(ApplicationProperties props, MeterRegistry meterRegistry, RateLimiterRegistry rateLimiterRegistry) {
        super(props, meterRegistry);
        workers = CacheBuilder.newBuilder().build(new CacheLoader<>() {
            @Override
            public ThreadPoolContextWorker load(String topic) {
                var tfb = new ThreadFactoryBuilder();
                tfb.setDaemon(false);
                tfb.setNameFormat("pubsub-worker-" + topic + "-%d");
                tfb.setThreadFactory(new ThreadFactory() {
                    @Override
                    public Thread newThread(Runnable r) {
                        return new PlatformThread(props, r);
                    }
                });

                var min = props.KAFKA_MIN_WORKERS.get();
                var max = props.KAFKA_MAX_WORKERS.get();
                var ttl = props.APP_PLATFORM_MAX_IDLE.get();
                var limiter = new TriggerOnceAfterNOccurrencesRateLimiter(topic, min, timer);

                var pool = new ThreadPoolExecutor(
                        min,
                        max,
                        ttl.toSeconds(),
                        TimeUnit.SECONDS,
                        new SynchronousQueue<>(),
                        tfb.build(),
                        new LogQueueFullCallerRunsPolicy(limiter));

                var metrics = new ExecutorServiceMetrics(
                        pool,
                        "pubsub-worker",
                        ImmutableList.of(Tag.of("topic", topic)));
                metrics.bindTo(meterRegistry);

                ThreadPoolContextWorker worker = createWorker(pool);
                worker.setBeanName(beanName + topic);
                return worker;
            }
        });
    }
    @Override
    public ContextWorker worker(PubsubWorkUnit unit) {
        return workers.getUnchecked(unit.topic());
    }
    @Override
    public void destroy() throws Exception {
        super.destroy();

        //
        // ~ gracefully terminate
        //
        for (ContextWorker pool : workers.asMap().values()) {
            pool.destroy();
        }

        //
        // ~ cleanUp
        //
        workers.invalidateAll();
    }
    protected ThreadPoolContextWorker createWorker(ExecutorService pool) {
        ThreadPoolContextWorker worker = new ThreadPoolContextWorker(props, meterRegistry, pool, true);
        worker.afterPropertiesSet();
        return worker;
    }
    @Override
    public String toString() {
        return workers.asMap().toString();
    }
}
