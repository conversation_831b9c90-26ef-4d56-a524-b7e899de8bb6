package com.turbospaces.gcp.pubsub.producer;

import java.util.concurrent.CompletableFuture;

import com.google.common.util.concurrent.FutureCallback;
import com.google.pubsub.v1.PubsubMessage;

public interface PubsubPublisher {
    CompletableFuture<String> publish(String topic, PubsubMessage record);
    CompletableFuture<String> publish(String topic, PubsubMessage record, FutureCallback<String> callback);
}
