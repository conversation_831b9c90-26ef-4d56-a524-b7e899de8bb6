package reactor.blockhound.integration;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.mutable.MutableBoolean;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.MockApiFactory;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class KafkaBlockHoundIntegrationTest {
    @Test
    public void works() throws Throwable {
        String topic = "topic" + System.currentTimeMillis();

        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors(), topic);
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        cfg.setLocalProperty(props.APP_BLOCKHOUND_ENABLED.getKey(), true);

        SimpleBootstrap bootstrap = new SimpleBootstrap(props);
        bootstrap.withKafka(port);
        bootstrap.run();

        KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
        KafkaProducerProperties producerProps = new KafkaProducerProperties(props, bootstrap.keyStore(), si);

        MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(bootstrap.meterRegistry());

        try {
            //
            // ~ this block should not fail
            //
            for (int i = 0; i < 10; i++) {
                MockApiFactory apiFactory = new MockApiFactory(props, new CommonObjectMapper());

                DefaultRequestReplyMapper replyMapper = new DefaultRequestReplyMapper(props, bootstrap.meterRegistry());
                replyMapper.setBeanName(PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
                replyMapper.afterPropertiesSet();

                KafkaWithMetricsProducerFactory producerFactory = new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);
                KafkaPostTemplate kafkaTemplate = new KafkaPostTemplate(
                        props,
                        bootstrap.tracer(),
                        apiFactory,
                        replyMapper,
                        producerFactory,
                        props.APP_TIMER_INTERVAL);

                MutableBoolean failed = new MutableBoolean(false);
                CountDownLatch executed = new CountDownLatch(1);

                try (DefaultPlatformExecutorService platform = new DefaultPlatformExecutorService(props, new SimpleMeterRegistry())) {
                    platform.setBeanName(PlatformUtil.randomUUID().toString());
                    platform.afterPropertiesSet();

                    platform.execute(new Runnable() {
                        @Override
                        public void run() {
                            DefaultBlockHoundIntegration.FLAG.set(true);

                            try {
                                log.info("about to make kafka call1 ...");
                                kafkaTemplate.send(topic, Long.toString(System.currentTimeMillis()).getBytes());
                            } catch (Throwable err) {
                                log.atError().setCause(err).log();
                                failed.setTrue();
                            } finally {
                                DefaultBlockHoundIntegration.FLAG.remove();
                                executed.countDown();
                            }
                        }
                    });

                    kafkaTemplate.destroy();
                }

                Assertions.assertTrue(executed.await(30, TimeUnit.SECONDS));
                Assertions.assertFalse(failed.get());
            }
        } finally {
            try {
                bootstrap.shutdown();
            } finally {
                broker.destroy();
            }
        }
    }
}
