package com.turbospaces.kafka;

import java.security.KeyStore;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.pekko.actor.typed.ActorRef;
import org.apache.pekko.actor.typed.ActorSystem;
import org.apache.pekko.actor.typed.Behavior;
import org.apache.pekko.actor.typed.javadsl.AbstractBehavior;
import org.apache.pekko.actor.typed.javadsl.ActorContext;
import org.apache.pekko.actor.typed.javadsl.Behaviors;
import org.apache.pekko.actor.typed.javadsl.Receive;
import org.apache.pekko.japi.function.Function;
import org.apache.pekko.japi.function.Function2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.ContainerProperties.AckMode;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaStreamConsumer;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import reactor.core.publisher.Flux;

public class PekkoToKafkaAcceptorTest {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Test
    public void works() throws Throwable {
        String topic = "topic" + System.currentTimeMillis();

        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors(), topic);
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            int actorsCount = 1024;
            List<String> actorKeys = Lists.newArrayList();
            for (int i = 0; i < actorsCount; i++) {
                actorKeys.add("actors:" + PlatformUtil.randomAlphanumeric(getClass().getSimpleName().length()));
            }
            int count = actorsCount * broker.getPartitionsPerTopic();
            CountDownLatch latch = new CountDownLatch(count);

            AtomicLong created = new AtomicLong();
            AtomicLong reused = new AtomicLong();
            AtomicLong consumerSum = new AtomicLong();
            CountDownLatch completed = new CountDownLatch(1);

            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());
            Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);

            KafkaContextWorkerFactory worker = new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, bootstrap.meterRegistry(), bootstrap.rateLimiterRegistry());
            worker.afterPropertiesSet();

            ActorSystem<Command> system = ActorSystem.create(Behaviors.setup(ctx -> {
                return new KafkaToAkkaDispatcher(ctx, worker, latch, created, reused);
            }), "default");

            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty("test.topic", topic);
            cfg.setLocalProperty("actor-system", system);
            cfg.setLocalProperty("completed", completed);
            cfg.setLocalProperty("consumerSum", consumerSum);

            bootstrap.withKafka(port);
            bootstrap.run();

            try {
                AtomicLong producerSum = new AtomicLong();

                KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
                KafkaProducerProperties producerProps = new KafkaProducerProperties(props, bootstrap.keyStore(), si);

                MicrometerProducerListener<byte[], byte[]> metrMicrometerProducerListener = new MicrometerProducerListener<>(bootstrap.meterRegistry());
                KafkaWithMetricsProducerFactory producerFactory = new KafkaWithMetricsProducerFactory(producerProps, metrMicrometerProducerListener);

                KafkaTemplate<byte[], byte[]> kafkaTemplate = new KafkaTemplate<>(producerFactory);

                for (int i = 0; i < count; i++) {
                    byte[] key = actorKeys.get(i % actorsCount).getBytes();
                    byte[] value = Hashing.murmur3_128().hashBytes(key).toString().getBytes();
                    kafkaTemplate.send(new ProducerRecord<>(topic, null, key, value)).whenComplete((result, ex) -> {
                        if (ex != null) {
                            logger.error(ex.getMessage(), ex);
                        } else {
                            producerSum.addAndGet(result.getRecordMetadata().offset());
                        }
                    });
                }

                Assertions.assertTrue(completed.await(1, TimeUnit.MINUTES));

                Assertions.assertEquals(actorsCount, created.get());
                Assertions.assertEquals(actorsCount * broker.getPartitionsPerTopic() - created.get(), reused.get());
                Assertions.assertEquals(producerSum.get(), consumerSum.get());
            } finally {
                try {
                    bootstrap.shutdown();
                } finally {
                    system.terminate();
                }
            }
        } finally {
            broker.destroy();
        }
    }

    @Configuration
    public static class AppConfig {
        @Value("${test.topic}")
        private String topic;

        @Bean
        public MicrometerConsumerListener<byte[], byte[]> consumerMetricsListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }
        @Bean
        public AbstractKafkaConsumerProperties kafkaProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }
        @Bean
        public ContainerProperties kafkaContainerProps(ManualAcknowledgingKafkaConsumer consumer) {
            ContainerProperties props = new ContainerProperties(topic);
            props.setAckMode(AckMode.MANUAL);
            props.setMessageListener(consumer);
            return props;
        }
        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }
        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }
        @Bean
        @SuppressWarnings("unchecked")
        public ManualAcknowledgingKafkaConsumer consumer(ApplicationConfig cfg, ApplicationProperties props) {
            return new ManualAcknowledgingKafkaConsumer(props, new KafkaStreamConsumer() {
                @Override
                public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment ack, Consumer<?, ?> consumer) {
                    ActorSystem<Command> system = (ActorSystem<Command>) cfg.getRawProperty("actor-system");
                    CountDownLatch completed = (CountDownLatch) cfg.getRawProperty("completed");
                    AtomicLong consumerSum = (AtomicLong) cfg.getRawProperty("consumerSum");

                    system.tell(new BatchCommand(consumerSum, flux, () -> {
                        ack.acknowledge();
                        completed.countDown();
                    }));
                }
            });
        }
        @Bean
        public KafkaAcceptorChannel kafkaChannel(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                ContainerProperties cfg) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, cfg) {};
            channel.setAutoStartup(true);
            return channel;
        }
    }

    //
    // ~ marker interface
    //
    private interface Command {}

    private static class BatchCommand implements Command {
        public AtomicLong offsets;
        public Flux<KafkaWorkUnit> flux;
        public Acknowledgment ack;

        public BatchCommand(AtomicLong offsets, Flux<KafkaWorkUnit> flux, Acknowledgment ack) {
            this.offsets = offsets;
            this.flux = flux;
            this.ack = ack;
        }
    }

    private static class KafkaInbound implements Command {
        public final CountDownLatch latch;
        public final KafkaWorkUnit unit;
        public final KafkaContextWorkerFactory worker;
        public final BatchCommand batch;
        public final ActorRef<Command> replyTo;

        public KafkaInbound(CountDownLatch latch, KafkaWorkUnit unit, KafkaContextWorkerFactory worker, BatchCommand batch, ActorRef<Command> replyTo) {
            this.latch = latch;
            this.unit = unit;
            this.batch = batch;
            this.replyTo = replyTo;
            this.worker = worker;
        }
    }

    private static class PipeCommand implements Command {
        public final Long offset;
        public final CountDownLatch latch;
        public final BatchCommand batch;
        public final ActorRef<Command> replyTo;

        public PipeCommand(Long offset, CountDownLatch latch, BatchCommand batch, ActorRef<Command> replyTo) {
            this.latch = latch;
            this.offset = offset;
            this.batch = batch;
            this.replyTo = replyTo;
        }
    }

    private static class ReplyCommand implements Command {
        public Long offset;
        public final CountDownLatch latch;
        public final BatchCommand batch;

        public ReplyCommand(Long offset, CountDownLatch latch, BatchCommand batch) {
            this.offset = offset;
            this.latch = latch;
            this.batch = batch;
        }
    }

    private static class KafkaToAkkaDispatcher extends AbstractBehavior<Command> {
        private final KafkaContextWorkerFactory worker;
        private final CountDownLatch latch;
        private final AtomicLong created;
        private final AtomicLong reused;

        public KafkaToAkkaDispatcher(
                ActorContext<Command> context,
                KafkaContextWorkerFactory worker,
                CountDownLatch latch,
                AtomicLong created,
                AtomicLong reused) {
            super(context);
            this.worker = worker;
            this.latch = latch;
            this.created = created;
            this.reused = reused;
        }
        @Override
        public Receive<Command> createReceive() {
            return newReceiveBuilder().onMessage(BatchCommand.class, (Function<BatchCommand, Behavior<Command>>) batch -> {
                batch.flux.collectList().subscribe(units -> {
                    for (KafkaWorkUnit unit : units) {
                        Optional<AsciiString> opt = Objects.isNull(unit.key()) ? Optional.empty() : Optional.of(new AsciiString(unit.key()));
                        if (opt.isPresent()) {
                            AsciiString pk = opt.get();
                            Optional<ActorRef<Void>> childOpt = getContext().getChild(pk.toString());
                            if (childOpt.isPresent()) {
                                reused.incrementAndGet();
                                ActorRef<Void> ref = childOpt.get();
                                ref.unsafeUpcast().tell(new KafkaInbound(latch, unit, worker, batch, getContext().getSelf()));
                            } else {
                                created.incrementAndGet();
                                ActorRef<Command> ref = getContext().spawn(Behaviors.setup(OneByOne::new), pk.toString());
                                ref.tell(new KafkaInbound(latch, unit, worker, batch, getContext().getSelf()));
                            }
                        }
                    }
                });

                return Behaviors.same();
            }).onMessage(ReplyCommand.class, (Function<ReplyCommand, Behavior<Command>>) reply -> {
                CountDownLatch l = reply.latch;
                BatchCommand batch = reply.batch;
                AtomicLong offsets = batch.offsets;
                Acknowledgment ack = batch.ack;

                offsets.addAndGet(reply.offset);
                l.countDown();

                if (l.getCount() == 0) {
                    ack.acknowledge();
                }

                return Behaviors.same();
            }).build();
        }
    }

    public static class OneByOne extends AbstractBehavior<Command> {
        public OneByOne(ActorContext<Command> context) {
            super(context);
        }
        @Override
        public Receive<Command> createReceive() {
            return newReceiveBuilder()
                    .onMessage(KafkaInbound.class, this::handleInbound)
                    .onMessage(PipeCommand.class, this::handlePipe)
                    .build();
        }
        private Behavior<Command> handleInbound(KafkaInbound inbound) {
            CompletableFuture<Long> f = new CompletableFuture<>();
            getContext().pipeToSelf(f,
                    (Function2<Long, Throwable, Command>) (arg, throwable) -> new PipeCommand(arg, inbound.latch, inbound.batch, inbound.replyTo));

            KafkaContextWorkerFactory worker = inbound.worker;
            KafkaWorkUnit unit = inbound.unit;

            worker.worker(unit).actor(unit).submit(() -> {
                f.complete(inbound.unit.offset());
            });

            return Behaviors.same();
        }
        private Behavior<Command> handlePipe(PipeCommand pipe) {
            pipe.replyTo.tell(new ReplyCommand(pipe.offset, pipe.latch, pipe.batch));
            return Behaviors.same();
        }
    }
}
