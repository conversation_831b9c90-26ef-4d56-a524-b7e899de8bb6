package com.turbospaces.kafka;

import java.net.URI;
import java.security.KeyStore;
import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.ListOffsetsResult;
import org.apache.kafka.clients.admin.OffsetSpec;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.common.KafkaFuture;
import org.apache.kafka.common.TopicPartition;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.ContainerProperties.AckMode;
import org.springframework.kafka.support.SendResult;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.google.common.util.concurrent.Futures;
import com.google.protobuf.Any;
import com.google.protobuf.Int32Value;
import com.turbospaces.api.StackTracer;
import com.turbospaces.api.Topic;
import com.turbospaces.api.TopicRegistry;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cache.ReplicatedCacheManager;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.dispatch.TransactionalRequestHandler;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaReadOnlyContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaRequestConsumer;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultNonPersistentPostTemplate;
import com.turbospaces.rpc.DefaultPostTemplate;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.rpc.TransactionalRequestOutcomePublisher;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import api.v1.MockApiFactory;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.kafka.KafkaMessageFactory;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.opentracing.Tracer;
import io.opentracing.noop.NoopTracerFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KafkaConsumerTest {
    public static final AtomicBoolean MESSAGE_RECEIVED = new AtomicBoolean(false);
    public static final AtomicBoolean REPLY_CONDITION_COMPLETED = new AtomicBoolean(false);
    public static final String REQ_TOPIC = "req";
    public static final String REPLY_TO_TOPIC = "replyTo";
    public static final int PARTITION = 0;

    @Test
    void testApplicationIsWaitingUntilReplyWhenConditionCompleted() throws Throwable {
        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors(), REQ_TOPIC, REPLY_TO_TOPIC);
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());
            cfg.setLocalProperty("test.topic", REQ_TOPIC);
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");

            Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.withKafka(port);
            bootstrap.run();

            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            var kafkaAdmin = AdminClient.create(configs);

            try {
                KafkaProducerProperties producerProps = new KafkaProducerProperties(props, bootstrap.keyStore(), si);

                MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(bootstrap.meterRegistry());
                KafkaWithMetricsProducerFactory producerFactory = new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);

                KafkaTemplate<byte[], byte[]> kafkaTemplate = new KafkaTemplate<>(producerFactory);
                CloudEventBuilder eventTemplate = CloudEventBuilder.v1().withSource(new URI("https://turbospaces.com"));


                var headers = api.v1.Headers.newBuilder();
                headers.setMessageId(PlatformUtil.randomUUID().toString());
                headers.setBrandName("bluedream");
                headers.setReplyTo(REPLY_TO_TOPIC);
                DefaultRequestWrapperFacade facade = new DefaultRequestWrapperFacade(eventTemplate, headers.build(), Any.pack(Int32Value.of(1)));

                byte[] k = headers.getMessageId().getBytes();

                var messageWriter = KafkaMessageFactory.createWriter(REQ_TOPIC, PARTITION, System.currentTimeMillis(), k);
                var record = messageWriter.writeBinary(facade);

                kafkaTemplate.send(record).whenComplete((result, ex) -> {
                    if (ex != null) {
                        log.error(ex.getMessage(), ex);
                    }
                });
                Awaitility.await().untilAsserted(() -> Assertions.assertTrue(MESSAGE_RECEIVED.get()));
                kafkaTemplate.destroy();
            } finally {
                Assertions.assertEquals(0, getTotalReplyRecords(kafkaAdmin));
                Assertions.assertFalse(REPLY_CONDITION_COMPLETED.get());
                bootstrap.shutdown();
                Awaitility.await().untilAsserted(() -> Assertions.assertTrue(REPLY_CONDITION_COMPLETED.get()));
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(1, getTotalReplyRecords(kafkaAdmin)));
            }
        } finally {
            broker.destroy();
        }
    }

    private static Long getTotalReplyRecords(AdminClient kafkaAdmin) throws ExecutionException, InterruptedException {
        Map<TopicPartition, OffsetSpec> partitions = new HashMap<>();
        KafkaFuture<TopicDescription> topicRes = kafkaAdmin.describeTopics(List.of(REPLY_TO_TOPIC)).topicNameValues().get(REPLY_TO_TOPIC);
        topicRes.get().partitions().forEach((p) -> partitions.put(new TopicPartition(REPLY_TO_TOPIC, p.partition()), OffsetSpec.latest()));
        return kafkaAdmin.listOffsets(partitions).all().get().values().stream()
                .map(ListOffsetsResult.ListOffsetsResultInfo::offset)
                .reduce(Long::sum)
                .get();
    }

    @EnableKafka
    @Configuration
    public static class AppConfig {
        @Value("${test.topic}")
        private String topic;

        @Bean
        public CommonObjectMapper mapper() {
            return new CommonObjectMapper();
        }

        @Bean
        public Tracer tracer() {
            return NoopTracerFactory.create();
        }

        @Bean
        public ApiFactory apiFactory(ApplicationProperties props, CommonObjectMapper mapper) {
            return new MockApiFactory(props, mapper);
        }

        @Bean
        public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new DefaultRequestReplyMapper(props, meterRegistry);
        }

        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }

        @Bean
        public KafkaReadOnlyContextWorkerFactory paymentKafkaContextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaReadOnlyContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }

        @Bean
        public KafkaProducerProperties producerProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaProducerProperties(props, keyStore, si);
        }

        @Bean
        public MicrometerProducerListener<byte[], byte[]> producerMetricsListener(MeterRegistry meterRegistry) {
            return new MicrometerProducerListener<>(meterRegistry);
        }

        @Bean
        public KafkaWithMetricsProducerFactory producerFactory(KafkaProducerProperties configs, MicrometerProducerListener<byte[], byte[]> metrics) {
            return new KafkaWithMetricsProducerFactory(configs, metrics);
        }

        @Bean
        public KafkaPostTemplate kafkaTemplate(
                ApplicationProperties props,
                Tracer tracer,
                ApiFactory factory,
                DefaultRequestReplyMapper requestReplyMapper,
                KafkaWithMetricsProducerFactory producerFactory) throws Exception {
            return new KafkaPostTemplate(
                    props,
                    tracer,
                    factory,
                    requestReplyMapper,
                    producerFactory,
                    () -> Duration.ofSeconds(30)
            );
        }

        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }

        @Bean
        public AbstractKafkaConsumerProperties kafkaProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }

        @Bean
        public ContainerProperties kafkaContainerProps(ApplicationProperties appProps, KafkaRequestConsumer consumer) {
            ContainerProperties props = new ContainerProperties(topic);
            props.setAckMode(AckMode.MANUAL);
            props.setConsumerRebalanceListener(consumer);
            props.setMessageListener(new ManualAcknowledgingKafkaConsumer(appProps, consumer));
            props.setSyncCommits(true);
            return props;
        }

        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }

        @Bean
        public RequestConsumerChannel kafkaChannel(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                ContainerProperties cfg) {
            return new RequestConsumerChannel(props, consumerFactory, cfg);
        }

        public static class RequestConsumerChannel extends KafkaAcceptorChannel {
            protected RequestConsumerChannel(ApplicationProperties props, ConsumerFactory<byte[], byte[]> consumerFactory, ContainerProperties containerProperties) {
                super(props, consumerFactory, containerProperties);
                setAutoStartup(true);
            }
        }

        @Bean
        @SuppressWarnings("unchecked")
        public DefaultNonPersistentPostTemplate<SendResult<byte[], byte[]>> nonPersistentPostTemplate(
                ApplicationProperties props,
                ApiFactory apiFactory,
                KafkaPostTemplate kafkaPostTemplate) {
            return new DefaultNonPersistentPostTemplate<>(props, apiFactory, new TransactionalRequestOutcomePublisher<>() {
                @Override
                public CompletionStage<SendResult<byte[], byte[]>> sendNotify(NotifyQueuePostSpec spec) {
                    return CompletableFuture.completedStage(Mockito.mock(SendResult.class));
                }
                @Override
                public CompletionStage<SendResult<byte[], byte[]>> sendEvent(EventQueuePostSpec spec) {
                    return kafkaPostTemplate.sendEvent(spec);
                }
                @Override
                public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
                    return kafkaPostTemplate.publishReply(outcome);
                }
                @Override
                public CompletionStage<?> publishNotifications(Topic destination, TransactionalRequestOutcome outcome) {
                    return CompletableFuture.completedStage(Mockito.mock(SendResult.class));
                }
                @Override
                public CompletionStage<?> publishEvents(Topic destination, TransactionalRequestOutcome outcome) {
                    return kafkaPostTemplate.publishEvents(destination, outcome);
                }
                @Override
                public ApiFactory apiFactory() {
                    return apiFactory;
                }
            });
        }

        @Bean
        public DefaultPostTemplate<SendResult<byte[], byte[]>> postTemplate(
                ApplicationProperties props,
                ApiFactory apiFactory,
                KafkaPostTemplate kafkaPostTemplate,
                DefaultNonPersistentPostTemplate<SendResult<byte[], byte[]>> nonPersistentPostTemplate) {
            return new DefaultPostTemplate<>(props, apiFactory, kafkaPostTemplate, nonPersistentPostTemplate);
        }

        @Bean
        public ReqHandler reqHandler() {
            return new ReqHandler();
        }

        @Bean
        public AdminClient kafkaAdmin(DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            return AdminClient.create(configs);
        }

        @Bean
        public KafkaRequestConsumer requestConsumer(
                ApplicationProperties props,
                ApiFactory apiFactory,
                Tracer tracer,
                MeterRegistry meterRegistry,
                DefaultPostTemplate<SendResult<byte[], byte[]>> template,
                List<TransactionalRequestHandler<?, ?>> handlers,
                KafkaContextWorkerFactory workerFactory,
                List<StackTracer> stackTracers) {
            return new KafkaRequestConsumer(
                    props,
                    tracer,
                    meterRegistry,
                    new ReplicatedCacheManager() {
                        @Override
                        public void clearAll(boolean localOnly) throws Throwable {

                        }
                    },
                    new TopicRegistry() {
                        @Override
                        public Iterator<Topic> iterator() {
                            return Collections.emptyIterator();
                        }
                        @Override
                        public Topic forName(String name) {
                            return new Topic() {
                                @Override
                                public AsciiString name() {
                                    return AsciiString.of(topic);
                                }
                                @Override
                                public void configure(ApplicationConfig cfg) {

                                }
                            };
                        }
                    },
                    template,
                    handlers,
                    workerFactory,
                    apiFactory,
                    stackTracers::iterator,
                    new CurrentTransactionProvider() {
                        @Override
                        public Optional<Object> current() {
                            return Optional.empty();
                        }
                    });
        }


        public static class ReqHandler extends TransactionalRequestHandler<Int32Value, Int32Value.Builder> {
            @Override
            public void apply(TransactionalRequest<Int32Value, Int32Value.Builder> cmd) throws Throwable {
                log.info("in cmd: {}", cmd.request().getValue());
                MESSAGE_RECEIVED.set(true);
                cmd.replyWhen(Futures.submit(() -> {
                    try {
                        log.info("starting hanging for 500 ms");
                        Thread.sleep(500);
                        cmd.reply().setValue(42);
                        REPLY_CONDITION_COMPLETED.set(true);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }, ForkJoinPool.commonPool()));
            }

            @Override
            public boolean actorIsRequired() {
                return false;
            }

            @Override
            public boolean isImmediateAcknowledge() {
                return false;
            }
        }
    }
}
