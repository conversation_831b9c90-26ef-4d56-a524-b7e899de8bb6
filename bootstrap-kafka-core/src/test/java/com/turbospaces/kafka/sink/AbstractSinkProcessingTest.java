package com.turbospaces.kafka.sink;

import java.security.KeyStore;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArraySet;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.ListConsumerGroupOffsetsResult;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.google.common.base.Suppliers;
import com.google.protobuf.Any;
import com.google.protobuf.Int32Value;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.Topic;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.MockApiFactory;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.opentracing.Tracer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
class AbstractSinkProcessingTest {
    public static final String EVENTS_TOPIC = PlatformUtil.randomAlphabetic(AbstractSinkProcessingTest.class.getSimpleName().length());
    public static final Topic TOPIC = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.of(EVENTS_TOPIC);
        }

        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };
    private static CopyOnWriteArraySet<Integer> PROCESSED_UNIQUE_RECORDS = new CopyOnWriteArraySet<>();
    public static final int PERMANT_ERROR_SIMULATION = 43;

    @Test
    void test() throws Throwable {
        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());
            // for speeding up test
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_MAX.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty(props.KAFKA_SPRING_BACK_OFF_ENABLED.getKey(), false);
            cfg.setLocalProperty(props.KAFKA_NACK_POLL_INTERVAL.getKey(), Duration.ofMillis(1));

            Bootstrap bootstrap = new SimpleBootstrap(props, AbstractSinkProcessingTest.AppConfig.class);
            bootstrap.withKafka(port);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                // Happy path
                KafkaPostTemplate postTemplate = applicationContext.getBean(KafkaPostTemplate.class);
                postTemplate.sendEvent(TOPIC, Int32Value.of(1), AsciiString.of("1")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(PERMANT_ERROR_SIMULATION), AsciiString.of("1")).toCompletableFuture().get();
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(2, PROCESSED_UNIQUE_RECORDS.size()));
                PROCESSED_UNIQUE_RECORDS.clear();
                postTemplate.sendEvent(TOPIC, Int32Value.of(2), AsciiString.of("1")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(3), AsciiString.of("1")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(4), AsciiString.of("1")).toCompletableFuture().get();
                // no more records processed
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(2, PROCESSED_UNIQUE_RECORDS.size()));

                ContainerProperties eventTopic = applicationContext.getBean(ContainerProperties.class);
                AdminClient kafkaAdmin = applicationContext.getBean(AdminClient.class);
                Awaitility.await().untilAsserted(() -> {
                    Long offset = 0L;
                    try {
                        ListConsumerGroupOffsetsResult result = kafkaAdmin.listConsumerGroupOffsets(eventTopic.getGroupId());
                        Map<TopicPartition, OffsetAndMetadata> offsets = result.partitionsToOffsetAndMetadata().get();
                        offset = offsets.values().stream().map(OffsetAndMetadata::offset).reduce(Long::sum).get();
                    } catch (Exception e) {
                        // ignore
                    }
                    // nothing commited
                    Assertions.assertEquals(0, offset); // ~ verify we acknowledged (committed)
                });
            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    @SneakyThrows
    public static void processRecord(KafkaWorkUnit unit, Any any) {
        Int32Value res = any.unpack(Int32Value.class);
        log.info("IN rk: {}, msg: {} topic: {} offset: {}", new String(unit.key()), res.getValue(), unit.topicPartition().topic(),
                unit.offset());
        PROCESSED_UNIQUE_RECORDS.add(res.getValue());
        if (res.getValue() == PERMANT_ERROR_SIMULATION) {
            throw new RuntimeException("Simulated error");
        }
    }

    public static class Sink extends AbstractSink {

        protected Sink(KafkaListenerEndpointRegistry registry, Property<Boolean> enabled) {
            super(registry, enabled);
        }

        @Override
        @SneakyThrows
        public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
            List<KafkaWorkUnit> records = flux.collectList().block();
            logger.info("IN accepted {} events ...", records.size());
            for (KafkaWorkUnit record : records) {
                try {
                    processRecord(record, Any.parseFrom(record.value().openStream()));
                    logger.info("IN processed");
                } catch (Exception e) {
                    logger.error("Error processing record", e);
                    throw new RuntimeException();
                }
            }
            acknowledgment.acknowledge();
        }
    }

    @Configuration
    @EnableKafka
    public static class AppConfig {
        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }

        @Bean
        public AbstractSinkProcessingTest.Sink sink(
                ApplicationProperties props,
                KafkaListenerEndpointRegistry registry) {
            Property<Boolean> prop = props.factory().get(EVENTS_TOPIC, Boolean.class).orElse(true);
            return new AbstractSinkProcessingTest.Sink(registry, prop);
        }

        @Bean
        public AbstractKafkaConsumerProperties kafkaConsumerProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }

        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }

        @Bean
        public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new DefaultRequestReplyMapper(props, meterRegistry);
        }

        @Bean
        public ContainerProperties containerProperties(AbstractSinkProcessingTest.Sink consumer, ApplicationProperties appProps) {
            ContainerProperties props = new ContainerProperties(EVENTS_TOPIC);


            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(appProps, consumer);


            props.setAckMode(ContainerProperties.AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important
            return props;
        }

        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }

        @Bean
        public KafkaWithMetricsProducerFactory producerFactory(
                ApplicationProperties props,
                DynamicCloud cloud,
                KeyStore keyStore,
                MeterRegistry meterRegistry) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            return new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);
        }

        @Bean
        public KafkaPostTemplate kafkaTemplate(
                ApplicationProperties props,
                Tracer tracer,
                DefaultRequestReplyMapper mapper,
                KafkaWithMetricsProducerFactory producerFactory) throws Exception {
            return new KafkaPostTemplate(
                    props,
                    tracer,
                    new MockApiFactory(props, new CommonObjectMapper()),
                    mapper,
                    producerFactory,
                    Suppliers.ofInstance(Duration.ofSeconds(30)));
        }

        @Bean
        public KafkaAcceptorChannel eventChannel(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                ContainerProperties cfg) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, cfg) {
            };
            channel.setAutoStartup(true);
            return channel;
        }

        @Bean
        public AdminClient kafkaAdmin(DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            return AdminClient.create(configs);
        }
    }

}
