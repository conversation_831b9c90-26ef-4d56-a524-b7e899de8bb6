package com.turbospaces.kafka.sink;

import java.security.KeyStore;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.consumer.Consumer;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.test.EmbeddedKafkaZKBroker;

import com.google.common.base.Suppliers;
import com.google.common.collect.Iterables;
import com.google.protobuf.Any;
import com.google.protobuf.util.Timestamps;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.DefaultKafkaSender;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.MockApiFactory;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import reactor.core.publisher.Flux;

class AbstractSinkTest {
    public static final String UPS = "sink";

    @Test
    public void testPartitionRewokeOnPause() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, 10);
        broker.afterPropertiesSet();

        try {
            int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1); // ~ max 1 consumer

            Bootstrap bootstrap = new SimpleBootstrap(props, AbstractSinkTest.AppConfig.class);
            bootstrap.withKafka(brokerPort);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                DefaultKafkaSender sender = applicationContext.getBean(DefaultKafkaSender.class);
                ContainerProperties containerProperties = applicationContext.getBean(ContainerProperties.class);
                AdminClient kafkaAdmin = applicationContext.getBean(AdminClient.class);

                sender.send(UPS, Any.pack(Timestamps.fromDate(new Date())).toByteArray()).get(1, TimeUnit.MINUTES);


                assertPartitionsAssigned(kafkaAdmin, containerProperties, 10);

                // disable sink
                cfg.setLocalProperty(UPS, false);
                assertPartitionsAssigned(kafkaAdmin, containerProperties, 0);

                // enable back
                cfg.setLocalProperty(UPS, true);
                sender.send(UPS, Any.pack(Timestamps.fromDate(new Date())).toByteArray()).get(1, TimeUnit.MINUTES);
                assertPartitionsAssigned(kafkaAdmin, containerProperties, 10);

            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    private static void assertPartitionsAssigned(AdminClient kafkaAdmin, ContainerProperties containerProperties, int actual) {
        Awaitility.await().untilAsserted(() -> {
            var res = kafkaAdmin.describeConsumerGroups(List.of(Objects.requireNonNull(containerProperties.getGroupId()))).all().get(1, TimeUnit.MINUTES);
            if (actual == 0) {
                Assertions.assertFalse(res.values().isEmpty());
                Assertions.assertTrue(res.values().iterator().next().members().isEmpty());

            } else {
                Assertions.assertFalse(res.values().isEmpty());
                Assertions.assertFalse(res.values().iterator().next().members().isEmpty());
                Assertions.assertEquals(res.values().iterator().next().members().iterator().next().assignment().topicPartitions().size(), actual);
            }
        });
    }

    public static class Sink extends AbstractSink {

        protected Sink(KafkaListenerEndpointRegistry registry, Property<Boolean> enabled) {
            super(registry, enabled);
        }

        @Override
        public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
            logger.info("IN");
            acknowledgment.acknowledge();
        }
    }

    @Configuration
    @EnableKafka
    public static class AppConfig {
        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }

        @Bean
        public AbstractSinkTest.Sink sink(ApplicationProperties props, KafkaListenerEndpointRegistry registry) {
            Property<Boolean> enabled = props.factory().get(UPS, Boolean.class).orElse(true);
            return new AbstractSinkTest.Sink(registry, enabled);
        }

        @Bean
        public AbstractKafkaConsumerProperties kafkaConsumerProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }

        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }

        @Bean
        public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new DefaultRequestReplyMapper(props, meterRegistry);
        }

        @Bean
        public ContainerProperties containerProperties(AbstractSinkTest.Sink sinkRetryEnabled, ApplicationProperties appProps) {
            ContainerProperties props = new ContainerProperties(UPS);

            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(appProps, sinkRetryEnabled);

            props.setAckMode(ContainerProperties.AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important

            return props;
        }


        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }

        @Bean
        public KafkaWithMetricsProducerFactory producerFactory(
                ApplicationProperties props,
                KeyStore keyStore,
                MeterRegistry meterRegistry,
                DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            return new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);
        }

        @Bean
        public DefaultKafkaSender kafkaTemplate(
                ApplicationProperties props,
                Tracer tracer,
                DefaultRequestReplyMapper mapper,
                KafkaWithMetricsProducerFactory producerFactory) throws Exception {
            return new KafkaPostTemplate(
                    props,
                    tracer,
                    new MockApiFactory(props, new CommonObjectMapper()),
                    mapper,
                    producerFactory,
                    Suppliers.ofInstance(Duration.ofSeconds(30)));
        }

        @Bean
        public KafkaAcceptorChannel kafkaChannelWithRetry(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                ContainerProperties containerProperties) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, containerProperties) {};
            channel.setAutoStartup(true);
            return channel;
        }

        @Bean
        public AdminClient kafkaAdmin(DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            return AdminClient.create(configs);
        }
    }
}