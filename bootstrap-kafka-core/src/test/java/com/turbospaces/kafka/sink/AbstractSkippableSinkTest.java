package com.turbospaces.kafka.sink;

import java.security.KeyStore;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Supplier;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.awaitility.Awaitility;
import org.jctools.maps.NonBlockingHashMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.google.common.base.Suppliers;
import com.google.protobuf.Any;
import com.google.protobuf.Int32Value;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.Topic;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.KafkaTopic;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.MockApiFactory;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.opentracing.Tracer;
import jakarta.inject.Named;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class AbstractSkippableSinkTest {
    public static final String EVENTS_TOPIC = PlatformUtil.randomAlphabetic(AbstractSkippableSinkTest.class.getSimpleName().length());
    public static final Topic TOPIC = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.of(EVENTS_TOPIC);
        }

        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };
    public static final int ONE_TIME_ERROR_SIMULATION = 42;
    public static final int PERMANT_ERROR_SIMULATION = 43;
    public static final String DEADLETTER_TOPIC_NAME = "deadletter";
    public static final String DEADLETTER_RETRY_ENABLED = "deadletter_retryEnabled";
    public static final String EVENT_PROPERTIES = "eventProperties";
    public static final String DEAD_LETTER_PROPERTIES = "deadLetterProperties";
    private static ConcurrentMap<String, Integer> RESULTS = new NonBlockingHashMap<>();

    @Test
    public void test() throws Throwable {
        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());
            // for speeding up test
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_MAX.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_FIRST.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_NUM.getKey(), 3);
            cfg.setLocalProperty(props.KAFKA_NACK_POLL_INTERVAL.getKey(), Duration.ofMillis(1));

            Bootstrap bootstrap = new SimpleBootstrap(props, AbstractSkippableSinkTest.AppConfig.class);
            bootstrap.withKafka(port);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                // Happy path
                KafkaPostTemplate postTemplate = applicationContext.getBean(KafkaPostTemplate.class);
                postTemplate.sendEvent(TOPIC, Int32Value.of(1), AsciiString.of("1")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(2), AsciiString.of("1")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(1), AsciiString.of("2")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(1), AsciiString.of("3")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(2), AsciiString.of("3")).toCompletableFuture().get();

                ContainerProperties eventTopic = applicationContext.getBean(EVENT_PROPERTIES, ContainerProperties.class);
                ContainerProperties deadletterTopic = applicationContext.getBean(DEAD_LETTER_PROPERTIES, ContainerProperties.class);
                AdminClient kafkaAdmin = applicationContext.getBean(AdminClient.class);

                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(5, getTotalOffsets(kafkaAdmin, eventTopic.getGroupId(), EVENTS_TOPIC)));
                // verify tasks was processed in correct order 2-1 = 1 not 1-2 = -1
                Assertions.assertEquals(3, RESULTS.size());
                Assertions.assertEquals(1, RESULTS.get("1"));
                Assertions.assertEquals(1, RESULTS.get("2"));
                Assertions.assertEquals(1, RESULTS.get("3"));
                RESULTS.clear();

                // test offset is moved and send to dead letter queue
                postTemplate.sendEvent(TOPIC, Int32Value.of(ONE_TIME_ERROR_SIMULATION), AsciiString.of("1")).toCompletableFuture().get();
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(1, RESULTS.size()));
                Assertions.assertEquals(6, getTotalOffsets(kafkaAdmin, eventTopic.getGroupId(), EVENTS_TOPIC));
                Assertions.assertEquals(1, (long) getTotalOffsets(kafkaAdmin, deadletterTopic.getGroupId(), DEADLETTER_TOPIC_NAME));
                RESULTS.clear();

                // new message is consumed
                postTemplate.sendEvent(TOPIC, Int32Value.of(1), AsciiString.of("2")).toCompletableFuture().get();
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(1, RESULTS.size()));
                Assertions.assertEquals(7, getTotalOffsets(kafkaAdmin, eventTopic.getGroupId(), EVENTS_TOPIC));
                RESULTS.clear();

                // test permanent error
                postTemplate.sendEvent(TOPIC, Int32Value.of(ONE_TIME_ERROR_SIMULATION), AsciiString.of("2")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(PERMANT_ERROR_SIMULATION), AsciiString.of("1")).toCompletableFuture().get();
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(2, RESULTS.size()));
                Assertions.assertEquals(2, (long) getTotalOffsets(kafkaAdmin, deadletterTopic.getGroupId(), DEADLETTER_TOPIC_NAME));

                // disable dead letter retry so all offsets are commited
                cfg.setLocalProperty(DEADLETTER_RETRY_ENABLED, false);
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(3, (long) getTotalOffsets(kafkaAdmin, deadletterTopic.getGroupId(), DEADLETTER_TOPIC_NAME)));
                RESULTS.clear();

                cfg.setLocalProperty(props.KAFKA_SINK_DEAD_LETTER_ENABLED.getKey(), false);
                postTemplate.sendEvent(TOPIC, Int32Value.of(PERMANT_ERROR_SIMULATION), AsciiString.of("1")).toCompletableFuture().get();
                postTemplate.sendEvent(TOPIC, Int32Value.of(1), AsciiString.of("2")).toCompletableFuture().get();
                Awaitility.await().untilAsserted(() -> Assertions.assertEquals(1, RESULTS.size()));
                Assertions.assertEquals(3, (long) getTotalOffsets(kafkaAdmin, deadletterTopic.getGroupId(), DEADLETTER_TOPIC_NAME));
                Assertions.assertEquals(10, getTotalOffsets(kafkaAdmin, eventTopic.getGroupId(), EVENTS_TOPIC));
                RESULTS.clear();
            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    private static Long getTotalOffsets(AdminClient kafkaAdmin, String groupId, String deadletterTopicName) {
        try {
            return kafkaAdmin.listConsumerGroupOffsets(groupId).partitionsToOffsetAndMetadata().get().entrySet().stream()
                    .filter(e -> e.getKey().topic().equals(deadletterTopicName))
                    .map(Map.Entry::getValue).map(OffsetAndMetadata::offset).reduce(Long::sum).get();
        } catch (Exception e) {
            return -1L;
        }
    }

    public static final KafkaTopic DEADLETTER_KAFKA_TOPIC = new KafkaTopic() {
        @Override
        public AsciiString name() {
            return AsciiString.of(DEADLETTER_TOPIC_NAME);
        }

        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };

    @SneakyThrows
    public static Object processRecord(KafkaWorkUnit unit, Any any) {
        Int32Value res = any.unpack(Int32Value.class);
        log.info("IN rk: {}, msg: {} topic: {} offset: {}", new String(unit.key()), res.getValue(), unit.topicPartition().topic(),
                unit.topicPartition().partition());
        if (res.getValue() == PERMANT_ERROR_SIMULATION) {
            if (unit.topicPartition().topic().equals(DEADLETTER_KAFKA_TOPIC.name().toString())) {
                RESULTS.put(new String(unit.key()), res.getValue());
            }
            throw new RuntimeException();
        }
        if (res.getValue() == ONE_TIME_ERROR_SIMULATION) {
            if (unit.topicPartition().topic().equals(DEADLETTER_KAFKA_TOPIC.name().toString())) {
                RESULTS.put(new String(unit.key()), res.getValue());
            } else {
                throw new RuntimeException();
            }
        }
        RESULTS.compute(new String(unit.key()), (key, prev) -> {
            if (prev != null && res.getValue() == prev) {
                // retry
                return prev + res.getValue();
            }
            // sequential call
            return prev == null ? res.getValue() : res.getValue() - prev;
        });
        return null;
    }

    public static class Sink extends AbstractSkippableSink {
        public Sink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry,
                KafkaContextWorkerFactory workerFactory,
                Property<Boolean> enabled,
                KafkaPostTemplate kafkaPostTemplate) {
            super(
                    props,
                    meterRegistry,
                    retryRegistry,
                    registry,
                    workerFactory,
                    enabled,
                    kafkaPostTemplate,
                    DEADLETTER_KAFKA_TOPIC, new Supplier<Boolean>() {
                        @Override
                        public Boolean get() {
                            return false;
                        }
                    });
        }

        @Override
        public void accept(KafkaWorkUnit unit, Any any) {
            AbstractSkippableSinkTest.processRecord(unit, any);
        }
    }

    public static class DeadLetterSink extends AbstractSequentialProcessingSink {
        protected DeadLetterSink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry,
                Property<Boolean> enabled,
                Supplier<Boolean> retryEnabled) {
            super(props, meterRegistry, retryRegistry, registry, enabled, retryEnabled);
        }
        @Override
        public void accept(KafkaWorkUnit record, Any event) throws Exception {
            AbstractSkippableSinkTest.processRecord(record, event);
        }
    }

    @Configuration
    @EnableKafka
    public static class AppConfig {
        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }
        @Bean
        public AbstractSkippableSinkTest.Sink sink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaContextWorkerFactory worker,
                KafkaListenerEndpointRegistry registry,
                KafkaPostTemplate kafkaTemplate) {
            Property<Boolean> prop = props.factory().get(EVENTS_TOPIC, Boolean.class).orElse(true);
            return new AbstractSkippableSinkTest.Sink(props, meterRegistry, retryRegistry, registry, worker, prop, kafkaTemplate);
        }

        @Bean
        public AbstractSkippableSinkTest.DeadLetterSink deadLetterSink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaContextWorkerFactory worker,
                KafkaListenerEndpointRegistry registry,
                KafkaPostTemplate kafkaTemplate) {
            Property<Boolean> prop = props.factory().get(DEADLETTER_TOPIC_NAME, Boolean.class).orElse(true);
            Property<Boolean> deadletterRetry = props.factory().get(DEADLETTER_RETRY_ENABLED, Boolean.class).orElse(true);
            return new AbstractSkippableSinkTest.DeadLetterSink(props, meterRegistry, retryRegistry, registry, prop, deadletterRetry);
        }
        @Bean
        public AbstractKafkaConsumerProperties kafkaConsumerProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }
        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }
        @Bean
        public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new DefaultRequestReplyMapper(props, meterRegistry);
        }
        @Bean(name = EVENT_PROPERTIES)
        public ContainerProperties containerProperties(AbstractSkippableSinkTest.Sink consumer, ApplicationProperties appProps) {
            ContainerProperties props = new ContainerProperties(EVENTS_TOPIC);

            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(appProps, consumer);

            props.setAckMode(ContainerProperties.AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important

            return props;
        }
        @Bean(name = DEAD_LETTER_PROPERTIES)
        public ContainerProperties deadLetterContainerProperties(AbstractSkippableSinkTest.DeadLetterSink consumer, ApplicationProperties appProps) {
            ContainerProperties props = new ContainerProperties(DEADLETTER_TOPIC_NAME);

            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(appProps, consumer);

            props.setAckMode(ContainerProperties.AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-deadletter-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important

            return props;
        }
        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }
        @Bean
        public KafkaWithMetricsProducerFactory producerFactory(
                ApplicationProperties props,
                DynamicCloud cloud,
                KeyStore keyStore,
                MeterRegistry meterRegistry) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            return new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);
        }
        @Bean
        public KafkaPostTemplate kafkaTemplate(
                ApplicationProperties props,
                Tracer tracer,
                DefaultRequestReplyMapper mapper,
                KafkaWithMetricsProducerFactory producerFactory) throws Exception {
            return new KafkaPostTemplate(
                    props,
                    tracer,
                    new MockApiFactory(props, new CommonObjectMapper()),
                    mapper,
                    producerFactory,
                    Suppliers.ofInstance(Duration.ofSeconds(30)));
        }
        @Bean
        public KafkaAcceptorChannel eventChannel(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                @Named(EVENT_PROPERTIES) ContainerProperties cfg) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, cfg) {};
            channel.setAutoStartup(true);
            return channel;
        }
        @Bean
        public KafkaAcceptorChannel deadLetterChannedl(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                @Named(DEAD_LETTER_PROPERTIES) ContainerProperties cfg) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, cfg) {};
            channel.setAutoStartup(true);
            return channel;
        }
        @Bean
        public AdminClient kafkaAdmin(DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            return AdminClient.create(configs);
        }
    }

}
