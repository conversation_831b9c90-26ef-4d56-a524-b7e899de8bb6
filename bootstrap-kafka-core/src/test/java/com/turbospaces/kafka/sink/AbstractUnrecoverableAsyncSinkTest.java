package com.turbospaces.kafka.sink;

import java.security.KeyStore;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.ListConsumerGroupOffsetsResult;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.ContainerProperties.AckMode;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.google.common.base.Suppliers;
import com.google.common.primitives.Ints;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.DefaultKafkaSender;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.MockApiFactory;
import io.github.resilience4j.core.functions.CheckedFunction;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.retry.Retry.Metrics;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import reactor.core.publisher.Flux;

public class AbstractUnrecoverableAsyncSinkTest {
    public static final String UPS = PlatformUtil.randomAlphabetic(AbstractUnrecoverableAsyncSinkTest.class.getSimpleName().length());

    @Test
    public void test() throws Throwable {
        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            int maxRetries = 5;
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1); // ~ max 1 consumer

            // ~ cut down retries
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_FIRST.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_MAX.getKey(), Duration.ofSeconds(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_NUM.getKey(), maxRetries);

            Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.withKafka(port);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                DefaultKafkaSender sender = applicationContext.getBean(DefaultKafkaSender.class);
                ContainerProperties containerProperties = applicationContext.getBean(ContainerProperties.class);
                AdminClient kafkaAdmin = applicationContext.getBean(AdminClient.class);
                sender.send(UPS, Ints.toByteArray(maxRetries));

                Metrics metrics = bootstrap.retryRegistry().retry(UPS).getMetrics();

                // ~ await asynchronously
                Awaitility.await().until(() -> metrics.getNumberOfSuccessfulCallsWithRetryAttempt() >= 1);

                // ~ some assertions
                Assertions.assertEquals(1, metrics.getNumberOfSuccessfulCallsWithRetryAttempt());
                Assertions.assertEquals(0, metrics.getNumberOfSuccessfulCallsWithoutRetryAttempt());
                Assertions.assertEquals(0, metrics.getNumberOfFailedCallsWithoutRetryAttempt());
                Assertions.assertEquals(0, metrics.getNumberOfFailedCallsWithRetryAttempt());

                ListConsumerGroupOffsetsResult result = kafkaAdmin.listConsumerGroupOffsets(containerProperties.getGroupId());
                Map<TopicPartition, OffsetAndMetadata> offsets = result.partitionsToOffsetAndMetadata().get();
                Long offset = offsets.values().stream().map(OffsetAndMetadata::offset).reduce(Long::sum).get();
                Assertions.assertEquals(1, offset); // ~ verify we acknowledged (committed)
            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    public static class Sink extends AbstractUnrecoverableAsyncSink {
        public Sink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry,
                Property<Boolean> enabled) {
            super(props, meterRegistry, retryRegistry, registry, UPS, enabled);
        }
        @Override
        public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
            List<KafkaWorkUnit> list = flux.collectList().block();
            applyRecordsAsync(list, new CheckedFunction<KafkaWorkUnit, Object>() {
                private int counter = 1;

                @Override
                public Object apply(KafkaWorkUnit t1) throws Throwable {
                    int maxRetries = Ints.fromByteArray(t1.value().read());

                    logger.info("iteration: {}, maxRetries: {}", counter, maxRetries);

                    if (counter >= maxRetries) {
                        return t1.toString();
                    }

                    while (true) {
                        counter++;
                        ExceptionUtils.wrapAndThrow(new RuntimeException("remote call failed on iteration: " + counter));
                    }
                }
            }, acknowledgment);
        }
    }

    @Configuration
    @EnableKafka
    public static class AppConfig {
        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }
        @Bean
        public Sink sink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry) {
            Property<Boolean> prop = props.factory().get(UPS, Boolean.class).orElse(true);
            return new Sink(props, meterRegistry, retryRegistry, registry, prop);
        }
        @Bean
        public AbstractKafkaConsumerProperties kafkaConsumerProps(ApplicationProperties props, DynamicCloud cloud, KeyStore keyStore) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }
        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }
        @Bean
        public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new DefaultRequestReplyMapper(props, meterRegistry);
        }
        @Bean
        public ContainerProperties containerProperties(Sink consumer, ApplicationProperties appProps) {
            ContainerProperties props = new ContainerProperties(UPS);

            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(appProps, consumer);

            props.setAckMode(AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important

            return props;
        }
        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }
        @Bean
        public KafkaWithMetricsProducerFactory producerFactory(
                ApplicationProperties props,
                KeyStore keyStore,
                MeterRegistry meterRegistry,
                DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            return new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);
        }
        @Bean
        public DefaultKafkaSender kafkaTemplate(
                ApplicationProperties props,
                Tracer tracer,
                DefaultRequestReplyMapper mapper,
                KafkaWithMetricsProducerFactory producerFactory) throws Exception {
            return new KafkaPostTemplate(
                    props,
                    tracer,
                    new MockApiFactory(props, new CommonObjectMapper()),
                    mapper,
                    producerFactory,
                    Suppliers.ofInstance(Duration.ofSeconds(30)));
        }
        @Bean
        public KafkaAcceptorChannel kafkaChannel(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                ContainerProperties cfg) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, cfg) {};
            channel.setAutoStartup(true);
            return channel;
        }
        @Bean
        public AdminClient kafkaAdmin(DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            return AdminClient.create(configs);
        }
    }
}
