package com.turbospaces.kafka.sink;

import java.security.KeyStore;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.ListConsumerGroupOffsetsResult;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.ContainerProperties.AckMode;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;
import org.springframework.kafka.test.EmbeddedKafkaZKBroker;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Suppliers;
import com.google.common.collect.Iterables;
import com.google.protobuf.Any;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.DefaultKafkaSender;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.MockApiFactory;
import api.v1.ObfuscatePrinter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.retry.Retry.Metrics;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class AbstractSequentialProcessingSinkTest {
    public static final String UPS_1 = PlatformUtil.randomAlphabetic(AbstractSequentialProcessingSinkTest.class.getSimpleName().length());
    public static final String UPS_2 = PlatformUtil.randomAlphabetic(AbstractSequentialProcessingSinkTest.class.getSimpleName().length() + 1);

    @Test
    public void testRetryEnabled() throws Throwable {
        EmbeddedKafkaZKBroker broker = new EmbeddedKafkaZKBroker(1, false, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();

        try {
            int brokerPort = Iterables.getOnlyElement(Arrays.asList(broker.getBrokerAddresses())).getPort();
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1); // ~ max 1 consumer

            // ~ cut down retries
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_FIRST.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_MAX.getKey(), Duration.ofMinutes(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_NUM.getKey(), 1);
            cfg.setLocalProperty(props.KAFKA_SPRING_BACK_OFF_ENABLED.getKey(), false);
            cfg.setLocalProperty(props.KAFKA_NACK_POLL_INTERVAL.getKey(), Duration.ofMillis(100));

            Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.withKafka(brokerPort);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                DefaultKafkaSender sender = applicationContext.getBean(DefaultKafkaSender.class);
                ContainerProperties containerProperties = applicationContext.getBean("containerPropertiesWithRetry", ContainerProperties.class);
                AdminClient kafkaAdmin = applicationContext.getBean(AdminClient.class);

                sender.send(UPS_1, Any.pack(Timestamps.fromDate(new Date())).toByteArray());

                Metrics metrics = bootstrap.retryRegistry().retry(Sink.class.getSimpleName()).getMetrics();
                // ~ await asynchronously
                Awaitility.await().until(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        return metrics.getNumberOfFailedCallsWithRetryAttempt() >= 1;
                    }
                });

                ListConsumerGroupOffsetsResult result = kafkaAdmin.listConsumerGroupOffsets(containerProperties.getGroupId());
                Map<TopicPartition, OffsetAndMetadata> offsets1 = result.partitionsToOffsetAndMetadata().get();

                Assertions.assertTrue(offsets1.isEmpty()); // ~ verify no offset committed

                Awaitility.await().until(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        return metrics.getNumberOfFailedCallsWithRetryAttempt() >= 2;
                    }
                });

                Map<TopicPartition, OffsetAndMetadata> offsets2 = result.partitionsToOffsetAndMetadata().get();
                Assertions.assertTrue(offsets2.isEmpty()); // ~ verify no offset committed

            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    @Test
    public void testRetryDisabled() throws Throwable {
        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors());
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());

            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1); // ~ max 1 consumer

            // ~ cut down retries
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_FIRST.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_MAX.getKey(), Duration.ofMinutes(1));
            cfg.setLocalProperty(props.APP_BACKOFF_RETRY_NUM.getKey(), 10);
            cfg.setLocalProperty(props.KAFKA_SPRING_BACK_OFF_ENABLED.getKey(), false);

            Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.withKafka(port);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                DefaultKafkaSender sender = applicationContext.getBean(DefaultKafkaSender.class);
                ContainerProperties containerProperties = applicationContext.getBean("containerPropertiesWithoutRetry", ContainerProperties.class);
                AdminClient kafkaAdmin = applicationContext.getBean(AdminClient.class);

                sender.send(UPS_2, Any.pack(Timestamps.fromDate(new Date())).toByteArray());

                Metrics metrics = bootstrap.retryRegistry().retry(Sink.class.getSimpleName()).getMetrics();
                // ~ await asynchronously
                Awaitility.await().until(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        return metrics.getNumberOfFailedCallsWithRetryAttempt() >= 1;
                    }
                });

                ListConsumerGroupOffsetsResult result = kafkaAdmin.listConsumerGroupOffsets(containerProperties.getGroupId());
                Map<TopicPartition, OffsetAndMetadata> offsets = result.partitionsToOffsetAndMetadata().get();

                Assertions.assertFalse(offsets.isEmpty()); // ~ verify no offset committed

            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    public static class Sink extends AbstractSequentialProcessingSink {
        private int counter = 1;

        public Sink(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry,
                Property<Boolean> enabled,
                Property<Boolean> retryEnabled) {
            super(props, meterRegistry, retryRegistry, registry, enabled, retryEnabled);
        }
        @Override
        public void accept(KafkaWorkUnit record, Any event) throws Exception {
            Timestamp unpack = event.unpack(Timestamp.class);
            logger.info("iteration: {}, date: {}", counter, ObfuscatePrinter.shortDebugString(unpack.toBuilder()));

            while (true) {
                counter++;
                ExceptionUtils.wrapAndThrow(new RuntimeException("remote call failed on iteration: " + counter));
            }
        }
    }

    @Configuration
    @EnableKafka
    public static class AppConfig {
        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }
        @Bean
        public Sink sinkRetryEnabled(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry) {
            Property<Boolean> prop1 = props.factory().get(UPS_1, Boolean.class).orElse(true);
            Property<Boolean> prop2 = props.factory().get(UPS_1, Boolean.class).orElse(true);
            return new Sink(props, meterRegistry, retryRegistry, registry, prop1, prop2);
        }
        @Bean
        public Sink sinkRetryDisabled(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RetryRegistry retryRegistry,
                KafkaListenerEndpointRegistry registry) {
            Property<Boolean> prop1 = props.factory().get(UPS_1, Boolean.class).orElse(true);
            Property<Boolean> prop2 = props.factory().get(UPS_1, Boolean.class).orElse(false);
            return new Sink(props, meterRegistry, retryRegistry, registry, prop1, prop2);
        }
        @Bean
        public AbstractKafkaConsumerProperties kafkaConsumerProps(
                ApplicationProperties props,
                KeyStore keyStore,
                DynamicCloud cloud,
                MeterRegistry meterRegistry) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }
        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }
        @Bean
        public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
            return new DefaultRequestReplyMapper(props, meterRegistry);
        }
        @Bean
        public ContainerProperties containerPropertiesWithRetry(@Qualifier("sinkRetryEnabled") Sink sinkRetryEnabled, ApplicationProperties applicationProperties) {
            ContainerProperties props = new ContainerProperties(UPS_1);

            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(applicationProperties, sinkRetryEnabled);

            props.setAckMode(AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important

            return props;
        }

        @Bean
        public ContainerProperties containerPropertiesWithoutRetry(@Qualifier("sinkRetryDisabled") Sink sinkRetryDisabled, ApplicationProperties appProps) {
            ContainerProperties props = new ContainerProperties(UPS_2);

            ManualAcknowledgingKafkaConsumer manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(appProps, sinkRetryDisabled);

            props.setAckMode(AckMode.MANUAL);
            props.setMessageListener(manualAcknowledgingKafkaConsumer);
            props.setGroupId(String.format("junit-%s-sink", System.currentTimeMillis()));
            props.setSyncCommits(true); // ~ important

            return props;
        }
        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }
        @Bean
        public KafkaWithMetricsProducerFactory producerFactory(
                ApplicationProperties props,
                KeyStore keyStore,
                DynamicCloud cloud,
                MeterRegistry meterRegistry) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            KafkaProducerProperties producerProps = new KafkaProducerProperties(props, keyStore, si);

            MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(meterRegistry);
            return new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);
        }
        @Bean
        public DefaultKafkaSender kafkaTemplate(
                ApplicationProperties props,
                Tracer tracer,
                DefaultRequestReplyMapper mapper,
                KafkaWithMetricsProducerFactory producerFactory) throws Exception {
            return new KafkaPostTemplate(
                    props,
                    tracer,
                    new MockApiFactory(props, new ObjectMapper()),
                    mapper,
                    producerFactory,
                    Suppliers.ofInstance(Duration.ofSeconds(30)));
        }
        @Bean
        public KafkaAcceptorChannel kafkaChannelWithRetry(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                @Qualifier("containerPropertiesWithRetry") ContainerProperties containerPropertiesWithRetry) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, containerPropertiesWithRetry) {};
            channel.setAutoStartup(true);
            return channel;
        }

        @Bean
        public KafkaAcceptorChannel kafkaChannelWithoutRetry(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                @Qualifier("containerPropertiesWithoutRetry") ContainerProperties containerPropertiesWithoutRetry) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, containerPropertiesWithoutRetry) {};
            channel.setAutoStartup(true);
            return channel;
        }
        @Bean
        public AdminClient kafkaAdmin(DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            Map<String, Object> configs = new HashMap<>();
            configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
            return AdminClient.create(configs);
        }
    }
}
