package com.turbospaces.kafka;

import java.util.Optional;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.search.RequiredSearch;

public class DefaultWorkerFactoryTest {
    @Test
    public void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        cfg.setLocalProperty(props.KAFKA_MIN_WORKERS.getKey(), 1);
        cfg.setLocalProperty(props.KAFKA_MAX_WORKERS.getKey(), 1);

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, RootContext.class);
        ConfigurableApplicationContext context = bootstrap.run();
        KafkaContextWorkerFactory workerFactory = context.getBean(KafkaContextWorkerFactory.class);

        ContextWorker pool1 = workerFactory.worker(new KafkaRecord(new ConsumerRecord<>("top", 1, 1024, null, new byte[] {})));
        ContextWorker pool2 = workerFactory.worker(new KafkaRecord(new ConsumerRecord<>("top", 2, 1024, null, new byte[] {})));

        pool1.execute(() -> {});
        pool2.execute(() -> {});

        RequiredSearch search1 = bootstrap.meterRegistry()
                .get("executor.pool.size")
                .tag("name", "kafka-worker")
                .tag("topic", "top")
                .tag("partition", "1");
        RequiredSearch search2 = bootstrap.meterRegistry()
                .get("executor.pool.size")
                .tag("name", "kafka-worker")
                .tag("topic", "top")
                .tag("partition", "2");

        Assertions.assertEquals(1.0, search1.gauge().value());
        Assertions.assertEquals(1.0, search2.gauge().value());
    }

    @Configuration
    public static class RootContext {
        @Bean
        public KafkaContextWorkerFactory workerFactory(ApplicationProperties props, MeterRegistry meterRegistry, RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }
    }
}