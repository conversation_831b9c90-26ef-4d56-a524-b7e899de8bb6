package com.turbospaces.kafka;

import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyStore;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.core.MicrometerProducerListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.ContainerProperties.AckMode;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.EmbeddedKafkaKraftBroker;

import com.google.common.util.concurrent.Uninterruptibles;
import com.google.protobuf.Any;
import com.google.protobuf.util.Timestamps;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.api.mappers.DefaultRequestFacadeMapper;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.kafka.consumer.AbstractKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaStreamConsumer;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.kafka.KafkaMessageFactory;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.GroupedFlux;
import reactor.core.publisher.Mono;

@Slf4j
public class KafkaAcceptorTest {
    @Test
    public void works() throws Throwable {
        String topic = "topic" + System.currentTimeMillis();

        EmbeddedKafkaBroker broker = new EmbeddedKafkaKraftBroker(1, Runtime.getRuntime().availableProcessors(), topic);
        broker.afterPropertiesSet();
        int port = Integer.parseInt(System.getProperty("spring.kafka.bootstrap-servers").split(":")[1]);

        try {
            int keys = 64;
            int count = keys * broker.getPartitionsPerTopic();
            CountDownLatch latch = new CountDownLatch(count);

            ApplicationConfig cfg = ApplicationConfig.mock();
            ApplicationProperties props = new ApplicationProperties(cfg.factory());
            cfg.setLocalProperty("test.topic", topic);
            cfg.setLocalProperty("latch", latch);
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");

            Bootstrap bootstrap = new SimpleBootstrap(props, AppConfig.class);
            bootstrap.withKafka(port);
            ConfigurableApplicationContext applicationContext = bootstrap.run();

            try {
                KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
                KafkaProducerProperties producerProps = new KafkaProducerProperties(props, bootstrap.keyStore(), si);

                MicrometerProducerListener<byte[], byte[]> metricsProducerListener = new MicrometerProducerListener<>(bootstrap.meterRegistry());
                KafkaWithMetricsProducerFactory producerFactory = new KafkaWithMetricsProducerFactory(producerProps, metricsProducerListener);

                KafkaTemplate<byte[], byte[]> kafkaTemplate = new KafkaTemplate<>(producerFactory);
                CloudEventBuilder eventTemplate = CloudEventBuilder.v1().withSource(new URI("https://turbospaces.com"));

                for (int i = 1; i <= count; i++) {
                    int partition = i % broker.getPartitionsPerTopic();

                    var headers = api.v1.Headers.newBuilder();
                    headers.setMessageId(PlatformUtil.randomUUID().toString());
                    headers.setBrandName("bluedream" + i);
                    headers.setReplyTo("replyTo" + i);
                    headers.setTimeout(i);
                    DefaultRequestWrapperFacade facade = new DefaultRequestWrapperFacade(eventTemplate, headers.build(), Any.pack(Timestamps.now()));

                    byte[] k = headers.getMessageId().getBytes();

                    var messageWriter = KafkaMessageFactory.createWriter(topic, partition, System.currentTimeMillis(), k);
                    var record = messageWriter.writeBinary(facade);

                    kafkaTemplate.send(record).whenComplete((result, ex) -> {
                        if (ex != null) {
                            log.error(ex.getMessage(), ex);
                        }
                    });
                }

                KafkaListenerEndpointRegistry registry = applicationContext.getBean(KafkaListenerEndpointRegistry.class);
                Collection<MessageListenerContainer> channels = registry.getAllListenerContainers();
                Assertions.assertEquals(1, channels.size());

                for (MessageListenerContainer it : channels) {
                    KafkaAcceptorChannel channel = (KafkaAcceptorChannel) it;
                    ManualAcknowledgingKafkaConsumer manualAck = (ManualAcknowledgingKafkaConsumer) channel.getContainerProperties().getMessageListener();
                    Assertions.assertTrue(manualAck.is(KafkaStreamConsumer.class));
                    Assertions.assertFalse(manualAck.is(Collection.class));
                    channel.start();
                }

                Assertions.assertTrue(latch.await(30, TimeUnit.SECONDS));

                for (MessageListenerContainer it : channels) {
                    it.stop();
                }

                kafkaTemplate.destroy();
            } finally {
                bootstrap.shutdown();
            }
        } finally {
            broker.destroy();
        }
    }

    @EnableKafka
    @Configuration
    public static class AppConfig {
        @Value("${test.topic}")
        private String topic;

        @Bean
        public MicrometerConsumerListener<byte[], byte[]> metricsConsumerListener(MeterRegistry meterRegistry) {
            return new MicrometerConsumerListener<>(meterRegistry);
        }
        @Bean
        public AbstractKafkaConsumerProperties kafkaProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
            KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
            return new KafkaConsumerProperties(props, keyStore, si);
        }
        @Bean
        public ContainerProperties kafkaContainerProps(ManualAcknowledgingKafkaConsumer consumer) {
            ContainerProperties props = new ContainerProperties(topic);
            props.setAckMode(AckMode.MANUAL);
            props.setConsumerRebalanceListener(consumer);
            props.setMessageListener(consumer);
            props.setSyncCommits(true);
            return props;
        }
        @Bean
        public KafkaWithMetricsConsumerFactory factory(AbstractKafkaConsumerProperties props, MicrometerConsumerListener<byte[], byte[]> listener) {
            return new KafkaWithMetricsConsumerFactory(props, listener);
        }
        @Bean
        public KafkaContextWorkerFactory contextWorkerFactory(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                RateLimiterRegistry rateLimiterRegistry) {
            return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
                @Override
                public Optional<Object> current() {
                    return Optional.empty();
                }
            }, props, meterRegistry, rateLimiterRegistry);
        }
        @Bean
        public ManualAcknowledgingKafkaConsumer consumer(ApplicationConfig cfg, ApplicationProperties props, KafkaContextWorkerFactory worker) throws URISyntaxException {
            CloudEventBuilder eventTemplate = CloudEventBuilder.v1().withSource(new URI("https://turbospaces.com"));
            DefaultRequestFacadeMapper mapper = new DefaultRequestFacadeMapper(eventTemplate);

            return new ManualAcknowledgingKafkaConsumer(props, new KafkaStreamConsumer() {
                @Override
                public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment ack, Consumer<?, ?> consumer) {
                    Flux<GroupedFlux<Optional<AsciiString>, KafkaWorkUnit>> groupBy = flux.groupBy(r -> Optional.ofNullable(new AsciiString(r.key())));
                    Flux<GroupedFlux<Optional<AsciiString>, KafkaWorkUnit>> key = groupBy.filter(KEY);

                    key.map(GroupedFlux::collectList).subscribe(new java.util.function.Consumer<Mono<List<KafkaWorkUnit>>>() {
                        @Override
                        public void accept(Mono<List<KafkaWorkUnit>> mono) {
                            mono.subscribe(new java.util.function.Consumer<List<KafkaWorkUnit>>() {
                                @Override
                                public void accept(List<KafkaWorkUnit> units) {
                                    log.info("mono: {}", units.size());

                                    for (KafkaWorkUnit unit : units) {
                                        var name = "ce_" + ApiFactory.CLOUD_EVENT_NATIVE_FORMAT;
                                        var isNative = Boolean.parseBoolean(new String(unit.headers().lastHeader(name).value()));
                                        Assertions.assertTrue(isNative);

                                        try {
                                            var reqw = mapper.unpack(unit);
                                            var headers = reqw.headers();
                                            Assertions.assertTrue(headers.getTimeout() > 0);
                                            Assertions.assertEquals("bluedream" + headers.getTimeout(), headers.getBrandName());
                                            Assertions.assertEquals("replyTo" + headers.getTimeout(), headers.getReplyTo());

                                        } catch (Exception err) {
                                            ExceptionUtils.wrapAndThrow(err);
                                        }

                                        worker.worker(unit).actor(unit).submit(new CheckedRunnable() {
                                            @Override
                                            public void run() {
                                                CountDownLatch latch = (CountDownLatch) cfg.getRawProperty("latch");
                                                Uninterruptibles.sleepUninterruptibly(1, TimeUnit.MILLISECONDS);
                                                latch.countDown();
                                                log.debug("curr count = {}, unit: {}", latch.getCount(), unit);
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    });

                    ack.acknowledge();
                }
            });
        }
        @Bean
        public KafkaAcceptorChannel kafkaChannel(
                ApplicationProperties props,
                KafkaWithMetricsConsumerFactory consumerFactory,
                ContainerProperties cfg) {
            KafkaAcceptorChannel channel = new KafkaAcceptorChannel(props, consumerFactory, cfg) {};
            channel.setAutoStartup(false);
            return channel;
        }
    }
}
