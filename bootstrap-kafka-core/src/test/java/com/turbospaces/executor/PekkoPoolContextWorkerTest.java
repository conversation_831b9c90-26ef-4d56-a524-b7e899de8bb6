package com.turbospaces.executor;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.kafka.KafkaRecord;

import io.vavr.CheckedFunction0;
import io.vavr.CheckedRunnable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class PekkoPoolContextWorkerTest {
    @Test
    void works() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());

        cfg.setLocalProperty(props.KAFKA_MIN_WORKERS.getKey(), 1);
        cfg.setLocalProperty(props.KAFKA_MAX_WORKERS.getKey(), 1);

        SimpleBootstrap bootstrap = new SimpleBootstrap(props);
        bootstrap.run();

        DefaultPlatformExecutorService platform = new DefaultPlatformExecutorService(props, bootstrap.meterRegistry());
        platform.setBeanName(PlatformUtil.randomUUID().toString());
        platform.afterPropertiesSet();

        PekkoPoolContextWorker worker = new PekkoPoolContextWorker(props, bootstrap.meterRegistry(), platform);
        worker.setBeanName(PlatformUtil.randomUUID().toString());
        worker.afterPropertiesSet();


        for (int i = 0; i < Runtime.getRuntime().availableProcessors(); i++) {
            var msg = Long.toString(i);
            var pool1 = worker.actor(new KafkaRecord(new ConsumerRecord<>("top", 1, 1024, "1".getBytes(), new byte[] {})));
            pool1.submit(new CheckedRunnable() {
                @Override
                public void run() {
                    log.debug("execution (1 actor) {} : {}", Thread.currentThread(), msg);
                }
            }).get();

            var pool2 = worker.actor(new KafkaRecord(new ConsumerRecord<>("top", 2, 1024, "2".getBytes(), new byte[] {})));
            pool2.submit(new CheckedFunction0<Long>() {
                @Override
                public Long apply() throws Throwable {
                    log.debug("execution (2 actor) {} : {}", Thread.currentThread(), msg);
                    return System.currentTimeMillis();
                }
            }).get();
        }

        worker.destroy();
        platform.destroy();
        bootstrap.shutdown();
    }
}
