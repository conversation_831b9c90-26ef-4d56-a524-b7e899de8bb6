package com.turbospaces.plugins;

import java.security.KeyStore;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.BootstrapContextClosedEvent;
import org.springframework.boot.BootstrapRegistry;
import org.springframework.boot.BootstrapRegistryInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.kafka.core.KafkaAdmin;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.netflix.archaius.api.Config;
import com.turbospaces.api.ReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaSecurity;
import com.turbospaces.ups.KafkaServiceInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KafkaBootstrapInitializer implements BootstrapRegistryInitializer, Ordered {
    private final KeyStore keyStore;
    private final ApplicationProperties props;
    private final Collection<Topic> topics;
    private final KafkaServiceInfo si;

    public KafkaBootstrapInitializer(KeyStore keyStore, ApplicationProperties props, KafkaServiceInfo si) throws Exception {
        this(keyStore, props, si, Collections.emptyList());
    }
    public KafkaBootstrapInitializer(KeyStore keyStore, ApplicationProperties props, KafkaServiceInfo si, Topic[] topics) throws Exception {
        this(keyStore, props, si, Arrays.asList(topics));
    }
    public KafkaBootstrapInitializer(KeyStore keyStore, ApplicationProperties props, KafkaServiceInfo si, Collection<Topic> topics) throws Exception {
        this.keyStore = Objects.requireNonNull(keyStore);
        this.props = Objects.requireNonNull(props);
        this.si = Objects.requireNonNull(si);
        this.topics = Objects.requireNonNull(topics);
    }
    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE;
    }
    @Override
    public void initialize(BootstrapRegistry registry) {
        //
        // ~ well we don't want to use response pattern via MQ anymore (we have direct non-persistent connections between nodes)
        //
        for (Topic topic : topics) {
            if (topic instanceof ReplyTopic) {
                throw new IllegalArgumentException("creation of reply topic is not longer supported and deprecated: " + topic.name());
            }
        }

        Duration timeout = props.APP_WAIT_FOR_HEALTHCHECKS_TIMEOUT.get();
        Map<String, Object> opts = new HashMap<>();

        if (props.KAFKA_MIGRATION_ENABLED.get()) {
            //
            // ~ SASL SSL
            //
            opts.putAll(KafkaSecurity.configureSasl(si, keyStore));

            KafkaAdmin kafkaAdmin = new KafkaAdmin(opts) {
                @Override
                protected Collection<NewTopic> newTopics() {
                    ImmutableList.Builder<NewTopic> toReturn = ImmutableList.builder();
                    for (Topic it : topics) {
                        String name = it.name().toString();
                        Config view = props.cfg().getPrefixedView(name);
                        if (view.isEmpty()) {
                            log.warn("will not create topic: {}", name);
                        } else {
                            Optional<Integer> numPartitions = Optional.empty();
                            Optional<Short> replicationFactor = Optional.empty();
                            Map<String, String> configs = Maps.newHashMap();

                            for (String key : view.keys()) {
                                String value = view.getRawProperty(key).toString();
                                if (Topic.PARTITIONS.equals(key)) {
                                    numPartitions = Optional.of(view.getInteger(key));
                                } else if (Topic.REPLICATION_FACTOR.equals(key)) {
                                    if (BooleanUtils.isFalse(props.APP_DEV_MODE.get())) {
                                        replicationFactor = Optional.of(view.getInteger(key).shortValue());
                                    }
                                } else {
                                    configs.put(key, value);
                                }
                            }

                            NewTopic newTopic = new NewTopic(name, numPartitions, replicationFactor).configs(configs);
                            log.info("about to configure: {}", newTopic);
                            toReturn.add(newTopic);
                        }
                    }
                    return toReturn.build();
                }
            };
            kafkaAdmin.setAutoCreate(true);
            kafkaAdmin.setModifyTopicConfigs(true);
            kafkaAdmin.setFatalIfBrokerNotAvailable(true);
            kafkaAdmin.setOperationTimeout((int) timeout.toSeconds());
            kafkaAdmin.setBootstrapServersSupplier(new Supplier<String>() {
                @Override
                public String get() {
                    return si.getBootstrapServers();
                }
            });
            StopWatch stopWatch = StopWatch.createStarted();
            boolean success = kafkaAdmin.initialize();
            registry.addCloseListener(new ApplicationListener<BootstrapContextClosedEvent>() {
                @Override
                public void onApplicationEvent(BootstrapContextClosedEvent event) {
                    ConfigurableApplicationContext context = event.getApplicationContext();
                    context.getBeanFactory().registerSingleton("kafka-admin" + Math.abs(hashCode()), kafkaAdmin);
                }
            });
            if (success) {
                stopWatch.stop();
                log.info("completed configuration of topics: {} in: {}", topics.stream().map(Topic::name).toList(), stopWatch);
            }
        }
    }
}
