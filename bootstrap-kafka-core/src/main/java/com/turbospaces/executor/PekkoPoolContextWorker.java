package com.turbospaces.executor;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

import org.apache.commons.lang3.time.StopWatch;
import org.apache.pekko.actor.BootstrapSetup;
import org.apache.pekko.actor.typed.ActorRef;
import org.apache.pekko.actor.typed.ActorSystem;
import org.apache.pekko.actor.typed.Behavior;
import org.apache.pekko.actor.typed.javadsl.AbstractBehavior;
import org.apache.pekko.actor.typed.javadsl.ActorContext;
import org.apache.pekko.actor.typed.javadsl.Behaviors;
import org.apache.pekko.actor.typed.javadsl.Receive;
import org.apache.pekko.dispatch.ExecutionContexts;
import org.apache.pekko.japi.function.Function;
import org.slf4j.MDC;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.typesafe.config.ConfigFactory;

import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import io.vavr.CheckedRunnable;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class PekkoPoolContextWorker extends AbstractContextWorker {
    private final ApplicationProperties props;
    private final MeterRegistry meterRegistry;
    private final Executor executor;
    private final boolean autoClose;
    private ActorSystem<Command> actorSystem;

    public PekkoPoolContextWorker(ApplicationProperties props, MeterRegistry meterRegistry, PlatformExecutorService executor) {
        this(props, meterRegistry, executor, false);
    }
    public PekkoPoolContextWorker(ApplicationProperties props, MeterRegistry meterRegistry, Executor executor, boolean autoClose) {
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.executor = Objects.requireNonNull(executor);
        this.autoClose = autoClose;
    }
    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();

        var map = new Properties();

        map.put("pekko.loglevel", "debug".toUpperCase());
        map.put("pekko.loggers.0", "org.apache.pekko.event.slf4j.Slf4jLogger");
        map.put("pekko.actor.debug.lifecycle", "on");
        map.put("pekko.actor.debug.autoreceive", "on");
        map.put("pekko.actor.debug.receive", "on");
        map.put("pekko.actor.debug.unhandled", "on");

        var cfg = ConfigFactory.parseProperties(map);
        var classLoader = Thread.currentThread().getContextClassLoader();
        var executionContext = ExecutionContexts.fromExecutor(MoreExecutors.directExecutor());
        var setup = BootstrapSetup.create().withClassloader(classLoader).withConfig(cfg).withDefaultExecutionContext(executionContext);

        actorSystem = ActorSystem.create(Behaviors.setup(ctx -> new ActorSystemDispatcher(ctx, executor, meterRegistry)), "default", setup);
    }
    @Override
    public ContextWorker forKey(WorkUnit unit) {
        var key = new AsciiString(unit.key());
        return new AbstractContextWorker() {
            @Override
            public void execute(Runnable command) {
                actorSystem.tell(new Command(key, command));
            }
            @Override
            public ListenableFuture<?> submit(CheckedRunnable command) {
                SettableFuture<Object> toReturn = SettableFuture.create();
                actorSystem.tell(new Command(key, command, toReturn));
                return toReturn;
            }
            @Override
            public <T> ListenableFuture<T> submit(CheckedFunction0<T> command) {
                SettableFuture<T> toReturn = SettableFuture.create();
                actorSystem.tell(new Command(key, command, toReturn));
                return toReturn;
            }
            @Override
            public ContextWorker forKey(WorkUnit workUnit) {
                throw new UnsupportedOperationException();
            }
        };
    }
    @Override
    public void execute(Runnable command) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        executor.execute(wrapRunnable(command, mdc));
    }
    @Override
    public void destroy() {
        super.destroy();

        try {
            actorSystem.terminate();
        } finally {
            //
            // ~ try to shutdown rejection handler if possible
            //
            if (autoClose) {
                if (executor instanceof ExecutorService service) {
                    StopWatch stopWatch = StopWatch.createStarted();
                    Duration timeout = props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get();
                    List<Runnable> never = PlatformUtil.shutdownExecutor(service, timeout);
                    stopWatch.stop();
                    log.debug("stopped worker executor in {}. {} task(s) were never executed ...", stopWatch, never.size());

                    if (service instanceof ThreadPoolExecutor tpe) {
                        var rejection = tpe.getRejectedExecutionHandler();
                        if (rejection instanceof LogQueueFullCallerRunsPolicy lqf) {
                            lqf.dispose();
                        }
                    }
                }
            }
        }
    }

    @RequiredArgsConstructor(access = lombok.AccessLevel.PRIVATE)
    @AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
    @SuppressWarnings("rawtypes")
    private static class Command {
        private final AsciiString key;
        private final Object task;
        private SettableFuture toComplete;
    }

    private static final class ActorSystemDispatcher extends AbstractBehavior<Command> {
        private final Executor executor;
        private final MeterRegistry meterRegistry;

        public ActorSystemDispatcher(ActorContext<Command> context, Executor executor, MeterRegistry meterRegistry) {
            super(context);
            this.executor = Objects.requireNonNull(executor);
            this.meterRegistry = Objects.requireNonNull(meterRegistry);
        }
        @Override
        public Receive<Command> createReceive() {
            return newReceiveBuilder().onMessage(Command.class, new Function<>() {
                @Override
                public Behavior<Command> apply(Command cmd) throws Exception {
                    var key = cmd.key;
                    var opt = getContext().getChild(key.toString());
                    if (opt.isPresent()) {
                        ActorRef<Void> ref = opt.get();
                        ref.unsafeUpcast().tell(cmd);
                    } else {
                        var serial = new SerialContextWorker(key, executor, meterRegistry);
                        ActorRef<Command> ref = getContext().spawn(Behaviors.setup(ctx -> new SerialActor(ctx, serial)), key.toString());
                        ref.tell(cmd);
                    }

                    return Behaviors.same();
                }
            }).build();
        }
    }

    private static final class SerialActor extends AbstractBehavior<Command> {
        private final ContextWorker worker;

        private SerialActor(ActorContext<Command> context, ContextWorker worker) {
            super(context);
            this.worker = Objects.requireNonNull(worker);
        }
        @Override
        public Receive<Command> createReceive() {
            return newReceiveBuilder().onAnyMessage(new Function<>() {
                @Override
                @SuppressWarnings("unchecked")
                public Behavior<Command> apply(Command cmd) throws Exception {
                    var task = cmd.task;
                    var toComplete = cmd.toComplete;

                    if (task instanceof CheckedFunction0 checked) {
                        toComplete.setFuture(worker.submit(checked));
                    }
                    else if (task instanceof CheckedRunnable checked) {
                        toComplete.setFuture(worker.submit(checked));
                    } else if (task instanceof Runnable runnable) {
                        worker.execute(runnable);
                    }

                    return Behaviors.same();
                }
            }).build();
        }
    }
}
