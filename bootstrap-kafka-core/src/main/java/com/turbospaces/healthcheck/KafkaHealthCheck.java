package com.turbospaces.healthcheck;

import java.security.KeyStore;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.producer.ProducerConfig;

import com.codahale.metrics.health.HealthCheck;
import com.google.common.collect.ImmutableSet;
import com.turbospaces.api.Topic;
import com.turbospaces.boot.AbstractHealtchCheck;
import com.turbospaces.kafka.KafkaSecurity;
import com.turbospaces.ups.KafkaServiceInfo;

import io.netty.util.AsciiString;

public class KafkaHealthCheck extends AbstractHealtchCheck {
    private final Map<String, Object> opts = new HashMap<>();
    private final Set<Topic> topics;
    private final AdminClient client;

    public KafkaHealthCheck(KafkaServiceInfo si, KeyStore keyStore, Topic[] topics) throws Exception {
        this(si, keyStore, Arrays.asList(topics));
    }
    public KafkaHealthCheck(KafkaServiceInfo si, KeyStore keyStore, Collection<Topic> topics) throws Exception {
        this.topics = ImmutableSet.copyOf(topics);

        opts.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
        opts.put(ProducerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, (int) TimeUnit.SECONDS.toMillis(10));
        opts.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, (int) TimeUnit.SECONDS.toMillis(5));

        //
        // ~ SASL SSL
        //
        opts.putAll(KafkaSecurity.configureSasl(si, keyStore));

        client = AdminClient.create(opts);
    }
    @Override
    protected Result check() throws Exception {
        List<String> names = topics.stream().map(Topic::name).map(AsciiString::toString).collect(Collectors.toUnmodifiableList());
        logger.debug("checking kafka topics {} ...", names);

        if (CollectionUtils.isEmpty(names)) {
            return HealthCheck.Result.healthy();
        }

        try {
            DescribeTopicsResult result = client.describeTopics(names);
            Set<String> keySet = result.topicNameValues().keySet();

            boolean alive = keySet.containsAll(names);
            if (alive) {
                return HealthCheck.Result.healthy("metadata found for: " + names);
            }

            String msg = "topic(s) metadata is not available for: " + topics;
            logger.warn(msg);
            return HealthCheck.Result.unhealthy(msg);
        } catch (Exception err) {
            logger.warn(err.getMessage(), err);
            return HealthCheck.Result.unhealthy(err);
        }
    }
    @Override
    public void destroy() throws Exception {
        if (client != null) {
            client.close();
        }
    }
    @Override
    public boolean isBootstrapOnly() {
        return false;
    }
}
