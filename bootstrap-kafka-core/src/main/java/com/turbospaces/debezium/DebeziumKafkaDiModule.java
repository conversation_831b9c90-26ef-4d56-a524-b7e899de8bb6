package com.turbospaces.debezium;

import java.security.KeyStore;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.MicrometerProducerListener;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.producer.DebeziumKafkaPostTemplate;
import com.turbospaces.kafka.producer.DebeziumKafkaProducerProperties;
import com.turbospaces.kafka.producer.DebeziumKafkaWithMetricsProducerFactory;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

@Configuration
public class DebeziumKafkaDiModule {
    @Bean
    public DebeziumKafkaProducerProperties debeziumProducerProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        KafkaServiceInfo cdcksi = UPSs.<KafkaServiceInfo> findServiceInfoByName(cloud, UPSs.CDC_KAFKA).orElse(ksi);
        return new DebeziumKafkaProducerProperties(props, keyStore, cdcksi);
    }
    @Bean
    public DebeziumKafkaWithMetricsProducerFactory debeziumProducerFactory(
            DebeziumKafkaProducerProperties configs,
            MicrometerProducerListener<byte[], byte[]> metrics) {
        return new DebeziumKafkaWithMetricsProducerFactory(configs, metrics);
    }
    @Bean
    public DebeziumKafkaPostTemplate debeziumKafkaTemplate(DebeziumKafkaWithMetricsProducerFactory producerFactory) {
        return new DebeziumKafkaPostTemplate(producerFactory);
    }
}
