package com.turbospaces.debezium;

import java.security.KeyStore;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.MicrometerConsumerListener;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.DebeziumBatchEventStreamKafkaWithMetricsConsumerFactory;
import com.turbospaces.kafka.consumer.DebeziumKafkaConsumerProperties;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

@Configuration
public class DebeziumWorkerKafkaDiModule {
    @Bean
    public DebeziumKafkaConsumerProperties batchKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
        KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        KafkaServiceInfo cdcksi = UPSs.<KafkaServiceInfo> findServiceInfoByName(cloud, UPSs.CDC_KAFKA).orElse(si);
        return new DebeziumKafkaConsumerProperties(props, keyStore, cdcksi);
    }
    @Bean
    public DebeziumBatchEventStreamKafkaWithMetricsConsumerFactory batchEventsConsumerFactory(
            DebeziumKafkaConsumerProperties cfg,
            MicrometerConsumerListener<byte[], byte[]> listener) {
        return new DebeziumBatchEventStreamKafkaWithMetricsConsumerFactory(cfg, listener);
    }
}
