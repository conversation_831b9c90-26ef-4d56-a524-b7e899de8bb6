package com.turbospaces.kafka;

import java.security.KeyStore;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.kafka.core.KafkaAdmin;

import com.google.common.collect.Lists;
import com.turbospaces.api.ConfigurableTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.KafkaServiceInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ScopedKafkaTopicInitializer implements ApplicationRunner {
    private final ApplicationProperties props;
    private final KafkaServiceInfo kafkaSi;
    private final KeyStore keyStore;
    private final List<ConfigurableTopic> topics;
    private final List<String> scopes;

    public ScopedKafkaTopicInitializer(ApplicationProperties props, KafkaServiceInfo kafkaSi, KeyStore keyStore, List<ConfigurableTopic> topics, List<String> scopes) {
        this.props = props;
        this.kafkaSi = kafkaSi;
        this.keyStore = keyStore;
        this.topics = topics;
        this.scopes = scopes;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Map<String, Object> opts = KafkaSecurity.configureSasl(kafkaSi, keyStore);

        KafkaAdmin kafkaAdmin = new KafkaAdmin(opts) {
            @Override
            protected Collection<NewTopic> newTopics() {
                List<NewTopic> toReturn = Lists.newArrayList();
                for (ConfigurableTopic topic : topics) {
                    for (String scope : scopes) {
                        String scopedTopicName = String.format("%s-%s", topic.name(), scope);
                        int replicationFactor = props.APP_DEV_MODE.get() ? 1 : topic.getReplicationFactor();

                        Map<String, String> configs = Map.of(
                                Topic.RETENTION_MS, String.valueOf(topic.getRetentionsMs()),
                                Topic.MAX_MESSAGE_BYTES, String.valueOf(topic.getMaxMessageBytes()));

                        NewTopic newTopic = new NewTopic(scopedTopicName, topic.getPartitions(), (short) replicationFactor).configs(configs);
                        log.info("about to configure: {}", newTopic);
                        toReturn.add(newTopic);
                    }
                }
                return toReturn;
            }
        };
        kafkaAdmin.setAutoCreate(true);
        kafkaAdmin.setModifyTopicConfigs(true);
        kafkaAdmin.setFatalIfBrokerNotAvailable(true);
        kafkaAdmin.setBootstrapServersSupplier(kafkaSi::getBootstrapServers);
        kafkaAdmin.initialize();
    }
}
