package com.turbospaces.kafka;

import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;

import com.google.common.collect.Maps;
import com.turbospaces.executor.WorkUnit;

public interface KafkaWorkUnit extends WorkUnit {
    int partition();
    long offset();
    Headers headers();

    default Map<String, String> headersAsMap() {
        Headers headers = headers();

        Map<String, String> toReturn = Maps.newHashMap();
        if (Objects.nonNull(headers)) {
            headers.forEach(new Consumer<Header>() {
                @Override
                public void accept(Header header) {
                    String key = header.key();
                    String value = StringUtils.EMPTY;
                    if (Objects.nonNull(header.value())) {
                        value = new String(header.value());
                    }
                    toReturn.putIfAbsent(key, value);
                }
            });
        }

        return toReturn;
    }
    default TopicPartition topicPartition() {
        return new TopicPartition(topic(), partition());
    }
}
