package com.turbospaces.kafka.producer;

import java.time.Duration;
import java.util.function.Supplier;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.DefaultRequestReplyMapper;

import api.v1.ApiFactory;
import io.opentracing.Tracer;

public class KafkaPostTemplate extends AbstractKafkaPostTemplate {
    public KafkaPostTemplate(
            ApplicationProperties props,
            Tracer tracer,
            ApiFactory apiFactory,
            DefaultRequestReplyMapper mapper,
            KafkaWithMetricsProducerFactory producerFactory,
            Supplier<Duration> timeout) {
        super(props, tracer, apiFactory, mapper, producerFactory, timeout);
    }
}
