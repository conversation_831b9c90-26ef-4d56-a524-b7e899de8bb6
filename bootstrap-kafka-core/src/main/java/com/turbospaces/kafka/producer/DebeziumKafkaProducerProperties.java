package com.turbospaces.kafka.producer;

import java.security.KeyStore;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.KafkaServiceInfo;

public class DebeziumKafkaProducerProperties extends AbstractKafkaProducerProperties {
    public DebeziumKafkaProducerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo serviceInfo) {
        super(props, keyStore, serviceInfo);
    }
}
