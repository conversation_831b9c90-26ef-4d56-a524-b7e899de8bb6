package com.turbospaces.kafka.producer;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;

import com.google.common.util.concurrent.FutureCallback;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.mdc.MdcUtil;

import io.vavr.Function0;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class DefaultKafkaSender extends KafkaTemplate<byte[], byte[]> implements KafkaPublisher {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final ThreadLocal<Boolean> flag = DefaultBlockHoundIntegration.FLAG;

    public DefaultKafkaSender(ApplicationProperties props, ProducerFactory<byte[], byte[]> producerFactory) {
        super(producerFactory);
        this.props = props;
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(ProducerRecord<byte[], byte[]> record, FutureCallback<SendResult<byte[], byte[]>> callback) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC

        //
        // ~ temporary allow blocking call even though it is not blocking by API specification
        //
        return DefaultBlockHoundIntegration.allowBlocking(new Function0<>() {
            @Override
            public CompletableFuture<SendResult<byte[], byte[]>> apply() {
                CompletableFuture<SendResult<byte[], byte[]>> future = send(record);
                return future.whenComplete(new BiConsumer<SendResult<byte[], byte[]>, Throwable>() {
                    @Override
                    public void accept(SendResult<byte[], byte[]> result, Throwable exception) {
                        MdcUtil.propagate(mdc);
                        try {
                            if (Objects.nonNull(result)) {
                                callback.onSuccess(result);
                            } else {
                                callback.onFailure(exception);
                            }
                        } finally {
                            if (Objects.nonNull(mdc)) {
                                MDC.clear();
                            }
                        }
                    }
                });
            }
        });
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(ProducerRecord<byte[], byte[]> record) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.send(record);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> sendDefault(byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.sendDefault(data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> sendDefault(byte[] key, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.sendDefault(key, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> sendDefault(Integer partition, byte[] key, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.sendDefault(partition, key, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> sendDefault(Integer partition, Long timestamp, byte[] key, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.sendDefault(partition, timestamp, key, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(String topic, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.send(topic, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(String topic, byte[] key, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.send(topic, key, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(String topic, Integer partition, byte[] key, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.send(topic, partition, key, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(String topic, Integer partition, Long timestamp, byte[] key, byte[] data) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.send(topic, partition, timestamp, key, data);
        } finally {
            flag.set(toReset);
        }
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> send(Message<?> message) {
        var toReset = flag.get();
        try {
            flag.set(false);
            return super.send(message);
        } finally {
            flag.set(toReset);
        }
    }
}
