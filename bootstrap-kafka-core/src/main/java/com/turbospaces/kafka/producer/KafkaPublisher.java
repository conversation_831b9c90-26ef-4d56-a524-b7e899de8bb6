package com.turbospaces.kafka.producer;

import java.util.concurrent.CompletableFuture;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.kafka.support.SendResult;

import com.google.common.util.concurrent.FutureCallback;

public interface KafkaPublisher {
    CompletableFuture<SendResult<byte[], byte[]>> send(ProducerRecord<byte[], byte[]> record, FutureCallback<SendResult<byte[], byte[]>> callback);
    CompletableFuture<SendResult<byte[], byte[]>> send(ProducerRecord<byte[], byte[]> record);
}
