package com.turbospaces.kafka.producer;

import java.util.Objects;
import java.util.UUID;

import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.kafka.support.SendResult;

import com.google.common.util.concurrent.FutureCallback;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LoggingCallback implements FutureCallback<SendResult<byte[], byte[]>> {
    private final String namespace;
    private final UUID messageId;
    private final long now;

    public LoggingCallback(String namespace, UUID messageId, long now) {
        this.namespace = Objects.requireNonNull(namespace);
        this.messageId = Objects.requireNonNull(messageId);
        this.now = now;
    }
    @Override
    public void onSuccess(SendResult<byte[], byte[]> result) {
        RecordMetadata meta = result.getRecordMetadata();
        log.debug("({}:{}) has been accepted by kafka (topic={},partition={},offset={}) in {}ms",
                namespace,
                messageId,
                meta.topic(),
                meta.partition(),
                meta.offset(),
                System.currentTimeMillis() - now);
    }
    @Override
    public void onFailure(Throwable ex) {
        log.error(ex.getMessage(), ex);
    }
}
