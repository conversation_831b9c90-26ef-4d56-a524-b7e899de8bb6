package com.turbospaces.kafka.producer;

import java.security.KeyStore;
import java.util.HashMap;

import org.apache.kafka.clients.producer.ProducerConfig;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaSecurity;
import com.turbospaces.ups.KafkaServiceInfo;

public abstract class AbstractKafkaProducerProperties extends HashMap<String, Object> {
    public AbstractKafkaProducerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo si) {
        put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
        put(ProducerConfig.ACKS_CONFIG, props.KAFKA_ACKS.get());
        put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, props.KAFKA_IDEMPOTENCE_ENABLE.get());
        put(ProducerConfig.MAX_BLOCK_MS_CONFIG, props.KAFKA_MAX_BLOCK.get().toMillis());
        put(ProducerConfig.COMPRESSION_TYPE_CONFIG, props.KAFKA_COMPRESSION_TYPE.get());
        put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, props.KAFKA_RECORD_MAX_REQUEST_SIZE.get());
        put(ProducerConfig.CLIENT_ID_CONFIG, props.CLOUD_APP_ID.get());

        //
        // ~ SASL SSL
        //
        putAll(KafkaSecurity.configureSasl(si, keyStore));
    }
}
