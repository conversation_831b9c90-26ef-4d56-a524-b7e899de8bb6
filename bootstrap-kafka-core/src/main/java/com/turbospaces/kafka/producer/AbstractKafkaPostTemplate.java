package com.turbospaces.kafka.producer;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.MDC;
import org.springframework.cloud.util.StandardUriInfoFactory;
import org.springframework.kafka.support.SendResult;

import com.google.common.util.concurrent.FutureCallback;
import com.google.protobuf.Any;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.MDCToHeadersContexPropagator;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.dispatch.RequestQueuePostSpec;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.dispatch.UntypedPostSpec;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.rpc.DefaultWrappedQueuePost;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.rpc.WrappedQueuePost;

import api.v1.ApiFactory;
import io.cloudevents.CloudEventData;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.kafka.KafkaMessageFactory;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.Tracer.SpanBuilder;
import io.opentracing.propagation.Format.Builtin;
import jakarta.inject.Inject;

public abstract class AbstractKafkaPostTemplate extends DefaultKafkaSender implements QueuePostTemplate<SendResult<byte[], byte[]>> {
    private final StandardUriInfoFactory uriFactory = new StandardUriInfoFactory();
    private final Tracer tracer;
    private final ApiFactory apiFactory;
    private final DefaultRequestReplyMapper mapper;
    private final Supplier<Duration> defaultTimeout;

    @Inject
    public AbstractKafkaPostTemplate(
            ApplicationProperties props,
            Tracer tracer,
            ApiFactory apiFactory,
            DefaultRequestReplyMapper mapper,
            KafkaWithMetricsProducerFactory producerFactory,
            Supplier<Duration> timeout) {
        super(props, producerFactory);
        this.tracer = Objects.requireNonNull(tracer);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.mapper = Objects.requireNonNull(mapper);
        this.defaultTimeout = Objects.requireNonNull(timeout);
    }
    @Override
    public ApiFactory apiFactory() {
        return apiFactory;
    }
    @Override
    public WrappedQueuePost sendReq(RequestQueuePostSpec spec) {
        RequestWrapperFacade reqw = apiFactory.requestMapper().pack(spec, new MDCToHeadersContexPropagator(), defaultTimeout.get());

        var operationTimeout = spec.timeout().orElse(defaultTimeout.get());
        long now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var messageId = spec.messageId();
        var typeUrl = reqw.body().getTypeUrl();

        byte[] key = null;
        if (spec.routingKey().isPresent()) {
            key = spec.routingKey().get().toByteArray();
        }

        var messageWriter = KafkaMessageFactory.createWriter(qualifier, null, now, key);
        var record = messageWriter.writeBinary(reqw);
        var post = span(spec, record);
        var future = mapper.acquire(messageId, operationTimeout);
        var callback = new LoggingCallback(typeUrl, messageId, now) {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                super.onSuccess(result);
                post.finish();
            }
            @Override
            public void onFailure(Throwable ex) {
                super.onFailure(ex);
                future.setException(ex);
                post.finish();
            }
        };

        send(record, new FutureCallback<SendResult<byte[], byte[]>>() {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                callback.onSuccess(result);
            }
            @Override
            public void onFailure(Throwable ex) {
                callback.onFailure(ex);
            }
        });

        return new DefaultWrappedQueuePost(future, reqw);
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> sendNotify(NotifyQueuePostSpec spec) {
        var now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var typeUrl = spec.body().getTypeUrl();
        var messageId = spec.messageId();

        byte[] key = null;
        if (spec.routingKey().isPresent()) {
            key = spec.routingKey().get().toByteArray();
        }

        var messageWriter = KafkaMessageFactory.createWriter(qualifier, null, now, key);
        var record = messageWriter.writeBinary(spec);

        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        if (Objects.nonNull(traceId)) {
            record.headers().add(MdcTags.MDC_TRACE_ID, traceId.getBytes());
        }

        var post = span(spec, record);
        var callback = new LoggingCallback(typeUrl, messageId, now) {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                super.onSuccess(result);
                post.finish();
            }
            @Override
            public void onFailure(Throwable ex) {
                super.onFailure(ex);
                post.finish();
            }
        };

        return send(record, new FutureCallback<>() {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                callback.onSuccess(result);
            }
            @Override
            public void onFailure(Throwable ex) {
                callback.onFailure(ex);

            }
        });
    }
    @Override
    public CompletableFuture<SendResult<byte[], byte[]>> sendEvent(EventQueuePostSpec spec) {
        var any = spec.pack(); // ~ not wrapping to request (Any)
        var now = System.currentTimeMillis();
        var qualifier = spec.topic().name().toString();
        var typeUrl = any.getTypeUrl();
        var messageId = PlatformUtil.randomUUID();

        byte[] key = null;
        if (spec.routingKey().isPresent()) {
            key = spec.routingKey().get().toByteArray();
        }

        var eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
        var event = eventTemplate.newBuilder()
                .withId(PlatformUtil.randomUUID().toString())
                .withData(new CloudEventData() {
                    @Override
                    public byte[] toBytes() {
                        return any.toByteArray();
                    }
                })
                .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                .withType(typeUrl);

        var messageWriter = KafkaMessageFactory.createWriter(qualifier, null, now, key);
        var record = messageWriter.writeBinary(event.build());

        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        if (Objects.nonNull(traceId)) {
            record.headers().add(MdcTags.MDC_TRACE_ID, traceId.getBytes());
        }

        var post = span(spec, record);
        var callback = new LoggingCallback(typeUrl, messageId, now) {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                super.onSuccess(result);
                post.finish();
            }
            @Override
            public void onFailure(Throwable ex) {
                super.onFailure(ex);
                post.finish();
            }
        };

        return send(record, new FutureCallback<>() {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                callback.onSuccess(result);
            }
            @Override
            public void onFailure(Throwable ex) {
                callback.onFailure(ex);
            }
        });
    }
    @Override
    public CompletionStage<?> publishReply(TransactionalRequestOutcome outcome) {
        ResponseWrapperFacade respw = outcome.getReply();
        String replyTo = MutableNonPersistentReplyTopic.asPersistentReplyTo(respw.headers().getReplyTo(), uriFactory);
        String typeUrl = respw.body().getTypeUrl();
        long now = System.currentTimeMillis();
        String messageId = respw.headers().getMessageId();
        String status = StringUtils.lowerCase(respw.status().errorCode().toString()).intern();

        log.trace("about to post {} to {} ...", typeUrl, replyTo);

        byte[] key = null;
        if (Objects.nonNull(outcome.getKey())) {
            key = outcome.getKey().toByteArray();
        }

        var messageWriter = KafkaMessageFactory.createWriter(replyTo, null, now, key);
        var producerRecord = messageWriter.writeBinary(respw);

        return send(producerRecord, new FutureCallback<>() {
            @Override
            public void onSuccess(SendResult<byte[], byte[]> result) {
                RecordMetadata meta = result.getRecordMetadata();
                log.debug("OUT ::: ({}:{}:s-{}) has been accepted by kafka (topic={}:partition={}:offset={},took={})",
                        typeUrl,
                        messageId,
                        status,
                        meta.topic(),
                        meta.partition(),
                        meta.offset(),
                        System.currentTimeMillis() - now);
            }
            @Override
            public void onFailure(Throwable ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }
    @Override
    public CompletionStage<?> publishNotifications(Topic topic, TransactionalRequestOutcome outcome) {
        List<NotificationWrapperFacade> notifications = outcome.getNotifications();
        if (log.isTraceEnabled()) {
            List<String> types = notifications.stream().map(NotificationWrapperFacade::body).map(Any::getTypeUrl).collect(Collectors.toList());
            log.trace("about to post {} to {} ...", types, topic.name());
        }

        CompletableFuture<?>[] list = new CompletableFuture<?>[notifications.size()];
        for (int i = 0; i < notifications.size(); i++) {
            NotificationWrapperFacade notify = notifications.get(i);
            String typeUrl = notify.body().getTypeUrl();
            long now = System.currentTimeMillis();

            byte[] key = null;
            if (Objects.nonNull(outcome.getKey())) {
                key = outcome.getKey().toByteArray();
            }

            var messageWriter = KafkaMessageFactory.createWriter(topic.name().toString(), null, now, key);
            var producerRecord = messageWriter.writeBinary(notify);
            list[i] = send(producerRecord, new FutureCallback<>() {
                @Override
                public void onSuccess(SendResult<byte[], byte[]> result) {
                    RecordMetadata meta = result.getRecordMetadata();
                    log.debug("OUT ::: ({}) has been accepted by kafka (topic={}:partition={}:offset={},took={})",
                            typeUrl,
                            meta.topic(),
                            meta.partition(),
                            meta.offset(),
                            System.currentTimeMillis() - now);
                }
                @Override
                public void onFailure(Throwable ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        }
        return CompletableFuture.allOf(list);
    }
    @Override
    public CompletionStage<?> publishEvents(Topic topic, TransactionalRequestOutcome outcome) {
        List<Any> eventStream = outcome.getEventStream();
        if (log.isTraceEnabled()) {
            var types = eventStream.stream().map(Any::getTypeUrl).collect(Collectors.toList());
            log.trace("about to post {} to {} ...", types, topic.name());
        }

        CompletableFuture<?>[] list = new CompletableFuture<?>[eventStream.size()];
        for (int i = 0; i < eventStream.size(); i++) {
            Any wrapper = eventStream.get(i);
            String typeUrl = wrapper.getTypeUrl();
            long now = System.currentTimeMillis();

            byte[] key = null;
            if (Objects.nonNull(outcome.getKey())) {
                key = outcome.getKey().toByteArray();
            }

            var eventTemplate = CloudEventBuilder.v1().withSource(URI.create(props.CLOUD_APP_ID.get()));
            var event = eventTemplate.newBuilder()
                    .withId(PlatformUtil.randomUUID().toString())
                    .withType(typeUrl)
                    .withData(new CloudEventData() {
                        @Override
                        public byte[] toBytes() {
                            return wrapper.toByteArray();
                        }
                    });

            var messageWriter = KafkaMessageFactory.createWriter(topic.name().toString(), null, now, key);
            var producerRecord = messageWriter.writeBinary(event.build());
            list[i] = send(producerRecord, new FutureCallback<>() {
                @Override
                public void onSuccess(SendResult<byte[], byte[]> result) {
                    RecordMetadata meta = result.getRecordMetadata();
                    log.debug("OUT ::: ({}) has been accepted by kafka (topic={}:partition={}:offset={},took={})",
                            wrapper.getTypeUrl(),
                            meta.topic(),
                            meta.partition(),
                            meta.offset(),
                            System.currentTimeMillis() - now);
                }
                @Override
                public void onFailure(Throwable ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        }
        return CompletableFuture.allOf(list);
    }
    private Span span(UntypedPostSpec spec, ProducerRecord<byte[], byte[]> record) {
        Span span = tracer.activeSpan();
        SpanBuilder post = tracer.buildSpan("kafka-post");

        if (Objects.nonNull(span)) {
            tracer.inject(span.context(), Builtin.TEXT_MAP, new KafkaSpanContextWriter(record));
            return post.asChildOf(span).start();
        }

        return post.start();
    }
}
