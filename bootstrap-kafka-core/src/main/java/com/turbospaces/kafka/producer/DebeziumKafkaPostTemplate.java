package com.turbospaces.kafka.producer;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.MDC;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.kafka.core.ProducerFactory;

import com.turbospaces.mdc.MdcUtil;

import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;

@RequiredArgsConstructor
public class DebeziumKafkaPostTemplate implements Producer<byte[], byte[]>, InitializingBean, DisposableBean {
    private final ProducerFactory<byte[], byte[]> producerFactory;
    @Delegate
    private Producer<byte[], byte[]> producer;

    @Override
    public void afterPropertiesSet() throws Exception {
        producer = producerFactory.createProducer();
    }
    @Override
    public void destroy() throws Exception {
        if (Objects.nonNull(producer)) {
            producer.close();
        }
    }
    @Override
    public Future<RecordMetadata> send(ProducerRecord<byte[], byte[]> record, Callback callback) {
        Map<String, String> mdc = MDC.getCopyOfContextMap(); // ~ capture MDC
        return producer.send(record, new Callback() {
            @Override
            public void onCompletion(RecordMetadata metadata, Exception exception) {
                MdcUtil.propagate(mdc);
                try {
                    callback.onCompletion(metadata, exception);
                } finally {
                    if (Objects.nonNull(mdc)) {
                        MDC.clear();
                    }
                }
            }
        });
    }
}
