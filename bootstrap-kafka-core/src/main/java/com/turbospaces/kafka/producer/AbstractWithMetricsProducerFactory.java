package com.turbospaces.kafka.producer;

import java.util.Map;

import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.MicrometerProducerListener;

public abstract class AbstractWithMetricsProducerFactory extends DefaultKafkaProducerFactory<byte[], byte[]> {
    public AbstractWithMetricsProducerFactory(Map<String, Object> configs, MicrometerProducerListener<byte[], byte[]> metrics) {
        super(configs);

        setKeySerializer(new ByteArraySerializer());
        setValueSerializer(new ByteArraySerializer());
        addListener(metrics);
    }
}
