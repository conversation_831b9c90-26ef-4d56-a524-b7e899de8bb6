package com.turbospaces.kafka.producer;

import java.util.Iterator;
import java.util.Map;

import org.apache.kafka.clients.producer.ProducerRecord;

import io.opentracing.propagation.TextMap;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class KafkaSpanContextWriter implements TextMap {
    private final ProducerRecord<byte[], byte[]> record;

    @Override
    public void put(String k, String v) {
        record.headers().add(k, v.getBytes());
    }
    @Override
    public Iterator<Map.Entry<String, String>> iterator() {
        throw new UnsupportedOperationException();
    }
}
