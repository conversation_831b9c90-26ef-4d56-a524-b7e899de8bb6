package com.turbospaces.kafka.producer;

import java.security.KeyStore;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.KafkaServiceInfo;

public class KafkaProducerProperties extends AbstractKafkaProducerProperties {
    public KafkaProducerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo serviceInfo) {
        super(props, keyStore, serviceInfo);
    }
}
