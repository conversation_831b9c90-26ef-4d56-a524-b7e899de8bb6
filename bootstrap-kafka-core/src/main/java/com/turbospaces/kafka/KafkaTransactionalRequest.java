package com.turbospaces.kafka;

import java.nio.file.Paths;
import java.util.Date;

import org.apache.commons.lang3.concurrent.ConcurrentException;
import org.apache.commons.lang3.concurrent.LazyInitializer;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.AbstractTransactionalRequest;
import com.turbospaces.executor.WorkUnit;

import api.v1.ObfuscatePrinter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

@Slf4j
public class KafkaTransactionalRequest<REQ extends Message, RESP extends Message.Builder> extends AbstractTransactionalRequest<REQ, RESP> {
    private final Date when;

    public KafkaTransactionalRequest(Class<REQ> reqType, RESP prototype, WorkUnit record, RequestWrapperFacade wrapper, Sinks.One<Boolean> latch) {
        super(prototype, record, wrapper, latch);
        this.req = new LazyInitializer<>() {
            @Override
            protected REQ initialize() throws ConcurrentException {
                Any body = wrapper.body();
                try {
                    REQ unpack = body.unpack(reqType);
                    log.debug("unpack: {}", ObfuscatePrinter.shortDebugString(unpack.toBuilder()));
                    return unpack;
                } catch (InvalidProtocolBufferException err) {
                    throw new ConcurrentException(err);
                }
            }
        };
        this.when = new Date(record.timestamp() == ConsumerRecord.NO_TIMESTAMP ? System.currentTimeMillis() : record.timestamp());
    }
    @Override
    public REQ request() throws Exception {
        return req.get();
    }
    @Override
    public Date timestamp() {
        return when;
    }
    public static <T> String jdbcOp(T req, String... args) {
        String typeUrl = PlatformUtil.toLowerUnderscore(req.getClass().getSimpleName());
        return Paths.get(String.format("jdbc_%s", typeUrl), args).toString();
    }
}
