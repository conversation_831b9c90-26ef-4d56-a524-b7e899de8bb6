package com.turbospaces.kafka;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;

import com.google.common.io.ByteSource;

import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class KafkaRecord implements KafkaWorkUnit {
    private final ConsumerRecord<byte[], byte[]> consumerRecord;

    @Override
    public String topic() {
        return consumerRecord.topic();
    }
    @Override
    public int partition() {
        return consumerRecord.partition();
    }
    @Override
    public long timestamp() {
        return consumerRecord.timestamp();
    }
    @Override
    public long offset() {
        return consumerRecord.offset();
    }
    @Override
    public Headers headers() {
        return consumerRecord.headers();
    }
    @Override
    public Optional<String> lastHeader(String key) {
        return Optional.ofNullable(headers().lastHeader(key)).map(new Function<>() {
            @Override
            public String apply(Header header) {
                return new String(header.value());
            }
        });
    }
    @Override
    public byte[] key() {
        return consumerRecord.key();
    }
    @Override
    public ByteSource value() {
        return ByteSource.wrap(consumerRecord.value());
    }
    @Override
    public boolean equals(Object obj) {
        KafkaRecord other = (KafkaRecord) obj;
        return new EqualsBuilder()
                .append(topic(), other.topic())
                .append(partition(), other.partition())
                .append(offset(), other.offset())
                .isEquals();
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(topic()).append(partition()).append(offset()).toHashCode();
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE)
                .append("topic", topic())
                .append("partition", partition())
                .append("offset", offset())
                .append("timestamp", timestamp())
                .append("key", Objects.isNull(key()) ? key() : new AsciiString(key()))
                .append("headers", headersAsMap())
                .build();
    }
}
