package com.turbospaces.kafka.sink;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.netflix.archaius.api.Property;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.EventQueuePostSpec;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.executor.ContextWorkerFactory;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.metrics.MetricTags;
import com.turbospaces.metrics.Metrics;

import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import reactor.core.publisher.Flux;

public abstract class AbstractSkippableSink extends AbstractSink {
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final RetryRegistry retryRegistry;
    protected final Supplier<Boolean> retryEnabled;
    private final ContextWorkerFactory<KafkaWorkUnit> workerFactory;
    private final Topic deadletterTopic;
    private final KafkaPostTemplate kafkaPostTemplate;
    protected final Tag sinkNameTag = Tag.of(MetricTags.NAME, getClass().getSimpleName());
    private Retry retry;

    protected AbstractSkippableSink(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RetryRegistry retryRegistry,
            KafkaListenerEndpointRegistry registry,
            ContextWorkerFactory<KafkaWorkUnit> workerFactory,
            Property<Boolean> enabled,
            KafkaPostTemplate kafkaPostTemplate,
            Topic deadletterTopic,
            Supplier<Boolean> retryEnabled) {
        super(registry, enabled);
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.retryRegistry = Objects.requireNonNull(retryRegistry);
        this.workerFactory = Objects.requireNonNull(workerFactory);
        this.deadletterTopic = Objects.requireNonNull(deadletterTopic);
        this.kafkaPostTemplate = Objects.requireNonNull(kafkaPostTemplate);
        this.retryEnabled = retryEnabled;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();

        IntervalFunction backoff = IntervalFunction.ofExponentialBackoff(
                Duration.ofMillis(100),
                IntervalFunction.DEFAULT_MULTIPLIER,
                Duration.ofMillis(500));
        this.retry = retryRegistry.retry(getClass().getSimpleName(), RetryConfig.custom().maxAttempts(3).intervalFunction(backoff).build());
    }
    @Override
    public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, org.apache.kafka.clients.consumer.Consumer<?, ?> consumer) {
        List<KafkaWorkUnit> list = flux.collectList().block();
        try {
            applyRecords(list, new BiConsumer<>() {
                @Override
                public void accept(KafkaWorkUnit unit, Any any) {
                    try {
                        AbstractSkippableSink.this.accept(unit, any);
                    } catch (Throwable e) {
                        ExceptionUtils.wrapAndThrow(e);
                    }
                }
            }, acknowledgment);
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    public abstract void accept(KafkaWorkUnit unit, Any any) throws Throwable;

    private void applyRecords(
            List<KafkaWorkUnit> records,
            BiConsumer<KafkaWorkUnit, Any> action,
            Acknowledgment ack) throws Throwable {
        boolean isDeadLetterEnabled = props.KAFKA_SINK_DEAD_LETTER_ENABLED.get(getClass().getSimpleName());
        List<ListenableFuture<?>> callbacks = Lists.newArrayListWithExpectedSize(records.size());

        if (records.isEmpty()) {
            return;
        }

        //
        // ~ double check configuration is correct
        //
        logger.trace(Iterables.getOnlyElement(records.stream().map(r -> r.topicPartition().topic()).collect(Collectors.toSet())));

        // ~ scheduling processing
        for (KafkaWorkUnit record : records) {
            try (InputStream io = record.value().openStream()) {
                Any.Builder anyb = Any.newBuilder();
                anyb.mergeFrom(io);
                Any any = anyb.build();

                ContextWorker worker = workerFactory.worker(record).ifPresent(record);
                var callback = FluentFuture.from(worker.submit(new CheckedRunnable() {
                    @Override
                    public void run() throws Throwable {
                        try {
                            setMdcMessageId();
                            io.github.resilience4j.core.functions.CheckedRunnable process = () -> processRecord(action, any, record);
                            if (retryEnabled.get()) {
                                process = Retry.decorateCheckedRunnable(retry, process);
                            }
                            process.run();
                        } catch (Exception err) {
                            if (isDeadLetterEnabled) {
                                logger.error("Error when processing kafka record, sending to dead letter", err);
                                EventQueuePostSpec.EventBuilder spec = EventQueuePostSpec.newBuilder(any);
                                spec.setTopic(deadletterTopic);
                                if (record.key() != null) {
                                    spec.setRoutingKey(AsciiString.of(new String(record.key())));
                                }

                                //
                                // ~ await for message to be posted in a blocking manner
                                //
                                var post = kafkaPostTemplate.sendEvent(spec.build());
                                post.get(props.KAFKA_SESSION_TIMEOUT.get().toSeconds(), TimeUnit.SECONDS);
                            } else {
                                throw err;
                            }
                        } finally {
                            MdcUtil.clearMdc(record);
                        }
                    }
                }));
                callback.addCallback(new FutureCallback<Object>() {
                    @Override
                    public void onSuccess(Object result) {

                    }
                    @Override
                    public void onFailure(Throwable t) {
                        Throwable rootCause = ExceptionUtils.getRootCause(t);
                        if (Objects.isNull(rootCause)) {
                            rootCause = t;
                        }
                        logger.warn("error when processing kafka record, skipping", rootCause);
                    }
                }, MoreExecutors.directExecutor());
                callbacks.add(callback);
            } catch (IOException e) {
                ExceptionUtils.wrapAndThrow(e);
            }
        }

        try {
            //
            // ~ throws execution Exception if at least one fails
            //
            Futures.allAsList(callbacks).get(props.KAFKA_POLL_MAX_INTERVAL.get().toSeconds(), TimeUnit.SECONDS);
            ack.acknowledge();
        } catch (Exception err) {
            logger.warn("Error when processing some kafka records, skipping", err);
        } finally {
            callbacks.forEach(new Consumer<ListenableFuture<?>>() {
                @Override
                public void accept(ListenableFuture<?> c) {
                    if (BooleanUtils.isFalse(c.isDone())) {
                        c.cancel(true);
                    }
                }
            });
        }
    }

    private void processRecord(BiConsumer<KafkaWorkUnit, Any> action, Any any, KafkaWorkUnit record) throws Throwable {
        Timer.Sample processingTime = Timer.start(meterRegistry);
        try {
            setMdc(record, any);
            logger.trace("IN ::: (t-{}):(offset-{}):(t-{}))", record.topic(), record.offset(), any.getTypeUrl());
            action.accept(record, any);
        } finally {
            processingTime.stop(meterRegistry.timer(
                    Metrics.REPLICATOR,
                    List.of(sinkNameTag, Tag.of(MetricTags.OPERATION, any.getTypeUrl()))));
        }
    }

    /**
     * Sets a unique message ID in the MDC for distributed tracing purposes.
     * Message ID acts as a Span ID to uniquely identify specific operations within a trace.
     */
    private static void setMdcMessageId() {
        String current = MDC.get(MdcTags.MDC_MESSAGE_ID);
        if (Objects.isNull(current)) {
            MDC.put(MdcTags.MDC_MESSAGE_ID, PlatformUtil.randomUUIDv7().toString());
        }
    }
}