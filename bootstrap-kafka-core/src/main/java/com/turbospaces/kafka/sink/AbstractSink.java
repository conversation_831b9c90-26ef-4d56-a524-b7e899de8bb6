package com.turbospaces.kafka.sink;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;

import com.google.protobuf.Any;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.SupersetFlag;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import com.turbospaces.kafka.consumer.KafkaStreamConsumer;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.util.Loggers;

/**
 * Abstract class that gives capability for sink to be disabled/paused dynamically.
 */
public abstract class AbstractSink implements KafkaStreamConsumer, BeanNameAware, InitializingBean, DisposableBean, ApplicationListener<ApplicationReadyEvent> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final AtomicBoolean alive = new AtomicBoolean();
    protected final Property<Boolean> enabled;
    protected final KafkaListenerEndpointRegistry registry;
    protected Sinks.Many<Boolean> processor;
    protected String beanName;
    private Disposable subscribe;
    private final Object lifecycleMonitor = new Object();

    protected AbstractSink(KafkaListenerEndpointRegistry registry, Property<Boolean> enabled) {
        this.registry = Objects.requireNonNull(registry);
        this.enabled = Objects.requireNonNull(enabled);
        this.processor = Sinks.many().replay().latestOrDefault(enabled.get());
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = Objects.requireNonNull(name);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        alive.set(true);

        Class<? extends AbstractSink> preserved = getClass();
        Flux<Boolean> flux = processor.asFlux().distinctUntilChanged(); // ~ filter out redundant noise

        //
        // ~ we accept super set flag natively
        //
        if (enabled instanceof SupersetFlag flag) {
            flux = flag.asFlux();
        } else {
            enabled.subscribe(new Consumer<Boolean>() {
                @Override
                public void accept(Boolean t) {
                    EmitResult emitResult = processor.tryEmitNext(t);
                    if (emitResult.isSuccess()) {

                    }
                }
            });
        }

        //
        // ~ subscribe to the wrapped into FLUX instead of property
        //
        subscribe = flux.log(Loggers.getLogger(getClass())).subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean value) {
                if (alive.get()) {
                    for (MessageListenerContainer container : registry.getAllListenerContainers()) {
                        if (container instanceof KafkaAcceptorChannel channel) {
                            Object messageListener = channel.getContainerProperties().getMessageListener();
                            if (messageListener instanceof ManualAcknowledgingKafkaConsumer manual) {
                                if (manual.is(preserved)) {
                                    logger.info("sink: {} active: {}", beanName, preserved);
                                    synchronized (lifecycleMonitor) {
                                        if (value) {
                                            if (container.isRunning()) {

                                            } else {
                                                logger.info("about to start sink: {}", beanName);
                                                container.start();
                                                logger.info("started sink: {}", beanName);
                                            }
                                        } else {
                                            if (container.isRunning()) {
                                                logger.info("about to stop sink: {}", beanName);
                                                container.stop();
                                                logger.info("stopped sink: {}", beanName);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }
    @Override
    public void destroy() throws Exception {
        alive.set(false);
        if (Objects.nonNull(subscribe)) {
            subscribe.dispose();
        }
    }
    public static void setMdc(KafkaWorkUnit record, Any any) {
        var traceIdFromHeader = record.headers().lastHeader(MdcTags.MDC_TRACE_ID);
        String traceId = traceIdFromHeader == null ? PlatformUtil.randomUUID().toString() : new String(traceIdFromHeader.value());
        MdcUtil.setMdc(record, any.getTypeUrl(), traceId);
    }
}