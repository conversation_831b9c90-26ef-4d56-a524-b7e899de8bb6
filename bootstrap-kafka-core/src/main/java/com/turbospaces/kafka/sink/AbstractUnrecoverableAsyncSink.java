package com.turbospaces.kafka.sink;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.kafka.KafkaWorkUnit;

import io.github.resilience4j.core.functions.CheckedFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Abstract class that allows parallel process of events in batch.
 */
public abstract class AbstractUnrecoverableAsyncSink extends AbstractSink {
    protected final String ups;
    protected final ApplicationProperties props;
    protected final RetryRegistry retryRegistry;
    protected Semaphore limiter = new Semaphore(Short.MAX_VALUE);

    private final PlatformExecutorService executor;

    protected AbstractUnrecoverableAsyncSink(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RetryRegistry retryRegistry,
            KafkaListenerEndpointRegistry registry,
            String ups,
            Property<Boolean> enabled) {
        super(registry, enabled);
        this.props = Objects.requireNonNull(props);
        this.retryRegistry = Objects.requireNonNull(retryRegistry);
        this.ups = Objects.requireNonNull(ups);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();

        executor.setBeanName(ups);
        executor.afterPropertiesSet();
    }
    @Override
    public void destroy() throws Exception {
        try {
            executor.destroy();
        } finally {
            super.destroy();
        }
    }
    protected void applyRecordsAsync(
            List<KafkaWorkUnit> records,
            CheckedFunction<KafkaWorkUnit, Object> action,
            Acknowledgment ack) {
        Retry retry = retryRegistry.retry(ups);
        CountDownLatch latch = new CountDownLatch(records.size());

        for (KafkaWorkUnit unit : records) {
            executor.submit(new io.vavr.CheckedRunnable() {
                @Override
                public void run() throws Throwable {
                    try {
                        //
                        // ~ retry each record individually using default retry mechanism
                        //
                        CheckedFunction<KafkaWorkUnit, Object> runnable = Retry.decorateCheckedFunction(retry, new CheckedFunction<KafkaWorkUnit, Object>() {
                            @Override
                            public Object apply(KafkaWorkUnit t1) throws Throwable {
                                //
                                // ~ somehow try to limit number of remote calls if necessary
                                // ~ doesn't matter success or failure, we don't want to face throttling issue on counterpart side
                                //
                                limiter.acquire();

                                try {
                                    return action.apply(t1);
                                } finally {
                                    limiter.release();
                                }
                            }
                        });

                        runnable.apply(unit); // ~ now run with retries and finally count down
                    } finally {
                        latch.countDown();
                    }
                }
            });
        }

        try {
            Duration duration = props.KAFKA_SESSION_TIMEOUT.get(); // ~ max possible timeout before re-balance
            latch.await(duration.toSeconds(), TimeUnit.SECONDS); // ~ simply wait for completion or re-balance (bad practice to block w/o timeout)
        } catch (InterruptedException err) {
            Thread.currentThread().interrupt();
        } finally {
            ack.acknowledge(); // ~ anyways commit since we are not recoverable
        }
    }
}
