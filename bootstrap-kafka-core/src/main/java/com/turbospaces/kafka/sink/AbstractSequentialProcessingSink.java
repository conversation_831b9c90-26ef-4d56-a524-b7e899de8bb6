package com.turbospaces.kafka.sink;

import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.google.protobuf.Any;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.metrics.MetricTags;
import com.turbospaces.metrics.Metrics;

import io.github.resilience4j.core.functions.CheckedRunnable;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import reactor.core.publisher.Flux;

/**
 * Abstract class that allows sequential process of events, assign unique trace if for each event processing.
 */
public abstract class AbstractSequentialProcessingSink extends AbstractSink {
    protected final ApplicationProperties props;
    protected final MeterRegistry meterRegistry;
    protected final RetryRegistry retryRegistry;
    protected final Supplier<Boolean> retryEnabled;
    protected final Tag sinkNameTag = Tag.of(MetricTags.NAME, getClass().getSimpleName());

    protected AbstractSequentialProcessingSink(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RetryRegistry retryRegistry,
            KafkaListenerEndpointRegistry registry,
            Property<Boolean> enabled, Supplier<Boolean> retryEnabled) {
        super(registry, enabled);
        this.props = Objects.requireNonNull(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.retryRegistry = Objects.requireNonNull(retryRegistry);
        this.retryEnabled = Objects.requireNonNull(retryEnabled);
    }

    public abstract void accept(KafkaWorkUnit record, Any event) throws Throwable;

    @Override
    public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        List<KafkaWorkUnit> records = flux.collectList().block();
        Retry retry = retryRegistry.retry(sinkNameTag.getValue());

        Timer.Sample batchTime = Timer.start(meterRegistry);
        logger.trace("accepted {} events ...", records.size());

        for (KafkaWorkUnit record : records) {
            try {
                Timer.Sample recordTime = Timer.start(meterRegistry);

                //
                // ~ retry each record individually using default retry mechanism
                //
                CheckedRunnable runnable = Retry.decorateCheckedRunnable(retry, new CheckedRunnable() {
                    @Override
                    public void run() throws Throwable {
                        try (InputStream io = record.value().openStream()) {
                            Any.Builder anyb = Any.newBuilder();
                            anyb.mergeFrom(io);
                            Any any = anyb.build();
                            setMdc(record, any);

                            logger.trace("IN ::: (t-{}):(offset-{}):(t-{}))", record.topic(), record.offset(), any.getTypeUrl());

                            accept(record, any);

                            recordTime.stop(meterRegistry.timer(
                                    Metrics.REPLICATOR,
                                    List.of(sinkNameTag, Tag.of(MetricTags.OPERATION, any.getTypeUrl()))));
                        } catch (Throwable err) {
                            logger.debug("Error when processing {}", record, err);
                            ExceptionUtils.wrapAndThrow(err);
                        }
                    }
                });

                try {
                    runnable.run();
                } catch (Throwable err) {
                    if (retryEnabled.get()) {
                        logger.error("Error when processing {}", record, err);
                        acknowledgment.nack(records.indexOf(record), props.KAFKA_NACK_POLL_INTERVAL.get());
                        return;
                    }
                    logger.warn("Error when processing {}, skipping", record, err);
                }
            } finally {
                MdcUtil.clearMdc(record);
            }
        }

        acknowledgment.acknowledge();
        batchTime.stop(meterRegistry.timer(Metrics.SINK, List.of(sinkNameTag)));
    }
}
