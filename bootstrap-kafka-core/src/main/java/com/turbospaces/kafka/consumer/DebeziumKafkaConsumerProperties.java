package com.turbospaces.kafka.consumer;

import java.security.KeyStore;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.KafkaServiceInfo;

public class DebeziumKafkaConsumerProperties extends AbstractBatchKafkaConsumerProperties {
    public DebeziumKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo serviceInfo) {
        super(props, keyStore, serviceInfo);
    }
}
