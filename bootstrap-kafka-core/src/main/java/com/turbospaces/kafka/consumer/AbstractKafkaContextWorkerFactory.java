package com.turbospaces.kafka.consumer;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.kafka.common.TopicPartition;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.collect.ImmutableList;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;
import com.turbospaces.executor.AbstractContextWorkerFactory;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.executor.LogQueueFullCallerRunsPolicy;
import com.turbospaces.executor.PlatformTransactionalThread;
import com.turbospaces.kafka.KafkaWorkUnit;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;

public abstract class AbstractKafkaContextWorkerFactory extends AbstractContextWorkerFactory<TopicPartition, KafkaWorkUnit> {
    public AbstractKafkaContextWorkerFactory(
            CurrentTransactionProvider currentTransaction,
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry) {
        super(props, meterRegistry);
        workers = CacheBuilder.newBuilder().build(new CacheLoader<TopicPartition, ContextWorker>() {
            @Override
            public ContextWorker load(TopicPartition tp) {
                var tfb = new ThreadFactoryBuilder();
                tfb.setDaemon(false);
                tfb.setNameFormat("kafka-worker-" + tp.topic() + "-" + tp.partition() + "-%d");
                tfb.setThreadFactory(new ThreadFactory() {
                    @Override
                    public Thread newThread(Runnable r) {
                        return new PlatformTransactionalThread(props, currentTransaction, r);
                    }
                });

                var min = props.KAFKA_MIN_WORKERS.get();
                var max = props.KAFKA_MAX_WORKERS.get();
                var ttl = props.APP_PLATFORM_MAX_IDLE.get();
                var limiter = new TriggerOnceAfterNOccurrencesRateLimiter(tp.toString(), min, timer);

                var pool = new ThreadPoolExecutor(
                        min,
                        max,
                        ttl.toSeconds(),
                        TimeUnit.SECONDS,
                        new SynchronousQueue<>(),
                        tfb.build(),
                        new LogQueueFullCallerRunsPolicy(limiter));

                var metrics = new ExecutorServiceMetrics(
                        pool,
                        "kafka-worker",
                        ImmutableList.of(
                                Tag.of("topic", tp.topic()),
                                Tag.of("partition", String.valueOf(tp.partition()))));
                metrics.bindTo(meterRegistry);

                ContextWorker worker = createWorker(pool);
                worker.setBeanName(beanName + tp.toString());
                return worker;
            }
        });
    }
    @Override
    public ContextWorker worker(KafkaWorkUnit unit) {
        return workers.getUnchecked(unit.topicPartition());
    }
    @Override
    public void destroy() throws Exception {
        super.destroy();

        //
        // ~ gracefully terminate
        //
        for (ContextWorker pool : workers.asMap().values()) {
            pool.destroy();
        }

        //
        // ~ cleanUp
        //
        workers.invalidateAll();

        //
        // ~ timer
        //
        PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
    }
    @Override
    public String toString() {
        return workers.asMap().toString();
    }
    protected abstract ContextWorker createWorker(ExecutorService pool);
}
