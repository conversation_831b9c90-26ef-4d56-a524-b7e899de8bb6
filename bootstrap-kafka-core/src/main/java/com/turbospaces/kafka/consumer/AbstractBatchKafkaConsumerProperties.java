package com.turbospaces.kafka.consumer;

import java.security.KeyStore;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.KafkaServiceInfo;

public abstract class AbstractBatchKafkaConsumerProperties extends AbstractKafkaConsumerProperties {
    public AbstractBatchKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo serviceInfo) {
        super(props, keyStore, serviceInfo);

        String appId = props.CLOUD_APP_ID.get();
        String resetPolicy = props.KAFKA_AUTO_OFFSET_RESET.get();
        int sessionTimeoutMs = (int) props.KAFKA_SESSION_TIMEOUT.get().toMillis();

        put(ConsumerConfig.GROUP_ID_CONFIG, appId);
        put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");

        if (StringUtils.isNotEmpty(resetPolicy)) {
            put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, resetPolicy);
        }

        // batch settings
        int fetchWait = (int) props.KAFKA_BATCH_FETCH_MAX_WAIT.get().toMillis();

        put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, props.KAFKA_BATCH_MAX_POLL.get());
        put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, props.KAFKA_BATCH_PARTITION_FETCH_MAX_BYTES.get());
        put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, props.KAFKA_BATCH_FETCH_MIN_BYTES.get());
        put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, fetchWait);

        put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, fetchWait + sessionTimeoutMs);
        put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, fetchWait + sessionTimeoutMs);
    }
}
