package com.turbospaces.kafka.consumer;

import java.io.ByteArrayOutputStream;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.MDC;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.kafka.support.Acknowledgment;

import com.codahale.metrics.jvm.ThreadDump;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.jpa.CompositeStackTracer;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.AbstractServerRequestConsumer;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.mdc.MdcTags;

import api.v1.ApiFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

public abstract class AbstractKafkaRequestConsumer
        extends AbstractServerRequestConsumer
        implements KafkaStreamConsumer, DisposableBean, FutureCallback<TransactionalRequestOutcome> {
    protected final ConcurrentLinkedQueue<ListenableFuture<?>> replyWhen = new ConcurrentLinkedQueue<>();

    protected AbstractKafkaRequestConsumer(
            ApplicationProperties props,
            ApiFactory apiFactory,
            CompositeStackTracer stackTracer) {
        super(props, apiFactory, stackTracer);
    }
    @Override
    public void destroy() throws Exception {
        logger.info("about to wait for {} replyWhen callbacks", replyWhen.size());

        //
        // ~ we keep iterating over again because it might be so that iterator is out dated during waiting for operations to complete
        //
        while (BooleanUtils.isFalse(replyWhen.isEmpty())) {
            for (Iterator<ListenableFuture<?>> it = replyWhen.iterator(); it.hasNext();) {
                var block = it.next();
                try {
                    block.get(timeout.toSeconds(), TimeUnit.SECONDS);
                } catch (InterruptedException err) {
                    logger.warn(err.getMessage(), err.getCause());
                    Thread.currentThread().interrupt();
                } catch (ExecutionException err) {
                    logger.error(err.getMessage(), err.getCause());
                } catch (TimeoutException err) {
                    logger.warn(err.getMessage(), err.getCause());
                } finally {
                    it.remove();
                    logger.info("replyWhen callbacks completed {}", replyWhen.size());
                }
            }
        }
    }
    @Override
    public void accept(Flux<KafkaWorkUnit> stream, Acknowledgment ack, Consumer<?, ?> consumer) {
        stream.collectList().subscribe(new java.util.function.Consumer<List<KafkaWorkUnit>>() {
            @Override
            public void accept(List<KafkaWorkUnit> l) {
                String batchId = Hashing.murmur3_32_fixed()
                        .newHasher()
                        .putInt(ack.hashCode()).putInt(l.size())
                        .hash().toString();
                StopWatch stopWatch = StopWatch.createStarted();
                CountDownLatch latch = new CountDownLatch(l.size());
                List<FluentFuture<TransactionalRequestOutcome>> tasks = new ArrayList<>(l.size());

                MDC.put(MdcTags.MDC_BATCH_ID, batchId);

                try {
                    for (KafkaWorkUnit unit : l) {
                        RequestWrapperFacade reqw = null;
                        try {
                            reqw = apiFactory.requestMapper().unpack(unit);
                        } catch (Throwable err) {
                            logger.error(err.getMessage(), err);
                            latch.countDown();
                        }

                        //
                        // ~ accept
                        //
                        if (Objects.nonNull(reqw)) {
                            var completable = Optional.<ListenableFuture<TransactionalRequestOutcome>> empty();
                            try {
                                Sinks.One<Boolean> signal = Sinks.one();
                                completable = logAndAccept(reqw, unit, signal);
                                signal.asMono().subscribe(new java.util.function.Consumer<>() {
                                    @Override
                                    public void accept(Boolean value) {
                                        if (Boolean.TRUE.equals(value)) {
                                            latch.countDown();
                                        }
                                    }
                                });
                            } catch (Throwable err) {
                                logger.error(err.getMessage(), err);
                                latch.countDown();
                                completable = Optional.of(convertUnhandledException(reqw, unit, err));
                            } finally {
                                if (completable.isPresent()) {
                                    var fluent = FluentFuture.from(completable.get());
                                    fluent.addCallback(AbstractKafkaRequestConsumer.this, MoreExecutors.directExecutor());
                                    tasks.add(fluent);
                                }
                            }
                        }
                    }

                    try {
                        boolean await = latch.await(timeout.toSeconds(), TimeUnit.SECONDS);

                        if (await) {
                            stopWatch.stop();
                            MDC.put(MdcTags.MDC_BATCH_TOOK, Long.toString(stopWatch.getDuration().toMillis()));
                            logger.info("completed batch: {} processing in: {} completed: {}", batchId, stopWatch, tasks);

                            var all = Futures.allAsList(tasks);
                            logger.trace("registered replyWhen future of ({}):{} items", Math.abs(all.hashCode()), tasks.size());
                            replyWhen.add(all);

                            //
                            // ~ this will complete and run on thread which completes last (slowest reply)
                            // ~ we are self-removing no matter success / exceptional
                            //
                            all.addListener(new Runnable() {
                                @Override
                                public void run() {
                                    if (replyWhen.remove(all)) {
                                        logger.trace("un-registered replyWhen future of ({}):{} items", Math.abs(all.hashCode()), tasks.size());
                                    }
                                }
                            }, MoreExecutors.directExecutor());
                        } else {
                            //
                            // ~
                            //
                            var complete = Lists.newArrayListWithExpectedSize(tasks.size());
                            var incomplete = Lists.newArrayListWithExpectedSize(tasks.size());
                            for (var it : tasks) {
                                if (it.isDone()) {
                                    complete.add(it);
                                } else {
                                    incomplete.add(it);
                                }
                            }

                            //
                            // ~ raise sentry alert
                            //
                            MDC.put(MdcTags.MDC_BATCH_TOOK, Long.toString(timeout.toMillis()));
                            logger.error("unable to complete batch: {} processing in: {} sec(s) pendingCount: {}, incomplete: {}, complete: {}",
                                    batchId,
                                    timeout.toSeconds(),
                                    latch.getCount(),
                                    incomplete,
                                    complete);

                            //
                            // ~ dump thread dump of all threads
                            // ~ we can use Acknowledgment#nack() but it will not change reality
                            //
                            ByteArrayOutputStream out = new ByteArrayOutputStream();
                            ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
                            ThreadDump dump = new ThreadDump(threadMXBean);
                            dump.dump(out);
                            logger.error(out.toString()); // ~ raise alert with stack trace

                            //
                            // ~ terminate JVM
                            //
                            if (props.KAFKA_SYSTEM_EXIT_ON_QUEUE_FULL.get()) {
                                //
                                // ~ try to close consumer gracefully
                                // ~ partition will be re-balanced to healthy node (we hope so at least)
                                //
                                try {
                                    consumer.close();
                                } catch (Exception err) {
                                    logger.error(err.getMessage(), err);
                                } finally {
                                    int exitCode = props.KAFKA_SYSTEM_EXIT_CODE.get();
                                    Duration delay = props.APP_SYSTEM_EXIT_DELAY.get();

                                    //
                                    // ~ just wait a little before exit (we want to give some time for sentry/ELK appenders to complete and report)
                                    //
                                    Thread.sleep(delay.toMillis());
                                    System.exit(exitCode);
                                }
                            } else {
                                //
                                // ~ use negative acknowledge if necessary
                                //
                                boolean nack = props.KAFKA_NACK_ON_QUEUE_FULL.get();
                                if (nack) {
                                    ack.nack(Duration.ofMillis(timeout.toMillis()));
                                } else {
                                    latch.await(); // ~ wait till the re-balance
                                }
                            }
                        }
                    } catch (InterruptedException err) {
                        logger.error(err.getMessage(), err);
                    } finally {
                        ack.acknowledge();
                    }
                } finally {
                    MDC.remove(MdcTags.MDC_BATCH_ID);
                    MDC.remove(MdcTags.MDC_BATCH_TOOK);
                }
            }
        });
    }
    @Override
    public void onPartitionsRevokedBeforeCommit(Consumer<?, ?> consumer, Collection<TopicPartition> partitions) {
        invalidateCache();
    }
    @Override
    public void onPartitionsRevokedAfterCommit(Consumer<?, ?> consumer, Collection<TopicPartition> partitions) {
        invalidateCache();
    }
    @Override
    public void onPartitionsLost(Consumer<?, ?> consumer, Collection<TopicPartition> partitions) {
        invalidateCache();
    }
    @Override
    public void onPartitionsAssigned(Consumer<?, ?> consumer, Collection<TopicPartition> partitions) {
        invalidateCache();
    }
    @Override
    public void onPartitionsLost(Collection<TopicPartition> partitions) {
        invalidateCache();
    }
    protected void invalidateCache() {

    }
}
