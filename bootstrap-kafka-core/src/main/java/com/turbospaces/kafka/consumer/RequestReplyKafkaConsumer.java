package com.turbospaces.kafka.consumer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.clients.consumer.Consumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.support.Acknowledgment;

import com.google.common.base.Suppliers;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Uninterruptibles;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.rpc.DefaultRequestReplyMapper;

import api.v1.ApiFactory;
import io.vavr.CheckedFunction0;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import jakarta.inject.Inject;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;
import reactor.core.publisher.Flux;

public class RequestReplyKafkaConsumer implements KafkaStreamConsumer {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ThreadLocal<Boolean> nonBlocking = DefaultBlockHoundIntegration.FLAG;
    private final DefaultRequestReplyMapper reqReplyMapper;
    private final KafkaContextWorkerFactory workerFactory;
    private final ApiFactory apiFactory;
    private Supplier<Integer> timeout;

    @Inject
    public RequestReplyKafkaConsumer(
            DefaultRequestReplyMapper reqReplyMapper,
            KafkaContextWorkerFactory workerFactory,
            ApiFactory apiFactory,
            Supplier<Integer> timeout) {
        this.reqReplyMapper = Objects.requireNonNull(reqReplyMapper);
        this.timeout = Suppliers.ofInstance(30);
        this.workerFactory = Objects.requireNonNull(workerFactory);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.timeout = Objects.requireNonNull(timeout);
    }
    @Override
    public void accept(Flux<KafkaWorkUnit> stream, Acknowledgment ack, Consumer<?, ?> consumer) {
        stream.collectList().subscribe(new java.util.function.Consumer<List<KafkaWorkUnit>>() {
            @Override
            public void accept(List<KafkaWorkUnit> l) {
                StopWatch stopWatch = StopWatch.createStarted();
                CountDownLatch latch = new CountDownLatch(l.size());
                List<Tuple3<FluentFuture<Any>, AtomicReference<UUID>, StopWatch>> tasks = new ArrayList<>(l.size());

                try {
                    for (KafkaWorkUnit record : l) {
                        ContextWorker worker = workerFactory.worker(record).ifPresent(record); // ~ schedule in parallel
                        StopWatch each = StopWatch.createStarted();
                        AtomicReference<UUID> messageId = new AtomicReference<>();
                        FluentFuture<Any> fluent = FluentFuture.from(worker.submit(new CheckedFunction0<Any>() {
                            @Override
                            public Any apply() throws Throwable {
                                ResponseFacadeMapper mapper = apiFactory.responseMapper();
                                ResponseWrapperFacade respw = mapper.unpack(record);
                                Any body = respw.body();
                                api.v1.Headers headers = respw.headers();
                                String operation = PlatformUtil.toLowerUnderscore(body.getTypeUrl());
                                String status = respw.status().errorCode().toString().toLowerCase().intern();

                                ApiFactory.setMdc(record, operation, headers);

                                Thread currentThread = Thread.currentThread();
                                String oldName = currentThread.getName();
                                String newName = oldName + "|" + operation;
                                currentThread.setName(newName);

                                var toReset = nonBlocking.get(); // ~ capture current value

                                try {
                                    nonBlocking.set(true); // ~ prohibit any blocking operation
                                    messageId.set(UUID.fromString(headers.getMessageId()));

                                    if (reqReplyMapper.contains(messageId.get())) {
                                        logger.debug("IN ::: (t-{}):(p-{}):(offset-{}):(t-{}(m-{},s-{})) {}",
                                                record.topic(),
                                                record.partition(),
                                                record.offset(),
                                                body.getTypeUrl(),
                                                headers.getMessageId(),
                                                status,
                                                record.headersAsMap());
                                        reqReplyMapper.complete(messageId.get(), respw);

                                        synchronized (each) {
                                            each.stop();
                                        }
                                    }

                                    return body;
                                } finally {
                                    nonBlocking.set(toReset); // ~ simply reset to previous value
                                    latch.countDown();
                                    currentThread.setName(oldName);
                                    MdcUtil.clearMdc(record);
                                }
                            }
                        }));
                        tasks.add(Tuple.of(fluent, messageId, each));
                        fluent.addCallback(new FutureCallback<Any>() {
                            @Override
                            public void onSuccess(Any result) {

                            }
                            @Override
                            public void onFailure(Throwable t) {
                                logger.error(t.getMessage(), t);
                            }
                        }, MoreExecutors.directExecutor());
                    }

                    try {
                        boolean await = latch.await(timeout.get(), TimeUnit.SECONDS);

                        if (await) {
                            List<String> log = Lists.newArrayListWithExpectedSize(tasks.size());
                            for (var it : tasks) {
                                Any any = Uninterruptibles.getUninterruptibly(it._1());
                                UUID messageId = it._2().get();
                                StopWatch each = it._3();
                                synchronized (each) {
                                    if (each.isStopped()) {
                                        log.add(ImmutableMap.of("type", any.getTypeUrl(), "messageId", messageId, "took", each).toString());
                                    }
                                }
                            }

                            stopWatch.stop();
                            MDC.put(MdcTags.MDC_BATCH_TOOK, Long.toString(stopWatch.getDuration().toMillis()));
                            logger.trace("completed batch processing of {} in: {} {}", l.size(), stopWatch, log);
                        } else {
                            //
                            // ~
                            //
                            var complete = new ArrayList<FluentFuture<Any>>(tasks.size());
                            var incomplete = new ArrayList<FluentFuture<Any>>(tasks.size());
                            for (var it : tasks) {
                                FluentFuture<Any> fluent = it._1();
                                if (fluent.isDone()) {
                                    complete.add(fluent);
                                } else {
                                    incomplete.add(fluent);
                                }
                            }

                            MDC.put(MdcTags.MDC_BATCH_TOOK, Long.toString(TimeUnit.SECONDS.toMillis(timeout.get())));
                            logger.error("unable to complete replies. count: {}, timeout: {}, complete: {}, incomplete: {}",
                                    l.size(),
                                    timeout.get(),
                                    complete,
                                    incomplete);
                        }
                    } catch (InterruptedException err) {
                        logger.warn(err.getMessage(), err);
                        Thread.currentThread().interrupt();
                    } catch (Exception err) {
                        logger.error(err.getMessage(), err);
                    } finally {
                        ack.acknowledge();
                    }
                } finally {
                    MDC.remove(MdcTags.MDC_BATCH_ID);
                    MDC.remove(MdcTags.MDC_BATCH_TOOK);
                }
            }
        });
    }
}
