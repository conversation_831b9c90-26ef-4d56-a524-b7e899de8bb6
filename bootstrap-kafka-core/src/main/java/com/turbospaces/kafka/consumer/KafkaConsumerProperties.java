package com.turbospaces.kafka.consumer;

import java.security.KeyStore;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.KafkaServiceInfo;

public class KafkaConsumerProperties extends AbstractKafkaConsumerProperties {
    public KafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo si) {
        super(props, keyStore, si);

        String appId = props.CLOUD_APP_ID.get();
        String resetPolicy = props.KAFKA_AUTO_OFFSET_RESET.get();

        put(ConsumerConfig.GROUP_ID_CONFIG, appId);
        put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");

        //
        // ~ reset
        //
        if (StringUtils.isNotEmpty(resetPolicy)) {
            put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, resetPolicy);
        }
    }
}
