package com.turbospaces.kafka.consumer;

import java.util.concurrent.ExecutorService;

import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.executor.ThreadPoolContextWorker;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

public final class KafkaContextWorkerFactory extends AbstractKafkaContextWorkerFactory {
    public KafkaContextWorkerFactory(
            CurrentTransactionProvider currentTransaction,
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry) {
        super(currentTransaction, props, meterRegistry, rateLimiterRegistry);
    }
    @Override
    protected ContextWorker createWorker(ExecutorService pool) {
        var worker = new ThreadPoolContextWorker(props, meterRegistry, pool, true);
        worker.afterPropertiesSet();
        return worker;
    }
}
