package com.turbospaces.kafka.consumer;

import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.MicrometerConsumerListener;

public class KafkaWithMetricsConsumerFactory extends DefaultKafkaConsumerFactory<byte[], byte[]> {
    public KafkaWithMetricsConsumerFactory(AbstractKafkaConsumerProperties configs, MicrometerConsumerListener<byte[], byte[]> listener) {
        super(configs);

        setKeyDeserializer(new ByteArrayDeserializer());
        setValueDeserializer(new ByteArrayDeserializer());
        addListener(listener);
    }
}
