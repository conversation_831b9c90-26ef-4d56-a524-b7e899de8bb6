package com.turbospaces.kafka.consumer;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.ConsumerAwareRebalanceListener;
import org.springframework.kafka.support.Acknowledgment;

import com.google.common.collect.Lists;
import com.turbospaces.kafka.KafkaRecord;
import com.turbospaces.kafka.KafkaWorkUnit;

import io.netty.util.AsciiString;
import reactor.core.publisher.Flux;
import reactor.core.publisher.GroupedFlux;

public interface KafkaStreamConsumer extends ConsumerAwareRebalanceListener {
    default void accept(List<ConsumerRecord<byte[], byte[]>> data, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        List<KafkaRecord> l = Lists.newLinkedList();
        for (ConsumerRecord<byte[], byte[]> record : data) {
            l.add(new KafkaRecord(record));
        }
        accept(Flux.fromIterable(l), acknowledgment, consumer);
    }
    default void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        throw new UnsupportedOperationException();
    }

    Predicate<GroupedFlux<Optional<AsciiString>, KafkaWorkUnit>> KEY = new Predicate<>() {
        @Override
        public boolean test(GroupedFlux<Optional<AsciiString>, KafkaWorkUnit> group) {
            return group.key().isPresent();
        }
    };
}
