package com.turbospaces.kafka.consumer;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ConsumerRecordRecoverer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.FixedBackOff;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableScheduledFuture;
import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.TriggerOnceAfterNOccurrencesRateLimiter;

public abstract class KafkaAcceptorChannel extends ConcurrentMessageListenerContainer<byte[], byte[]> implements InitializingBean {
    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final ListeningScheduledExecutorService timer;
    protected final LoadingCache<TopicPartition, TriggerOnceAfterNOccurrencesRateLimiter> cache;
    private ListenableScheduledFuture<?> task;

    protected KafkaAcceptorChannel(ApplicationProperties props, ConsumerFactory<byte[], byte[]> consumerFactory, ContainerProperties containerProperties) {
        super(consumerFactory, containerProperties);
        this.props = Objects.requireNonNull(props);
        this.cache = CacheBuilder.newBuilder().build(new CacheLoader<>() {
            @Override
            public TriggerOnceAfterNOccurrencesRateLimiter load(TopicPartition key) throws Exception {
                var min = props.KAFKA_MIN_WORKERS.get();
                return new TriggerOnceAfterNOccurrencesRateLimiter(key.topic(), min, timer);
            }
        });

        //
        // ~ just one thread
        //
        this.timer = MoreExecutors.listeningDecorator(Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setPriority(Thread.MIN_PRIORITY);
                t.setDaemon(true);
                return t;
            }
        }));
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        Duration duration = props.APP_TIMER_INTERVAL.get();
        task = timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                cache.cleanUp();
            }
        }, 0, duration.toSeconds(), TimeUnit.SECONDS);

        if (BooleanUtils.isFalse(props.KAFKA_SPRING_BACK_OFF_ENABLED.get())) {
            //
            // ~ we don't need any back-off, but maybe some dead letter recoverer
            //
            var errorHandler = new DefaultErrorHandler(new ConsumerRecordRecoverer() {
                @Override
                public void accept(ConsumerRecord<?, ?> record, Exception err) {
                    var tp = new TopicPartition(record.topic(), record.partition());
                    var limiter = cache.getUnchecked(tp);
                    var msg = String.format("Record reached max retry attempts. topic: %s, partition: %d, offset: %d, check consumer lag metrics",
                            record.topic(),
                            record.partition(),
                            record.offset());

                    //
                    // ~ well we can't raise sentry alert for each record it is crazy, what if batch contains 100K records
                    //
                    log.atLevel(limiter.acquirePermission() ? Level.ERROR : Level.WARN).log(msg, err);
                }
            }, new FixedBackOff(0L, 0L));
            errorHandler.setAckAfterHandle(false);
            setCommonErrorHandler(errorHandler);
        }
    }
    @Override
    public void destroy() {
        try {
            super.destroy();
            for (var limiter : cache.asMap().values()) {
                limiter.dispose();
            }
        } finally {
            cache.invalidateAll();
            if (Objects.nonNull(task)) {
                task.cancel(true);
            }
        }
    }
    protected void subscribeOnChangeConcurrency(Property<Boolean> enabled) {
        props.KAFKA_MAX_POLL_CONCURRENCY.subscribe(new Consumer<Integer>() {
            @Override
            public void accept(Integer value) {
                if (Boolean.TRUE.equals(enabled.get())) {
                    if (isRunning()) {
                        stop();
                    }
                    setConcurrency(value);
                    start();
                    log.info("concurrency changed to: {} ", value);
                }
            }
        });
    }
}
