package com.turbospaces.kafka.consumer;

import java.security.KeyStore;
import java.util.HashMap;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.StickyAssignor;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaSecurity;
import com.turbospaces.ups.KafkaServiceInfo;

public abstract class AbstractKafkaConsumerProperties extends HashMap<String, Object> {
    protected AbstractKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo si) {
        int heartbeatMs = (int) props.KAFKA_HEARTBEAT_INTERVAL.get().toMillis();
        int sessionTimeoutMs = (int) props.KAFKA_SESSION_TIMEOUT.get().toMillis();
        int maxPollIntervalMs = (int) props.KAFKA_POLL_MAX_INTERVAL.get().toMillis();
        int maxPollRecords = props.KAFKA_POLL_MAX_RECORDS.get();

        put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, si.getBootstrapServers());
        put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, props.KAFKA_AUTO_OFFSET_RESET.orElse("latest").get());
        put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, heartbeatMs);
        put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, sessionTimeoutMs);
        put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, maxPollIntervalMs);
        put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG, StickyAssignor.class.getName());

        //
        // ~ SASL SSL
        //
        putAll(KafkaSecurity.configureSasl(si, keyStore));
    }
}
