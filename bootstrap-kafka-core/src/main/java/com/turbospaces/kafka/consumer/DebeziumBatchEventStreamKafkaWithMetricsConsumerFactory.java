package com.turbospaces.kafka.consumer;

import org.springframework.kafka.core.MicrometerConsumerListener;

public class DebeziumBatchEventStreamKafkaWithMetricsConsumerFactory extends KafkaWithMetricsConsumerFactory {
    public DebeziumBatchEventStreamKafkaWithMetricsConsumerFactory(DebeziumKafkaConsumerProperties configs, MicrometerConsumerListener<byte[], byte[]> listener) {
        super(configs, listener);
    }
}
