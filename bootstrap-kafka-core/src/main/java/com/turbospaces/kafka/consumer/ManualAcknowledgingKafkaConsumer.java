package com.turbospaces.kafka.consumer;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaWorkUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.BatchAcknowledgingConsumerAwareMessageListener;
import org.springframework.kafka.support.Acknowledgment;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@RequiredArgsConstructor
public class ManualAcknowledgingKafkaConsumer implements BatchAcknowledgingConsumerAwareMessageListener<byte[], byte[]>, KafkaStreamConsumer {
    private final ApplicationProperties props;
    private final KafkaStreamConsumer streamConsumer;

    @Override
    public void onMessage(List<ConsumerRecord<byte[], byte[]>> data, Acknowledgment ack, Consumer<?, ?> consumer) {
        StopWatch stopWatch = StopWatch.createStarted();
        AtomicBoolean nack = new AtomicBoolean(false);
        try {
            streamConsumer.accept(data, new Acknowledgment() {
                @Override
                public void acknowledge() {
                    stopWatch.stop();
                    ack.acknowledge();
                    log.trace("accepted batch of {} items in: {}", data.size(), stopWatch);
                }

                @Override
                public void nack(Duration sleep) {
                    ack.nack(sleep);
                    nack.set(true);
                }

                @Override
                public void nack(int index, Duration sleep) {
                    ack.nack(index, sleep);
                    nack.set(true);
                }

                @Override
                public void acknowledge(int index) {
                    ack.acknowledge(index);
                }
            }, consumer);
        } catch (Throwable err) {
            log.error(err.getMessage(), err);

            //
            // ~ well if nobody handled exception in code properly, we simply pause consumption and retry in 1m
            //
            if (Boolean.FALSE.equals(nack.get())) {
                ack.nack(0, props.KAFKA_NACK_POLL_INTERVAL.get());
            }
        }
    }

    @Override
    public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        streamConsumer.accept(flux, acknowledgment, consumer);
    }

    public boolean is(Class<?> type) {
        return type.isAssignableFrom(streamConsumer.getClass());
    }
}