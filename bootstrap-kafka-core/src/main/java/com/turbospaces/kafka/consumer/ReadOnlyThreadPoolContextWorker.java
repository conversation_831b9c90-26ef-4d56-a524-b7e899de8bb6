package com.turbospaces.kafka.consumer;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.ContextWorker;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.mdc.MdcTags;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReadOnlyThreadPoolContextWorker extends ThreadPoolContextWorker {
    private LoadingCache<String, Semaphore> quotaByActor;

    public ReadOnlyThreadPoolContextWorker(ApplicationProperties props, MeterRegistry meterRegistry, Executor executor, boolean autoClose) {
        super(props, meterRegistry, executor, autoClose);
    }
    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
        this.quotaByActor = CacheBuilder.newBuilder()
                .expireAfterAccess(props.CACHE_DEFAULT_MAX_TTL.get())
                .build(new CacheLoader<>() {
                    @Override
                    public Semaphore load(String key) {
                        return new Semaphore(props.KAFKA_MAX_REQUESTS_PER_ACTOR.get());
                    }
                });

        Duration cleanupInterval = props.APP_TIMER_INTERVAL.get();
        new GuavaCacheMetrics<>(quotaByActor, "quota-by-actor", Collections.emptyList()).bindTo(meterRegistry);
        timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                long size = quotaByActor.size();
                if (size > 0) {
                    log.debug("about to cleanUp auth0 cache of {} items ...", size);
                }
                quotaByActor.cleanUp();
            }
        }, cleanupInterval.toSeconds(), cleanupInterval.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public ContextWorker ifPresent(WorkUnit unit) {
        if (props.KAFKA_DO_NOT_QUEUE_PER_READ_ONLY_ACTOR.get()) {
            return this;
        }
        return super.ifPresent(unit);
    }
    @Override
    public void execute(Runnable command) {
        var actor = MDC.get(MdcTags.MDC_ROUTING_KEY);
        if (StringUtils.isNotEmpty(actor)) {
            Runnable toExecute = new Runnable() {
                @Override
                public void run() {
                    boolean acquired = false;
                    Semaphore quota = null;
                    Duration timeout = props.BATCH_COMPLETION_TIMEOUT.get();
                    int permits = props.KAFKA_MAX_REQUESTS_PER_ACTOR.get();
                    try {
                        quota = quotaByActor.get(actor);
                        acquired = quota.tryAcquire(timeout.toSeconds(), TimeUnit.SECONDS);
                        if (acquired) {
                            command.run();
                        } else {
                            log.error("Quota exceeded the limit of {} permits and no lock was acquired in {}", permits, timeout);
                        }
                    } catch (Exception er) {
                        ExceptionUtils.wrapAndThrow(er);
                    } finally {
                        if (acquired) {
                            quota.release();
                        }
                    }
                }
            };
            Map<String, String> mdc = MDC.getCopyOfContextMap();
            executor.execute(wrapRunnable(toExecute, mdc));
        } else {
            super.execute(command);
        }
    }
}
