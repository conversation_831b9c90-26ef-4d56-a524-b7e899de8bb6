package com.turbospaces.kafka.consumer;

import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

import org.apache.kafka.common.header.Header;

import com.google.common.collect.ImmutableMap;
import com.turbospaces.kafka.KafkaWorkUnit;

import io.opentracing.propagation.TextMap;

public class KafkaSpanContextExtractor implements TextMap {
    private final KafkaWorkUnit record;

    public KafkaSpanContextExtractor(KafkaWorkUnit record) {
        this.record = Objects.requireNonNull(record);
    }
    @Override
    public Iterator<Map.Entry<String, String>> iterator() {
        ImmutableMap.Builder<String, String> b = ImmutableMap.builder();
        for (Header header : record.headers()) {
            String k = header.key();
            String v = new String(header.value(), StandardCharsets.UTF_8);
            b.put(k, v);
        }
        return b.build().entrySet().iterator();
    }
    @Override
    public void put(String key, String value) {

    }
}
