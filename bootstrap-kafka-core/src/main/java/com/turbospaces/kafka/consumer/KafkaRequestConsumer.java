package com.turbospaces.kafka.consumer;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.turbospaces.api.ReadOnlyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.api.TopicRegistry;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.jpa.CompositeStackTracer;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.cache.ReplicatedCacheManager;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.SafeRequestHandler;
import com.turbospaces.dispatch.TransactionalRequestHandler;
import com.turbospaces.dispatch.TransactionalRequestOutcome;
import com.turbospaces.dispatch.WorkerCompletableTask;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.kafka.KafkaTransactionalRequest;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.TransactionalRequestOutcomePublisher;

import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.propagation.Format.Builtin;
import jakarta.inject.Inject;
import reactor.core.publisher.Sinks;

@Service
public class KafkaRequestConsumer extends AbstractKafkaRequestConsumer {
    private final TransactionalRequestOutcomePublisher<?> outcomePublisher;
    private final Tracer tracer;
    private final MeterRegistry meterRegistry;
    private final ReplicatedCacheManager cacheManager;
    private final TopicRegistry topicRegistry;
    private final Map<String, TransactionalRequestHandler<?, ?>> handlers;
    private final AbstractKafkaContextWorkerFactory workerFactory;

    @Inject
    public KafkaRequestConsumer(
            ApplicationProperties props,
            Tracer tracer,
            MeterRegistry meterRegistry,
            ReplicatedCacheManager cacheManager,
            TopicRegistry topicRegistry,
            TransactionalRequestOutcomePublisher<?> outcomePublisher,
            List<TransactionalRequestHandler<?, ?>> handlers,
            AbstractKafkaContextWorkerFactory workerFactory,
            ApiFactory apiFactory,
            CompositeStackTracer stackTracer,
            CurrentTransactionProvider tx) {
        super(props, apiFactory, stackTracer);
        this.outcomePublisher = Objects.requireNonNull(outcomePublisher);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.tracer = Objects.requireNonNull(tracer);
        this.cacheManager = Objects.requireNonNull(cacheManager);
        this.topicRegistry = Objects.requireNonNull(topicRegistry);
        this.handlers = ImmutableMap.copyOf(handlers.stream().collect(
                Collectors.toMap(new Function<TransactionalRequestHandler<?, ?>, String>() {
                    @Override
                    public String apply(TransactionalRequestHandler<?, ?> h) {
                        Message defaultInstance = com.google.protobuf.Internal.getDefaultInstance(h.requestType());
                        Any any = Any.pack(defaultInstance);
                        return any.getTypeUrl();
                    }
                }, Function.identity())));
        this.workerFactory = Objects.requireNonNull(workerFactory);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public WorkerCompletableTask schedule(WorkUnit unit, RequestWrapperFacade reqw, Sinks.One<Boolean> latch) throws Throwable {
        var record = (KafkaWorkUnit) unit;
        var body = reqw.body();
        var span = span(record, reqw);
        var typeUrl = body.getTypeUrl();
        var handler = Objects.requireNonNull(handlers.get(typeUrl), "unable to locate handler for type: " + typeUrl);

        // if (!handler.consumer().topic().equals(record.topic())) {
        // throw ApplicationException.of("Command route is wrong expected topic: " + handler.consumer().topic(), Code.ERR_SYSTEM);
        // }

        var callbackExecutor = MoreExecutors.directExecutor();
        var workUnit = (KafkaWorkUnit) unit;
        var worker = workerFactory.worker(workUnit);
        var tx = new KafkaTransactionalRequest<>(handler.requestType(), handler.newReply(), record, reqw, latch);
        var sh = new SafeRequestHandler(meterRegistry, tracer, reqw, apiFactory, stackTracer, span, record, tx, handler);
        var run = handler.decorate(topicRegistry, unit, sh);
        var callback = new FutureCallback<>() {
            @Override
            public void onSuccess(Object result) {
                //
                // ~ access executor not to clean it up, in case there are pending long running tasks in queue
                //
                worker.actor(unit, handler.actorIsRequired());
            }

            @Override
            public void onFailure(Throwable t) {
                logger.error(t.getMessage(), t);
            }
        };

        Topic topic = topicRegistry.forName(workUnit.topic());
        if (topic instanceof ReadOnlyTopic) {
            logger.trace("got request from read-only topic: {}, about to early acknowledge: {}", topic.name(), unit);
            sh.getTransaction().ack();
        } else if (handler.isImmediateAcknowledge()) {
            logger.trace("got request from topic: {}, about to early acknowledge: {}", topic.name(), unit);
            sh.getTransaction().ack();
        } else if (BooleanUtils.isFalse(handler.actorIsRequired())) {
            logger.trace("got non-sequential request from topic: {}, about to early acknowledge: {}", topic.name(), unit);
            sh.getTransaction().ack();
        }

        //
        // ~ should not happen
        //
        if (handler.actorIsRequired() && Objects.isNull(unit.key())) {
            throw new IllegalArgumentException("no routing key provided for " + unit.toString());
        }

        var actor = worker.actor(unit, handler.actorIsRequired());
        FluentFuture.from(actor.submit(run)).addCallback(callback, callbackExecutor);
        return sh.get();
    }

    @Override
    public void onSuccess(TransactionalRequestOutcome result) {
        try {
            outcomePublisher.publishReply(result);
            outcomePublisher.publishNotifications(apiFactory.notifyTopic(), result);
            outcomePublisher.publishEvents(apiFactory.eventsTopic(), result);
        } catch (Exception err) {
            logger.atError().setCause(err).log();
        }
    }

    @Override
    public void onFailure(Throwable t) {
        try {

        } finally {

        }
    }

    private Span span(KafkaWorkUnit record, RequestWrapperFacade reqw) {
        var body = reqw.body();
        var operation = PlatformUtil.toLowerUnderscore(body.getTypeUrl());

        //
        // ~ extract tracing information
        //
        var parentScope = tracer.extract(Builtin.TEXT_MAP, new KafkaSpanContextExtractor(record));

        //
        // ~ create child span
        //
        var buildSpan = tracer.buildSpan(operation);
        if (parentScope != null) {
            buildSpan = buildSpan.asChildOf(parentScope);
        }

        var span = buildSpan.start();
        span.setTag(MdcTags.MDC_MESSAGE_ID, reqw.headers().getMessageId());

        if (Objects.nonNull(record.key())) {
            AsciiString partitionKey = new AsciiString(record.key());
            span.setTag(MdcTags.MDC_ROUTING_KEY, partitionKey.toString());
        }

        return span;
    }

    @Override
    protected void invalidateCache() {
        try {
            cacheManager.clearAll(true);
        } catch (Throwable err) {
            logger.error(err.getMessage(), err);
        }
    }
}
