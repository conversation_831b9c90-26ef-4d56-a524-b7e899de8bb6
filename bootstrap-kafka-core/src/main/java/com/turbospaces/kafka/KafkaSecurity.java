package com.turbospaces.kafka;

import java.io.File;
import java.security.KeyStore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.security.auth.SecurityProtocol;
import org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule;
import org.apache.kafka.common.security.plain.PlainLoginModule;

import com.google.cloud.hosted.kafka.auth.GcpLoginCallbackHandler;
import com.google.common.collect.Iterables;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ssl.SSL;
import com.turbospaces.ups.KafkaServiceInfo;

import io.netty.handler.codec.http.QueryStringDecoder;
import lombok.experimental.UtilityClass;

@UtilityClass
public class KafkaSecurity {
    public static final String QUEURY_PARAM_SSL = "ssl";

    public static Map<String, Object> configureSasl(KafkaServiceInfo si, KeyStore keystore) {
        Map<String, Object> opts = new HashMap<>();
        if (isSaslUsernamePassword(si)) {
            String module = PlainLoginModule.class.getName();
            String username = si.getUserName();
            String password = si.getPassword();

            String keystorePassword = PlatformUtil.randomAlphanumeric(KafkaSecurity.class.getName().length());
            File keystoreFile = SSL.dumpTrustStore(keystore, keystorePassword);

            opts.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, SecurityProtocol.SASL_SSL.name);
            opts.put(SaslConfigs.SASL_MECHANISM, "plain".toUpperCase());
            opts.put(SaslConfigs.SASL_JAAS_CONFIG, String.format("%s required username=\"%s\" password=\"%s\";", module, username, password));

            opts.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, StringUtils.EMPTY);
            opts.put(SslConfigs.SSL_TRUSTSTORE_TYPE_CONFIG, "jks");
            opts.put(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG, keystoreFile.getAbsolutePath());
            opts.put(SslConfigs.SSL_KEY_PASSWORD_CONFIG, keystorePassword);
        } else if (isSaslOauth(si)) {
            String module = OAuthBearerLoginModule.class.getName();

            opts.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, SecurityProtocol.SASL_SSL.name);
            opts.put(SaslConfigs.SASL_MECHANISM, "oauthbearer".toUpperCase());
            opts.put(SaslConfigs.SASL_JAAS_CONFIG, String.format("%s required;", module));
            opts.put(SaslConfigs.SASL_LOGIN_CALLBACK_HANDLER_CLASS, GcpLoginCallbackHandler.class.getName());
        }

        return opts;
    }
    public static boolean isSaslUsernamePassword(KafkaServiceInfo si) {
        return StringUtils.isNotEmpty(si.getUserName()) && StringUtils.isNotEmpty(si.getPassword());
    }
    public static boolean isSaslOauth(KafkaServiceInfo si) {
        QueryStringDecoder decoder = new QueryStringDecoder(si.getUri());
        Map<String, List<String>> params = decoder.parameters();
        boolean secure = false;
        if (params.containsKey(QUEURY_PARAM_SSL)) {
            secure = Boolean.parseBoolean(Iterables.getOnlyElement(params.get(QUEURY_PARAM_SSL)));
        }
        return secure;
    }
}
