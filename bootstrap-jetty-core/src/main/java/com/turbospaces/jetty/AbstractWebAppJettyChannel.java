package com.turbospaces.jetty;

import java.util.Objects;

import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.eclipse.jetty.servlet.ServletMapping;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.cfg.ApplicationProperties;

import io.dropwizard.metrics.servlets.HealthCheckServlet;
import io.dropwizard.metrics.servlets.PingServlet;
import io.micrometer.core.instrument.MeterRegistry;

public abstract class AbstractWebAppJettyChannel extends JettyChannel {
    private final HealthCheckRegistry healthCheckRegistry;

    protected AbstractWebAppJettyChannel(ApplicationProperties props, MeterRegistry meterRegistry, HealthCheckRegistry healthCheckRegistry, int port) {
        super(props, meterRegistry, port);
        this.healthCheckRegistry = Objects.requireNonNull(healthCheckRegistry);
    }
    protected ServletContextHandler createPingServlet(String contextPath) {
        ServletMapping servletMapping = new ServletMapping();
        servletMapping.setServletName("ping");
        servletMapping.setPathSpec("/*");

        ServletContextHandler ping = new ServletContextHandler();
        ServletHandler sh = new ServletHandler();
        sh.setServlets(new ServletHolder[] { new ServletHolder(servletMapping.getServletName(), PingServlet.class) });
        sh.setServletMappings(new ServletMapping[] { servletMapping });
        ping.setServletHandler(sh);
        ping.setContextPath(contextPath);

        return ping;
    }
    protected ServletContextHandler createHealthCheckServlet(String contextPath) {
        ServletMapping servletMapping = new ServletMapping();
        servletMapping.setServletName("healthcheck");
        servletMapping.setPathSpec("/*");

        HealthCheckServlet servlet = new HealthCheckServlet(healthCheckRegistry);

        ServletContextHandler ping = new ServletContextHandler();
        ServletHandler sh = new ServletHandler();
        sh.setServlets(new ServletHolder[] { new ServletHolder(servletMapping.getServletName(), servlet) });
        sh.setServletMappings(new ServletMapping[] { servletMapping });
        ping.setServletHandler(sh);
        ping.setContextPath(contextPath);

        return ping;
    }
}
