package com.turbospaces.jetty;

import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.boot.Bootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;

import io.micrometer.core.instrument.MeterRegistry;

public class JettyTest {
    @Test
    public void works() throws Throwable {
        int port = PlatformUtil.findAvailableTcpPort();

        ApplicationConfig cfg = ApplicationConfig.mock(port);
        Bootstrap bootstrap = new SimpleBootstrap(new ApplicationProperties(cfg.factory()), AppConfig.class);
        bootstrap.run();

        try {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet req = new HttpGet();
                req.setURI(new URIBuilder().setScheme("http").setHost("localhost").setPort(port).setPath("/ping").build());
                try (CloseableHttpResponse resp = httpClient.execute(req)) {
                    Assertions.assertEquals(HttpStatus.SC_OK, resp.getStatusLine().getStatusCode());
                }
            }
        } finally {
            bootstrap.shutdown();
        }
    }

    @Configuration
    public static class AppConfig {
        @Bean
        public AbstractWebAppJettyChannel adminChannel(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                HealthCheckRegistry healthCheckRegistry) throws Throwable {
            return new AbstractWebAppJettyChannel(props, meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get()) {
                @Override
                public void afterPropertiesSet() throws Exception {
                    addHandler(createPingServlet("/ping"));
                    super.afterPropertiesSet();
                }
            };
        }
    }
}
